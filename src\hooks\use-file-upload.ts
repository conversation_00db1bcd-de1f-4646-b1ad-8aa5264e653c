import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

export interface UploadedFile {
  originalName: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  size: number;
  mimeType: string;
}

export interface UploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  result?: UploadedFile;
}

export function useFileUpload() {
  const t = useTranslations('messages');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);

  const uploadFiles = async (files: File[]): Promise<UploadedFile[]> => {
    if (files.length === 0) {
      throw new Error(t('upload.errors.fileRequired'));
    }

    setIsUploading(true);

    // 初始化进度状态
    const initialProgress: UploadProgress[] = files.map(file => ({
      file,
      progress: 0,
      status: 'pending',
    }));
    setUploadProgress(initialProgress);

    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      // 更新状态为上传中
      setUploadProgress(prev =>
        prev.map(item => ({
          ...item,
          status: 'uploading' as const,
          progress: 50,
        })),
      );

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '上传失败');
      }

      // 更新状态为成功
      setUploadProgress(prev =>
        prev.map((item, index) => ({
          ...item,
          status: 'success' as const,
          progress: 100,
          result: result.data[index],
        })),
      );

      toast.success(
        `${t('success.upload')} ${files.length} ${t('upload.fileCount')}`,
      );
      return result.data;
    } catch (error) {
      // 更新状态为错误
      const errorMessage =
        error instanceof Error ? error.message : t('error.upload');
      setUploadProgress(prev =>
        prev.map(item => ({
          ...item,
          status: 'error' as const,
          error: errorMessage,
        })),
      );

      toast.error(errorMessage);
      throw error;
    } finally {
      setIsUploading(false);
      // 3秒后清除进度状态
      setTimeout(() => {
        setUploadProgress([]);
      }, 3000);
    }
  };

  const uploadSingleFile = async (file: File): Promise<UploadedFile> => {
    const results = await uploadFiles([file]);
    return results[0];
  };

  const resetProgress = () => {
    setUploadProgress([]);
  };

  return {
    uploadFiles,
    uploadSingleFile,
    isUploading,
    uploadProgress,
    resetProgress,
  };
}
