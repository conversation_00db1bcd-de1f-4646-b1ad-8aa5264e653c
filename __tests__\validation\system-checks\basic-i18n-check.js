const fs = require('fs');
const path = require('path');

console.log('=== RefundGo 邮件国际化快速检查 ===\n');

const files = [
  'src/lib/email-templates/verification-code-i18n.ts',
  'src/lib/email-templates/task-completed-i18n.ts',
  'src/lib/email-templates/deposit-success-i18n.ts',
  'src/lib/email-templates/deposit-failed-i18n.ts',
  'src/lib/email-templates/withdrawal-approved-i18n.ts',
  'src/lib/email-templates/withdrawal-rejected-i18n.ts',
  'src/lib/email-templates/task-cancelled-publisher-i18n.ts',
  'src/lib/email-templates/task-cancelled-accepter-i18n.ts',
  'src/lib/email-templates/task-accepted-publisher-i18n.ts',
  'src/lib/email.ts',
  'src/app/api/send-email/route.ts',
];

let existingFiles = 0;
let i18nSupport = 0;

files.forEach(file => {
  try {
    const filePath = path.join(__dirname, '../../../', file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      console.log(`✅ ${file}`);
      existingFiles++;

      // 检查国际化支持
      if (
        content.includes('language?:') ||
        content.includes('zh:') ||
        content.includes('subjects')
      ) {
        console.log(`   🌐 支持国际化`);
        i18nSupport++;
      }
    } else {
      console.log(`❌ ${file} - 文件不存在`);
    }
  } catch (error) {
    console.log(`❌ ${file} - 读取错误: ${error.message}`);
  }
});

console.log(`\n📊 检查结果:`);
console.log(`   文件存在: ${existingFiles}/${files.length}`);
console.log(`   国际化支持: ${i18nSupport}/${files.length}`);

if (existingFiles === files.length && i18nSupport >= 9) {
  console.log('\n🎉 RefundGo邮件国际化系统基本完成！');
  console.log('\n✨ 已实现功能：');
  console.log('   • 完整的中英文双语邮件模板');
  console.log('   • 基于用户注册语言偏好的语言检测');
  console.log('   • 邮件主题和内容的国际化');
  console.log('   • 统一的邮件样式设计');
  console.log('   • API路由的语言支持');

  console.log('\n🌐 支持的邮件类型：');
  console.log('   • 注册验证码邮件');
  console.log('   • 任务完成通知邮件');
  console.log('   • 充值成功/失败邮件');
  console.log('   • 提现审核通过/拒绝邮件');
  console.log('   • 委托取消通知邮件');
  console.log('   • 委托被接受通知邮件');

  console.log('\n🔧 使用方式：');
  console.log('   • 系统自动根据用户注册语言发送邮件');
  console.log('   • 支持中文(zh)和英文(en)');
  console.log('   • API: POST /api/send-email');
} else {
  console.log('\n⚠️ 部分功能需要完善');
}

console.log('\n=== 检查完成 ===');
