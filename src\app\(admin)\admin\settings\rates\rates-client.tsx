'use client';

import {
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Calculator,
  Percent,
  // DollarSign,
  // CreditCard,
  // ShoppingCart,
  // Smartphone,
  AlertTriangle,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// 平台数据
interface Platform {
  id: number;
  name: string;
  status: '启用' | '禁用';
  createdAt: string;
}

// 商品分类数据
interface Category {
  id: number;
  name: string;
  status: '启用' | '禁用';
  createdAt: string;
}

// 拒付类型数据
interface ChargebackType {
  id: number;
  name: string;
  rate: number;
  status: '启用' | '禁用';
  createdAt: string;
}

// 支付方式数据
interface PaymentMethod {
  id: number;
  name: string;
  rate: number;
  status: '启用' | '禁用';
  createdAt: string;
}

// 系统费率配置
interface SystemRates {
  noEvidenceExtraRate: number; // 无凭证额外费率
  depositRatio: number; // 押金比例
}

export default function RatesSettingsClient() {
  // 模拟数据
  const [platforms, setPlatforms] = useState<Platform[]>([
    { id: 1, name: '淘宝', status: '启用', createdAt: '2024-01-01' },
    { id: 2, name: '京东', status: '启用', createdAt: '2024-01-01' },
    { id: 3, name: '拼多多', status: '启用', createdAt: '2024-01-01' },
    { id: 4, name: '抖音', status: '启用', createdAt: '2024-01-01' },
    { id: 5, name: '小红书', status: '禁用', createdAt: '2024-01-01' },
  ]);

  const [categories, setCategories] = useState<Category[]>([
    { id: 1, name: '服装鞋帽', status: '启用', createdAt: '2024-01-01' },
    { id: 2, name: '数码家电', status: '启用', createdAt: '2024-01-01' },
    { id: 3, name: '美妆护肤', status: '启用', createdAt: '2024-01-01' },
    { id: 4, name: '食品饮料', status: '启用', createdAt: '2024-01-01' },
    { id: 5, name: '母婴用品', status: '禁用', createdAt: '2024-01-01' },
  ]);

  const [chargebackTypes, setChargebackTypes] = useState<ChargebackType[]>([
    {
      id: 1,
      name: '虚假发货',
      rate: 5.0,
      status: '启用',
      createdAt: '2024-01-01',
    },
    {
      id: 2,
      name: '商品不符',
      rate: 4.5,
      status: '启用',
      createdAt: '2024-01-01',
    },
    {
      id: 3,
      name: '质量问题',
      rate: 4.0,
      status: '启用',
      createdAt: '2024-01-01',
    },
    {
      id: 4,
      name: '物流问题',
      rate: 3.5,
      status: '启用',
      createdAt: '2024-01-01',
    },
    {
      id: 5,
      name: '服务态度',
      rate: 3.0,
      status: '启用',
      createdAt: '2024-01-01',
    },
    {
      id: 6,
      name: '虚假宣传',
      rate: 6.0,
      status: '禁用',
      createdAt: '2024-01-01',
    },
  ]);

  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);

  const [systemRates, setSystemRates] = useState<SystemRates>({
    noEvidenceExtraRate: 0,
    depositRatio: 0,
  });

  // 对话框状态
  const [isAddPlatformOpen, setIsAddPlatformOpen] = useState(false);
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isAddChargebackOpen, setIsAddChargebackOpen] = useState(false);
  const [isAddPaymentOpen, setIsAddPaymentOpen] = useState(false);

  // 表单状态
  const [newPlatform, setNewPlatform] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [newChargeback, setNewChargeback] = useState({ name: '', rate: 0 });
  const [newPayment, setNewPayment] = useState({ name: '', rate: 0 });

  // 添加平台
  const handleAddPlatform = () => {
    if (!newPlatform.trim()) {
      toast.error('请输入平台名称');
      return;
    }

    const newId = Math.max(...platforms.map(p => p.id)) + 1;
    setPlatforms([
      ...platforms,
      {
        id: newId,
        name: newPlatform,
        status: '启用',
        createdAt: new Date().toISOString().split('T')[0],
      },
    ]);

    setNewPlatform('');
    setIsAddPlatformOpen(false);
    toast.success('平台添加成功');
  };

  // 添加商品分类
  const handleAddCategory = () => {
    if (!newCategory.trim()) {
      toast.error('请输入分类名称');
      return;
    }

    const newId = Math.max(...categories.map(c => c.id)) + 1;
    setCategories([
      ...categories,
      {
        id: newId,
        name: newCategory,
        status: '启用',
        createdAt: new Date().toISOString().split('T')[0],
      },
    ]);

    setNewCategory('');
    setIsAddCategoryOpen(false);
    toast.success('分类添加成功');
  };

  // 添加拒付类型
  const handleAddChargeback = () => {
    if (!newChargeback.name.trim()) {
      toast.error('请输入拒付类型名称');
      return;
    }
    if (newChargeback.rate <= 0) {
      toast.error('请输入有效的费率');
      return;
    }

    const newId = Math.max(...chargebackTypes.map(c => c.id)) + 1;
    setChargebackTypes([
      ...chargebackTypes,
      {
        id: newId,
        name: newChargeback.name,
        rate: newChargeback.rate,
        status: '启用',
        createdAt: new Date().toISOString().split('T')[0],
      },
    ]);

    setNewChargeback({ name: '', rate: 0 });
    setIsAddChargebackOpen(false);
    toast.success('拒付类型添加成功');
  };

  // 添加支付方式
  const handleAddPayment = () => {
    if (!newPayment.name.trim()) {
      toast.error('请输入支付方式名称');
      return;
    }
    if (newPayment.rate <= 0) {
      toast.error('请输入有效的费率');
      return;
    }

    const newId = Math.max(...paymentMethods.map(p => p.id)) + 1;
    setPaymentMethods([
      ...paymentMethods,
      {
        id: newId,
        name: newPayment.name,
        rate: newPayment.rate,
        status: '启用',
        createdAt: new Date().toISOString().split('T')[0],
      },
    ]);

    setNewPayment({ name: '', rate: 0 });
    setIsAddPaymentOpen(false);
    toast.success('支付方式添加成功');
  };

  // 删除项目
  const handleDelete = (type: string, id: number) => {
    switch (type) {
      case 'platform':
        setPlatforms(platforms.filter(p => p.id !== id));
        break;
      case 'category':
        setCategories(categories.filter(c => c.id !== id));
        break;
      case 'chargeback':
        setChargebackTypes(chargebackTypes.filter(c => c.id !== id));
        break;
      case 'payment':
        setPaymentMethods(paymentMethods.filter(p => p.id !== id));
        break;
    }
    toast.success('删除成功');
  };

  // 更新系统费率
  const handleUpdateSystemRates = () => {
    toast.success('系统费率配置已更新');
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    return status === '启用'
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800';
  };

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='费率设置' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题 */}
        <div className='space-y-1'>
          <h1 className='text-2xl font-bold tracking-tight'>费率设置</h1>
          <p className='text-sm text-muted-foreground'>
            管理平台、商品分类、拒付类型和支付方式的费率配置
          </p>
        </div>

        <Tabs defaultValue='platforms' className='w-full'>
          <TabsList className='grid w-full grid-cols-5'>
            <TabsTrigger value='platforms'>平台管理</TabsTrigger>
            <TabsTrigger value='categories'>商品分类</TabsTrigger>
            <TabsTrigger value='chargebacks'>拒付类型</TabsTrigger>
            <TabsTrigger value='payments'>支付方式</TabsTrigger>
            <TabsTrigger value='system'>系统费率</TabsTrigger>
          </TabsList>

          {/* 平台管理 */}
          <TabsContent value='platforms' className='space-y-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>平台管理</CardTitle>
                <Dialog
                  open={isAddPlatformOpen}
                  onOpenChange={setIsAddPlatformOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加平台
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加平台</DialogTitle>
                      <DialogDescription>添加新的电商平台</DialogDescription>
                    </DialogHeader>
                    <div>
                      <Label htmlFor='platform-name'>平台名称</Label>
                      <Input
                        id='platform-name'
                        placeholder='请输入平台名称'
                        value={newPlatform}
                        onChange={e => setNewPlatform(e.target.value)}
                      />
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddPlatformOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddPlatform}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>平台名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {platforms.map(platform => (
                      <TableRow key={platform.id}>
                        <TableCell className='font-medium'>
                          {platform.name}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(platform.status)}>
                            {platform.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{platform.createdAt}</TableCell>
                        <TableCell className='text-right'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem>
                                <Edit className='mr-2 h-4 w-4' />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className='text-red-600'
                                onClick={() =>
                                  handleDelete('platform', platform.id)
                                }
                              >
                                <Trash2 className='mr-2 h-4 w-4' />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 商品分类 */}
          <TabsContent value='categories' className='space-y-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>商品分类</CardTitle>
                <Dialog
                  open={isAddCategoryOpen}
                  onOpenChange={setIsAddCategoryOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加分类
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加商品分类</DialogTitle>
                      <DialogDescription>添加新的商品分类</DialogDescription>
                    </DialogHeader>
                    <div>
                      <Label htmlFor='category-name'>分类名称</Label>
                      <Input
                        id='category-name'
                        placeholder='请输入分类名称'
                        value={newCategory}
                        onChange={e => setNewCategory(e.target.value)}
                      />
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddCategoryOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddCategory}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>分类名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.map(category => (
                      <TableRow key={category.id}>
                        <TableCell className='font-medium'>
                          {category.name}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(category.status)}>
                            {category.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{category.createdAt}</TableCell>
                        <TableCell className='text-right'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem>
                                <Edit className='mr-2 h-4 w-4' />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className='text-red-600'
                                onClick={() =>
                                  handleDelete('category', category.id)
                                }
                              >
                                <Trash2 className='mr-2 h-4 w-4' />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 拒付类型 */}
          <TabsContent value='chargebacks' className='space-y-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>拒付类型</CardTitle>
                <Dialog
                  open={isAddChargebackOpen}
                  onOpenChange={setIsAddChargebackOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加拒付类型
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加拒付类型</DialogTitle>
                      <DialogDescription>
                        添加新的拒付类型和对应费率
                      </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div>
                        <Label htmlFor='chargeback-name'>拒付类型</Label>
                        <Input
                          id='chargeback-name'
                          placeholder='请输入拒付类型名称'
                          value={newChargeback.name}
                          onChange={e =>
                            setNewChargeback({
                              ...newChargeback,
                              name: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='chargeback-rate'>费率 (%)</Label>
                        <Input
                          id='chargeback-rate'
                          type='number'
                          step='0.1'
                          placeholder='请输入费率'
                          value={newChargeback.rate}
                          onChange={e =>
                            setNewChargeback({
                              ...newChargeback,
                              rate: parseFloat(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddChargebackOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddChargeback}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>拒付类型</TableHead>
                      <TableHead>费率</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {chargebackTypes.map(chargeback => (
                      <TableRow key={chargeback.id}>
                        <TableCell className='font-medium'>
                          {chargeback.name}
                        </TableCell>
                        <TableCell>
                          <span className='font-medium text-orange-600'>
                            {chargeback.rate}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(chargeback.status)}>
                            {chargeback.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{chargeback.createdAt}</TableCell>
                        <TableCell className='text-right'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem>
                                <Edit className='mr-2 h-4 w-4' />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className='text-red-600'
                                onClick={() =>
                                  handleDelete('chargeback', chargeback.id)
                                }
                              >
                                <Trash2 className='mr-2 h-4 w-4' />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 支付方式 */}
          <TabsContent value='payments' className='space-y-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>支付方式</CardTitle>
                <Dialog
                  open={isAddPaymentOpen}
                  onOpenChange={setIsAddPaymentOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加支付方式
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加支付方式</DialogTitle>
                      <DialogDescription>
                        添加新的支付方式和对应费率
                      </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div>
                        <Label htmlFor='payment-name'>支付名称</Label>
                        <Input
                          id='payment-name'
                          placeholder='请输入支付方式名称'
                          value={newPayment.name}
                          onChange={e =>
                            setNewPayment({
                              ...newPayment,
                              name: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='payment-rate'>费率 (%)</Label>
                        <Input
                          id='payment-rate'
                          type='number'
                          step='0.1'
                          placeholder='请输入费率'
                          value={newPayment.rate}
                          onChange={e =>
                            setNewPayment({
                              ...newPayment,
                              rate: parseFloat(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddPaymentOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddPayment}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>支付名称</TableHead>
                      <TableHead>费率</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentMethods.map(payment => (
                      <TableRow key={payment.id}>
                        <TableCell className='font-medium'>
                          {payment.name}
                        </TableCell>
                        <TableCell>
                          <span className='font-medium text-blue-600'>
                            {payment.rate}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(payment.status)}>
                            {payment.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{payment.createdAt}</TableCell>
                        <TableCell className='text-right'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem>
                                <Edit className='mr-2 h-4 w-4' />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className='text-red-600'
                                onClick={() =>
                                  handleDelete('payment', payment.id)
                                }
                              >
                                <Trash2 className='mr-2 h-4 w-4' />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 系统费率 */}
          <TabsContent value='system' className='space-y-4'>
            <div className='grid gap-6 md:grid-cols-2'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <AlertTriangle className='h-5 w-5 text-orange-500' />
                    无凭证额外费率
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='no-evidence-rate'>费率 (%)</Label>
                    <Input
                      id='no-evidence-rate'
                      type='number'
                      step='0.1'
                      value={systemRates.noEvidenceExtraRate}
                      onChange={e =>
                        setSystemRates({
                          ...systemRates,
                          noEvidenceExtraRate: parseFloat(e.target.value) || 0,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    当委托无拒付凭证时，额外收取的费率
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Percent className='h-5 w-5 text-blue-500' />
                    押金比例
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='deposit-ratio'>比例 (%)</Label>
                    <Input
                      id='deposit-ratio'
                      type='number'
                      step='0.1'
                      value={systemRates.depositRatio}
                      onChange={e =>
                        setSystemRates({
                          ...systemRates,
                          depositRatio: parseFloat(e.target.value) || 0,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    用户接单时需要缴纳的押金比例
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardContent className='pt-6'>
                <div className='flex justify-end'>
                  <Button onClick={handleUpdateSystemRates}>
                    <Calculator className='mr-2 h-4 w-4' />
                    保存系统费率配置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminPageLayout>
  );
}
