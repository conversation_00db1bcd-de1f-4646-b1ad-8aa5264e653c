import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { PaymentManager } from '@/lib/payment/payment-manager';

// 会员购买请求验证Schema
const purchaseSchema = z.object({
  planId: z.string().min(1, '请选择要购买的套餐'),
  planName: z.string().min(1, '套餐名称不能为空'),
  paymentMethod: z.string().min(1, '请选择支付方式'),
  action: z.enum(['upgrade', 'renew']).default('upgrade'), // 升级或续费
});

export async function POST(request: NextRequest) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 2. 验证请求数据
    const body = await request.json();
    const validation = purchaseSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: '数据验证失败',
          details: validation.error.issues.map(issue => issue.message),
        },
        { status: 400 },
      );
    }

    const { planId, planName, paymentMethod, action } = validation.data;

    // 3. 验证套餐是否存在
    const plan = await prisma.membershipPlan.findFirst({
      where: {
        id: planId,
        isActive: true,
      },
    });

    if (!plan) {
      return NextResponse.json(
        { success: false, message: '选择的套餐不存在或已禁用' },
        { status: 400 },
      );
    }

    // 4. 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    // 5. 验证操作类型
    if (action === 'upgrade') {
      // 升级验证逻辑
      const currentPlanName =
        user.memberPlan === 'FREE'
          ? '免费版'
          : user.memberPlan === 'PRO'
            ? '专业版'
            : '商业版';

      if (plan.name === currentPlanName) {
        return NextResponse.json(
          { success: false, message: '您已经是此套餐用户' },
          { status: 400 },
        );
      }

      // 防止降级
      if (plan.name === '免费版') {
        return NextResponse.json(
          { success: false, message: '不支持降级到免费版' },
          { status: 400 },
        );
      }

      // 防止商业版降级到专业版
      if (user.memberPlan === 'BUSINESS' && plan.name === '专业版') {
        return NextResponse.json(
          { success: false, message: '商业版用户不支持降级到专业版' },
          { status: 400 },
        );
      }
    } else if (action === 'renew') {
      // 续费验证逻辑
      if (user.memberPlan === 'FREE') {
        return NextResponse.json(
          { success: false, message: '免费版用户无需续费' },
          { status: 400 },
        );
      }

      const currentPlanName = user.memberPlan === 'PRO' ? '专业版' : '商业版';
      if (plan.name !== currentPlanName) {
        return NextResponse.json(
          { success: false, message: '续费套餐与当前套餐不匹配' },
          { status: 400 },
        );
      }
    }

    // 6. 验证支付方式
    const paymentConfigs = await prisma.paymentConfig.findMany({
      where: { isEnabled: true },
    });

    let selectedProvider: string = '';
    let isValidMethod = false;
    let feeRate: number = 0;
    let minAmount: number = 0;

    // 检查支付方式是否可用
    for (const config of paymentConfigs) {
      if (
        config.provider === 'yunpay' &&
        ['alipay', 'wxpay', 'paypal'].includes(paymentMethod)
      ) {
        const settings = config.settings as any;
        const paymentMethods = settings.paymentMethods || {};
        if (paymentMethods[paymentMethod]?.enabled) {
          selectedProvider = 'yunpay';
          isValidMethod = true;
          feeRate = paymentMethods[paymentMethod].feeRate || 0;
          minAmount = paymentMethods[paymentMethod].minAmount || 1;
          break;
        }
      } else if (
        config.provider === 'nowpayments' &&
        paymentMethod === 'crypto'
      ) {
        const settings = config.settings as any;
        selectedProvider = 'nowpayments';
        isValidMethod = true;
        feeRate = settings.feeRate || 0;
        minAmount = settings.minAmount || 1;
        break;
      }
    }

    if (!isValidMethod) {
      return NextResponse.json(
        {
          success: false,
          message: '选择的支付方式不可用',
        },
        { status: 400 },
      );
    }

    // 7. 验证最低支付金额
    if (plan.price < minAmount) {
      return NextResponse.json(
        {
          success: false,
          message: `最低支付金额为$${minAmount.toFixed(2)}`,
        },
        { status: 400 },
      );
    }

    // 8. 计算支付金额
    const membershipPrice = plan.price; // 会员套餐价格
    const feeAmount = (membershipPrice * feeRate) / 100; // 手续费
    const totalPaymentAmount = membershipPrice + feeAmount; // 总支付金额

    // 9. 生成订单号
    const orderPrefix =
      action === 'upgrade' ? 'MEMBERSHIP_UPGRADE' : 'MEMBERSHIP_RENEW';
    const orderNo = `${orderPrefix}_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // 10. 构建回调URL
    const notifyUrl = `${process.env.NEXTAUTH_URL}/api/payments/notify/${selectedProvider}`;
    const returnUrl = `${process.env.NEXTAUTH_URL}/membership?status=success`;

    // 11. 创建支付订单
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        orderNo,
        provider: selectedProvider,
        paymentMethod,
        amount: totalPaymentAmount,
        currency: 'USD',
        description: `会员${action === 'upgrade' ? '升级' : '续费'} - ${plan.name}`,
        userId: session.user.id,
        status: 'PENDING',
        expiredAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟过期
        notifyUrl,
        returnUrl,
        metadata: {
          action, // upgrade 或 renew
          planId: plan.id,
          planName: plan.name,
          membershipPrice, // 实际会员价格
          feeAmount: parseFloat(feeAmount.toFixed(2)), // 手续费
          feeRate, // 手续费率
          previousPlan: user.memberPlan,
          previousExpiry: user.memberPlanExpiry,
        },
      },
    });

    // 12. 创建钱包交易记录
    await prisma.walletTransaction.create({
      data: {
        userId: session.user.id,
        type: 'MEMBERSHIP',
        amount: -totalPaymentAmount, // 负数表示支出
        status: 'PENDING',
        description: `会员${action === 'upgrade' ? '升级' : '续费'} - ${plan.name}（含手续费${feeAmount.toFixed(2)}）`,
        reference: orderNo,
      },
    });

    // 13. 初始化支付管理器
    const paymentManager = new PaymentManager();

    try {
      // 获取支付提供商
      const provider = await paymentManager.getProvider(selectedProvider);
      if (!provider) {
        throw new Error(`支付提供商 ${selectedProvider} 不可用`);
      }

      // 创建支付
      const paymentParams = {
        orderNo,
        amount: totalPaymentAmount,
        currency: 'USD',
        paymentMethod,
        description: `会员${action === 'upgrade' ? '升级' : '续费'} - ${plan.name}`,
        returnUrl,
        notifyUrl,
        userId: session.user.id,
      };

      const paymentResult = await provider.createPayment(paymentParams);

      // 更新支付订单信息，包括货币转换信息
      const updateData: any = {
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        thirdOrderNo: paymentResult.thirdOrderNo,
      };

      // 如果有货币转换信息，记录到数据库
      if (paymentResult.currencyConversion) {
        const conversion = paymentResult.currencyConversion;
        updateData.originalAmount = conversion.originalAmount;
        updateData.originalCurrency = conversion.originalCurrency;
        updateData.convertedAmount = conversion.convertedAmount;
        updateData.convertedCurrency = conversion.convertedCurrency;
        updateData.exchangeRate = conversion.exchangeRate;
        updateData.exchangeRateSource = conversion.source;
      }

      await prisma.paymentOrder.update({
        where: { orderNo },
        data: updateData,
      });

      // 记录支付日志
      await prisma.paymentLog.create({
        data: {
          orderNo,
          action: `CREATE_MEMBERSHIP_${action.toUpperCase()}`,
          request: {
            ...validation.data,
            feeRate,
            feeAmount,
            totalPaymentAmount,
          },
          response: JSON.parse(JSON.stringify(paymentResult)),
          status: 'SUCCESS',
          message: `Membership ${action} payment created successfully`,
        },
      });

      return NextResponse.json({
        success: true,
        message: `会员${action === 'upgrade' ? '升级' : '续费'}订单创建成功`,
        data: {
          orderNo,
          planName: plan.name,
          membershipPrice,
          feeAmount: parseFloat(feeAmount.toFixed(2)),
          feeRate,
          totalPaymentAmount: parseFloat(totalPaymentAmount.toFixed(2)),
          paymentUrl: paymentResult.paymentUrl,
          qrCode: paymentResult.qrCode,
          expiredAt: paymentOrder.expiredAt,
          method: paymentMethod,
          action,
        },
      });
    } catch (paymentError: any) {
      // 更新订单和交易状态为失败
      await Promise.all([
        prisma.paymentOrder.update({
          where: { orderNo },
          data: { status: 'FAILED' },
        }),
        prisma.walletTransaction.updateMany({
          where: { reference: orderNo },
          data: { status: 'FAILED' },
        }),
      ]);

      // 记录错误日志
      await prisma.paymentLog.create({
        data: {
          orderNo,
          action: `CREATE_MEMBERSHIP_${action.toUpperCase()}`,
          request: {
            ...validation.data,
            feeRate,
            feeAmount,
            totalPaymentAmount,
          },
          response: { error: paymentError.message },
          status: 'FAILED',
          message: paymentError.message,
        },
      });

      throw paymentError;
    }
  } catch (error) {
    console.error('会员购买API错误:', error);
    return NextResponse.json(
      {
        success: false,
        error: '创建会员购买订单失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}
