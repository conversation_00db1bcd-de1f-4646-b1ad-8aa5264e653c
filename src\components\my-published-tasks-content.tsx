'use client';

import {
  Clock,
  Calendar,
  Building2,
  FileText,
  CheckCircle,
  AlertCircle,
  XCircle,
  Users,
  Eye,
  Trash2,
  Loader2,
  X,
  Upload,
  Truck,
  Package,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useMemo, useEffect } from 'react';

import { CancelTaskDialog } from '@/components/cancel-task-dialog';
import { ConfirmDeliveryDialog } from '@/components/confirm-delivery-dialog';
import { CountdownTimer } from '@/components/countdown-timer';
import { LogisticsInfoDialog } from '@/components/logistics-info-dialog';
import { PublishedTaskDetailSheet } from '@/components/published-task-detail-sheet';
import { ResubmitEvidenceDialog } from '@/components/resubmit-evidence-dialog';
import { ReviewOrderDialog } from '@/components/review-order-dialog';
import { SubmitEvidenceDialog } from '@/components/submit-evidence-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ViewOrderDialog } from '@/components/view-order-dialog';
import { useCancelTask } from '@/hooks/use-cancel-task';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import { UploadedFile } from '@/hooks/use-file-upload';
import {
  usePublishedTasks,
  type PublishedTask,
} from '@/hooks/use-published-tasks';
import { useResponsive } from '@/hooks/use-responsive';
import { useResubmitEvidence } from '@/hooks/use-resubmit-evidence';
import { useSubmitEvidence } from '@/hooks/use-submit-evidence';
import {
  getTaskCommission,
  getTaskBaseCommission,
} from '@/lib/utils/commission';

interface PublishedTaskCardProps {
  task: PublishedTask;
}

function PublishedTaskCard({ task }: PublishedTaskCardProps) {
  const t = useTranslations('my-published-tasks');
  const tPublish = useTranslations('publish');

  const [mounted, setMounted] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const cancelTask = useCancelTask();
  const resubmitEvidenceMutation = useResubmitEvidence();
  const submitEvidenceMutation = useSubmitEvidence();

  useEffect(() => {
    setMounted(true);

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const { chargebackTypes, paymentMethods, systemRate } = useCommissionRates();

  // 获取平台名称的翻译
  const getPlatformName = (platform: any) => {
    if (typeof platform === 'object' && platform?.name) {
      // 如果是对象，尝试翻译name字段
      return (
        tPublish(`platformSelection.platformLabels.${platform.name}` as any) ||
        platform.name
      );
    }
    if (typeof platform === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.platformLabels.${platform}` as any) ||
        platform
      );
    }
    return platform;
  };

  // 获取分类名称的翻译
  const getCategoryName = (category: any) => {
    if (typeof category === 'object' && category?.name) {
      // 如果是对象，尝试翻译name字段
      return (
        tPublish(`platformSelection.categoryLabels.${category.name}` as any) ||
        category.name
      );
    }
    if (typeof category === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.categoryLabels.${category}` as any) ||
        category
      );
    }
    return category;
  };

  const handleResubmitEvidence = (data: {
    taskId: string;
    files: UploadedFile[];
  }) => {
    resubmitEvidenceMutation.mutate(data);
  };

  const handleSubmitEvidence = (data: {
    taskId: string;
    files: UploadedFile[];
  }) => {
    submitEvidenceMutation.mutate(data);
  };

  const calculateTotalPrice = () => {
    const unitPrice = task.unitPrice || 0;
    const quantity = task.quantity || 1;
    return unitPrice * quantity;
  };

  const taskForCommission = {
    totalAmount: task.totalAmount,
    unitPrice: task.unitPrice,
    chargebackTypeIds: task.chargebackTypes,
    paymentMethodIds: task.paymentMethods,
    evidenceUploadType: task.evidenceUploadType,
  };
  const commission = getTaskCommission(
    taskForCommission,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算基础酬金（不包含证据费用）
  const baseCommission = getTaskBaseCommission(
    taskForCommission,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  const calculateDeposit = () => {
    if (!systemRate) return 0;
    const totalPrice = calculateTotalPrice();
    return totalPrice * (systemRate.depositRatio / 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-100';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-100';
      case 'RECRUITING':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-100';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-100';
      case 'PENDING_LOGISTICS':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-100';
      case 'PENDING_REVIEW':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-100';
      case 'PENDING_DELIVERY':
        return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-100';
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-100';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-100';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className='h-4 w-4' />;
      case 'REJECTED':
        return <XCircle className='h-4 w-4' />;
      case 'RECRUITING':
        return <CheckCircle className='h-4 w-4' />;
      case 'IN_PROGRESS':
        return <AlertCircle className='h-4 w-4' />;
      case 'PENDING_LOGISTICS':
        return <Upload className='h-4 w-4' />;
      case 'PENDING_REVIEW':
        return <Eye className='h-4 w-4' />;
      case 'PENDING_DELIVERY':
        return <Truck className='h-4 w-4' />;
      case 'COMPLETED':
        return <CheckCircle className='h-4 w-4' />;
      case 'EXPIRED':
        return <XCircle className='h-4 w-4' />;
      case 'CANCELLED':
        return <XCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  const getPublishedStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return t('status.PENDING');
      case 'REJECTED':
        return t('status.REJECTED');
      case 'RECRUITING':
        return t('status.RECRUITING');
      case 'IN_PROGRESS':
        return t('status.IN_PROGRESS');
      case 'PENDING_LOGISTICS':
        return t('status.PENDING_LOGISTICS');
      case 'PENDING_REVIEW':
        return t('status.PENDING_REVIEW');
      case 'PENDING_DELIVERY':
        return t('status.PENDING_DELIVERY');
      case 'COMPLETED':
        return t('status.COMPLETED');
      case 'EXPIRED':
        return t('status.EXPIRED');
      case 'CANCELLED':
        return t('status.CANCELLED');
      default:
        return status;
    }
  };

  const getEvidenceStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-100 text-gray-600';

    switch (status) {
      case 'PENDING_SUBMISSION':
        return 'bg-gray-100 text-gray-600';
      case 'UNDER_REVIEW':
        return 'bg-blue-100 text-blue-600';
      case 'NO_EVIDENCE':
        return 'bg-orange-100 text-orange-600';
      case 'REVIEWED':
        return 'bg-green-100 text-green-600';
      case 'REJECTED':
        return 'bg-red-100 text-red-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const getEvidenceStatusLabel = (status: string | null) => {
    if (!status) return t('evidenceStatus.PENDING_SUBMISSION');

    switch (status) {
      case 'PENDING_SUBMISSION':
        return t('evidenceStatus.PENDING_SUBMISSION');
      case 'UNDER_REVIEW':
        return t('evidenceStatus.UNDER_REVIEW');
      case 'NO_EVIDENCE':
        return t('evidenceStatus.NO_EVIDENCE');
      case 'REVIEWED':
        return t('evidenceStatus.REVIEWED');
      case 'REJECTED':
        return t('evidenceStatus.REJECTED');
      default:
        return t('evidenceStatus.PENDING_SUBMISSION');
    }
  };

  const getEvidenceStatusIcon = (status: string | null) => {
    if (!status) return <AlertCircle className='h-4 w-4' />;

    switch (status) {
      case 'PENDING_SUBMISSION':
        return <AlertCircle className='h-4 w-4' />;
      case 'UNDER_REVIEW':
        return <Clock className='h-4 w-4' />;
      case 'NO_EVIDENCE':
        return <X className='h-4 w-4' />;
      case 'REVIEWED':
        return <CheckCircle className='h-4 w-4' />;
      case 'REJECTED':
        return <X className='h-4 w-4' />;
      default:
        return <AlertCircle className='h-4 w-4' />;
    }
  };

  const formatDateTime = (dateString: string) => {
    if (!mounted) {
      return t('loading.tasks');
    }
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getTaskStatusCountdown = () => {
    if (!mounted) {
      return { text: t('loading.tasks'), isExpired: false, isUrgent: false };
    }

    const now = currentTime;

    switch (task.status) {
      case 'PENDING':
        // 审核中不显示倒计时，但保留函数返回值以防其他地方调用
        return { text: t('status.PENDING'), isExpired: false, isUrgent: false };

      case 'REJECTED':
        return {
          text: t('status.REJECTED'),
          isExpired: false,
          isUrgent: false,
        };

      case 'RECRUITING':
        if (task.expiresAt) {
          const expiresTime = new Date(task.expiresAt);
          const diffMs = expiresTime.getTime() - now.getTime();

          if (diffMs <= 0) {
            return {
              text: t('status.EXPIRED'),
              isExpired: true,
              isUrgent: false,
            };
          }

          const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

          let timeText = '';
          if (days > 0) {
            timeText = `${days}${t('countdown.days')}${hours}${t('countdown.hours')}${minutes}${t('countdown.minutes')}${seconds}${t('countdown.seconds')}`;
          } else if (hours > 0) {
            timeText = `${hours}${t('countdown.hours')}${minutes}${t('countdown.minutes')}${seconds}${t('countdown.seconds')}`;
          } else if (minutes > 0) {
            timeText = `${minutes}${t('countdown.minutes')}${seconds}${t('countdown.seconds')}`;
          } else {
            timeText = `${seconds}${t('countdown.seconds')}`;
          }

          return { text: timeText, isExpired: false, isUrgent: days < 1 };
        }
        return {
          text: t('countdown.noDeadline'),
          isExpired: false,
          isUrgent: false,
        };

      case 'IN_PROGRESS':
        if (task.acceptedAt) {
          const acceptedTime = new Date(task.acceptedAt);
          const deadline = new Date(
            acceptedTime.getTime() + 24 * 60 * 60 * 1000,
          );
          const diffMs = deadline.getTime() - now.getTime();

          if (diffMs <= 0) {
            return {
              text: t('countdown.timeout'),
              isExpired: true,
              isUrgent: false,
            };
          }

          const totalHours = Math.floor(diffMs / (1000 * 60 * 60));
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

          let timeText = '';
          if (totalHours > 0) {
            timeText = `${totalHours}${t('countdown.hours')}${minutes}${t('countdown.minutes')}${seconds}${t('countdown.seconds')}`;
          } else if (minutes > 0) {
            timeText = `${minutes}${t('countdown.minutes')}${seconds}${t('countdown.seconds')}`;
          } else {
            timeText = `${seconds}${t('countdown.seconds')}`;
          }

          return { text: timeText, isExpired: false, isUrgent: totalHours < 2 };
        }
        return {
          text: t('countdown.calculating'),
          isExpired: false,
          isUrgent: false,
        };

      case 'PENDING_LOGISTICS':
        if (task.logisticsDeadline) {
          const deadline = new Date(task.logisticsDeadline);
          const diffMs = deadline.getTime() - now.getTime();

          if (diffMs <= 0) {
            return {
              text: t('countdown.timeout'),
              isExpired: true,
              isUrgent: false,
            };
          }

          const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

          let timeText = '';

          if (days > 0) {
            timeText = `${days}${t('countdown.days')}${hours}${t('countdown.hours')}${minutes}${t('countdown.minutes')}`;
          } else if (hours > 0) {
            timeText = `${hours}${t('countdown.hours')}${minutes}${t('countdown.minutes')}`;
          } else {
            timeText = `${minutes}${t('countdown.minutes')}`;
          }

          const isUrgent = days < 1;

          return {
            text: timeText,
            isExpired: false,
            isUrgent,
          };
        } else {
          return {
            text: t('countdown.waitingLogistics'),
            isExpired: false,
            isUrgent: false,
          };
        }

      case 'PENDING_REVIEW':
        if (task.logisticsReviewDeadline) {
          const deadline = new Date(task.logisticsReviewDeadline);
          const diffMs = deadline.getTime() - now.getTime();

          if (diffMs <= 0) {
            return {
              text: t('countdown.timeout'),
              isExpired: true,
              isUrgent: false,
            };
          }

          const hours = Math.floor(diffMs / (1000 * 60 * 60));
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

          let timeText = '';
          if (hours > 0) {
            timeText = `${hours}${t('countdown.hours')}${minutes}${t('countdown.minutes')}`;
          } else {
            timeText = `${minutes}${t('countdown.minutes')}`;
          }

          return {
            text: t('countdown.reviewTimeLimit', { time: timeText }),
            isExpired: false,
            isUrgent: hours < 2,
          };
        } else {
          return {
            text: t('countdown.waitingReview'),
            isExpired: false,
            isUrgent: false,
          };
        }

      case 'PENDING_DELIVERY':
        if (task.deliveryDeadline) {
          const deadline = new Date(task.deliveryDeadline);
          const diffMs = deadline.getTime() - now.getTime();

          if (diffMs <= 0) {
            return {
              text: t('countdown.autoConfirmDelivery'),
              isExpired: true,
              isUrgent: false,
            };
          }

          const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

          let timeText = '';
          if (days > 0) {
            timeText = t('countdown.autoConfirmIn', {
              time: `${days}${t('countdown.days')}`,
            });
          } else if (hours > 0) {
            timeText = t('countdown.autoConfirmIn', {
              time: `${hours}${t('countdown.hours')}`,
            });
          } else {
            timeText = t('countdown.autoConfirmIn', {
              time: `${minutes}${t('countdown.minutes')}`,
            });
          }

          const isUrgent = days < 3;

          return {
            text: timeText,
            isExpired: false,
            isUrgent,
          };
        } else {
          return {
            text: t('countdown.waitingConfirmDelivery'),
            isExpired: false,
            isUrgent: false,
          };
        }

      case 'COMPLETED':
        return {
          text: t('status.COMPLETED'),
          isExpired: false,
          isUrgent: false,
        };

      case 'EXPIRED':
        return { text: t('status.EXPIRED'), isExpired: true, isUrgent: false };

      case 'CANCELLED':
        return {
          text: t('status.CANCELLED'),
          isExpired: false,
          isUrgent: false,
        };

      default:
        return {
          text: t('countdown.unknownStatus'),
          isExpired: false,
          isUrgent: false,
        };
    }
  };

  const countdown = getTaskStatusCountdown();

  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardContent className='space-y-4 pt-6'>
        {/* 平台信息和委托状态 */}
        <div className='flex items-start justify-between'>
          <div className='flex items-center gap-2'>
            <Building2 className='h-4 w-4 text-gray-600 dark:text-gray-300' />
            <span className='font-medium'>
              {getPlatformName(task.platform)} -{' '}
              {getCategoryName(task.category)}
            </span>
          </div>
          <div className='text-right'>
            <Badge
              className={`${getStatusColor(task.status)} transition-colors duration-200`}
            >
              {getStatusIcon(task.status)}
              <span className='ml-1'>
                {getPublishedStatusLabel(task.status)}
              </span>
            </Badge>
          </div>
        </div>

        {/* 商品信息和酬金 */}
        <div className='grid grid-cols-2 gap-4 py-3 bg-muted/30 rounded-lg px-3'>
          <div className='space-y-1'>
            <span className='text-xs text-gray-600 dark:text-gray-300'>
              {t('card.totalPrice')}
            </span>
            <div className='font-semibold text-sm'>
              ${calculateTotalPrice().toFixed(2)}
            </div>
          </div>
          <div className='space-y-1'>
            <span className='text-xs text-gray-600 dark:text-gray-300'>
              {t('card.taskReward')}
            </span>
            <div className='font-semibold text-green-600 text-sm'>
              ${baseCommission.toFixed(2)}
            </div>
          </div>
        </div>

        {/* 证据状态和委托倒计时 */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-1'>
            <FileText className='h-4 w-4 text-gray-600 dark:text-gray-300' />
            <span className='text-sm text-gray-600 dark:text-gray-300'>
              {t('card.evidenceStatus')}:
            </span>
            <Badge className={getEvidenceStatusColor(task.evidenceStatus)}>
              {getEvidenceStatusIcon(task.evidenceStatus)}
              <span className='ml-1'>
                {getEvidenceStatusLabel(task.evidenceStatus)}
              </span>
            </Badge>
          </div>

          {/* 委托倒计时 - 招募中和等待物流单号状态显示倒计时 */}
          {(task.status === 'RECRUITING' ||
            task.status === 'PENDING_LOGISTICS') && (
            <span
              className={`text-xs ${
                countdown.isExpired
                  ? 'text-red-600'
                  : countdown.isUrgent
                    ? 'text-orange-600'
                    : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              {countdown.text}
            </span>
          )}
        </div>

        {/* 操作按钮 */}
        <div className='flex gap-2 pt-2'>
          <PublishedTaskDetailSheet task={task}>
            <Button size='sm' variant='outline' className='flex-1'>
              <Eye className='h-4 w-4 mr-1' />
              {t('actions.viewDetails')}
            </Button>
          </PublishedTaskDetailSheet>

          {/* 补充证据按钮 - 当证据状态为待上传时显示 */}
          {(!task.evidenceStatus ||
            task.evidenceStatus === 'PENDING_SUBMISSION') && (
            <SubmitEvidenceDialog
              taskId={task.id}
              onSubmit={handleSubmitEvidence}
            >
              <Button
                size='sm'
                variant='outline'
                className='flex-1'
                disabled={submitEvidenceMutation.isPending}
              >
                <Upload className='h-4 w-4 mr-1' />
                {t('actions.submitEvidence')}
              </Button>
            </SubmitEvidenceDialog>
          )}

          {/* 重审证据按钮 */}
          {task.evidenceStatus === 'REJECTED' && (
            <ResubmitEvidenceDialog
              taskId={task.id}
              rejectionReason={task.evidenceRejectReason}
              onSubmit={handleResubmitEvidence}
            >
              <Button
                size='sm'
                variant='outline'
                className='flex-1'
                disabled={resubmitEvidenceMutation.isPending}
              >
                <Upload className='h-4 w-4 mr-1' />
                {t('actions.resubmitEvidence')}
              </Button>
            </ResubmitEvidenceDialog>
          )}

          {/* 审核订单信息按钮 */}
          {task.status === 'PENDING_REVIEW' && (
            <ReviewOrderDialog
              taskId={task.id}
              orderNumber={task.orderNumber || ''}
              orderScreenshot={task.orderScreenshot || ''}
              trackingNumber={task.trackingNumber || ''}
              logisticsScreenshots={task.logisticsScreenshots || []}
              onReview={approved => {}}
            >
              <Button
                size='sm'
                className='flex-1 bg-blue-600 hover:bg-blue-700'
              >
                <Eye className='h-4 w-4 mr-1' />
                {t('actions.reviewOrder')}
              </Button>
            </ReviewOrderDialog>
          )}

          {/* 查看订单按钮 - 只显示订单信息，不包含物流（因为还在等待物流单号） */}
          {task.orderNumber && task.status === 'PENDING_LOGISTICS' && (
            <ViewOrderDialog
              taskId={task.id}
              orderNumber={task.orderNumber}
              orderScreenshot={task.orderScreenshot}
              trackingNumber=''
              logisticsScreenshots={[]}
              orderReviewDeadline={task.orderReviewDeadline}
              logisticsReviewDeadline={task.logisticsReviewDeadline}
              taskStatus={task.status}
            >
              <Button size='sm' variant='outline' className='flex-1'>
                <Package className='h-4 w-4 mr-1' />
                {t('actions.viewOrder')}
              </Button>
            </ViewOrderDialog>
          )}

          {/* 查看物流按钮 - PENDING_DELIVERY状态下显示，物流信息中包含订单号 */}
          {task.status === 'PENDING_DELIVERY' && task.trackingNumber && (
            <LogisticsInfoDialog
              taskId={task.id}
              trackingNumber={task.trackingNumber}
              taskStatus={task.status}
              orderNumber={task.orderNumber}
              orderScreenshot={task.orderScreenshot}
              logisticsScreenshots={task.logisticsScreenshots}
              orderReviewDeadline={task.orderReviewDeadline}
              logisticsReviewDeadline={task.logisticsReviewDeadline}
            >
              <Button size='sm' variant='outline' className='flex-1'>
                <Truck className='h-4 w-4 mr-1' />
                {t('actions.viewLogistics')}
              </Button>
            </LogisticsInfoDialog>
          )}

          {/* 确认收货按钮 */}
          {task.status === 'PENDING_DELIVERY' && (
            <ConfirmDeliveryDialog
              taskId={task.id}
              commission={commission}
              deposit={calculateDeposit()}
              accepterName={
                task.accepter?.nickname || t('accepterStatus.accepter')
              }
              onConfirm={() => {
                window.location.reload();
              }}
            >
              <Button
                size='sm'
                className='flex-1 bg-green-600 hover:bg-green-700'
              >
                <CheckCircle className='h-4 w-4 mr-1' />
                {t('actions.confirmDelivery')}
              </Button>
            </ConfirmDeliveryDialog>
          )}

          {/* 取消按钮 */}
          {(task.status === 'RECRUITING' || task.status === 'PENDING') && (
            <CancelTaskDialog
              task={task}
              onCancel={() => {
                // 取消成功后的回调处理
                window.location.reload();
              }}
            >
              <Button
                size='sm'
                variant='outline'
                className='flex-1 text-red-600 hover:text-red-700 hover:bg-red-50'
                disabled={cancelTask.isPending}
                title={`${t('status.title')}: ${getPublishedStatusLabel(task.status)}`}
              >
                <Trash2 className='h-4 w-4 mr-1' />
                {cancelTask.isPending
                  ? t('loading.processing')
                  : t('actions.cancel')}
              </Button>
            </CancelTaskDialog>
          )}
        </div>

        {/* 接单人信息 - 显示在操作按钮下方 */}
        {(() => {
          let displayText = '';

          // 优先根据委托状态显示，某些状态下不显示接单人信息
          switch (task.status) {
            case 'PENDING':
              displayText = t('accepterStatus.pending');
              break;
            case 'REJECTED':
              displayText = t('accepterStatus.rejected');
              break;
            case 'EXPIRED':
              displayText = t('accepterStatus.expired');
              break;
            case 'CANCELLED':
              displayText = t('accepterStatus.cancelled');
              break;
            case 'COMPLETED':
              displayText = t('accepterStatus.completed');
              break;
            case 'RECRUITING':
              displayText = t('accepterStatus.recruiting');
              break;
            default:
              // 其他状态下，如果有接单者则显示接单人信息
              if (task.accepter) {
                displayText = `${t('accepterStatus.accepter')}: ${task.accepter.nickname}`;
              } else {
                displayText = t('accepterStatus.recruiting');
              }
          }

          return (
            <div className='pt-2 text-xs text-gray-600 dark:text-gray-300 text-center'>
              {displayText}
            </div>
          );
        })()}
      </CardContent>
    </Card>
  );
}

// 响应式任务筛选标签组件
interface ResponsiveTaskTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  stats: {
    total: number;
    available: number;
    inProgress: number;
    completed: number;
    expired: number;
    cancelled: number;
  };
  t: (key: string) => string;
}

function ResponsiveTaskTabs({
  activeTab,
  onTabChange,
  stats,
  t,
}: ResponsiveTaskTabsProps) {
  const { isMobile, isTablet, breakpoint } = useResponsive();

  const tabItems = [
    {
      value: 'all',
      label: t('tabs.all'),
      shortLabel: 'All',
      count: stats.total,
    },
    {
      value: 'available',
      label: t('tabs.available'),
      shortLabel: 'Open',
      count: stats.available,
    },
    {
      value: 'in_progress',
      label: t('tabs.inProgress'),
      shortLabel: 'Active',
      count: stats.inProgress,
    },
    {
      value: 'completed',
      label: t('tabs.completed'),
      shortLabel: 'Done',
      count: stats.completed,
    },
    {
      value: 'expired',
      label: t('tabs.expired'),
      shortLabel: 'Expired',
      count: stats.expired,
    },
    {
      value: 'cancelled',
      label: t('tabs.cancelled'),
      shortLabel: 'Cancel',
      count: stats.cancelled,
    },
  ];

  // 移动端：使用水平滚动
  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block sm:hidden'>{item.label}</span>
                <span className='hidden sm:block'>
                  {item.label} ({item.count})
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  // 平板端：使用2行3列布局
  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label} ({item.count})
            </button>
          ))}
        </div>
      </Tabs>
    );
  }

  // 桌面端：保持原有的6列网格布局
  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className='grid w-full grid-cols-6'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label} ({item.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

export function MyPublishedTasksContent() {
  const t = useTranslations('my-published-tasks');

  const [activeTab, setActiveTab] = useState('all');

  const {
    data: taskData,
    isLoading,
    error,
  } = usePublishedTasks({
    status: activeTab === 'all' ? undefined : activeTab,
  });

  const stats = taskData?.data?.stats || {
    total: 0,
    available: 0,
    inProgress: 0,
    completed: 0,
    expired: 0,
    cancelled: 0,
  };

  const filteredTasks = useMemo(() => {
    const tasks = taskData?.data?.tasks || [];
    switch (activeTab) {
      case 'available':
        return tasks.filter(task => task.status === 'RECRUITING');
      case 'in_progress':
        return tasks.filter(task => task.status === 'IN_PROGRESS');
      case 'completed':
        return tasks.filter(task => task.status === 'COMPLETED');
      case 'expired':
        return tasks.filter(task => task.status === 'EXPIRED');
      case 'cancelled':
        return tasks.filter(task => task.status === 'CANCELLED');
      default:
        return tasks;
    }
  }, [taskData?.data?.tasks, activeTab]);

  if (error) {
    return (
      <div className='flex items-center justify-center py-12'>
        <p className='text-sm text-red-600'>{t('messages.loadFailed')}</p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            {t('navigation.title')}
          </h1>
          <p className='text-gray-600 dark:text-gray-300'>
            {t('navigation.description')}
          </p>
        </div>
        <Button
          variant='outline'
          onClick={() => (window.location.href = '/publish')}
        >
          <Building2 className='h-4 w-4 mr-2' />
          {t('actions.publishNew')}
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className='grid grid-cols-2 md:grid-cols-6 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold'>{stats.total}</div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('stats.total')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {stats.available}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.available')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {stats.inProgress}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.inProgress')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {stats.completed}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.completed')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-red-600'>
                {stats.expired}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.expired')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-gray-600'>
                {stats.cancelled}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.cancelled')}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 委托筛选标签 - 响应式设计 */}
      <ResponsiveTaskTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        stats={stats}
        t={t}
      />

      {/* 标签内容 */}
      <div className='mt-6'>
        {isLoading ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Loader2 className='h-8 w-8 animate-spin mb-4' />
              <p className='text-gray-600 dark:text-gray-300'>
                {t('loading.tasks')}
              </p>
            </CardContent>
          </Card>
        ) : filteredTasks.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <div className='w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4'>
                <Building2 className='h-8 w-8 text-gray-600 dark:text-gray-300' />
              </div>
              <h3 className='text-lg font-medium mb-2'>{t('empty.title')}</h3>
              <p className='text-gray-600 dark:text-gray-300 text-center mb-4'>
                {activeTab === 'all' ? t('empty.all') : t('empty.filtered')}
              </p>
              {activeTab === 'all' && (
                <Button
                  className='bg-blue-600 hover:bg-blue-700'
                  onClick={() => (window.location.href = '/publish')}
                >
                  <Building2 className='h-4 w-4 mr-2' />
                  {t('actions.publishFirst')}
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {filteredTasks.map(task => (
              <PublishedTaskCard key={task.id} task={task} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
