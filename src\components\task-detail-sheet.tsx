'use client';

import {
  Clock,
  DollarSign,
  Calendar,
  Tag,
  CreditCard,
  Building2,
  FileText,
  Package,
  AlertCircle,
  CheckCircle,
  Info,
  X,
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useAcceptTask } from '@/hooks/use-accept-task';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import { Task, Platform, TaskStatus, EvidenceStatus } from '@/lib/types/task';
import {
  getTaskCommission,
  getTaskBaseCommission,
} from '@/lib/utils/commission';

import { ConfirmAcceptTaskDialog } from './confirm-accept-task-dialog';

interface TaskDetailSheetProps {
  task: Task;
  children: React.ReactNode;
}

export function TaskDetailSheet({ task, children }: TaskDetailSheetProps) {
  const t = useTranslations('Tasks.taskDetail');
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取当前用户会话信息
  const { data: session } = useSession();

  // 获取费率数据
  const { chargebackTypes, paymentMethods, systemRate } = useCommissionRates();

  // 接受委托的hook
  const acceptTaskMutation = useAcceptTask();

  // 检查当前用户是否为委托发布者
  const isTaskPublisher =
    session?.user?.id === (task.publisherId || task.publisher?.id);

  // 计算酬金（使用真实的费率数据）
  const commission = getTaskCommission(
    task,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算基础酬金（不包含证据费用）
  const baseCommission = getTaskBaseCommission(
    task,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算产品总价：单价 × 数量
  const calculateTotalPrice = () => {
    const unitPrice = task.unitPrice || 0;
    const quantity = task.quantity || 1;
    return unitPrice * quantity;
  };

  // 计算所需押金：产品总价 × 押金比例
  const calculateDeposit = () => {
    const totalPrice = calculateTotalPrice();
    const depositRatio = systemRate?.depositRatio || 0;
    // 将百分比转换为小数（例如：10.0% -> 0.10）
    return totalPrice * (depositRatio / 100);
  };

  // 获取平台显示名称
  const tPublish = useTranslations('Publish');
  const getPlatformName = (
    platform: Platform | string | { id: string; name: string },
  ) => {
    if (typeof platform === 'string') {
      return (
        tPublish(`platformSelection.platformLabels.${platform}`) || platform
      );
    }
    if (typeof platform === 'object' && platform?.name) {
      return (
        tPublish(`platformSelection.platformLabels.${platform.name}`) ||
        platform.name
      );
    }
    return String(platform || t('basicInfo.unknownPlatform'));
  };

  // 获取分类显示名称
  const getCategoryName = (category: string | { id: string; name: string }) => {
    if (typeof category === 'string') {
      return (
        tPublish(`platformSelection.categoryLabels.${category}`) || category
      );
    }
    if (typeof category === 'object' && category?.name) {
      return (
        tPublish(`platformSelection.categoryLabels.${category.name}`) ||
        category.name
      );
    }
    return String(category || t('basicInfo.unknownCategory'));
  };

  const getEvidenceStatusColor = (status?: EvidenceStatus) => {
    if (!status)
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-900 dark:text-gray-300 dark:hover:bg-gray-800';

    switch (status) {
      case EvidenceStatus.PENDING_SUBMISSION:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-900 dark:text-gray-300 dark:hover:bg-gray-800';
      case EvidenceStatus.UNDER_REVIEW:
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800';
      case EvidenceStatus.NO_EVIDENCE:
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900 dark:text-orange-300 dark:hover:bg-orange-800';
      case EvidenceStatus.REVIEWED:
        return 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800';
      case EvidenceStatus.REJECTED:
        return 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-900 dark:text-gray-300 dark:hover:bg-gray-800';
    }
  };

  const getEvidenceStatusIcon = (status?: EvidenceStatus) => {
    if (!status) return <Clock className='h-3 w-3' />;

    switch (status) {
      case EvidenceStatus.PENDING_SUBMISSION:
        return <Clock className='h-3 w-3' />;
      case EvidenceStatus.UNDER_REVIEW:
        return <AlertCircle className='h-3 w-3' />;
      case EvidenceStatus.NO_EVIDENCE:
        return <X className='h-3 w-3' />;
      case EvidenceStatus.REVIEWED:
        return <CheckCircle className='h-3 w-3' />;
      case EvidenceStatus.REJECTED:
        return <X className='h-3 w-3' />;
      default:
        return <Clock className='h-3 w-3' />;
    }
  };

  const getEvidenceStatusDescription = (status?: EvidenceStatus) => {
    if (!status) return t('evidenceStatus.descriptions.default');

    switch (status) {
      case EvidenceStatus.PENDING_SUBMISSION:
        return t('evidenceStatus.descriptions.PENDING_SUBMISSION');
      case EvidenceStatus.UNDER_REVIEW:
        return t('evidenceStatus.descriptions.UNDER_REVIEW');
      case EvidenceStatus.NO_EVIDENCE:
        return t('evidenceStatus.descriptions.NO_EVIDENCE');
      case EvidenceStatus.REVIEWED:
        return t('evidenceStatus.descriptions.REVIEWED');
      case EvidenceStatus.REJECTED:
        return t('evidenceStatus.descriptions.REJECTED');
      default:
        return t('evidenceStatus.descriptions.default');
    }
  };

  const calculateTimeLeft = () => {
    if (!mounted || !task.expiresAt) {
      return t('basicInfo.calculating');
    }

    const now = new Date().getTime();
    const deadlineTime = new Date(task.expiresAt).getTime();
    const difference = deadlineTime - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        return `${days}${t('basicInfo.days')}${hours}${t('basicInfo.hours')}${minutes}${t('basicInfo.minutes')}`;
      } else if (hours > 0) {
        return `${hours}${t('basicInfo.hours')}${minutes}${t('basicInfo.minutes')}`;
      } else {
        return `${minutes}${t('basicInfo.minutes')}`;
      }
    } else {
      return t('basicInfo.expired');
    }
  };

  // 格式化日期时间的安全函数
  const formatDateTime = (date?: Date) => {
    if (!date) return t('basicInfo.noData');
    return new Date(date).toLocaleString();
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='w-full sm:max-w-2xl overflow-y-auto'>
        <SheetHeader className='space-y-3'>
          <SheetTitle className='text-xl font-bold'>{t('title')}</SheetTitle>
          <SheetDescription className='text-base'>
            {getPlatformName(task.platform)} - {getCategoryName(task.category)}
          </SheetDescription>
        </SheetHeader>

        <div className='space-y-6 py-6'>
          {/* 基本信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <Info className='h-5 w-5' />
                {t('basicInfo.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <Building2 className='h-4 w-4 text-gray-600 dark:text-gray-300' />
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('basicInfo.platform')}:
                  </span>
                  <span className='font-medium'>
                    {getPlatformName(task.platform)}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Tag className='h-4 w-4 text-gray-600 dark:text-gray-300' />
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('basicInfo.category')}:
                  </span>
                  <span className='font-medium'>
                    {getCategoryName(task.category)}
                  </span>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <Clock className='h-4 w-4 text-gray-600 dark:text-gray-300' />
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('basicInfo.timeLeft')}:
                  </span>
                  <span className='font-medium text-orange-600'>
                    {calculateTimeLeft()}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Calendar className='h-4 w-4 text-gray-600 dark:text-gray-300' />
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('basicInfo.deadline')}:
                  </span>
                  <span className='font-medium'>
                    {formatDateTime(task.expiresAt)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 商品信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <Package className='h-5 w-5' />
                {t('productInfo.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-1'>
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('productInfo.unitPrice')}
                  </span>
                  <div className='text-lg font-semibold text-green-600'>
                    ${task.unitPrice?.toFixed(2) || '0.00'}
                  </div>
                </div>
                <div className='space-y-1'>
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('productInfo.quantity')}
                  </span>
                  <div className='text-lg font-semibold'>
                    {task.quantity || 1} {t('productInfo.pieces')}
                  </div>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-1'>
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('productInfo.totalPrice')}
                  </span>
                  <div className='text-lg font-semibold text-blue-600'>
                    ${calculateTotalPrice().toFixed(2) || '0.00'}
                  </div>
                </div>
                <div className='space-y-1'>
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {t('productInfo.deposit')}
                  </span>
                  <div className='text-lg font-semibold text-orange-600'>
                    ${calculateDeposit().toFixed(2) || '0.00'}
                  </div>
                </div>
              </div>

              <div className='space-y-1'>
                <span className='text-sm text-gray-600 dark:text-gray-300'>
                  {t('productInfo.commission')}
                </span>
                <div className='text-xl font-bold text-green-600'>
                  ${baseCommission.toFixed(2)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 拒付类型和支付方式 */}
          <Card>
            <CardContent className='space-y-4 pt-6'>
              <div className='space-y-2'>
                <span className='text-sm font-medium text-gray-600 dark:text-gray-300'>
                  {t('paymentInfo.chargebackType')}
                </span>
                <div className='flex flex-wrap gap-2'>
                  {task.chargebackTypeIds?.length ? (
                    task.chargebackTypeIds.map((typeId, index) => {
                      const typeData = chargebackTypes.find(
                        ct => ct.id === typeId,
                      );
                      return (
                        <Badge
                          key={index}
                          variant='outline'
                          className='text-xs'
                        >
                          {typeData?.name
                            ? tPublish(
                                `platformSelection.chargebackTypeLabels.${typeData.name}` as any,
                              ) || typeData.name
                            : typeId}
                        </Badge>
                      );
                    })
                  ) : (
                    <span className='text-sm text-gray-600 dark:text-gray-300'>
                      {t('paymentInfo.noData')}
                    </span>
                  )}
                </div>
              </div>

              <div className='space-y-2'>
                <span className='text-sm font-medium text-gray-600 dark:text-gray-300'>
                  {t('paymentInfo.paymentMethods')}
                </span>
                <div className='flex flex-wrap gap-2'>
                  {task.paymentMethodIds?.length ? (
                    task.paymentMethodIds.map((methodId, index) => {
                      const methodData = Array.isArray(paymentMethods)
                        ? paymentMethods.find(pm => pm.id === methodId)
                        : null;
                      return (
                        <Badge
                          key={index}
                          variant='secondary'
                          className='text-xs'
                        >
                          <CreditCard className='h-3 w-3 mr-1' />
                          {methodData?.name
                            ? tPublish(
                                `platformSelection.paymentMethodLabels.${methodData.name}` as any,
                              ) || methodData.name
                            : methodId}
                        </Badge>
                      );
                    })
                  ) : (
                    <span className='text-sm text-gray-600 dark:text-gray-300'>
                      {t('paymentInfo.noData')}
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 证据状态 */}
          <Card>
            <CardContent className='pt-6'>
              <div className='space-y-3'>
                <div className='flex items-center gap-2'>
                  <FileText className='h-4 w-4 text-gray-600 dark:text-gray-300' />
                  <span className='text-sm font-medium text-gray-600 dark:text-gray-300'>
                    {t('evidenceStatus.title')}
                  </span>
                  <Badge
                    className={`${getEvidenceStatusColor(task.evidenceStatus as EvidenceStatus)} transition-colors duration-200`}
                  >
                    {getEvidenceStatusIcon(
                      task.evidenceStatus as EvidenceStatus,
                    )}
                    <span className='ml-1'>
                      {t(
                        `evidenceStatuses.${task.evidenceStatus || EvidenceStatus.PENDING_SUBMISSION}`,
                      )}
                    </span>
                  </Badge>
                </div>

                <Alert>
                  <AlertDescription className='text-sm'>
                    {getEvidenceStatusDescription(
                      task.evidenceStatus as EvidenceStatus,
                    )}
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>

          {/* 委托要求 */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>
                {t('requirements.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-3'>
                <div className='flex items-start gap-3'>
                  <div className='w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center flex-shrink-0 mt-0.5'>
                    <span className='text-xs font-bold text-blue-600 dark:text-blue-300'>
                      1
                    </span>
                  </div>
                  <div>
                    <h4 className='font-medium'>
                      {t('requirements.timeFrame.title')}
                    </h4>
                    <p className='text-sm text-gray-600 dark:text-gray-300'>
                      {t('requirements.timeFrame.description', {
                        timeLimit: task.timeLimit || 24,
                      })}
                    </p>
                  </div>
                </div>

                <div className='flex items-start gap-3'>
                  <div className='w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center flex-shrink-0 mt-0.5'>
                    <span className='text-xs font-bold text-green-600 dark:text-green-300'>
                      2
                    </span>
                  </div>
                  <div>
                    <h4 className='font-medium'>
                      {t('requirements.documentation.title')}
                    </h4>
                    <p className='text-sm text-gray-600 dark:text-gray-300'>
                      {t('requirements.documentation.description')}
                    </p>
                  </div>
                </div>

                <div className='flex items-start gap-3'>
                  <div className='w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center flex-shrink-0 mt-0.5'>
                    <span className='text-xs font-bold text-orange-600 dark:text-orange-300'>
                      3
                    </span>
                  </div>
                  <div>
                    <h4 className='font-medium'>
                      {t('requirements.completion.title')}
                    </h4>
                    <p className='text-sm text-gray-600 dark:text-gray-300'>
                      {t('requirements.completion.description')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <SheetFooter className='flex gap-3 pt-6'>
          <Button
            variant='outline'
            onClick={() => setIsOpen(false)}
            className='flex-1'
          >
            {t('actions.close')}
          </Button>

          {/* 接受委托模态框 */}
          <ConfirmAcceptTaskDialog
            task={{
              id: task.id,
              platform: task.platform,
              category: task.category,
              quantity: task.quantity || 1,
              unitPrice: task.unitPrice || 0,
              totalAmount: calculateTotalPrice(),
              evidenceStatus: task.evidenceStatus,
              evidenceUploadType: task.evidenceUploadType,
              commission: baseCommission,
              deposit: calculateDeposit(),
            }}
            isTaskPublisher={isTaskPublisher}
            onAccept={() => setIsOpen(false)}
          >
            <Button className='flex-1 bg-green-600 hover:bg-green-700'>
              {t('actions.acceptTask')}
            </Button>
          </ConfirmAcceptTaskDialog>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
