import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 2. 获取查询参数
    const { searchParams } = new URL(request.url);
    const limit = Math.min(
      10,
      Math.max(1, parseInt(searchParams.get('limit') || '3', 10)),
    );

    // 3. 获取所有待招募的委托（过滤过期委托）
    const availableTasks = await prisma.task.findMany({
      where: {
        status: TaskStatus.RECRUITING,
        // 注释掉排除自己委托的条件，保持与委托大厅一致
        // publisherId: {
        //   not: session.user.id
        // },
        // 添加过期时间检查，确保只显示未过期的委托
        expiresAt: {
          gt: new Date(),
        },
      },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc', // 改为按创建时间降序排序，推荐最新创建的委托
      },
      take: limit, // 直接取指定数量的最新委托
    });

    // 4. 如果委托数量不足，返回所有可用委托
    if (availableTasks.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
      });
    }

    // 5. 直接使用最新发布的委托（已经按发布时间排序）
    const selectedTasks = availableTasks;

    // 6. 获取拒付类型和支付方式数据
    const [chargebackTypes, paymentMethods] = await Promise.all([
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    // 7. 创建ID到名称的映射
    const chargebackTypeMap = chargebackTypes.reduce(
      (map, type) => {
        map[type.id] = type.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const paymentMethodMap = paymentMethods.reduce(
      (map, method) => {
        map[method.id] = method.name;
        return map;
      },
      {} as Record<string, string>,
    );

    // 8. 处理委托数据
    const processedTasks = selectedTasks.map(task => ({
      id: task.id,
      title: task.title,
      productUrl: task.productUrl,
      productDescription: task.productDescription,
      quantity: task.quantity,
      unitPrice: task.unitPrice,
      totalAmount: task.totalAmount,
      finalTotal: task.finalTotal,
      listingTime: task.listingTime,
      recipientName: task.recipientName,
      recipientPhone: task.recipientPhone,
      shippingAddress: task.shippingAddress,
      evidenceUploadType: task.evidenceUploadType,
      evidenceStatus: task.evidenceStatus,
      evidenceFiles: task.evidenceFiles,
      cartScreenshots: task.cartScreenshots,
      chargebackTypeIds: task.chargebackTypeIds,
      paymentMethodIds: task.paymentMethodIds,
      status: task.status,
      expiresAt: task.expiresAt?.toISOString(),
      createdAt: task.createdAt.toISOString(),
      updatedAt: task.updatedAt.toISOString(),
      publishedAt: task.publishedAt?.toISOString(),
      publisher: {
        id: task.publisher.id,
        name: task.publisher.name || '未知用户',
        email: task.publisher.email,
      },
      platform: {
        id: task.platform.id,
        name: task.platform.name,
      },
      category: {
        id: task.category.id,
        name: task.category.name,
      },
      // 添加映射后的名称数组，方便前端显示
      chargebackTypes: task.chargebackTypeIds.map(
        id => chargebackTypeMap[id] || id,
      ),
      paymentMethods: task.paymentMethodIds.map(
        id => paymentMethodMap[id] || id,
      ),
    }));

    return NextResponse.json({
      success: true,
      data: processedTasks,
    });
  } catch (error) {
    console.error('获取推荐委托失败:', error);
    return NextResponse.json(
      { success: false, message: '获取推荐委托失败' },
      { status: 500 },
    );
  }
}
