import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { sendDepositSuccessEmail, sendDepositFailedEmail } from '@/lib/email';

// 测试邮件发送验证schema
const testEmailSchema = z.object({
  type: z.enum(['deposit-success', 'deposit-failed']),
  to: z.string().email(),
  language: z.enum(['zh', 'en']).optional(),
  data: z.object({}).passthrough().optional(),
});

// 生成测试数据
const generateTestData = (type: string, language: 'zh' | 'en', to: string) => {
  const baseData = {
    userName: language === 'zh' ? '测试用户' : 'Test User',
    userEmail: to,
    amount: 100.0,
    currency: 'USD',
    transactionId: `TEST_${Date.now()}`,
    paymentMethod: language === 'zh' ? '测试支付方式' : 'Test Payment Method',
    language,
  };

  switch (type) {
    case 'deposit-success':
      return {
        ...baseData,
        processedAt: new Date().toISOString(),
        newBalance: 250.0,
      };

    case 'deposit-failed':
      return {
        ...baseData,
        failureReason:
          language === 'zh'
            ? '这是一个测试失败原因，用于验证邮件模板显示效果'
            : 'This is a test failure reason for email template verification',
        failedAt: new Date().toISOString(),
      };

    default:
      throw new Error(`Unsupported email type: ${type}`);
  }
};

export async function POST(request: NextRequest) {
  try {
    // 检查是否为开发环境
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: '测试邮件功能仅在开发环境中可用' },
        { status: 403 },
      );
    }

    const body = await request.json();

    const validation = testEmailSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '参数验证失败',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { type, to, language = 'zh', data } = validation.data;

    // 使用提供的数据或生成测试数据
    const emailData = data || generateTestData(type, language, to);

    // 确保语言和邮箱设置正确
    emailData.language = language;
    emailData.userEmail = to;

    let result;

    // 根据类型发送相应的邮件
    switch (type) {
      case 'deposit-success':
        // @ts-expect-error - Test route with dynamic data
        result = await sendDepositSuccessEmail(to, emailData);
        break;

      case 'deposit-failed':
        // @ts-expect-error - Test route with dynamic data
        result = await sendDepositFailedEmail(to, emailData);
        break;

      default:
        return NextResponse.json(
          { success: false, error: '不支持的邮件类型' },
          { status: 400 },
        );
    }

    return NextResponse.json(
      {
        success: result.success,
        message: result.success ? `测试邮件已发送到 ${to}` : '测试邮件发送失败',
        type,
        language,
        to,
        data: emailData,
        error: result.success ? undefined : result.error,
      },
      {
        status: result.success ? 200 : 500,
      },
    );
  } catch (error) {
    console.error('测试邮件发送失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '测试邮件发送失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

// GET方法返回可用的测试邮件类型
export async function GET() {
  return NextResponse.json({
    success: true,
    availableTypes: [
      {
        type: 'deposit-success',
        name: '充值成功确认邮件',
        description: '用户充值成功后发送的确认邮件',
        supportedLanguages: ['zh', 'en'],
      },
      {
        type: 'deposit-failed',
        name: '充值失败通知邮件',
        description: '用户充值失败后发送的通知邮件',
        supportedLanguages: ['zh', 'en'],
      },
    ],
    usage: {
      preview: 'GET /api/email/preview?type=deposit-success&language=zh',
      test: 'POST /api/email/test',
      testBody: {
        type: 'deposit-success',
        to: '<EMAIL>',
        language: 'zh',
        data: {
          userName: '测试用户',
          amount: 100.0,
          currency: 'USD',
          // ... 其他可选参数
        },
      },
    },
    note: '测试功能仅在开发环境中可用',
  });
}
