'use client';

import {
  Crown,
  Check,
  X,
  Calendar,
  TrendingUp,
  Shield,
  Headphones,
  Infinity,
  Star,
  Zap,
  Loader2,
} from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  useMembershipPlans,
  useUpgradeMembership,
  useRenewMembership,
} from '@/hooks/use-membership-plans';
import { useUserMembership } from '@/hooks/use-user-membership';

import { MembershipPaymentDialog } from './membership-payment-dialog';

export function MembershipContent() {
  const t = useTranslations('Membership');
  const locale = useLocale();
  const [mounted, setMounted] = useState(false);
  const [paymentDialog, setPaymentDialog] = useState<{
    open: boolean;
    planId: string;
    planName: string;
    price: number;
    action: 'upgrade' | 'renew';
  }>({
    open: false,
    planId: '',
    planName: '',
    price: 0,
    action: 'upgrade',
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取真实数据
  const {
    data: membership,
    isLoading: membershipLoading,
    error: membershipError,
  } = useUserMembership();
  const {
    data: plans,
    isLoading: plansLoading,
    error: plansError,
  } = useMembershipPlans();
  const upgradeMutation = useUpgradeMembership();
  const renewMutation = useRenewMembership();

  // 格式化日期
  const formatDate = (date: Date | string) => {
    if (!mounted) return t('time.loading');
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // 根据当前语言设置格式化日期
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    return dateObj.toLocaleDateString(localeCode, {
      year: 'numeric',
      month: locale === 'zh' ? 'numeric' : 'long',
      day: 'numeric',
    });
  };

  // 获取会员等级颜色
  const getTierColor = (memberPlan: string) => {
    switch (memberPlan) {
      case 'FREE':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'PRO':
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-100 dark:border-blue-600';
      case 'BUSINESS':
        return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-100 dark:border-purple-600';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取会员等级图标
  const getTierIcon = (memberPlan: string) => {
    switch (memberPlan) {
      case 'FREE':
        return <Shield className='h-4 w-4' />;
      case 'PRO':
        return <Star className='h-4 w-4' />;
      case 'BUSINESS':
        return <Crown className='h-4 w-4' />;
      default:
        return <Shield className='h-4 w-4' />;
    }
  };

  // 获取套餐名称显示
  const getPlanDisplayName = (memberPlan: string) => {
    switch (memberPlan) {
      case 'FREE':
        return t('plans.free');
      case 'PRO':
        return t('plans.pro');
      case 'BUSINESS':
        return t('plans.business');
      default:
        return memberPlan;
    }
  };

  // 计算委托使用进度
  const getTaskProgress = () => {
    if (!membership?.membershipPlan) {
      return { percentage: 0, text: t('plans.noData') };
    }

    const maxTasks = membership.membershipPlan.maxTasks;
    const usedTasks = membership.tasksUsedThisMonth || 0;

    if (maxTasks === null) {
      return { percentage: 0, text: t('plans.unlimited') };
    }

    const percentage = (usedTasks / maxTasks) * 100;
    return {
      percentage: Math.min(percentage, 100),
      text: `${usedTasks} / ${maxTasks}`,
    };
  };

  // 计算白名单使用进度
  const getWhitelistProgress = () => {
    if (!membership?.membershipPlan) {
      return { percentage: 0, text: t('plans.noData') };
    }

    const totalSlots = membership.membershipPlan.whitelistSlots;
    const usedSlots = membership.whitelistSlotsUsed || 0;

    if (totalSlots === 0) {
      return { percentage: 0, text: t('plans.noQuota') };
    }

    const percentage = (usedSlots / totalSlots) * 100;
    return {
      percentage: Math.min(percentage, 100),
      text: `${usedSlots} / ${totalSlots}`,
    };
  };

  // 处理升级
  const handleUpgrade = async (planId: string, planName: string) => {
    if (!membership) return;

    // 检查是否是当前套餐
    if (membership.membershipPlan?.id === planId) {
      toast.info(t('messages.currentPlanInfo'), {
        description: t('messages.currentPlanDesc'),
      });
      return;
    }

    try {
      const result = await upgradeMutation.mutateAsync({ planId, planName });

      if (result.needPayment) {
        // 打开支付对话框
        setPaymentDialog({
          open: true,
          planId: result.planId,
          planName: result.planName,
          price: result.price,
          action: 'upgrade',
        });
      }
    } catch (error) {
      // 错误已在mutation中处理
    }
  };

  // 处理续费
  const handleRenew = async () => {
    if (!membership?.membershipPlan) return;

    try {
      const result = await renewMutation.mutateAsync();

      if (result.needPayment) {
        // 打开支付对话框
        setPaymentDialog({
          open: true,
          planId: result.planId,
          planName: result.planName,
          price: result.price,
          action: 'renew',
        });
      }
    } catch (error) {
      // 错误已在mutation中处理
    }
  };

  // 加载状态
  if (membershipLoading || plansLoading) {
    return (
      <div className='flex items-center justify-center py-12'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2 text-sm text-muted-foreground'>
          {t('messages.loading')}
        </span>
      </div>
    );
  }

  // 错误状态
  if (membershipError || plansError) {
    return (
      <div className='text-center py-12'>
        <p className='text-sm text-red-600'>{t('messages.loadError')}</p>
      </div>
    );
  }

  // 数据不存在
  if (!membership || !plans) {
    return (
      <div className='text-center py-12'>
        <p className='text-sm text-muted-foreground'>{t('messages.noData')}</p>
      </div>
    );
  }

  const taskProgress = getTaskProgress();
  const whitelistProgress = getWhitelistProgress();

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            {t('navigation.title')}
          </h1>
          <p className='text-muted-foreground'>{t('navigation.description')}</p>
        </div>
      </div>

      {/* 当前会员状态 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Crown className='h-5 w-5' />
            {t('status.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='space-y-2'>
              <div className='text-sm text-muted-foreground'>
                {t('status.currentPlan')}
              </div>
              <Badge
                className={`${getTierColor(membership.memberPlan)} text-base px-3 py-1`}
              >
                {getTierIcon(membership.memberPlan)}
                <span className='ml-1'>
                  {getPlanDisplayName(membership.memberPlan)}
                </span>
              </Badge>
            </div>

            <div className='space-y-2'>
              <div className='text-sm text-muted-foreground'>
                {t('status.validUntil')}
              </div>
              <div className='font-medium'>
                {membership.memberPlanExpiry
                  ? formatDate(membership.memberPlanExpiry)
                  : t('status.permanent')}
              </div>
            </div>

            <div className='space-y-2'>
              <div className='text-sm text-muted-foreground'>
                {t('status.taskUsage')}
              </div>
              <div className='space-y-1'>
                <div className='flex justify-between text-sm'>
                  <span>{taskProgress.text}</span>
                  {membership.membershipPlan?.maxTasks !== null && (
                    <span>{taskProgress.percentage.toFixed(0)}%</span>
                  )}
                </div>
                {membership.membershipPlan?.maxTasks !== null && (
                  <Progress value={taskProgress.percentage} className='h-2' />
                )}
              </div>
            </div>

            <div className='space-y-2'>
              <div className='text-sm text-muted-foreground'>
                {t('status.whitelistUsage')}
              </div>
              <div className='space-y-1'>
                <div className='flex justify-between text-sm'>
                  <span>{whitelistProgress.text}</span>
                  {membership.membershipPlan &&
                    membership.membershipPlan.whitelistSlots > 0 && (
                      <span>{whitelistProgress.percentage.toFixed(0)}%</span>
                    )}
                </div>
                {membership.membershipPlan &&
                  membership.membershipPlan.whitelistSlots > 0 && (
                    <Progress
                      value={whitelistProgress.percentage}
                      className='h-2'
                    />
                  )}
              </div>
            </div>
          </div>

          {membership.memberPlan !== 'FREE' && (
            <div className='mt-4 pt-4 border-t'>
              <Button
                onClick={handleRenew}
                className='w-full md:w-auto'
                disabled={renewMutation.isPending}
              >
                <Calendar className='h-4 w-4 mr-2' />
                {renewMutation.isPending
                  ? t('actions.processing')
                  : t('actions.renew')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 套餐对比 */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
        {plans.map(plan => {
          const isCurrentPlan = membership.membershipPlan?.id === plan.id;
          const planType =
            plan.name === t('plans.free')
              ? 'FREE'
              : plan.name === t('plans.pro')
                ? 'PRO'
                : 'BUSINESS';

          return (
            <Card
              key={plan.id}
              className={`relative ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
            >
              {isCurrentPlan && (
                <div className='absolute -top-3 left-1/2 transform -translate-x-1/2'>
                  <Badge className='bg-primary text-primary-foreground'>
                    {t('plans.current')}
                  </Badge>
                </div>
              )}

              <CardHeader className='text-center'>
                <div className='flex justify-center mb-2'>
                  {getTierIcon(planType)}
                </div>
                <CardTitle className='text-xl'>{plan.name}</CardTitle>
                <div className='text-3xl font-bold'>
                  ${plan.price}
                  <span className='text-base font-normal text-muted-foreground'>
                    /{plan.period}
                  </span>
                </div>
              </CardHeader>

              <CardContent className='space-y-4'>
                <div className='space-y-3'>
                  <div className='flex items-center gap-2'>
                    <Check className='h-4 w-4 text-green-600' />
                    <span className='text-sm'>
                      {plan.maxTasks === null
                        ? t('plans.unlimited')
                        : `${t('features.monthlyTasks')}${plan.maxTasks}${t('features.times')}`}
                      {t('features.taskLimit')}
                    </span>
                  </div>

                  <div className='flex items-center gap-2'>
                    <Check className='h-4 w-4 text-green-600' />
                    <span className='text-sm'>{plan.taskTypes.join('、')}</span>
                  </div>

                  <div className='flex items-center gap-2'>
                    <Check className='h-4 w-4 text-green-600' />
                    <span className='text-sm'>
                      {t('features.platformRate')} {plan.platformRate}%
                    </span>
                  </div>

                  <div className='flex items-center gap-2'>
                    {plan.whitelistSlots > 0 ? (
                      <Check className='h-4 w-4 text-green-600' />
                    ) : (
                      <X className='h-4 w-4 text-red-600' />
                    )}
                    <span className='text-sm'>
                      {plan.whitelistSlots === 0
                        ? t('features.none')
                        : `${plan.whitelistSlots}${t('features.slots')}`}
                      {t('features.whitelistSlots')}
                    </span>
                  </div>

                  <div className='flex items-center gap-2'>
                    <Headphones className='h-4 w-4 text-blue-600' />
                    <span className='text-sm'>{plan.supportLevel}</span>
                  </div>
                </div>

                <div className='pt-4'>
                  {isCurrentPlan ? (
                    <Button disabled className='w-full'>
                      <Crown className='h-4 w-4 mr-2' />
                      {t('actions.current')}
                    </Button>
                  ) : planType === 'FREE' ? (
                    <Button variant='outline' disabled className='w-full'>
                      {t('actions.noDowngrade')}
                    </Button>
                  ) : membership.memberPlan === 'BUSINESS' &&
                    planType === 'PRO' ? (
                    <Button variant='outline' disabled className='w-full'>
                      {t('actions.noDowngrade')}
                    </Button>
                  ) : (
                    <Button
                      onClick={() => handleUpgrade(plan.id, plan.name)}
                      className='w-full'
                      variant={planType === 'BUSINESS' ? 'default' : 'outline'}
                      disabled={upgradeMutation.isPending}
                    >
                      <TrendingUp className='h-4 w-4 mr-2' />
                      {upgradeMutation.isPending
                        ? t('actions.processing')
                        : t('actions.upgrade')}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 权益说明 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Zap className='h-5 w-5' />
            {t('benefits.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <h4 className='font-medium'>{t('benefits.taskLimitTitle')}</h4>
              <p className='text-sm text-muted-foreground'>
                {t('benefits.taskLimitDesc')}
              </p>
            </div>

            <div className='space-y-2'>
              <h4 className='font-medium'>{t('benefits.platformRateTitle')}</h4>
              <p className='text-sm text-muted-foreground'>
                {t('benefits.platformRateDesc')}
              </p>
            </div>

            <div className='space-y-2'>
              <h4 className='font-medium'>{t('benefits.whitelistTitle')}</h4>
              <p className='text-sm text-muted-foreground'>
                {t('benefits.whitelistDesc')}
              </p>
            </div>

            <div className='space-y-2'>
              <h4 className='font-medium'>{t('benefits.supportTitle')}</h4>
              <p className='text-sm text-muted-foreground'>
                {t('benefits.supportDesc')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 支付对话框 */}
      <MembershipPaymentDialog
        open={paymentDialog.open}
        onOpenChange={open => setPaymentDialog(prev => ({ ...prev, open }))}
        planId={paymentDialog.planId}
        planName={paymentDialog.planName}
        price={paymentDialog.price}
        action={paymentDialog.action}
        onSuccess={() => {
          // 支付成功后刷新会员数据
          window.location.reload();
        }}
      />
    </div>
  );
}
