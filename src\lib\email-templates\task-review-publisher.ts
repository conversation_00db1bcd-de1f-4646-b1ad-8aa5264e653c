export interface TaskReviewPublisherEmailData {
  publisherName: string;
  publisherEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  reviewedAt: string;
  approved: boolean;
  rejectReason?: string;
  listingTime?: string;
  expiresAt?: string;
}

export const taskReviewPublisherTemplate = (
  data: TaskReviewPublisherEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>委托审核结果通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: ${data.approved ? '#28a745' : '#dc3545'}; margin-bottom: 20px; text-align: center;">
        ${data.approved ? '✅ 委托审核通过' : '❌ 委托审核未通过'}
      </h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.publisherName}！您提交的委托审核${data.approved ? '已通过' : '未通过'}。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">审核时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.reviewedAt}</span>
        </div>
      </div>
      
      ${
        data.approved
          ? `
        <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p style="color: #155724; margin: 0; font-size: 14px;">
            ✅ <strong>委托审核通过：</strong><br>
            • 委托已成功发布到委托大厅<br>
            • 上架时长：${data.listingTime || '未知'}<br>
            • 到期时间：${data.expiresAt || '未知'}<br>
            • 用户现在可以看到并接受您的委托<br>
            • 您可以在"我的发布"中查看委托进度
          </p>
        </div>
      `
          : `
        <div style="background: #f8d7da; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p style="color: #721c24; margin: 0; font-size: 14px;">
            ❌ <strong>委托审核未通过：</strong><br>
            • 拒绝原因：${data.rejectReason || '未提供具体原因'}<br>
            • 您可以修改委托内容后重新提交<br>
            • 如有疑问，请联系客服咨询<br>
            • 委托费用将在24小时内自动退还
          </p>
        </div>
      `
      }
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
