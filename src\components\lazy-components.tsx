'use client';

import dynamic from 'next/dynamic';
import React, { ComponentType, Suspense } from 'react';

import { EnhancedLoading } from '@/components/enhanced-loading';
import { Skeleton } from '@/components/ui/skeleton';

// 通用加载组件
const DefaultLoadingComponent = () => (
  <EnhancedLoading type='skeleton' skeletonType='dashboard' />
);

const FormLoadingComponent = () => (
  <EnhancedLoading type='skeleton' skeletonType='form' />
);

const ListLoadingComponent = () => (
  <EnhancedLoading type='skeleton' skeletonType='list' />
);

const ProfileLoadingComponent = () => (
  <EnhancedLoading type='skeleton' skeletonType='profile' />
);

// 小型组件加载器
const SmallComponentLoader = () => (
  <div className='flex items-center justify-center p-4'>
    <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary'></div>
  </div>
);

// 卡片加载器
const CardLoader = () => (
  <div className='p-4 border rounded-lg space-y-3'>
    <Skeleton className='h-4 w-3/4' />
    <Skeleton className='h-20 w-full' />
    <div className='flex justify-between'>
      <Skeleton className='h-3 w-16' />
      <Skeleton className='h-3 w-12' />
    </div>
  </div>
);

// 懒加载的用户页面组件
export const LazyDashboardContent = dynamic(
  () =>
    import('@/components/dashboard-content').then(mod => ({
      default: mod.DashboardContent,
    })),
  {
    loading: DefaultLoadingComponent,
    ssr: false, // 仪表盘内容通常是用户特定的，不需要 SSR
  },
);

export const LazyTasksPageContent = dynamic(
  () => import('@/components/tasks-page-content'),
  {
    loading: ListLoadingComponent,
    ssr: false,
  },
);

export const LazyAccountSecurityContent = dynamic(
  () =>
    import('@/components/account-security-content').then(mod => ({
      default: mod.AccountSecurityContent,
    })),
  {
    loading: FormLoadingComponent,
    ssr: false,
  },
);

export const LazyWhitelistContent = dynamic(
  () =>
    import('@/components/whitelist-content').then(mod => ({
      default: mod.WhitelistContent,
    })),
  {
    loading: ListLoadingComponent,
    ssr: false,
  },
);

// 懒加载的表单组件
export const LazyPublishTaskContent = dynamic(
  () =>
    import('@/components/publish-task-content').then(mod => ({
      default: mod.PublishTaskContent,
    })),
  {
    loading: FormLoadingComponent,
    ssr: false,
  },
);

export const LazyWalletContent = dynamic(
  () =>
    import('@/components/wallet-content').then(mod => ({
      default: mod.WalletContent,
    })),
  {
    loading: FormLoadingComponent,
    ssr: false,
  },
);

// 懒加载的图表组件 (暂时注释掉，因为组件不存在)
// export const LazyChart = dynamic(
//   () => import('@/components/charts'),
//   {
//     loading: () => (
//       <div className="h-64 w-full">
//         <Skeleton className="h-full w-full" />
//       </div>
//     ),
//     ssr: false,
//   }
// );

// export const LazyAnalyticsDashboard = dynamic(
//   () => import('@/components/analytics-dashboard'),
//   {
//     loading: DefaultLoadingComponent,
//     ssr: false,
//   }
// );

// 懒加载的模态框组件
export const LazyModal = dynamic(
  () => import('@/components/ui/dialog').then(mod => ({ default: mod.Dialog })),
  {
    loading: SmallComponentLoader,
    ssr: false,
  },
);

export const LazyConfirmDialog = dynamic(
  () =>
    import('@/components/feedback-system').then(mod => ({
      default: mod.ConfirmDialog,
    })),
  {
    loading: SmallComponentLoader,
    ssr: false,
  },
);

// 懒加载的富文本编辑器 (暂时注释掉，因为组件不存在)
// export const LazyRichTextEditor = dynamic(
//   () => import('@/components/rich-text-editor'),
//   {
//     loading: () => (
//       <div className="border rounded-lg p-4">
//         <Skeleton className="h-32 w-full" />
//       </div>
//     ),
//     ssr: false,
//   }
// );

// 懒加载的文件上传组件
export const LazyFileUpload = dynamic(
  () =>
    import('@/components/ui/file-upload').then(mod => ({
      default: mod.FileUpload,
    })),
  {
    loading: () => (
      <div className='border-2 border-dashed border-muted-foreground/25 rounded-lg p-8'>
        <Skeleton className='h-16 w-full' />
      </div>
    ),
    ssr: false,
  },
);

// 懒加载的数据表格 (暂时注释掉，因为组件不存在)
// export const LazyDataTable = dynamic(
//   () => import('@/components/data-table'),
//   {
//     loading: ListLoadingComponent,
//     ssr: false,
//   }
// );

// 懒加载的日历组件 (暂时注释掉，因为组件不存在)
// export const LazyCalendar = dynamic(
//   () => import('@/components/ui/calendar').then(mod => ({ default: mod.Calendar })),
//   {
//     loading: () => (
//       <div className="p-4">
//         <Skeleton className="h-64 w-full" />
//       </div>
//     ),
//     ssr: false,
//   }
// );

// 懒加载的地图组件 (暂时注释掉，因为组件不存在)
// export const LazyMap = dynamic(
//   () => import('@/components/map'),
//   {
//     loading: () => (
//       <div className="h-96 w-full bg-muted rounded-lg flex items-center justify-center">
//         <div className="text-muted-foreground">加载地图中...</div>
//       </div>
//     ),
//     ssr: false,
//   }
// );

// 懒加载的通知组件
export const LazyNotificationCenter = dynamic(
  () =>
    import('@/components/feedback-system').then(mod => ({
      default: mod.NotificationCenter,
    })),
  {
    loading: SmallComponentLoader,
    ssr: false,
  },
);

export const LazyFeedbackCollector = dynamic(
  () =>
    import('@/components/feedback-system').then(mod => ({
      default: mod.FeedbackCollector,
    })),
  {
    loading: SmallComponentLoader,
    ssr: false,
  },
);

// 懒加载的会话监控
export const LazySessionMonitor = dynamic(
  () => import('@/components/session-monitor'),
  {
    loading: () => null, // 会话监控不需要显示加载状态
    ssr: false,
  },
);

// 懒加载的键盘导航调试
export const LazyKeyboardNavigationDebug = dynamic(
  () =>
    import('@/components/keyboard-navigation').then(mod => ({
      default: mod.KeyboardNavigationDebug,
    })),
  {
    loading: () => null,
    ssr: false,
  },
);

// 懒加载的设备信息
export const LazyDeviceInfo = dynamic(
  () =>
    import('@/components/responsive-utils').then(mod => ({
      default: mod.DeviceInfo,
    })),
  {
    loading: () => null,
    ssr: false,
  },
);

// 懒加载的导航调试
export const LazyNavigationDebug = dynamic(
  () =>
    import('@/components/navigation-status').then(mod => ({
      default: mod.NavigationDebug,
    })),
  {
    loading: () => null,
    ssr: false,
  },
);

// 高阶组件：为任何组件添加懒加载
export function withLazyLoading<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  loadingComponent?: ComponentType | (() => React.ReactElement),
  options?: {
    ssr?: boolean;
  },
) {
  return dynamic(importFn, {
    loading:
      typeof loadingComponent === 'function' && loadingComponent.length === 0
        ? (loadingComponent as () => React.ReactElement)
        : loadingComponent
          ? () => React.createElement(loadingComponent as ComponentType)
          : () => <SmallComponentLoader />,
    ssr: options?.ssr ?? true,
  });
}

// 条件懒加载：只在特定条件下懒加载
export function conditionalLazyLoad<P extends object>(
  condition: boolean,
  lazyComponent: ComponentType<P>,
  eagerComponent: ComponentType<P>,
) {
  return condition ? lazyComponent : eagerComponent;
}

// 预加载函数
export function preloadComponent(importFn: () => Promise<any>) {
  if (typeof window !== 'undefined') {
    // 在浏览器环境中预加载
    importFn().catch(() => {
      // 忽略预加载错误
    });
  }
}

// 预加载关键组件
export function preloadCriticalComponents() {
  // 预加载用户可能很快会用到的组件
  preloadComponent(() => import('@/components/dashboard-content'));
  preloadComponent(() => import('@/components/tasks-page-content'));
  preloadComponent(() => import('@/components/feedback-system'));
}

// 基于路由的预加载
export function preloadRouteComponents(route: string) {
  switch (route) {
    case '/dashboard':
      preloadComponent(() => import('@/components/dashboard-content'));
      // preloadComponent(() => import('@/components/charts')); // 组件不存在，暂时注释
      break;
    case '/tasks':
      preloadComponent(() => import('@/components/tasks-page-content'));
      // preloadComponent(() => import('@/components/data-table')); // 组件不存在，暂时注释
      break;
    case '/publish':
      preloadComponent(() => import('@/components/publish-task-content'));
      preloadComponent(() => import('@/components/ui/file-upload'));
      break;
    case '/profile':
      preloadComponent(() => import('@/components/account-security-content'));
      // preloadComponent(() => import('@/components/profile-form')); // 组件不存在，暂时注释
      break;
    default:
      break;
  }
}

// Suspense 包装器组件
interface SuspenseWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  errorBoundary?: boolean;
}

export function SuspenseWrapper({
  children,
  fallback = <SmallComponentLoader />,
  errorBoundary = true,
}: SuspenseWrapperProps) {
  const content = <Suspense fallback={fallback}>{children}</Suspense>;

  if (errorBoundary) {
    // 这里可以添加错误边界组件
    return content;
  }

  return content;
}

// PWA 相关组件 (暂时注释掉，因为组件不存在)
// export const LazyPWAInstallPrompt = dynamic(
//   () => import('@/components/pwa-install-prompt'),
//   {
//     loading: SmallComponentLoader,
//     ssr: false,
//   }
// );

// export const LazyOfflineIndicator = dynamic(
//   () => import('@/components/offline-indicator'),
//   {
//     loading: () => null,
//     ssr: false,
//   }
// );

export default LazyDashboardContent;
