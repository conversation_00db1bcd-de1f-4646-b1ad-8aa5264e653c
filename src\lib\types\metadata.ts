import { Metadata } from 'next';

// Metadata配置接口
export interface MetadataProps {
  title?: string;
  description?: string;
  image?: string;
  icons?: string;
  keywords?: string[];
  authors?: { name: string; url?: string }[];
  robots?: string;
}

// 网站配置接口
export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  creator: string;
  keywords: string[];
}

// 常用页面标题配置
export const PAGE_TITLES = {
  HOME: '首页',
  SIGN_IN: '登录',
  SIGN_UP: '注册',
  DASHBOARD: '仪表盘',
  PROFILE: '个人资料',
  SETTINGS: '设置',
  HELP: '帮助中心',
  ABOUT: '关于我们',
  WALLET: '我的钱包',
  MEMBERSHIP: '会员套餐',
  WHITELIST: '商铺白名单',
} as const;

// 常用页面描述配置
export const PAGE_DESCRIPTIONS = {
  HOME: '欢迎来到RefundGo，高效管理您的工作委托和项目协作',
  SIGN_IN: '登录您的账户，开始高效的委托管理之旅',
  SIGN_UP: '创建新账户，加入我们的委托管理平台',
  DASHBOARD: '查看您的委托概览、项目进度和工作统计',
  PROFILE: '管理您的个人信息和账户设置',
  SETTINGS: '个性化设置和系统配置',
  HELP: '获取使用帮助和常见问题解答',
  ABOUT: '了解我们的产品和团队',
  WALLET: '管理您的钱包余额、充值提现和交易记录',
  MEMBERSHIP: '查看和管理您的会员套餐，升级获得更多权益',
  WHITELIST: '管理您的商铺白名单，保护信任的店铺',
} as const;
