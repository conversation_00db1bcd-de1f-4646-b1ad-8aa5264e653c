# AI Assistant Guide for Refundgo Web 2

## Project Understanding

This is a Next.js 14+ task marketplace platform with user authentication, payment processing, and
administrative features. Always consider the full-stack nature when making suggestions.

## Key Principles

1. **Type Safety First**: Always use TypeScript with proper types
2. **Server Components**: Default to RSC, use 'use client' only when needed
3. **Database Consistency**: Use Prisma for all database operations
4. **Security**: Validate inputs, check authentication, sanitize data
5. **Performance**: Consider caching, lazy loading, and optimization

## When Helping With

### Components

- Use shadcn/ui patterns and Radix primitives
- Implement proper accessibility (ARIA labels, keyboard navigation)
- Follow responsive design principles
- Use Tailwind CSS utility classes
- Consider loading and error states

### API Routes

- Validate requests with Zod schemas
- Handle authentication with NextAuth
- Use proper HTTP status codes
- Implement error handling
- Consider rate limiting for sensitive endpoints

### Database Operations

- Use Prisma client with proper error handling
- Implement transactions for complex operations
- Consider database performance and indexing
- Use proper relations and includes
- Handle connection pooling

### Authentication

- Use NextAuth v5 patterns
- Implement proper session management
- Check permissions for protected routes
- Handle authentication errors gracefully
- Consider CSRF protection

### Forms

- Use React Hook Form with Zod validation
- Implement proper error display
- Consider accessibility for form elements
- Handle loading states during submission
- Provide clear feedback to users

### Testing

- Write meaningful test descriptions
- Mock external dependencies properly
- Test both happy path and error cases
- Consider integration tests for complex flows
- Use proper test data setup and cleanup

## Common Patterns to Follow

### Error Handling

```typescript
try {
  // Operation
} catch (error) {
  console.error('Context:', error);
  return { error: 'User-friendly message' };
}
```

### API Response Format

```typescript
// Success
{ success: true, data: result }

// Error
{ success: false, error: 'Message' }
```

### Component Props

```typescript
interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // Specific props
}
```

### Database Queries

```typescript
const result = await prisma.model.findMany({
  where: {
    /* conditions */
  },
  select: {
    /* only needed fields */
  },
  include: {
    /* relations */
  },
});
```

## File Organization

- Components in `/components` with clear naming
- Utilities in `/lib` with single responsibility
- Types in `/types` with descriptive names
- API routes following REST conventions
- Tests co-located with components

## Performance Considerations

- Use Next.js Image component for images
- Implement proper caching strategies
- Consider bundle size impact
- Use dynamic imports for heavy components
- Optimize database queries

## Security Checklist

- Validate all user inputs
- Check authentication for protected routes
- Sanitize data before database operations
- Use environment variables for secrets
- Implement proper CORS policies

## Common Mistakes to Avoid

- Don't use 'use client' unnecessarily
- Don't forget error boundaries
- Don't skip input validation
- Don't expose sensitive data in client components
- Don't forget to handle loading states

## When Suggesting Code

1. Provide complete, working examples
2. Include proper TypeScript types
3. Add error handling
4. Consider edge cases
5. Follow project conventions
6. Include relevant imports
7. Add helpful comments for complex logic

## Questions to Ask

- What's the user's role/permission level?
- Does this need real-time updates?
- What's the expected data volume?
- Are there any specific performance requirements?
- Should this be cached?
- What error scenarios should be handled?
