'use client';

import { useRouter } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { useEffect, useRef, useState, useCallback } from 'react';
import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

// 会话配置（与服务端保持一致）
const SESSION_CONFIG = {
  TIMEOUT_DURATION: 30 * 60 * 1000, // 30分钟
  WARNING_DURATION: 5 * 60 * 1000, // 5分钟
  REFRESH_INTERVAL: 5 * 60 * 1000, // 5分钟
  CHECK_INTERVAL: 60 * 1000, // 1分钟检查一次
} as const;

interface SessionMonitorProps {
  children?: React.ReactNode;
}

export function SessionMonitor({ children }: SessionMonitorProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [showWarning, setShowWarning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const lastActivityRef = useRef(Date.now());
  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 更新最后活动时间
  const updateLastActivity = () => {
    lastActivityRef.current = Date.now();
  };

  // 检查会话状态
  const checkSessionStatus = useCallback(async () => {
    if (status !== 'authenticated' || !session) {
      return;
    }

    try {
      const response = await fetch('/api/auth/session-status', {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Session check failed');
      }

      const data = await response.json();

      if (data.status === 'expired') {
        await handleSessionExpired();
      } else if (data.status === 'warning') {
        setTimeLeft(data.timeLeft);
        setShowWarning(true);
      }
    } catch (error) {
      console.error('Session status check failed:', error);
      // 如果检查失败，可能是网络问题，不立即登出
    }
  }, [status, session]);

  // 处理会话过期
  const handleSessionExpired = async () => {
    toast.error('会话已过期，请重新登录');
    await signOut({
      callbackUrl: '/sign-in',
      redirect: true,
    });
  };

  // 延长会话
  const extendSession = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/extend-session', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        setShowWarning(false);
        updateLastActivity();
        toast.success('会话已延长');
      } else {
        throw new Error('Failed to extend session');
      }
    } catch (error) {
      console.error('Failed to extend session:', error);
      toast.error('延长会话失败，请重新登录');
      await handleSessionExpired();
    }
  }, []);

  // 处理用户活动
  const handleUserActivity = useCallback(() => {
    updateLastActivity();

    // 如果显示警告且用户有活动，自动延长会话
    if (showWarning) {
      extendSession();
    }
  }, [showWarning, extendSession]);

  // 设置活动监听器
  useEffect(() => {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity, true);
      });
    };
  }, [handleUserActivity]);

  // 定期检查会话状态
  useEffect(() => {
    if (status === 'authenticated') {
      const interval = setInterval(
        checkSessionStatus,
        SESSION_CONFIG.CHECK_INTERVAL,
      );
      return () => clearInterval(interval);
    }
  }, [status, session, checkSessionStatus]);

  // 警告倒计时
  useEffect(() => {
    if (showWarning && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1000) {
            handleSessionExpired();
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [showWarning, timeLeft]);

  // 格式化剩余时间
  const formatTimeLeft = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 如果用户未登录，不显示监控器
  if (status !== 'authenticated') {
    return <>{children}</>;
  }

  return (
    <>
      {children}

      <AlertDialog open={showWarning} onOpenChange={setShowWarning}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>会话即将过期</AlertDialogTitle>
            <AlertDialogDescription>
              您的会话将在 <strong>{formatTimeLeft(timeLeft)}</strong> 后过期。
              <br />
              是否要延长会话时间？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleSessionExpired}>
              登出
            </AlertDialogCancel>
            <AlertDialogAction onClick={extendSession}>
              延长会话
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

// 会话状态 Hook
export function useSessionStatus() {
  const { data: session, status } = useSession();
  const [sessionStatus, setSessionStatus] = useState<
    'active' | 'warning' | 'expired'
  >('active');
  const [timeLeft, setTimeLeft] = useState(0);

  useEffect(() => {
    if (status === 'authenticated') {
      const checkStatus = async () => {
        try {
          const response = await fetch('/api/auth/session-status');
          if (response.ok) {
            const data = await response.json();
            setSessionStatus(data.status);
            setTimeLeft(data.timeLeft || 0);
          }
        } catch (error) {
          console.error('Failed to check session status:', error);
        }
      };

      const interval = setInterval(checkStatus, 60000); // 每分钟检查一次
      checkStatus(); // 立即检查一次

      return () => clearInterval(interval);
    }
  }, [status]);

  return {
    sessionStatus,
    timeLeft,
    isAuthenticated: status === 'authenticated',
    session,
  };
}

// 自动刷新会话 Hook
export function useSessionRefresh() {
  const { data: session } = useSession();

  useEffect(() => {
    if (session) {
      const refreshSession = async () => {
        try {
          await fetch('/api/auth/refresh-session', {
            method: 'POST',
            credentials: 'include',
          });
        } catch (error) {
          console.error('Failed to refresh session:', error);
        }
      };

      const interval = setInterval(
        refreshSession,
        SESSION_CONFIG.REFRESH_INTERVAL,
      );
      return () => clearInterval(interval);
    }
  }, [session]);
}

export default SessionMonitor;
