export interface PaymentProvider {
  name: string;
  createPayment(params: CreatePaymentParams): Promise<CreatePaymentResult>;
  queryPayment(orderNo: string): Promise<QueryPaymentResult>;
  verifyNotify(data: any, signature?: string): boolean;
  refundPayment?(params: RefundParams): Promise<RefundResult>;
}

export interface CreatePaymentParams {
  orderNo: string;
  amount: number;
  currency: string;
  description?: string;
  notifyUrl: string;
  returnUrl?: string;
  paymentMethod?: string;
  userId?: string;
  clientip?: string;
  metadata?: Record<string, any>;
  // 货币转换相关参数
  originalAmount?: number;
  originalCurrency?: string;
  convertedAmount?: number;
  convertedCurrency?: string;
  exchangeRate?: number;
  exchangeRateSource?: string;
}

export interface CreatePaymentResult {
  success: boolean;
  paymentUrl?: string;
  qrCode?: string;
  thirdOrderNo?: string;
  message?: string;
  // 货币转换信息
  currencyConversion?: {
    originalAmount: number;
    originalCurrency: string;
    convertedAmount: number;
    convertedCurrency: string;
    exchangeRate: number;
    source: string;
  };
}

export interface QueryPaymentResult {
  success: boolean;
  status?: string;
  amount?: number;
  currency?: string;
  thirdOrderNo?: string;
  message?: string;
  paidAmount?: number; // 实际支付金额
  paymentMethod?: string; // 支付方式
}

export interface RefundParams {
  orderNo: string;
  thirdOrderNo?: string;
  amount?: number;
  reason?: string;
}

export interface RefundResult {
  success: boolean;
  refundId?: string;
  message?: string;
}

export interface PaymentConfig {
  id: string;
  provider: string;
  name: string;
  isEnabled: boolean;
  settings: Record<string, any>;
}

// 聚合易支付配置接口
export interface YunPayConfig {
  pid: string;
  key: string;
  apiUrl: string;
  paymentMethods?: {
    alipay?: {
      enabled: boolean;
      name: string;
      description: string;
      feeRate: number;
      minAmount: number;
    };
    wxpay?: {
      enabled: boolean;
      name: string;
      description: string;
      feeRate: number;
      minAmount: number;
    };
    paypal?: {
      enabled: boolean;
      name: string;
      description: string;
      feeRate: number;
      minAmount: number;
    };
  };
}

// NOWPayments配置接口
export interface NOWPaymentsConfig {
  apiKey: string;
  apiUrl: string;
  ipnSecret?: string;
  sandbox?: boolean; // 沙盒模式开关
  feeRate?: number;
  minAmount?: number;
}
