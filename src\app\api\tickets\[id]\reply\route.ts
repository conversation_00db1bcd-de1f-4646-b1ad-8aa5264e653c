import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { createServerTranslator } from '@/lib/server-i18n';
import { replyTicketSchema } from '@/lib/types/ticket';

// 工单回复
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 创建服务器端翻译器
    const { t } = createServerTranslator(request);

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t('loginRequired') }, { status: 401 });
    }

    const { id: ticketId } = await params;
    const body = await request.json();

    // 验证请求数据
    const validation = replyTicketSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: t('invalidData'), details: validation.error.errors },
        { status: 400 },
      );
    }

    const { content } = validation.data;

    // 验证工单权限（用户只能回复自己的工单）
    const ticket = await prisma.ticket.findFirst({
      where: {
        id: ticketId,
        userId: session.user.id,
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: t('ticketNotFound') }, { status: 404 });
    }

    // 检查工单状态（已关闭或已取消的工单不能回复）
    if (ticket.status === 'CLOSED' || ticket.status === 'CANCELLED') {
      return NextResponse.json({ error: t('ticketClosed') }, { status: 400 });
    }

    // 创建回复
    const reply = await prisma.ticketReply.create({
      data: {
        ticketId,
        content,
        authorId: session.user.id,
        authorName: session.user.name || session.user.email || '用户',
        isStaff: false,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // 如果工单状态是待处理，自动更新为处理中
    if (ticket.status === 'PENDING') {
      await prisma.ticket.update({
        where: { id: ticketId },
        data: {
          status: 'IN_PROGRESS',
          updatedAt: new Date(),
        },
      });
    } else {
      // 更新工单的最后更新时间
      await prisma.ticket.update({
        where: { id: ticketId },
        data: { updatedAt: new Date() },
      });
    }

    return NextResponse.json(reply, { status: 201 });
  } catch (error) {
    console.error('API错误:', error);
    const { t } = createServerTranslator(request);
    return NextResponse.json({ error: t('serverError') }, { status: 500 });
  }
}
