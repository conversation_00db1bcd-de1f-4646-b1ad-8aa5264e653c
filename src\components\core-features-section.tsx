"use client";

import { motion } from "framer-motion";
import {
  Shield,
  Zap,
  Users,
  TrendingUp,
  Award,
  Clock,
  CheckCircle,
  Star,
  Sparkles,
  Target,
  Globe,
  Heart,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";

export function CoreFeaturesSection() {
  const t = useTranslations("HomePage");
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      icon: Shield,
      title: t("features.security.title"),
      description: t("features.security.description"),
      iconColor: "text-blue-600 dark:text-blue-400",
      bgColor: "from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20",
      borderColor: "from-blue-500 to-blue-600",
    },
    {
      icon: Zap,
      title: t("features.efficiency.title"),
      description: t("features.efficiency.description"),
      iconColor: "text-green-600 dark:text-green-400",
      bgColor: "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
      borderColor: "from-green-500 to-green-600",
    },
    {
      icon: Users,
      title: t("features.professional.title"),
      description: t("features.professional.description"),
      iconColor: "text-purple-600 dark:text-purple-400",
      bgColor: "from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",
      borderColor: "from-purple-500 to-purple-600",
    },
    {
      icon: TrendingUp,
      title: t("features.profit.title"),
      description: t("features.profit.description"),
      iconColor: "text-orange-600 dark:text-orange-400",
      bgColor: "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20",
      borderColor: "from-orange-500 to-orange-600",
    },
  ];



  return (
    <section id="features" className="py-16 md:py-24 bg-background dark:bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <Badge className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white px-6 py-2 text-sm font-medium mb-6">
            <Sparkles className="h-4 w-4 mr-2" />
            {t("features.coreAdvantages")}
          </Badge>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary dark:text-text-primary mb-6">
            {t("features.title")}
          </h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary max-w-3xl mx-auto">
            {t("features.subtitle")}
          </p>
        </motion.div>

        {/* 主要功能特色 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.05, y: -10 }}
              onHoverStart={() => setActiveFeature(index)}
              className="group cursor-pointer"
            >
              <Card className="h-full bg-card dark:bg-card border-0 shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden">
                <div className={`h-2 bg-gradient-to-r ${feature.borderColor}`} />
                <CardHeader className="text-center pb-4 pt-8">
                  <div className={`mx-auto mb-6 p-4 bg-gradient-to-r ${feature.bgColor} rounded-2xl w-fit group-hover:scale-110 transition-all duration-300`}>
                    <feature.icon className={`h-8 w-8 ${feature.iconColor}`} />
                  </div>
                  <CardTitle className="text-xl font-bold text-text-primary dark:text-text-primary mb-3">
                    {feature.title}
                  </CardTitle>
                  <CardDescription className="text-text-secondary dark:text-text-secondary leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
