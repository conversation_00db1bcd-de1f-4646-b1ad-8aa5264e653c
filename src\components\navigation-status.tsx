'use client';

import {
  MapPin,
  Shield,
  User,
  Settings,
  Home,
  ChevronRight,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { useActiveRoute } from '@/hooks/use-active-route';
import { cn } from '@/lib/utils';

interface NavigationStatusProps {
  className?: string;
  showPath?: boolean;
  showContext?: boolean;
}

export function NavigationStatus({
  className,
  showPath = true,
  showContext = true,
}: NavigationStatusProps) {
  const {
    pathname,
    isUserArea,
    isAdminArea,
    requiresAuth,
    getNavigationContext,
  } = useActiveRoute();

  const context = getNavigationContext();

  const getAreaIcon = () => {
    if (context.isHome) return <Home className='h-3 w-3' />;
    if (isAdminArea()) return <Settings className='h-3 w-3' />;
    if (isUserArea()) return <User className='h-3 w-3' />;
    return <MapPin className='h-3 w-3' />;
  };

  const getAreaLabel = () => {
    if (context.isHome) return '首页';
    if (isAdminArea()) return '管理区域';
    if (isUserArea()) return '用户区域';
    return '公共区域';
  };

  const getAreaVariant = () => {
    if (context.isHome) return 'default';
    if (isAdminArea()) return 'destructive';
    if (isUserArea()) return 'secondary';
    return 'outline';
  };

  return (
    <div className={cn('flex items-center gap-2 text-xs', className)}>
      {showContext && (
        <div className='flex items-center gap-2'>
          <Badge variant={getAreaVariant()} className='flex items-center gap-1'>
            {getAreaIcon()}
            {getAreaLabel()}
          </Badge>

          {requiresAuth() && (
            <Badge variant='outline' className='flex items-center gap-1'>
              <Shield className='h-3 w-3' />
              需要登录
            </Badge>
          )}
        </div>
      )}

      {showPath && (
        <div className='flex items-center gap-1 text-muted-foreground'>
          <ChevronRight className='h-3 w-3' />
          <code className='text-xs bg-muted px-1 py-0.5 rounded'>
            {pathname}
          </code>
        </div>
      )}
    </div>
  );
}

// 面包屑路径组件
export function BreadcrumbPath({ className }: { className?: string }) {
  const { getBreadcrumbs } = useActiveRoute();
  const breadcrumbs = getBreadcrumbs();

  return (
    <div className={cn('flex items-center gap-1 text-sm', className)}>
      {breadcrumbs.map((crumb, index) => (
        <div key={crumb.href} className='flex items-center gap-1'>
          {index > 0 && (
            <ChevronRight className='h-3 w-3 text-muted-foreground' />
          )}
          <span
            className={cn(
              'transition-colors',
              crumb.isActive
                ? 'text-foreground font-medium'
                : 'text-muted-foreground hover:text-foreground',
            )}
          >
            {crumb.label}
          </span>
        </div>
      ))}
    </div>
  );
}

// 导航上下文显示组件
export function NavigationContext({ className }: { className?: string }) {
  const context = useActiveRoute().getNavigationContext();

  return (
    <div className={cn('space-y-2 text-xs', className)}>
      <div className='flex items-center gap-2'>
        <span className='font-medium'>当前位置:</span>
        <code className='bg-muted px-1 py-0.5 rounded'>
          {context.currentPath}
        </code>
      </div>

      <div className='flex flex-wrap gap-2'>
        {context.isHome && <Badge variant='default'>首页</Badge>}
        {context.isUserArea && <Badge variant='secondary'>用户区域</Badge>}
        {context.isAdminArea && <Badge variant='destructive'>管理区域</Badge>}
        {context.requiresAuth && <Badge variant='outline'>需要认证</Badge>}
      </div>
    </div>
  );
}

// 开发模式下的导航调试组件
export function NavigationDebug() {
  const { pathname, originalPathname, getNavigationContext, getBreadcrumbs } =
    useActiveRoute();

  const context = getNavigationContext();
  const breadcrumbs = getBreadcrumbs();

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className='fixed bottom-4 right-4 max-w-sm p-3 bg-background border rounded-lg shadow-lg text-xs space-y-2 z-50'>
      <div className='font-semibold text-primary'>导航调试信息</div>

      <div className='space-y-1'>
        <div>
          <strong>标准路径:</strong> {pathname}
        </div>
        <div>
          <strong>原始路径:</strong> {originalPathname}
        </div>
      </div>

      <div className='space-y-1'>
        <div>
          <strong>区域类型:</strong>
        </div>
        <div className='pl-2 space-y-0.5'>
          <div>首页: {context.isHome ? '✅' : '❌'}</div>
          <div>用户区域: {context.isUserArea ? '✅' : '❌'}</div>
          <div>管理区域: {context.isAdminArea ? '✅' : '❌'}</div>
          <div>需要认证: {context.requiresAuth ? '✅' : '❌'}</div>
        </div>
      </div>

      <div className='space-y-1'>
        <div>
          <strong>面包屑:</strong>
        </div>
        <div className='pl-2 space-y-0.5'>
          {breadcrumbs.map((crumb, index) => (
            <div key={index} className='flex items-center gap-1'>
              <span>{crumb.label}</span>
              {crumb.isActive && (
                <Badge variant='outline' className='text-xs'>
                  当前
                </Badge>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default NavigationStatus;
