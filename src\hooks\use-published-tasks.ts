import { useQuery } from '@tanstack/react-query';

interface PublishedTaskFilters {
  status?: string;
  page?: number;
  limit?: number;
}

interface PublishedTask {
  id: string;
  platform: string;
  category: string;
  chargebackTypes: string[];
  paymentMethods: string[];
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  finalTotal: number;
  productUrl: string;
  productDescription: string;
  recipientName: string;
  recipientPhone: string;
  shippingAddress: string;
  listingTime: string;
  status: string;
  evidenceStatus: string | null;
  evidenceRejectReason?: string;
  evidenceUploadType: string;
  evidenceFiles: string[];
  cartScreenshots: string[];

  // 订单信息
  orderNumber?: string;
  orderScreenshot?: string;

  // 物流信息
  trackingNumber?: string;
  logisticsScreenshots?: string[];
  logisticsDeadline?: string;

  // 审核时间相关
  orderReviewDeadline?: string; // 订单审核截止时间（提交订单后5小时）
  logisticsReviewDeadline?: string; // 物流审核截止时间（提交物流后24小时）
  deliveryDeadline?: string; // 确认收货截止时间（审核通过后30天）

  // 审核信息
  reviewedAt?: string;
  reviewRejectReason?: string;

  accepter: {
    id: string;
    nickname: string;
    email: string;
  } | null;
  createdAt: string;
  publishedAt?: string;
  acceptedAt?: string;
  expiresAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  deadline: string;
}

interface PublishedTaskStats {
  total: number;
  available: number;
  inProgress: number;
  completed: number;
  expired: number;
  cancelled: number;
}

interface PublishedTaskResponse {
  success: boolean;
  data: {
    tasks: PublishedTask[];
    stats: PublishedTaskStats;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export function usePublishedTasks(filters: PublishedTaskFilters = {}) {
  return useQuery<PublishedTaskResponse>({
    queryKey: ['published-tasks', filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(
        `/api/user/published-tasks?${params.toString()}`,
      );

      if (!response.ok) {
        throw new Error('获取发布委托失败');
      }

      return response.json();
    },
    staleTime: 30 * 1000, // 30秒
    refetchOnWindowFocus: false,
  });
}

export type { PublishedTask, PublishedTaskStats, PublishedTaskFilters };
