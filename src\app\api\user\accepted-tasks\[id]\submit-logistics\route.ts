import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendLogisticsReviewPublisherEmail } from '@/lib/email';
import { getTaskCommission } from '@/lib/utils/commission';

// 提交物流单号请求验证Schema
const submitLogisticsSchema = z.object({
  trackingNumber: z.string().min(6, '物流单号至少6位'),
  logisticsScreenshots: z
    .array(z.string())
    .min(1, '请至少上传一张物流截图')
    .max(5, '最多上传5张物流截图'),
});

// 提交物流单号API
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;
    const userId = session.user.id;

    // 2. 解析请求数据
    const body = await request.json();
    const validation = submitLogisticsSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 },
      );
    }

    const { trackingNumber, logisticsScreenshots } = validation.data;

    // 3. 查找委托并验证
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            name: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 4. 验证用户权限
    if (task.accepterId !== userId) {
      return NextResponse.json(
        { error: '只能提交自己接受的委托的物流单号' },
        { status: 403 },
      );
    }

    // 5. 检查委托状态
    if (task.status !== ('PENDING_LOGISTICS' as any)) {
      return NextResponse.json(
        { error: '只能为等待物流单号状态的委托提交物流单号' },
        { status: 400 },
      );
    }

    // 6. 获取系统费率配置和其他费率数据
    const [systemRate, chargebackTypes, paymentMethods] = await Promise.all([
      prisma.systemRate.findFirst(),
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    if (!systemRate) {
      return NextResponse.json({ error: '系统配置错误' }, { status: 500 });
    }

    // 7. 计算相关金额
    const totalPrice = task.unitPrice * task.quantity;
    const depositAmount = totalPrice * (systemRate.depositRatio / 100);

    // 使用真实的酬金计算函数（包含证据状态）
    const taskWithEvidenceStatus = {
      ...task,
      evidenceStatus: task.evidenceStatus?.toString(),
    };
    const commissionAmount = getTaskCommission(
      taskWithEvidenceStatus,
      chargebackTypes,
      paymentMethods,
      systemRate,
    );

    // 8. 计算合并审核截止时间（从提交物流开始的24小时后）
    const now = new Date();
    const reviewDeadline = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后

    // 9. 使用事务处理提交物流单号的所有操作
    const result = await prisma.$transaction(async tx => {
      // 9.1 更新委托状态为等待发布者审核，保存物流单号和截图
      const updatedTask = await tx.task.update({
        where: { id: taskId },
        data: {
          status: 'PENDING_REVIEW' as any,
          trackingNumber,
          logisticsScreenshots,
        },
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          accepter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              name: true,
            },
          },
          category: {
            select: {
              name: true,
            },
          },
        },
      });

      // 9.2 单独更新合并审核截止时间（避免类型问题）
      await tx.$executeRaw`
        UPDATE tasks 
        SET "logisticsReviewDeadline" = ${reviewDeadline}
        WHERE id = ${taskId}
      `;

      return {
        task: updatedTask,
        trackingNumber,
        reviewDeadline,
      };
    });

    // 10. 发送邮件通知发布者
    try {
      const publisherEmail = result.task.publisher.email;
      if (publisherEmail) {
        await sendLogisticsReviewPublisherEmail(publisherEmail, {
          publisherName: result.task.publisher.name || '用户',
          publisherEmail,
          taskId: result.task.id,
          platform: result.task.platform.name,
          category: result.task.category.name,
          quantity: task.quantity,
          unitPrice: task.unitPrice,
          totalAmount: task.unitPrice * task.quantity,
          submittedAt: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
          }),
          accepterName: result.task.accepter?.name || '用户',
          accepterEmail: result.task.accepter?.email || '',
          orderNumber: task.orderNumber || '',
          trackingNumber: result.trackingNumber,
          reviewDeadline: result.reviewDeadline.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          }),
          logisticsScreenshots,
        });
      }
    } catch (emailError) {
      console.error('发送物流审核邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    // 11. 返回成功响应
    return NextResponse.json({
      success: true,
      message: `物流信息已提交！发布者将在24小时内对订单和物流信息进行审核`,
      data: {
        task: {
          id: result.task.id,
          status: result.task.status,
          platform: result.task.platform.name,
          category: result.task.category.name,
          reviewDeadline: result.reviewDeadline,
        },
        logistics: {
          trackingNumber: result.trackingNumber,
          logisticsScreenshots,
          submitTime: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
