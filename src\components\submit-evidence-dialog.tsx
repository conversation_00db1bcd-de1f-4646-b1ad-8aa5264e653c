'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Upload,
  FileImage,
  X,
  CheckCircle,
  AlertTriangle,
  Video,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { FileUpload } from '@/components/ui/file-upload';
import { Label } from '@/components/ui/label';
import { UploadedFile } from '@/hooks/use-file-upload';

const createSubmitEvidenceSchema = (t: any) =>
  z.object({
    evidenceFiles: z
      .array(z.any())
      .min(1, t('dialog.submitEvidence.validation.filesRequired')),
  });

type SubmitEvidenceForm = z.infer<
  ReturnType<typeof createSubmitEvidenceSchema>
>;

interface SubmitEvidenceDialogProps {
  children: React.ReactNode;
  taskId: string;
  onSubmit?: (data: { taskId: string; files: UploadedFile[] }) => void;
}

export function SubmitEvidenceDialog({
  children,
  taskId,
  onSubmit,
}: SubmitEvidenceDialogProps) {
  const t = useTranslations('MyPublishedTasks');
  const [isOpen, setIsOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitEvidenceSchema = createSubmitEvidenceSchema(t);

  const {
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<SubmitEvidenceForm>({
    resolver: zodResolver(submitEvidenceSchema),
  });

  const handleFileChange = (files: UploadedFile[]) => {
    setUploadedFiles(files);
    setValue('evidenceFiles', files);
  };

  const onFormSubmit = async (data: SubmitEvidenceForm) => {
    setIsSubmitting(true);

    try {
      // 调用父组件的提交处理函数，传递已上传的文件信息
      onSubmit?.({ taskId, files: uploadedFiles });

      // 重置表单和状态
      reset();
      setUploadedFiles([]);
      setIsOpen(false);

      toast.success(t('messages.submitEvidenceSuccess'), {
        description: t('messages.submitEvidenceSuccessDesc'),
      });
    } catch (error) {
      toast.error(t('messages.submitEvidenceError'), {
        description: t('messages.submitEvidenceErrorDesc'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    reset();
    setUploadedFiles([]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle>{t('dialog.submitEvidence.title')}</DialogTitle>
          <DialogDescription>
            {t('dialog.submitEvidence.description')}
          </DialogDescription>
        </DialogHeader>

        <Alert>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            {t('dialog.submitEvidence.uploadRequirements')}
          </AlertDescription>
        </Alert>

        <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-6'>
          <div className='space-y-4'>
            <Label>{t('dialog.submitEvidence.evidenceFiles')}</Label>
            <FileUpload
              value={uploadedFiles}
              onChange={handleFileChange}
              accept='image/*,video/*'
              maxFiles={10}
              maxSize={50 * 1024 * 1024} // 50MB
              multiple={true}
              placeholder={t('upload.dragAndDrop')}
              description={t('upload.formats.all')}
            />
            {errors.evidenceFiles && (
              <p className='text-sm text-red-500'>
                {errors.evidenceFiles.message}
              </p>
            )}
          </div>

          <div className='flex justify-end space-x-2'>
            <Button type='button' variant='outline' onClick={handleClose}>
              {t('confirm.delete.cancel')}
            </Button>
            <Button
              type='submit'
              disabled={isSubmitting || uploadedFiles.length === 0}
            >
              {isSubmitting
                ? t('actions.submitting')
                : t('dialog.submitEvidence.submit')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
