import { getUserRegistrationLanguage } from '@/lib/user-language';

// 邮件样式常量 - 统一的CSS样式字符串 (RefundGo品牌标准)
export const emailStyles = `
  /* 移动优先响应式设计 */
  @media only screen and (max-width: 600px) {
    .email-container {
      padding: 10px !important;
    }
    .email-content {
      padding: 20px !important;
    }
    .email-header {
      padding: 20px 15px !important;
    }
  }

  .email-container {
    max-width: 600px;
    margin: 0 auto;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    padding: 20px;
    min-width: 320px; /* 确保最小宽度支持 */
  }

  .email-header {
    background: #ffffff;
    color: #1e293b;
    padding: 30px 20px;
    text-align: center;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }

  .email-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #1e293b;
    letter-spacing: -0.5px;
  }

  /* RefundGo品牌标识 */
  .brand-logo {
    display: inline-block;
    font-size: 32px;
    font-weight: 700;
    color: #3b82f6;
    text-decoration: none;
    margin-bottom: 8px;
  }

  .email-content {
    background: white;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }

  .email-footer {
    text-align: center;
    color: #64748b;
    font-size: 12px;
    margin-top: 30px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }

  .email-footer-links {
    margin-top: 16px;
  }

  .email-footer a {
    color: #3b82f6;
    text-decoration: none;
  }

  .email-footer a:hover {
    text-decoration: underline;
  }

  .email-buttons {
    text-align: center;
    margin: 30px 0;
  }

  .email-button {
    display: inline-block;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    margin: 8px;
    text-align: center;
    font-size: 14px;
  }

  .email-button-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
  }

  .email-button-secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
  }

  .verification-code {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    padding: 20px;
    margin: 20px 0;
    border-radius: 8px;
    letter-spacing: 4px;
    font-family: 'Courier New', monospace;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .info-box {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
  }

  .warning-box {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
  }

  .success-box {
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
  }
`;

// 向后兼容的内联样式对象
export const emailStylesLegacy = {
  container: `
    max-width: 600px;
    margin: 0 auto;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    padding: 20px;
  `,
  header: `
    background: #ffffff;
    color: #1e293b;
    padding: 30px 20px;
    text-align: center;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #e2e8f0;
  `,
  title: `
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #1e293b;
  `,
  content: `
    background: white;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  `,
  code: `
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    padding: 20px;
    margin: 20px 0;
    border-radius: 8px;
    letter-spacing: 4px;
    font-family: 'Courier New', monospace;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  `,
  infoBox: `
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
  `,
  warningBox: `
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
  `,
  footer: `
    text-align: center;
    color: #666;
    font-size: 12px;
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;
  `,
  successBox: `
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
  `,
  button: `
    display: inline-block;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    margin: 20px 0;
    text-align: center;
  `,
};

/**
 * 获取用户的邮件语言设置
 * @param userEmail 用户邮箱
 * @returns 用户的语言设置
 */
export async function getUserEmailLanguage(
  userEmail: string,
): Promise<'zh' | 'en'> {
  try {
    return await getUserRegistrationLanguage(userEmail);
  } catch (error) {
    console.error('获取用户邮件语言失败:', error);
    return 'en'; // 默认英文
  }
}

/**
 * 邮件翻译 Hook
 * 提供邮件模板的国际化支持
 */
export function useEmailTranslation() {
  /**
   * 根据用户邮箱获取对应语言的邮件内容
   * @param userEmail 用户邮箱
   * @param templateFunction 邮件模板函数
   * @param templateData 模板数据
   * @returns 国际化的邮件内容
   */
  const getLocalizedEmail = async <T>(
    userEmail: string,
    templateFunction: (data: T & { language?: 'zh' | 'en' }) => string,
    templateData: T,
  ): Promise<string> => {
    const language = await getUserEmailLanguage(userEmail);
    return templateFunction({ ...templateData, language });
  };

  /**
   * 直接指定语言获取邮件内容
   * @param language 语言设置
   * @param templateFunction 邮件模板函数
   * @param templateData 模板数据
   * @returns 指定语言的邮件内容
   */
  const getEmailWithLanguage = <T>(
    language: 'zh' | 'en',
    templateFunction: (data: T & { language?: 'zh' | 'en' }) => string,
    templateData: T,
  ): string => {
    return templateFunction({ ...templateData, language });
  };

  return {
    getLocalizedEmail,
    getEmailWithLanguage,
    getUserEmailLanguage,
  };
}

/**
 * 邮件主题翻译
 */
export const emailSubjects = {
  zh: {
    verification: {
      login: '登录验证码',
      register: '注册验证码',
      'reset-password': '重置密码验证码',
      'verify-current-email': '邮箱验证码',
      'change-email': '更换邮箱验证码',
    },
    welcome: '欢迎加入 RefundGo',
    passwordReset: '密码重置成功',
    taskNotification: '委托通知',
    paymentConfirmation: '付款确认',
  },
  en: {
    verification: {
      login: 'Login Verification Code',
      register: 'Registration Verification Code',
      'reset-password': 'Password Reset Verification Code',
      'verify-current-email': 'Email Verification Code',
      'change-email': 'Email Change Verification Code',
    },
    welcome: 'Welcome to RefundGo',
    passwordReset: 'Password Reset Successful',
    taskNotification: 'Task Notification',
    paymentConfirmation: 'Payment Confirmation',
  },
};

/**
 * 获取邮件主题
 * @param type 邮件类型
 * @param language 语言
 * @param action 操作类型（可选）
 * @returns 邮件主题
 */
export function getEmailSubject(
  type: keyof typeof emailSubjects.zh,
  language: 'zh' | 'en' = 'zh',
  action?: string,
): string {
  const subjects = emailSubjects[language];

  if (type === 'verification' && action) {
    const verificationSubjects = subjects.verification as Record<
      string,
      string
    >;
    return verificationSubjects[action] || verificationSubjects.register;
  }

  const subject = subjects[type];
  if (typeof subject === 'string') {
    return subject;
  }

  return subjects.welcome;
}

/**
 * 格式化邮件中的日期时间
 * @param dateString 日期字符串
 * @param language 语言
 * @returns 格式化的日期时间
 */
export function formatEmailDateTime(
  dateString: string,
  language: 'zh' | 'en' = 'zh',
): string {
  const date = new Date(dateString);

  if (language === 'en') {
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });
  } else {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });
  }
}

/**
 * 格式化邮件中的金额
 * @param amount 金额
 * @param currency 货币
 * @param language 语言
 * @returns 格式化的金额
 */
export function formatEmailAmount(
  amount: number,
  currency: string = 'USD',
  language: 'zh' | 'en' = 'zh',
): string {
  if (language === 'en') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  } else {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency,
    }).format(amount);
  }
}
