-- Create<PERSON><PERSON>
CREATE TYPE "EvidenceType" AS ENUM ('CART_SCREENSHOT', 'ORDER_CONFIRMATION', 'PAYMENT_PROOF', 'SHIPPING_LABEL', 'DELIVERY_PROOF', 'PRODUCT_PHOTO', 'COMMUNICATION_RECORD', 'REFUND_PROOF', 'OTHER');

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "registrationLanguage" TEXT NOT NULL DEFAULT 'en';

-- AlterTable
ALTER TABLE "payment_orders" ADD COLUMN     "convertedAmount" DECIMAL(10,2),
ADD COLUMN     "convertedCurrency" TEXT DEFAULT 'CNY',
ADD COLUMN     "exchangeRate" DECIMAL(10,6),
ADD COLUMN     "exchangeRateSource" TEXT,
ADD COLUMN     "originalAmount" DECIMAL(10,2),
ADD COLUMN     "originalCurrency" TEXT DEFAULT 'USD';

-- CreateTable
CREATE TABLE "task_evidence" (
    "id" TEXT NOT NULL,
    "taskId" TEXT NOT NULL,
    "evidenceType" "EvidenceType" NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "fileSize" INTEGER,
    "mimeType" TEXT,
    "description" TEXT,
    "status" "EvidenceStatus" NOT NULL DEFAULT 'PENDING_SUBMISSION',
    "reviewNote" TEXT,
    "reviewedBy" TEXT,
    "reviewedAt" TIMESTAMP(6),
    "uploadedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "task_evidence_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "task_evidence" ADD CONSTRAINT "task_evidence_reviewedBy_fkey" FOREIGN KEY ("reviewedBy") REFERENCES "User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "task_evidence" ADD CONSTRAINT "task_evidence_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "task_evidence" ADD CONSTRAINT "task_evidence_uploadedBy_fkey" FOREIGN KEY ("uploadedBy") REFERENCES "User"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
