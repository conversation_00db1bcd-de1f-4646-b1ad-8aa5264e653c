'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Smartphone,
  CreditCard,
  Bitcoin,
  Settings2,
  AlertCircle,
} from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';

const yunpayConfigSchema = z.object({
  provider: z.literal('yunpay'),
  name: z.string().min(1, '请输入显示名称'),
  isEnabled: z.boolean(),
  settings: z.object({
    pid: z.string().min(1, '请输入商户ID'),
    key: z.string().min(1, '请输入商户密钥'),
    apiUrl: z.string().url('请输入正确的API地址'),
    paymentMethods: z.object({
      alipay: z.object({
        enabled: z.boolean(),
        name: z.string().min(1, '请输入Alipay显示名称'),
        description: z.string().min(1, '请输入Alipay描述'),
        feeRate: z
          .number()
          .min(0, '手续费率不能为负数')
          .max(100, '手续费率不能超过100%'),
        minAmount: z
          .number()
          .min(0.01, '最小支付金额不能小于0.01')
          .max(10000, '最小支付金额不能超过10000'),
      }),
      wxpay: z.object({
        enabled: z.boolean(),
        name: z.string().min(1, '请输入WeChat Pay显示名称'),
        description: z.string().min(1, '请输入WeChat Pay描述'),
        feeRate: z
          .number()
          .min(0, '手续费率不能为负数')
          .max(100, '手续费率不能超过100%'),
        minAmount: z
          .number()
          .min(0.01, '最小支付金额不能小于0.01')
          .max(10000, '最小支付金额不能超过10000'),
      }),
      paypal: z.object({
        enabled: z.boolean(),
        name: z.string().min(1, '请输入PayPal显示名称'),
        description: z.string().min(1, '请输入PayPal描述'),
        feeRate: z
          .number()
          .min(0, '手续费率不能为负数')
          .max(100, '手续费率不能超过100%'),
        minAmount: z
          .number()
          .min(0.01, '最小支付金额不能小于0.01')
          .max(10000, '最小支付金额不能超过10000'),
      }),
    }),
  }),
});

const nowpaymentsConfigSchema = z.object({
  provider: z.literal('nowpayments'),
  name: z.string().min(1, '请输入显示名称'),
  isEnabled: z.boolean(),
  settings: z.object({
    apiKey: z.string().min(1, '请输入API密钥'),
    apiUrl: z.string().url('请输入正确的API地址'),
    ipnSecret: z.string().optional(),
    sandbox: z.boolean().optional(),
    feeRate: z
      .number()
      .min(0, '手续费率不能为负数')
      .max(100, '手续费率不能超过100%'),
    minAmount: z
      .number()
      .min(0.01, '最小支付金额不能小于0.01')
      .max(10000, '最小支付金额不能超过10000'),
  }),
});

export default function PaymentConfigsPage() {
  const queryClient = useQueryClient();

  const { data: configs, isLoading } = useQuery({
    queryKey: ['payment-configs'],
    queryFn: async () => {
      const res = await fetch('/api/admin/payment-configs');
      if (!res.ok) throw new Error('获取配置失败');
      return res.json();
    },
  });

  const updateConfigMutation = useMutation({
    mutationFn: async (data: any) => {
      const res = await fetch('/api/admin/payment-configs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!res.ok) throw new Error('保存配置失败');
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-configs'] });
      toast.success('配置保存成功');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const yunpayForm = useForm({
    resolver: zodResolver(yunpayConfigSchema),
    defaultValues: {
      provider: 'yunpay',
      name: '聚合易支付',
      isEnabled: false,
      settings: {
        pid: '',
        key: '',
        apiUrl: 'https://pay.747099.xyz',
        paymentMethods: {
          alipay: {
            enabled: false,
            name: 'Alipay',
            description: 'Alipay扫码支付',
            feeRate: 2.5,
            minAmount: 1.0,
          },
          wxpay: {
            enabled: false,
            name: 'WeChat Pay',
            description: 'WeChat Pay扫码支付',
            feeRate: 2.0,
            minAmount: 0.5,
          },
          paypal: {
            enabled: false,
            name: 'PayPal',
            description: 'PayPal在线支付',
            feeRate: 3.5,
            minAmount: 1.0,
          },
        },
      },
    },
  });

  const nowpaymentsForm = useForm({
    resolver: zodResolver(nowpaymentsConfigSchema),
    defaultValues: {
      provider: 'nowpayments',
      name: 'NOWPayments',
      isEnabled: false,
      settings: {
        apiKey: '',
        apiUrl: 'https://api.nowpayments.io',
        ipnSecret: '',
        sandbox: false,
        feeRate: 1.0,
        minAmount: 5.0,
      },
    },
  });

  // 从数据库加载配置后初始化表单
  useEffect(() => {
    if (configs && configs.length > 0) {
      const yunpayConfig = configs.find(
        (config: any) => config.provider === 'yunpay',
      );
      const nowpaymentsConfig = configs.find(
        (config: any) => config.provider === 'nowpayments',
      );

      if (yunpayConfig) {
        // 确保所有字段都有默认值
        const configWithDefaults = {
          ...yunpayConfig,
          settings: {
            ...yunpayConfig.settings,
            paymentMethods: {
              alipay: {
                enabled: false,
                name: 'Alipay',
                description: 'Alipay扫码支付',
                feeRate: 2.5,
                minAmount: 1.0,
                ...yunpayConfig.settings?.paymentMethods?.alipay,
              },
              wxpay: {
                enabled: false,
                name: 'WeChat Pay',
                description: 'WeChat Pay扫码支付',
                feeRate: 2.0,
                minAmount: 0.5,
                ...yunpayConfig.settings?.paymentMethods?.wxpay,
              },
              paypal: {
                enabled: false,
                name: 'PayPal',
                description: 'PayPal在线支付',
                feeRate: 3.5,
                minAmount: 1.0,
                ...yunpayConfig.settings?.paymentMethods?.paypal,
              },
            },
          },
        };
        yunpayForm.reset(configWithDefaults);
      }

      if (nowpaymentsConfig) {
        // 确保所有字段都有默认值
        const configWithDefaults = {
          ...nowpaymentsConfig,
          settings: {
            apiKey: '',
            apiUrl: 'https://api.nowpayments.io',
            ipnSecret: '',
            sandbox: false,
            feeRate: 1.0,
            minAmount: 5.0,
            ...nowpaymentsConfig.settings,
          },
        };
        nowpaymentsForm.reset(configWithDefaults);
      }
    }
  }, [configs, yunpayForm, nowpaymentsForm]);

  // 检查字符串是否全部为星号
  const isAllStars = (value: string): boolean => {
    return !!(value && value.length > 0 && /^\*+$/.test(value));
  };

  // 处理表单提交，智能处理敏感字段
  const handleYunpaySubmit = (data: any) => {
    // 如果敏感字段全部为星号且用户没有修改，则保持原值让后端处理
    const processedData = { ...data };

    // 如果 key 字段全部为星号，表示用户没有修改，保持原值
    if (processedData.settings.key && isAllStars(processedData.settings.key)) {
      // 保持原始结构，让后端处理
    }

    updateConfigMutation.mutate(processedData);
  };

  const handleNowpaymentsSubmit = (data: any) => {
    // 如果敏感字段全部为星号且用户没有修改，则保持原值让后端处理
    const processedData = { ...data };

    // 如果 apiKey 或 ipnSecret 字段全部为星号，表示用户没有修改，保持原值
    if (
      processedData.settings.apiKey &&
      isAllStars(processedData.settings.apiKey)
    ) {
      // 保持原始结构，让后端处理
    }
    if (
      processedData.settings.ipnSecret &&
      isAllStars(processedData.settings.ipnSecret)
    ) {
      // 保持原始结构，让后端处理
    }

    updateConfigMutation.mutate(processedData);
  };

  const isYunpayEnabled = yunpayForm.watch('isEnabled');
  const isNowpaymentsEnabled = nowpaymentsForm.watch('isEnabled');

  if (isLoading) {
    return (
      <AdminPageLayout
        title='系统设置'
        breadcrumbPage='支付配置'
        href='/admin'
        showBackButton={true}
      >
        <div className='flex items-center justify-center h-96'>
          <div className='flex items-center gap-2'>
            <div className='w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin'></div>
            加载中...
          </div>
        </div>
      </AdminPageLayout>
    );
  }

  return (
    <AdminPageLayout
      title='系统设置'
      breadcrumbPage='支付配置'
      href='/admin'
      showBackButton={true}
    >
      <div className='space-y-8'>
        {/* 页面标题 */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>支付配置</h1>
            <p className='text-muted-foreground mt-2'>
              管理支付提供商和支付方式设置
            </p>
          </div>
          <Settings2 className='h-8 w-8 text-muted-foreground' />
        </div>

        {/* 配置区域 */}
        <div className='grid gap-8 xl:grid-cols-2'>
          {/* 聚合易支付配置 */}
          <Card className='relative'>
            <CardHeader className='pb-4'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <div className='p-2 bg-blue-100 rounded-lg'>
                    <CreditCard className='h-5 w-5 text-blue-600' />
                  </div>
                  <div>
                    <CardTitle className='text-lg'>聚合易支付</CardTitle>
                    <p className='text-sm text-muted-foreground'>
                      传统支付方式集成
                    </p>
                  </div>
                </div>
                <Badge variant={isYunpayEnabled ? 'default' : 'secondary'}>
                  {isYunpayEnabled ? '已启用' : '未启用'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              <Form {...yunpayForm}>
                <form
                  onSubmit={yunpayForm.handleSubmit(handleYunpaySubmit)}
                  className='space-y-6'
                >
                  {/* 基础配置 */}
                  <div className='space-y-4'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h4 className='text-sm font-medium'>启用状态</h4>
                        <p className='text-xs text-muted-foreground'>
                          开启后可使用聚合易支付
                        </p>
                      </div>
                      <FormField
                        control={yunpayForm.control}
                        name='isEnabled'
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='grid gap-4'>
                      <FormField
                        control={yunpayForm.control}
                        name='settings.pid'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>商户ID (PID)</FormLabel>
                            <FormControl>
                              <Input placeholder='请输入商户ID' {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={yunpayForm.control}
                        name='settings.key'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>商户密钥</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='password'
                                  placeholder={
                                    isAllStars(field.value)
                                      ? '已保存(如需修改请重新输入)'
                                      : '请输入商户密钥'
                                  }
                                  {...field}
                                />
                                {isAllStars(field.value) && (
                                  <div className='absolute inset-y-0 right-0 pr-3 flex items-center'>
                                    <div className='text-xs text-green-600 bg-green-50 px-2 py-1 rounded'>
                                      已保存
                                    </div>
                                  </div>
                                )}
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={yunpayForm.control}
                        name='settings.apiUrl'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API地址</FormLabel>
                            <FormControl>
                              <Input
                                placeholder='https://pay.747099.xyz'
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* 支付方式配置 */}
                  <div className='space-y-4'>
                    <div>
                      <h4 className='text-sm font-medium mb-3'>支付方式</h4>
                      <div className='space-y-3'>
                        {/* Alipay */}
                        <div className='p-4 border rounded-lg space-y-3'>
                          <div className='flex items-center justify-between'>
                            <div className='flex items-center gap-3'>
                              <Smartphone className='h-5 w-5 text-blue-600' />
                              <div>
                                <p className='font-medium text-sm'>Alipay</p>
                                <p className='text-xs text-muted-foreground'>
                                  Alipay扫码支付
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={yunpayForm.control}
                              name='settings.paymentMethods.alipay.enabled'
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isYunpayEnabled}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* 手续费和最小金额设置 */}
                          {yunpayForm.watch(
                            'settings.paymentMethods.alipay.enabled',
                          ) && (
                            <div className='pt-2 border-t space-y-4'>
                              <div className='grid grid-cols-2 gap-4'>
                                <FormField
                                  control={yunpayForm.control}
                                  name='settings.paymentMethods.alipay.feeRate'
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>手续费率</FormLabel>
                                      <FormControl>
                                        <div className='flex items-center gap-2'>
                                          <Input
                                            type='number'
                                            placeholder='2.5'
                                            step='0.1'
                                            min='0'
                                            max='100'
                                            {...field}
                                            value={field.value || ''}
                                            onChange={e =>
                                              field.onChange(
                                                parseFloat(e.target.value) || 0,
                                              )
                                            }
                                            disabled={!isYunpayEnabled}
                                            className='w-20'
                                          />
                                          <span className='text-sm text-muted-foreground'>
                                            %
                                          </span>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={yunpayForm.control}
                                  name='settings.paymentMethods.alipay.minAmount'
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>最小支付金额</FormLabel>
                                      <FormControl>
                                        <div className='flex items-center gap-2'>
                                          <Input
                                            type='number'
                                            placeholder='1.0'
                                            step='0.01'
                                            min='0.01'
                                            max='10000'
                                            {...field}
                                            value={field.value || ''}
                                            onChange={e =>
                                              field.onChange(
                                                parseFloat(e.target.value) ||
                                                  0.01,
                                              )
                                            }
                                            disabled={!isYunpayEnabled}
                                            className='w-24'
                                          />
                                          <span className='text-sm text-muted-foreground'>
                                            USD
                                          </span>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>

                        {/* WeChat Pay */}
                        <div className='p-4 border rounded-lg space-y-3'>
                          <div className='flex items-center justify-between'>
                            <div className='flex items-center gap-3'>
                              <Smartphone className='h-5 w-5 text-green-600' />
                              <div>
                                <p className='font-medium text-sm'>
                                  WeChat Pay
                                </p>
                                <p className='text-xs text-muted-foreground'>
                                  WeChat Pay扫码支付
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={yunpayForm.control}
                              name='settings.paymentMethods.wxpay.enabled'
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isYunpayEnabled}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* 手续费和最小金额设置 */}
                          {yunpayForm.watch(
                            'settings.paymentMethods.wxpay.enabled',
                          ) && (
                            <div className='pt-2 border-t space-y-4'>
                              <div className='grid grid-cols-2 gap-4'>
                                <FormField
                                  control={yunpayForm.control}
                                  name='settings.paymentMethods.wxpay.feeRate'
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>手续费率</FormLabel>
                                      <FormControl>
                                        <div className='flex items-center gap-2'>
                                          <Input
                                            type='number'
                                            placeholder='2.0'
                                            step='0.1'
                                            min='0'
                                            max='100'
                                            {...field}
                                            value={field.value || ''}
                                            onChange={e =>
                                              field.onChange(
                                                parseFloat(e.target.value) || 0,
                                              )
                                            }
                                            disabled={!isYunpayEnabled}
                                            className='w-20'
                                          />
                                          <span className='text-sm text-muted-foreground'>
                                            %
                                          </span>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={yunpayForm.control}
                                  name='settings.paymentMethods.wxpay.minAmount'
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>最小支付金额</FormLabel>
                                      <FormControl>
                                        <div className='flex items-center gap-2'>
                                          <Input
                                            type='number'
                                            placeholder='0.5'
                                            step='0.01'
                                            min='0.01'
                                            max='10000'
                                            {...field}
                                            value={field.value || ''}
                                            onChange={e =>
                                              field.onChange(
                                                parseFloat(e.target.value) ||
                                                  0.01,
                                              )
                                            }
                                            disabled={!isYunpayEnabled}
                                            className='w-24'
                                          />
                                          <span className='text-sm text-muted-foreground'>
                                            USD
                                          </span>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>

                        {/* PayPal */}
                        <div className='p-4 border rounded-lg space-y-3'>
                          <div className='flex items-center justify-between'>
                            <div className='flex items-center gap-3'>
                              <CreditCard className='h-5 w-5 text-blue-700' />
                              <div>
                                <p className='font-medium text-sm'>PayPal</p>
                                <p className='text-xs text-muted-foreground'>
                                  PayPal在线支付
                                </p>
                              </div>
                            </div>
                            <FormField
                              control={yunpayForm.control}
                              name='settings.paymentMethods.paypal.enabled'
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                      disabled={!isYunpayEnabled}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* 手续费和最小金额设置 */}
                          {yunpayForm.watch(
                            'settings.paymentMethods.paypal.enabled',
                          ) && (
                            <div className='pt-2 border-t space-y-4'>
                              <div className='grid grid-cols-2 gap-4'>
                                <FormField
                                  control={yunpayForm.control}
                                  name='settings.paymentMethods.paypal.feeRate'
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>手续费率</FormLabel>
                                      <FormControl>
                                        <div className='flex items-center gap-2'>
                                          <Input
                                            type='number'
                                            placeholder='3.5'
                                            step='0.1'
                                            min='0'
                                            max='100'
                                            {...field}
                                            value={field.value || ''}
                                            onChange={e =>
                                              field.onChange(
                                                parseFloat(e.target.value) || 0,
                                              )
                                            }
                                            disabled={!isYunpayEnabled}
                                            className='w-20'
                                          />
                                          <span className='text-sm text-muted-foreground'>
                                            %
                                          </span>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                <FormField
                                  control={yunpayForm.control}
                                  name='settings.paymentMethods.paypal.minAmount'
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>最小支付金额</FormLabel>
                                      <FormControl>
                                        <div className='flex items-center gap-2'>
                                          <Input
                                            type='number'
                                            placeholder='1.0'
                                            step='0.01'
                                            min='0.01'
                                            max='10000'
                                            {...field}
                                            value={field.value || ''}
                                            onChange={e =>
                                              field.onChange(
                                                parseFloat(e.target.value) ||
                                                  0.01,
                                              )
                                            }
                                            disabled={!isYunpayEnabled}
                                            className='w-24'
                                          />
                                          <span className='text-sm text-muted-foreground'>
                                            USD
                                          </span>
                                        </div>
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button
                    type='submit'
                    className='w-full'
                    disabled={updateConfigMutation.isPending}
                  >
                    {updateConfigMutation.isPending
                      ? '保存中...'
                      : '保存聚合易配置'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>

          {/* NOWPayments配置 */}
          <Card className='relative'>
            <CardHeader className='pb-4'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <div className='p-2 bg-orange-100 rounded-lg'>
                    <Bitcoin className='h-5 w-5 text-orange-600' />
                  </div>
                  <div>
                    <CardTitle className='text-lg'>NOWPayments</CardTitle>
                    <p className='text-sm text-muted-foreground'>
                      加密货币支付集成
                    </p>
                  </div>
                </div>
                <Badge variant={isNowpaymentsEnabled ? 'default' : 'secondary'}>
                  {isNowpaymentsEnabled ? '已启用' : '未启用'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              <Form {...nowpaymentsForm}>
                <form
                  onSubmit={nowpaymentsForm.handleSubmit(
                    handleNowpaymentsSubmit,
                  )}
                  className='space-y-6'
                >
                  <div className='space-y-4'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h4 className='text-sm font-medium'>启用状态</h4>
                        <p className='text-xs text-muted-foreground'>
                          开启后可使用加密货币支付
                        </p>
                      </div>
                      <FormField
                        control={nowpaymentsForm.control}
                        name='isEnabled'
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='grid gap-4'>
                      <FormField
                        control={nowpaymentsForm.control}
                        name='settings.apiKey'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API密钥</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='password'
                                  placeholder={
                                    isAllStars(field.value)
                                      ? '已保存(如需修改请重新输入)'
                                      : '请输入API密钥'
                                  }
                                  {...field}
                                />
                                {isAllStars(field.value) && (
                                  <div className='absolute inset-y-0 right-0 pr-3 flex items-center'>
                                    <div className='text-xs text-green-600 bg-green-50 px-2 py-1 rounded'>
                                      已保存
                                    </div>
                                  </div>
                                )}
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={nowpaymentsForm.control}
                        name='settings.apiUrl'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API地址</FormLabel>
                            <FormControl>
                              <Input
                                placeholder='https://api.nowpayments.io'
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={nowpaymentsForm.control}
                        name='settings.sandbox'
                        render={({ field }) => (
                          <FormItem>
                            <div className='flex items-center justify-between'>
                              <div>
                                <FormLabel>沙盒模式</FormLabel>
                                <p className='text-xs text-muted-foreground'>
                                  开启后使用测试环境
                                  (api-sandbox.nowpayments.io)
                                </p>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={!isNowpaymentsEnabled}
                                />
                              </FormControl>
                            </div>
                            {field.value && (
                              <Alert className='mt-2'>
                                <AlertCircle className='h-4 w-4' />
                                <AlertDescription className='text-sm'>
                                  <strong>重要：</strong>
                                  沙盒模式需要使用专门的沙盒API密钥，不能使用生产环境密钥。
                                  请在{' '}
                                  <a
                                    href='https://account-sandbox.nowpayments.io/'
                                    target='_blank'
                                    rel='noopener noreferrer'
                                    className='underline'
                                  >
                                    NOWPayments沙盒后台
                                  </a>{' '}
                                  获取沙盒密钥。
                                </AlertDescription>
                              </Alert>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={nowpaymentsForm.control}
                        name='settings.ipnSecret'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>IPN密钥 (可选)</FormLabel>
                            <FormControl>
                              <div className='relative'>
                                <Input
                                  type='password'
                                  placeholder={
                                    isAllStars(field.value)
                                      ? '已保存(如需修改请重新输入)'
                                      : '用于验证回调签名'
                                  }
                                  {...field}
                                />
                                {isAllStars(field.value) && (
                                  <div className='absolute inset-y-0 right-0 pr-3 flex items-center'>
                                    <div className='text-xs text-green-600 bg-green-50 px-2 py-1 rounded'>
                                      已保存
                                    </div>
                                  </div>
                                )}
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className='grid grid-cols-2 gap-4'>
                        <FormField
                          control={nowpaymentsForm.control}
                          name='settings.feeRate'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>手续费率</FormLabel>
                              <FormControl>
                                <div className='flex items-center gap-2'>
                                  <Input
                                    type='number'
                                    placeholder='1.5'
                                    step='0.1'
                                    min='0'
                                    max='100'
                                    {...field}
                                    value={field.value || ''}
                                    onChange={e =>
                                      field.onChange(
                                        parseFloat(e.target.value) || 0,
                                      )
                                    }
                                    disabled={!isNowpaymentsEnabled}
                                    className='w-20'
                                  />
                                  <span className='text-sm text-muted-foreground'>
                                    %
                                  </span>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={nowpaymentsForm.control}
                          name='settings.minAmount'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>最小支付金额</FormLabel>
                              <FormControl>
                                <div className='flex items-center gap-2'>
                                  <Input
                                    type='number'
                                    placeholder='5.0'
                                    step='0.01'
                                    min='0.01'
                                    max='10000'
                                    {...field}
                                    value={field.value || ''}
                                    onChange={e =>
                                      field.onChange(
                                        parseFloat(e.target.value) || 0.01,
                                      )
                                    }
                                    disabled={!isNowpaymentsEnabled}
                                    className='w-24'
                                  />
                                  <span className='text-sm text-muted-foreground'>
                                    USD
                                  </span>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>

                  <Button
                    type='submit'
                    className='w-full'
                    disabled={updateConfigMutation.isPending}
                  >
                    {updateConfigMutation.isPending
                      ? '保存中...'
                      : '保存NOWPayments配置'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        {/* 配置说明 */}
        <Alert>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>
            <div className='space-y-2'>
              <p className='font-medium'>配置说明：</p>
              <ul className='text-sm space-y-1 ml-4'>
                <li>
                  • <strong>聚合易支付</strong>
                  ：提供Alipay、PayPal等传统支付方式，可单独设置手续费率和最小支付金额
                </li>
                <li>
                  • <strong>NOWPayments</strong>
                  ：支持300+种加密货币，用户跳转到托管页面完成支付
                </li>
                <li>
                  • <strong>手续费设置</strong>
                  ：支持为每种支付方式设置不同的手续费率（0-100%）
                </li>
                <li>
                  • <strong>最小支付金额</strong>
                  ：设置每种支付方式的最小支付金额（0.01-10000
                  USD），低于此金额将无法支付
                </li>
                <li>
                  • <strong>支付方式</strong>
                  ：可单独启用/禁用各种支付方式，前端会动态显示手续费信息
                </li>
                <li>
                  • <strong>安全性</strong>
                  ：所有敏感信息将被加密存储，配置修改会立即生效
                </li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </AdminPageLayout>
  );
}
