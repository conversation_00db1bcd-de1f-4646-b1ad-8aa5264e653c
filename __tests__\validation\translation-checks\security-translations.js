// 测试账号安全页面翻译修复效果
const fs = require('fs');
const path = require('path');

// 读取组件文件
const componentPath = path.join(
  __dirname,
  '../../../src/components/account-security-content.tsx'
);
const componentContent = fs.readFileSync(componentPath, 'utf8');

// 读取翻译文件
const zhTransPath = path.join(
  __dirname,
  '../../../messages/zh/account-security.json'
);
const enTransPath = path.join(
  __dirname,
  '../../../messages/en/account-security.json'
);

const zhTrans = JSON.parse(fs.readFileSync(zhTransPath, 'utf8'));
const enTrans = JSON.parse(fs.readFileSync(enTransPath, 'utf8'));

console.log('=== 账号安全页面翻译修复验证 ===\n');

// 检查修复的问题
const issues = [
  {
    description: '昵称长度提示',
    pattern: /昵称长度应在2-20个字符之间/,
    fixed: /\{t\('nickname\.lengthHint'\)\}/,
    zhKey: 'nickname.lengthHint',
    enKey: 'nickname.lengthHint',
  },
  {
    description: '保存按钮',
    pattern: /保存/,
    fixed: /\{t\('actions\.save'\)\}/,
    zhKey: 'actions.save',
    enKey: 'actions.save',
  },
  {
    description: '已验证状态',
    pattern: /已验证/,
    fixed: /\{t\('security\.emailVerified'\)\}/,
    zhKey: 'security.emailVerified',
    enKey: 'security.emailVerified',
  },
  {
    description: '未验证状态',
    pattern: /未验证/,
    fixed: /\{t\('security\.emailUnverified'\)\}/,
    zhKey: 'security.emailUnverified',
    enKey: 'security.emailUnverified',
  },
  {
    description: '取消按钮',
    pattern: /取消/,
    fixed: /\{t\('actions\.cancel'\)\}/,
    zhKey: 'actions.cancel',
    enKey: 'actions.cancel',
  },
  {
    description: '下一步按钮',
    pattern: /下一步/,
    fixed: /\{t\('actions\.next'\)\}/,
    zhKey: 'actions.next',
    enKey: 'actions.next',
  },
  {
    description: '返回上一步按钮',
    pattern: />\s*返回上一步\s*</,
    fixed: /\{t\('actions\.back'\)\}/,
    zhKey: 'actions.back',
    enKey: 'actions.back',
  },
  {
    description: '确认更换按钮',
    pattern: /确认更换/,
    fixed: /\{t\('actions\.confirmChange'\)\}/,
    zhKey: 'actions.confirmChange',
    enKey: 'actions.confirmChange',
  },
  {
    description: '确认修改按钮',
    pattern: /确认修改/,
    fixed: /\{t\('actions\.confirm'\)\}/,
    zhKey: 'actions.confirm',
    enKey: 'actions.confirm',
  },
];

let allFixed = true;

issues.forEach((issue, index) => {
  console.log(`${index + 1}. ${issue.description}:`);

  // 检查是否还有硬编码的中文
  const hasHardcoded = issue.pattern.test(componentContent);

  // 检查是否使用了翻译函数
  const hasTranslation = issue.fixed.test(componentContent);

  // 检查翻译文件中是否有对应的键
  const zhValue = getNestedValue(zhTrans, issue.zhKey);
  const enValue = getNestedValue(enTrans, issue.enKey);

  if (hasHardcoded) {
    console.log('   ❌ 仍有硬编码中文文本');
    allFixed = false;
  } else if (!hasTranslation) {
    console.log('   ❌ 未使用翻译函数');
    allFixed = false;
  } else if (!zhValue || !enValue) {
    console.log('   ❌ 翻译文件中缺少对应键值');
    console.log(`      中文: ${zhValue || '缺失'}`);
    console.log(`      英文: ${enValue || '缺失'}`);
    allFixed = false;
  } else {
    console.log('   ✅ 已修复');
    console.log(`      中文: ${zhValue}`);
    console.log(`      英文: ${enValue}`);
  }
  console.log('');
});

// 辅助函数：获取嵌套对象的值
function getNestedValue(obj, key) {
  return key.split('.').reduce((o, k) => o && o[k], obj);
}

console.log('=== 修复结果总结 ===');
if (allFixed) {
  console.log('✅ 所有翻译问题已修复！');
  console.log('现在在英文环境下，所有对话框按钮和状态文本都应该显示为英文。');
} else {
  console.log('❌ 仍有问题需要修复');
}

console.log('\n=== 测试建议 ===');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 访问: http://localhost:3001/en');
console.log('3. 登录后访问账号安全页面');
console.log('4. 测试各个对话框的按钮和文本显示');
console.log('5. 切换语言验证翻译效果');
