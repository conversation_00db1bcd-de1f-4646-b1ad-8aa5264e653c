'use client';

import { Al<PERSON><PERSON>riangle, RefreshCw, Home } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Global error boundary component
 * Catches and displays errors in the application
 */
export default function Error({ error, reset }: ErrorProps) {
  React.useEffect(() => {
    // Log the error to the console or an error monitoring service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center'>
        <div className='mb-6'>
          <AlertTriangle className='mx-auto h-16 w-16 text-red-500' />
        </div>

        <h1 className='mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100'>
          Something went wrong
        </h1>

        <p className='mb-6 text-gray-600 dark:text-gray-400'>
          Sorry, the application encountered an unexpected error. Please try
          refreshing the page or returning to the homepage.
        </p>

        {/* Display error details in development environment */}
        {process.env.NODE_ENV === 'development' && (
          <div className='mb-6 rounded-lg bg-red-50 p-4 text-left dark:bg-red-900/20'>
            <h3 className='mb-2 font-semibold text-red-800 dark:text-red-200'>
              Error Details:
            </h3>
            <pre className='text-xs text-red-700 dark:text-red-300 overflow-auto'>
              {error.message}
            </pre>
            {error.digest && (
              <p className='mt-2 text-xs text-red-600 dark:text-red-400'>
                Error ID: {error.digest}
              </p>
            )}
          </div>
        )}

        <div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
          <Button
            onClick={reset}
            variant='default'
            className='flex items-center gap-2'
          >
            <RefreshCw className='h-4 w-4' />
            Retry
          </Button>

          <Button
            onClick={() => (window.location.href = '/')}
            variant='outline'
            className='flex items-center gap-2'
          >
            <Home className='h-4 w-4' />
            Go to Homepage
          </Button>
        </div>
      </div>
    </div>
  );
}
