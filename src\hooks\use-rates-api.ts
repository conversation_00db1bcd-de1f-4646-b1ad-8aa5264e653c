import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  Platform,
  Category,
  ChargebackType,
  PaymentMethod,
  SystemRate,
  CreatePlatformRequest,
  UpdatePlatformRequest,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateChargebackTypeRequest,
  UpdateChargebackTypeRequest,
  CreatePaymentMethodRequest,
  UpdatePaymentMethodRequest,
  UpdateSystemRateRequest,
  ApiResponse,
} from '@/types/rates';

// Platform hooks
export function usePlatforms() {
  return useQuery({
    queryKey: ['platforms'],
    queryFn: async (): Promise<Platform[]> => {
      const response = await fetch('/api/admin/platforms');
      const data: ApiResponse<Platform[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取平台列表失败');
      }

      return data.data || [];
    },
  });
}

export function useCreatePlatform() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreatePlatformRequest): Promise<Platform> => {
      const response = await fetch('/api/admin/platforms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<Platform> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建平台失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['platforms'] });
      toast.success('平台创建成功');
    },
    onError: error => {
      toast.error(error.message || '创建平台失败');
    },
  });
}

export function useUpdatePlatform() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdatePlatformRequest;
    }): Promise<Platform> => {
      const response = await fetch(`/api/admin/platforms/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<Platform> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新平台失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['platforms'] });
      toast.success('平台更新成功');
    },
    onError: error => {
      toast.error(error.message || '更新平台失败');
    },
  });
}

export function useDeletePlatform() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/admin/platforms/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '删除平台失败');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['platforms'] });
      toast.success('平台删除成功');
    },
    onError: error => {
      toast.error(error.message || '删除平台失败');
    },
  });
}

// Category hooks
export function useCategories() {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async (): Promise<Category[]> => {
      const response = await fetch('/api/admin/categories');
      const data: ApiResponse<Category[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取分类列表失败');
      }

      return data.data || [];
    },
  });
}

export function useCreateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateCategoryRequest): Promise<Category> => {
      const response = await fetch('/api/admin/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<Category> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建分类失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('分类创建成功');
    },
    onError: error => {
      toast.error(error.message || '创建分类失败');
    },
  });
}

export function useUpdateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateCategoryRequest;
    }): Promise<Category> => {
      const response = await fetch(`/api/admin/categories/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<Category> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新分类失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('分类更新成功');
    },
    onError: error => {
      toast.error(error.message || '更新分类失败');
    },
  });
}

export function useDeleteCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/admin/categories/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '删除分类失败');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['categories'] });
      toast.success('分类删除成功');
    },
    onError: error => {
      toast.error(error.message || '删除分类失败');
    },
  });
}

// ChargebackType hooks
export function useChargebackTypes() {
  return useQuery({
    queryKey: ['chargeback-types'],
    queryFn: async (): Promise<ChargebackType[]> => {
      const response = await fetch('/api/admin/chargeback-types');
      const data: ApiResponse<ChargebackType[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取拒付类型列表失败');
      }

      return data.data || [];
    },
  });
}

export function useCreateChargebackType() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateChargebackTypeRequest,
    ): Promise<ChargebackType> => {
      const response = await fetch('/api/admin/chargeback-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<ChargebackType> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建拒付类型失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chargeback-types'] });
      toast.success('拒付类型创建成功');
    },
    onError: error => {
      toast.error(error.message || '创建拒付类型失败');
    },
  });
}

export function useUpdateChargebackType() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateChargebackTypeRequest;
    }): Promise<ChargebackType> => {
      const response = await fetch(`/api/admin/chargeback-types/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<ChargebackType> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新拒付类型失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chargeback-types'] });
      toast.success('拒付类型更新成功');
    },
    onError: error => {
      toast.error(error.message || '更新拒付类型失败');
    },
  });
}

export function useDeleteChargebackType() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/admin/chargeback-types/${id}`, {
        method: 'DELETE',
      });

      const result: ApiResponse<void> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '删除拒付类型失败');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chargeback-types'] });
      toast.success('拒付类型删除成功');
    },
    onError: error => {
      toast.error(error.message || '删除拒付类型失败');
    },
  });
}

// PaymentMethod hooks
export function usePaymentMethods() {
  return useQuery({
    queryKey: ['payment-methods'],
    queryFn: async (): Promise<PaymentMethod[]> => {
      const response = await fetch('/api/admin/payment-methods');
      const data: ApiResponse<PaymentMethod[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取支付方式列表失败');
      }

      return data.data || [];
    },
  });
}

export function useCreatePaymentMethod() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreatePaymentMethodRequest,
    ): Promise<PaymentMethod> => {
      const response = await fetch('/api/admin/payment-methods', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<PaymentMethod> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建支付方式失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-methods'] });
      toast.success('支付方式创建成功');
    },
    onError: error => {
      toast.error(error.message || '创建支付方式失败');
    },
  });
}

export function useUpdatePaymentMethod() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdatePaymentMethodRequest;
    }): Promise<PaymentMethod> => {
      const response = await fetch(`/api/admin/payment-methods/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<PaymentMethod> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新支付方式失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-methods'] });
      toast.success('支付方式更新成功');
    },
    onError: error => {
      toast.error(error.message || '更新支付方式失败');
    },
  });
}

export function useDeletePaymentMethod() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/admin/payment-methods/${id}`, {
        method: 'DELETE',
      });

      const result: ApiResponse<void> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '删除支付方式失败');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-methods'] });
      toast.success('支付方式删除成功');
    },
    onError: error => {
      toast.error(error.message || '删除支付方式失败');
    },
  });
}

// System Rate hooks
export function useSystemRate() {
  return useQuery({
    queryKey: ['system-rate'],
    queryFn: async (): Promise<SystemRate> => {
      const response = await fetch('/api/admin/system-rates');
      const data: ApiResponse<SystemRate> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取系统费率配置失败');
      }

      return data.data!;
    },
  });
}

// 用户级别的系统费率查询（只读）
export function useUserSystemRate() {
  return useQuery({
    queryKey: ['user-system-rate'],
    queryFn: async (): Promise<SystemRate> => {
      const response = await fetch('/api/user/system-rates');
      const data: ApiResponse<SystemRate> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取系统费率配置失败');
      }

      return data.data!;
    },
  });
}

export function useUpdateSystemRate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateSystemRateRequest): Promise<SystemRate> => {
      const response = await fetch('/api/admin/system-rates', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<SystemRate> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新系统费率配置失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-rate'] });
      toast.success('系统费率配置更新成功');
    },
    onError: error => {
      toast.error(error.message || '更新系统费率配置失败');
    },
  });
}
