import { NextRequest, NextResponse } from 'next/server';

import { LocationData } from '@/types/crisp';

// 强制动态渲染，因为此路由使用了 request.headers
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取用户 IP 地址
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ip = forwarded?.split(',')[0] || realIp;

    if (!ip) {
      return NextResponse.json(
        { error: '无法获取客户端IP地址' },
        { status: 400 },
      );
    }

    // 使用免费的 IP 地理位置服务
    const response = await fetch(
      `http://ip-api.com/json/${ip}?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,currency,query,as,asname,org,isp,mobile,proxy,hosting`,
      {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; RefundGo/1.0)',
        },
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.status === 'fail') {
      // 对于保留IP地址范围（本地IP）返回基础数据而不是错误
      if (data.message === 'reserved range') {
        const reservedIpData: LocationData = {
          ip: data.query,
          city: undefined,
          country: undefined,
          timezone: 'UTC',
        };
        return NextResponse.json(reservedIpData);
      }
      throw new Error(data.message || 'IP 地理位置查询失败');
    }

    // 转换为我们的 LocationData 格式
    const locationData: LocationData = {
      ip: data.query,
      city: data.city,
      region: data.regionName,
      country: data.country,
      countryCode: data.countryCode,
      latitude: data.lat,
      longitude: data.lon,
      timezone: data.timezone,
      currency: data.currency,
      postal: data.zip,
      calling_code: getCallingCode(data.countryCode),
      emoji_flag: getCountryFlag(data.countryCode),
      company: {
        name: data.isp,
        domain: data.org,
        type: data.mobile
          ? 'Mobile'
          : data.proxy
            ? 'Proxy'
            : data.hosting
              ? 'Hosting'
              : 'Broadband',
      },
      asn: {
        asn: data.as?.split(' ')[0],
        name: data.asname,
      },
      threat: {
        is_proxy: data.proxy,
        is_datacenter: data.hosting,
        is_anonymous: data.proxy || data.hosting,
        scores: {
          trust_score: data.proxy || data.hosting ? 30 : 85,
        },
      },
      time_zone: {
        name: data.timezone,
        current_time: new Date().toLocaleString('zh-CN', {
          timeZone: data.timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        }),
      },
    };

    return NextResponse.json(locationData);
  } catch (error) {
    console.error('获取 IP 地理位置失败:', error);

    // 返回错误响应，不提供模拟数据
    return NextResponse.json(
      { error: '获取IP地理位置信息失败' },
      { status: 500 },
    );
  }
}

// 获取国家电话区号
function getCallingCode(countryCode: string): string {
  const callingCodes: { [key: string]: string } = {
    CN: '+86',
    US: '+1',
    GB: '+44',
    JP: '+81',
    KR: '+82',
    DE: '+49',
    FR: '+33',
    IT: '+39',
    ES: '+34',
    CA: '+1',
    AU: '+61',
    IN: '+91',
    BR: '+55',
    RU: '+7',
    MX: '+52',
    AR: '+54',
    CL: '+56',
    CO: '+57',
    PE: '+51',
    VE: '+58',
    TH: '+66',
    VN: '+84',
    ID: '+62',
    MY: '+60',
    SG: '+65',
    PH: '+63',
    TW: '+886',
    HK: '+852',
    MO: '+853',
    AE: '+971',
    SA: '+966',
    TR: '+90',
    EG: '+20',
    ZA: '+27',
    NG: '+234',
    KE: '+254',
    GH: '+233',
    MA: '+212',
    DZ: '+213',
    TN: '+216',
    LY: '+218',
    SD: '+249',
    ET: '+251',
    UG: '+256',
    TZ: '+255',
    RW: '+250',
    BF: '+226',
    ML: '+223',
    NE: '+227',
    TD: '+235',
    CF: '+236',
    CM: '+237',
    GA: '+241',
    CG: '+242',
    CD: '+243',
    AO: '+244',
    ZM: '+260',
    ZW: '+263',
    BW: '+267',
    SZ: '+268',
    LS: '+266',
    MZ: '+258',
    MG: '+261',
    MU: '+230',
    SC: '+248',
    KM: '+269',
    DJ: '+253',
    SO: '+252',
    ER: '+291',
    SS: '+211',
    LR: '+231',
    SL: '+232',
    GN: '+224',
    GW: '+245',
    CV: '+238',
    ST: '+239',
    GQ: '+240',
    NL: '+31',
    BE: '+32',
    CH: '+41',
    AT: '+43',
    SE: '+46',
    NO: '+47',
    DK: '+45',
    FI: '+358',
    IS: '+354',
    IE: '+353',
    PT: '+351',
    GR: '+30',
    CY: '+357',
    MT: '+356',
    LU: '+352',
    MC: '+377',
    SM: '+378',
    VA: '+379',
    AD: '+376',
    LI: '+423',
    PL: '+48',
    CZ: '+420',
    SK: '+421',
    HU: '+36',
    SI: '+386',
    HR: '+385',
    BA: '+387',
    RS: '+381',
    ME: '+382',
    MK: '+389',
    AL: '+355',
    BG: '+359',
    RO: '+40',
    MD: '+373',
    UA: '+380',
    BY: '+375',
    LT: '+370',
    LV: '+371',
    EE: '+372',
    GE: '+995',
    AM: '+374',
    AZ: '+994',
    KZ: '+7',
    KG: '+996',
    TJ: '+992',
    TM: '+993',
    UZ: '+998',
    AF: '+93',
    PK: '+92',
    BD: '+880',
    LK: '+94',
    MV: '+960',
    NP: '+977',
    BT: '+975',
    MM: '+95',
    LA: '+856',
    KH: '+855',
    BN: '+673',
    TL: '+670',
    MN: '+976',
    KP: '+850',
    FJ: '+679',
    PG: '+675',
    SB: '+677',
    VU: '+678',
    NC: '+687',
    PF: '+689',
    WF: '+681',
    CK: '+682',
    NU: '+683',
    TO: '+676',
    WS: '+685',
    KI: '+686',
    TV: '+688',
    NR: '+674',
    PW: '+680',
    MH: '+692',
    FM: '+691',
    GU: '+1',
    MP: '+1',
    AS: '+1',
    UM: '+1',
    VI: '+1',
    PR: '+1',
    JM: '+1',
    TT: '+1',
    BB: '+1',
    GD: '+1',
    VC: '+1',
    LC: '+1',
    DM: '+1',
    AG: '+1',
    KN: '+1',
    MS: '+1',
    AI: '+1',
    VG: '+1',
    TC: '+1',
    BS: '+1',
    CU: '+53',
    HT: '+509',
    DO: '+1',
    CR: '+506',
    PA: '+507',
    BZ: '+501',
    GT: '+502',
    HN: '+504',
    SV: '+503',
    NI: '+505',
    UY: '+598',
    PY: '+595',
    BO: '+591',
    EC: '+593',
    GY: '+592',
    SR: '+597',
    GF: '+594',
    FK: '+500',
    GS: '+500',
    SH: '+290',
    PM: '+508',
    GL: '+299',
    FO: '+298',
    SJ: '+47',
    AX: '+358',
    IO: '+246',
    CC: '+61',
    CX: '+61',
    HM: '+672',
    NF: '+672',
    AQ: '+672',
    BV: '+47',
    TF: '+262',
    YT: '+262',
    RE: '+262',
    MQ: '+596',
    GP: '+590',
    BL: '+590',
    MF: '+590',
    GI: '+350',
    JE: '+44',
    GG: '+44',
    IM: '+44',
    PS: '+970',
    EH: '+212',
    IR: '+98',
    IQ: '+964',
    SY: '+963',
    LB: '+961',
    JO: '+962',
    IL: '+972',
    KW: '+965',
    BH: '+973',
    QA: '+974',
    OM: '+968',
    YE: '+967',
  };

  return callingCodes[countryCode] || '+1';
}

// 获取国家旗帜表情符号
function getCountryFlag(countryCode: string): string {
  const flags: { [key: string]: string } = {
    CN: '🇨🇳',
    US: '🇺🇸',
    GB: '🇬🇧',
    JP: '🇯🇵',
    KR: '🇰🇷',
    DE: '🇩🇪',
    FR: '🇫🇷',
    IT: '🇮🇹',
    ES: '🇪🇸',
    CA: '🇨🇦',
    AU: '🇦🇺',
    IN: '🇮🇳',
    BR: '🇧🇷',
    RU: '🇷🇺',
    MX: '🇲🇽',
    AR: '🇦🇷',
    CL: '🇨🇱',
    CO: '🇨🇴',
    PE: '🇵🇪',
    VE: '🇻🇪',
    TH: '🇹🇭',
    VN: '🇻🇳',
    ID: '🇮🇩',
    MY: '🇲🇾',
    SG: '🇸🇬',
    PH: '🇵🇭',
    TW: '🇹🇼',
    HK: '🇭🇰',
    MO: '🇲🇴',
    AE: '🇦🇪',
    SA: '🇸🇦',
    TR: '🇹🇷',
    EG: '🇪🇬',
    ZA: '🇿🇦',
    NG: '🇳🇬',
    KE: '🇰🇪',
    GH: '🇬🇭',
    MA: '🇲🇦',
    DZ: '🇩🇿',
    TN: '🇹🇳',
    LY: '🇱🇾',
    SD: '🇸🇩',
    ET: '🇪🇹',
    UG: '🇺🇬',
    TZ: '🇹🇿',
    RW: '🇷🇼',
    BF: '🇧🇫',
    ML: '🇲🇱',
    NE: '🇳🇪',
    TD: '🇹🇩',
    CF: '🇨🇫',
    CM: '🇨🇲',
    GA: '🇬🇦',
    CG: '🇨🇬',
    CD: '🇨🇩',
    AO: '🇦🇴',
    ZM: '🇿🇲',
    ZW: '🇿🇼',
    BW: '🇧🇼',
    SZ: '🇸🇿',
    LS: '🇱🇸',
    MZ: '🇲🇿',
    MG: '🇲🇬',
    MU: '🇲🇺',
    SC: '🇸🇨',
    KM: '🇰🇲',
    DJ: '🇩🇯',
    SO: '🇸🇴',
    ER: '🇪🇷',
    SS: '🇸🇸',
    LR: '🇱🇷',
    SL: '🇸🇱',
    GN: '🇬🇳',
    GW: '🇬🇼',
    CV: '🇨🇻',
    ST: '🇸🇹',
    GQ: '🇬🇶',
    NL: '🇳🇱',
    BE: '🇧🇪',
    CH: '🇨🇭',
    AT: '🇦🇹',
    SE: '🇸🇪',
    NO: '🇳🇴',
    DK: '🇩🇰',
    FI: '🇫🇮',
    IS: '🇮🇸',
    IE: '🇮🇪',
    PT: '🇵🇹',
    GR: '🇬🇷',
    CY: '🇨🇾',
    MT: '🇲🇹',
    LU: '🇱🇺',
    MC: '🇲🇨',
    SM: '🇸🇲',
    VA: '🇻🇦',
    AD: '🇦🇩',
    LI: '🇱🇮',
    PL: '🇵🇱',
    CZ: '🇨🇿',
    SK: '🇸🇰',
    HU: '🇭🇺',
    SI: '🇸🇮',
    HR: '🇭🇷',
    BA: '🇧🇦',
    RS: '🇷🇸',
    ME: '🇲🇪',
    MK: '🇲🇰',
    AL: '🇦🇱',
    BG: '🇧🇬',
    RO: '🇷🇴',
    MD: '🇲🇩',
    UA: '🇺🇦',
    BY: '🇧🇾',
    LT: '🇱🇹',
    LV: '🇱🇻',
    EE: '🇪🇪',
    GE: '🇬🇪',
    AM: '🇦🇲',
    AZ: '🇦🇿',
    KZ: '🇰🇿',
    KG: '🇰🇬',
    TJ: '🇹🇯',
    TM: '🇹🇲',
    UZ: '🇺🇿',
    AF: '🇦🇫',
    PK: '🇵🇰',
    BD: '🇧🇩',
    LK: '🇱🇰',
    MV: '🇲🇻',
    NP: '🇳🇵',
    BT: '🇧🇹',
    MM: '🇲🇲',
    LA: '🇱🇦',
    KH: '🇰🇭',
    BN: '🇧🇳',
    TL: '🇹🇱',
    MN: '🇲🇳',
    KP: '🇰🇵',
    FJ: '🇫🇯',
    PG: '🇵🇬',
    SB: '🇸🇧',
    VU: '🇻🇺',
    NC: '🇳🇨',
    PF: '🇵🇫',
    WF: '🇼🇫',
    CK: '🇨🇰',
    NU: '🇳🇺',
    TO: '🇹🇴',
    WS: '🇼🇸',
    KI: '🇰🇮',
    TV: '🇹🇻',
    NR: '🇳🇷',
    PW: '🇵🇼',
    MH: '🇲🇭',
    FM: '🇫🇲',
    GU: '🇬🇺',
    MP: '🇲🇵',
    AS: '🇦🇸',
    UM: '🇺🇲',
    VI: '🇻🇮',
    PR: '🇵🇷',
    JM: '🇯🇲',
    TT: '🇹🇹',
    BB: '🇧🇧',
    GD: '🇬🇩',
    VC: '🇻🇨',
    LC: '🇱🇨',
    DM: '🇩🇲',
    AG: '🇦🇬',
    KN: '🇰🇳',
    MS: '🇲🇸',
    AI: '🇦🇮',
    VG: '🇻🇬',
    TC: '🇹🇨',
    BS: '🇧🇸',
    CU: '🇨🇺',
    HT: '🇭🇹',
    DO: '🇩🇴',
    CR: '🇨🇷',
    PA: '🇵🇦',
    BZ: '🇧🇿',
    GT: '🇬🇹',
    HN: '🇭🇳',
    SV: '🇸🇻',
    NI: '🇳🇮',
    UY: '🇺🇾',
    PY: '🇵🇾',
    BO: '🇧🇴',
    EC: '🇪🇨',
    GY: '🇬🇾',
    SR: '🇸🇷',
    GF: '🇬🇫',
    FK: '🇫🇰',
    GS: '🇬🇸',
    SH: '🇸🇭',
    PM: '🇵🇲',
    GL: '🇬🇱',
    FO: '🇫🇴',
    SJ: '🇸🇯',
    AX: '🇦🇽',
    IO: '🇮🇴',
    CC: '🇨🇨',
    CX: '🇨🇽',
    HM: '🇭🇲',
    NF: '🇳🇫',
    AQ: '🇦🇶',
    BV: '🇧🇻',
    TF: '🇹🇫',
    YT: '🇾🇹',
    RE: '🇷🇪',
    MQ: '🇲🇶',
    GP: '🇬🇵',
    BL: '🇧🇱',
    MF: '🇲🇫',
    GI: '🇬🇮',
    JE: '🇯🇪',
    GG: '🇬🇬',
    IM: '🇮🇲',
    PS: '🇵🇸',
    EH: '🇪🇭',
    IR: '🇮🇷',
    IQ: '🇮🇶',
    SY: '🇸🇾',
    LB: '🇱🇧',
    JO: '🇯🇴',
    IL: '🇮🇱',
    KW: '🇰🇼',
    BH: '🇧🇭',
    QA: '🇶🇦',
    OM: '🇴🇲',
    YE: '🇾🇪',
  };

  return flags[countryCode] || '🌍';
}
