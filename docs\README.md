# RefundGo Documentation

Welcome to the comprehensive documentation for RefundGo, a multilingual task publishing and completion platform built with Next.js 14+ and modern React patterns.

## 📑 Table of Contents

- [📚 Documentation Structure](#-documentation-structure)
  - [🚀 Getting Started](#-getting-started)
  - [🏗️ Architecture & Design](#️-architecture--design)
  - [🔧 Development](#-development)
  - [🌍 Features & Integrations](#-features--integrations)
  - [🎨 UI Components & Design](#-ui-components--design)
  - [🚀 Deployment & Operations](#-deployment--operations)
  - [📋 Implementation History](#-implementation-history)
  - [🔬 Detailed Implementation](#-detailed-implementation)
  - [🧪 Testing & Quality](#-testing--quality)
  - [📚 Additional Resources](#-additional-resources)
- [🔍 Quick Navigation](#-quick-navigation)
- [🛠️ Key Technologies](#️-key-technologies)
- [📖 Documentation Standards](#-documentation-standards)
- [🔗 External Resources](#-external-resources)
- [📝 Contributing to Documentation](#-contributing-to-documentation)

## 📚 Documentation Structure

### 🚀 Getting Started

Essential documentation for new developers joining the RefundGo project.

- **[Project Overview](project-context.md)** - Complete application overview including purpose, features, tech stack, and architecture
- **[Development Environment](getting-started/vscode-setup.md)** - VSCode configuration, extensions, and development tools setup
- **[Code Quality & Standards](getting-started/eslint-configuration.md)** - ESLint rules, Prettier configuration, and coding standards

### 🏗️ Architecture & Design

System design documentation covering technical architecture, data models, and development patterns.

- **[System Architecture](architecture/system-architecture.md)** - Complete technical architecture, technology stack, and design patterns
- **[Database Schema](project-context.md#database-schema-prisma)** - Prisma data models, relationships, and database design
- **[API Reference](project-context.md#api-endpoints)** - REST API endpoints, authentication, and integration patterns
- **[User Roles & Permissions](architecture/user-roles.md)** - User access control, role definitions, and permission system
- **[Feature Modules](architecture/feature-modules.md)** - Modular architecture approach and feature organization

### 🔧 Development

Core development guides covering setup, patterns, and development workflows.

- **[Development Guide](development-guide.md)** - Complete development setup, coding patterns, and best practices
- **[User Interaction Guide](user-interaction-guide.md)** - UX patterns, user flows, and interaction design guidelines
- **[AI Assistant Guide](ai-assistant-guide.md)** - AI integration patterns, usage guidelines, and implementation details

### 🌍 Features & Integrations

Feature-specific documentation covering major platform capabilities and third-party integrations.

- **[Internationalization](features/internationalization.md)** - Complete i18n implementation with next-intl, language detection, and translation management
- **[Email System](features/email-system.md)** - Multilingual email templates, automated notifications, and delivery tracking
- **[Payment Processing](features/currency-conversion.md)** - Multi-provider payment system, currency conversion, and transaction handling
- **[Authentication System](project-context.md#authentication)** - NextAuth v5 implementation, OAuth providers, and session management
- **[Scheduled Tasks](features/cron-job-setup.md)** - Cron job configuration, automated processes, and task scheduling
- **[Translation Tools](features/i18n-ally-setup-guide.md)** - i18n Ally setup, translation workflows, and localization tools
- **[YunPay Integration](features/yunpay-php-sdk-analysis.md)** - Chinese payment provider integration, SDK analysis, and implementation guide

### 🎨 UI Components & Design

User interface documentation covering design system, components, and styling implementations.

- **[Component Library](ui-components/component-library.md)** - Complete design system with shadcn/ui, component patterns, and usage guidelines
- **[Responsive Design](ui-components/responsive-design.md)** - Mobile-first responsive implementation, breakpoints, and layout patterns
- **[Dark Mode & Theming](ui-components/DARK_MODE_TEXT_VISIBILITY_FIX.md)** - Dark mode implementation, theme switching, and text visibility fixes
- **[Navigation Systems](ui-components/NAVBAR_OVERFLOW_FIX.md)** - Navigation component fixes, overflow handling, and mobile optimization
- **[Footer Integration](ui-components/FOOTER_THEME_TOGGLE_FIX.md)** - Footer component theming, theme toggle integration, and styling
- **[Layout Standards](ui-components/layout-width-consistency-fix.md)** - Container width standardization and layout consistency fixes
- **[Card Components](ui-components/MEMBERSHIP_CARD_BORDER_RADIUS_FIX.md)** - Membership card styling, border radius fixes, and visual improvements

### 🚀 Deployment & Operations

Production deployment, environment configuration, and operational procedures.

- **[Production Deployment](project-context.md#deployment)** - Build process, deployment pipeline, and production environment setup
- **[Environment Configuration](project-context.md#environment-variables)** - Environment variables, secrets management, and configuration setup
- **[Monitoring & Operations](project-context.md#monitoring-and-operations)** - Error tracking, performance monitoring, and operational procedures
- **[Deployment Guides](deployment/)** - Detailed deployment documentation and infrastructure guides (coming soon)

### 📋 Implementation History

Historical record of major implementations, bug fixes, and system improvements.

- **[Recent Fixes Summary](changelog/recent-fixes.md)** - Consolidated overview of recent bug fixes, UI improvements, and feature enhancements
- **[Logo System Overhaul](changelog/LOGO_FIXES_SUMMARY.md)** - Complete logo system implementation with VIP integration and theme support
- **[Footer Redesign](changelog/footer-final-summary.md)** - Footer simplification, content reduction, and layout improvements
- **[CSS Architecture Refactoring](changelog/css-refactoring-summary.md)** - Performance optimization with 91% CSS reduction and Tailwind migration
- **[Email System Enhancement](changelog/email-system-implementation-summary.md)** - Multilingual email system with automated notifications and language detection
- **[Homepage Redesign](changelog/homepage-redesign-summary.md)** - Homepage layout updates, styling improvements, and responsive design
- **[Authentication UI Redesign](changelog/login-redesign-completion-report.md)** - Login system UI improvements and user experience enhancements
- **[Payment Integration Fixes](changelog/yunpay-amount-mismatch-fix.md)** - YunPay integration bug fixes and currency handling improvements
- **[Homepage Logo Implementation](changelog/HOMEPAGE_LOGO_UPDATE.md)** - Homepage-specific logo implementation and integration
- **[Logo Component Development](changelog/LOGO_IMPLEMENTATION.md)** - Core logo component architecture and development process

### 🔬 Detailed Implementation

In-depth technical implementation guides for complex features and systems.

- **[Comprehensive Responsive Tabs](implementation/COMPREHENSIVE_RESPONSIVE_TABS_IMPLEMENTATION.md)** - Complete responsive tab system with mobile-first design and progressive enhancement
- **[i18n Responsive Tab Restoration](implementation/I18N_RESPONSIVE_TABS_RESTORATION.md)** - Internationalization integration for responsive tab components
- **[Task-Specific Tab Implementation](implementation/RESPONSIVE_TASK_TABS_IMPLEMENTATION.md)** - Task management responsive tabs with filtering and state management

### 🧪 Testing & Quality

Testing strategies, quality assurance procedures, and validation approaches.

- **[Testing Guide](testing-guide.md)** - Comprehensive testing strategies including unit, integration, and e2e testing
- **[Translation Testing](testing/test-translation.md)** - i18n testing approaches, validation methods, and quality assurance

### 📚 Additional Resources

Supplementary documentation and analysis reports.

- **[User Console Layout Analysis](user-console-layout-analysis.md)** - Console interface analysis and layout patterns (Chinese language document)
- **[Documentation Reorganization](REORGANIZATION_SUMMARY.md)** - Complete summary of documentation restructuring process and improvements

## 🔍 Quick Navigation

### For New Developers

**Complete onboarding path for developers new to the RefundGo project:**

1. Start with [Project Overview](project-context.md) to understand the application architecture and features
2. Follow [Development Guide](development-guide.md) for complete development setup and patterns
3. Configure [Development Environment](getting-started/vscode-setup.md) with VSCode and essential tools
4. Review [Code Quality Standards](getting-started/eslint-configuration.md) for coding conventions
5. Study [Testing Guide](testing-guide.md) for quality assurance practices

### For Feature Development

**Architecture and implementation guidance for building new features:**

1. Study [System Architecture](architecture/system-architecture.md) for overall technical design and patterns
2. Review [Feature Modules](architecture/feature-modules.md) for modular architecture approach
3. Understand [User Roles & Permissions](architecture/user-roles.md) for access control implementation
4. Reference [API Endpoints](project-context.md#api-endpoints) for backend integration patterns
5. Follow [Development Guide](development-guide.md) for implementation best practices

### For UI/UX Work

**Design system and user interface development resources:**

1. Master [Component Library](ui-components/component-library.md) for design system and component patterns
2. Implement [Responsive Design](ui-components/responsive-design.md) following mobile-first principles
3. Apply [Dark Mode & Theming](ui-components/DARK_MODE_TEXT_VISIBILITY_FIX.md) for consistent theming
4. Follow [User Interaction Guide](user-interaction-guide.md) for UX patterns and user flows
5. Reference [Layout Standards](ui-components/layout-width-consistency-fix.md) for consistent layouts

### For Internationalization Work

**Complete i18n implementation and translation management:**

1. Study [Internationalization Guide](features/internationalization.md) for comprehensive i18n implementation
2. Setup [Translation Tools](features/i18n-ally-setup-guide.md) for efficient translation management
3. Implement [Email System](features/email-system.md) multilingual templates and language detection
4. Follow [Translation Testing](testing/test-translation.md) for quality assurance and validation

### For Deployment & Operations

**Production deployment and operational procedures:**

1. Configure [Environment Variables](project-context.md#environment-variables) for different environments
2. Follow [Production Deployment](project-context.md#deployment) process and best practices
3. Setup [Monitoring & Operations](project-context.md#monitoring-and-operations) for error tracking and performance
4. Review [Recent Fixes](changelog/recent-fixes.md) for latest improvements and known issues

## 🛠️ Key Technologies

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth v5 (beta)
- **UI**: Tailwind CSS, shadcn/ui, Radix UI
- **State Management**: TanStack Query, Zustand
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Playwright

## 📖 Documentation Standards

### File Organization

- Core documentation in root `/docs` folder
- Getting started guides in `/docs/getting-started/`
- Architecture documentation in `/docs/architecture/`
- Feature-specific docs in `/docs/features/`
- UI component docs in `/docs/ui-components/`
- Implementation details in `/docs/implementation/`
- Testing documentation in `/docs/testing/`
- Change history in `/docs/changelog/`
- Deployment guides in `/docs/deployment/`
- Archive old content in `/docs/archive-old/`

### Naming Conventions

- Use kebab-case for file names
- Include descriptive prefixes (e.g., `api-`, `ui-`, `fix-`)
- Use `.md` extension for all documentation

### Content Guidelines

- Start with clear overview and objectives
- Include code examples where relevant
- Maintain consistent formatting
- Update modification dates
- Cross-reference related documents

## 🔗 External Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [NextAuth.js](https://authjs.dev/)
- [next-intl](https://next-intl-docs.vercel.app/)

## 📝 Contributing to Documentation

1. Follow the established structure and naming conventions
2. Update this README when adding new documentation
3. Ensure all internal links are functional
4. Include relevant code examples and implementation details
5. Maintain consistent formatting and style

---

**Last Updated**: 2025-01-29  
**Documentation Version**: 2.0  
**Project Version**: RefundGo Web 2.0
