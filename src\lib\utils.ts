import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

import { MetadataProps, SiteConfig } from './types/metadata';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 格式化价格为美元格式
export const formatPrice = (price: number): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  return formatter.format(price);
};

export const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// 网站配置
const SITE_CONFIG: SiteConfig = {
  name: 'RefundGo',
  description: '高效的委托管理和协作平台',
  url: 'https://www.example.com', // 请替换为实际域名
  creator: '@hairunhuang',
  keywords: ['委托管理', '项目协作', '工作效率', '团队协作', 'Next.js'],
};

export function constructMetadata({
  title,
  description = SITE_CONFIG.description,
  image = '/og-image.jpg',
  icons = '/favicon.ico',
  keywords = SITE_CONFIG.keywords,
  authors,
  robots = 'index, follow',
}: MetadataProps = {}) {
  // 格式化标题
  const formattedTitle = title
    ? `${title} - ${SITE_CONFIG.name}`
    : SITE_CONFIG.name;

  return {
    title: formattedTitle,
    description,
    keywords: keywords.join(', '),
    authors: authors || [{ name: SITE_CONFIG.creator }],
    robots,
    icons,
    openGraph: {
      title: formattedTitle,
      description,
      siteName: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
      type: 'website' as const,
      images: [{ url: image }],
    },
    twitter: {
      card: 'summary_large_image' as const,
      title: formattedTitle,
      description,
      images: [image],
      creator: SITE_CONFIG.creator,
    },
    metadataBase: new URL(SITE_CONFIG.url),
  };
}
