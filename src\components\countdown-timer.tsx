'use client';

import { useState, useEffect } from 'react';

import { cn } from '@/lib/utils';

interface CountdownTimerProps {
  deadline: string | Date;
  className?: string;
  onExpire?: () => void;
}

export function CountdownTimer({
  deadline,
  className,
  onExpire,
}: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = useState<{
    hours: number;
    minutes: number;
    seconds: number;
    isExpired: boolean;
    isUrgent: boolean;
  }>({
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false,
    isUrgent: false,
  });

  useEffect(() => {
    const updateTimer = () => {
      const now = new Date();
      const endDate = new Date(deadline);
      const diffMs = endDate.getTime() - now.getTime();

      if (diffMs <= 0) {
        setTimeLeft({
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true,
          isUrgent: false,
        });
        if (onExpire) {
          onExpire();
        }
        return;
      }

      const hours = Math.floor(diffMs / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
      const isUrgent = diffMs < 2 * 60 * 60 * 1000; // 少于2小时为紧急状态

      setTimeLeft({
        hours,
        minutes,
        seconds,
        isExpired: false,
        isUrgent,
      });
    };

    // 立即更新一次
    updateTimer();

    // 每秒更新
    const timer = setInterval(updateTimer, 1000);

    return () => clearInterval(timer);
  }, [deadline, onExpire]);

  if (timeLeft.isExpired) {
    return (
      <span className={cn('text-red-600 font-medium', className)}>已超时</span>
    );
  }

  const textColor = timeLeft.isUrgent ? 'text-orange-600' : 'text-blue-600';

  return (
    <span className={cn(textColor, 'font-medium', className)}>
      {timeLeft.hours > 0 && `${timeLeft.hours}小时`}
      {timeLeft.minutes > 0 && `${timeLeft.minutes}分`}
      {timeLeft.hours === 0 && timeLeft.minutes < 10 && `${timeLeft.seconds}秒`}
      {timeLeft.hours === 0 &&
        timeLeft.minutes === 0 &&
        timeLeft.seconds > 0 &&
        '不到1分钟'}
      后截止
    </span>
  );
}
