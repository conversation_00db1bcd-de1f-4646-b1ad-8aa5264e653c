'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { MyAcceptedTasksContent } from '@/components/my-accepted-tasks-content';
import { UserPageLayout } from '@/components/user-page-layout';

export default function MyAcceptedTasksPage() {
  const t = useTranslations('MyAcceptedTasks');

  useEffect(() => {
    document.title = `${t('navigation.title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('navigation.title')}
      breadcrumbPage={t('navigation.breadcrumb')}
      href='/my-accepted-tasks'
    >
      <MyAcceptedTasksContent />
    </UserPageLayout>
  );
}
