'use client';

import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import { Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState, useEffect } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Testimonial {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  company?: string;
  rating: number;
  content: string;
  date: string;
  platform?: string;
  amount?: string;
  timeframe?: string;
  serviceType?: string;
  badge?: string;
}

interface TestimonialsCarouselProps {
  testimonials: Testimonial[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  className?: string;
}

export function TestimonialsCarousel({
  testimonials,
  autoPlay = true,
  autoPlayInterval = 5000,
  showControls = true,
  className = '',
}: TestimonialsCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  // 自动播放逻辑
  useEffect(() => {
    if (!autoPlay || isHovered || testimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % testimonials.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, isHovered, testimonials.length]);

  const goToNext = () => {
    setCurrentIndex(prev => (prev + 1) % testimonials.length);
  };

  const goToPrevious = () => {
    setCurrentIndex(
      prev => (prev - 1 + testimonials.length) % testimonials.length,
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (!testimonials.length) return null;

  return (
    <div
      className={`relative ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 主要内容区域 */}
      <div className='relative overflow-hidden'>
        <AnimatePresence mode='wait'>
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
            className='w-full'
          >
            <TestimonialCard testimonial={testimonials[currentIndex]} />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* 控制按钮 */}
      {showControls && testimonials.length > 1 && (
        <>
          <Button
            variant='ghost'
            size='icon'
            className='absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white text-gray-700 border-gray-300'
            onClick={goToPrevious}
            aria-label='Previous testimonial'
          >
            <ChevronLeft className='h-4 w-4' />
          </Button>
          <Button
            variant='ghost'
            size='icon'
            className='absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white text-gray-700 border-gray-300'
            onClick={goToNext}
            aria-label='Next testimonial'
          >
            <ChevronRight className='h-4 w-4' />
          </Button>
        </>
      )}

      {/* 指示器 */}
      {testimonials.length > 1 && (
        <div className='flex justify-center mt-6 space-x-2'>
          {testimonials.map((_, index) => (
            <button
              key={index}
              type='button'
              aria-label={`Go to slide ${index + 1}`}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-blue-600 w-8'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              onClick={() => goToSlide(index)}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// 单个评价卡片组件
function TestimonialCard({ testimonial }: { testimonial: Testimonial }) {
  return (
    <Card className='glass-linear border-white/10 mobile-card h-full testimonial-card'>
      <CardContent className='p-4 md:p-6 h-full flex flex-col'>
        <div className='flex flex-col space-y-3 flex-grow'>
          {/* 评价内容 */}
          <blockquote className='text-sm md:text-base text-gray-700 text-center leading-relaxed mobile-text flex-grow'>
            {testimonial.content}
          </blockquote>

          {/* 评分 */}
          <div className='flex justify-center space-x-1'>
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 star ${
                  i < testimonial.rating
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-200'
                }`}
              />
            ))}
          </div>

          {/* 用户信息 */}
          <div className='flex items-center justify-center space-x-3 pt-2'>
            <Avatar className='h-10 w-10 mobile-avatar avatar'>
              <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
              <AvatarFallback className='bg-gray-100 text-gray-700'>
                {testimonial.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className='text-center'>
              <div className='font-semibold text-gray-900 text-sm mobile-text'>
                {testimonial.name}
              </div>
              <div className='text-xs text-gray-600 mobile-text'>
                {testimonial.role}
                {testimonial.company && ` • ${testimonial.company}`}
              </div>
              {/* 添加评价日期和平台信息 */}
              {(testimonial.platform || testimonial.date) && (
                <div className='text-xs text-gray-500 mt-1'>
                  {testimonial.platform && (
                    <span className='inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-full mr-2'>
                      {testimonial.platform}
                    </span>
                  )}
                  {testimonial.date && <span>{testimonial.date}</span>}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 双行水平滚动展示组件
interface TestimonialsDoubleRowScrollProps {
  testimonials: Testimonial[];
  speed?: number;
  className?: string;
}

export function TestimonialsDoubleRowScroll({
  testimonials,
  speed = 40,
  className = '',
}: TestimonialsDoubleRowScrollProps) {
  const [isHovered, setIsHovered] = useState(false);
  const firstRowControls = useAnimation();
  const secondRowControls = useAnimation();

  // 将评价分成两行
  const firstRowTestimonials = testimonials.filter(
    (_, index) => index % 2 === 0,
  );
  const secondRowTestimonials = testimonials.filter(
    (_, index) => index % 2 === 1,
  );

  // 复制数据以实现无缝滚动
  const duplicatedFirstRow = [
    ...firstRowTestimonials,
    ...firstRowTestimonials,
    ...firstRowTestimonials,
  ];
  const duplicatedSecondRow = [
    ...secondRowTestimonials,
    ...secondRowTestimonials,
    ...secondRowTestimonials,
  ];

  const cardWidth = 320; // w-80 = 320px
  const gap = 24; // space-x-6 = 24px
  const totalWidth = (cardWidth + gap) * firstRowTestimonials.length;

  // 启动动画
  useEffect(() => {
    if (!isHovered) {
      // 第一行：从右往左滚动
      firstRowControls.start({
        x: [-totalWidth, 0],
        transition: {
          duration: speed,
          repeat: Infinity,
          ease: 'linear',
          repeatType: 'loop',
        },
      });

      // 第二行：从左往右滚动
      secondRowControls.start({
        x: [0, -totalWidth],
        transition: {
          duration: speed,
          repeat: Infinity,
          ease: 'linear',
          repeatType: 'loop',
        },
      });
    } else {
      // 暂停时停止动画，保持当前位置
      firstRowControls.stop();
      secondRowControls.stop();
    }
  }, [isHovered, firstRowControls, secondRowControls, speed, totalWidth]);

  return (
    <div
      className={`relative overflow-hidden space-y-6 testimonials-double-row ${className} ${isHovered ? 'paused' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 第一行：从右往左滚动 */}
      <motion.div
        animate={firstRowControls}
        className='flex space-x-6'
        style={{ width: 'fit-content' }}
      >
        {duplicatedFirstRow.map((testimonial, index) => (
          <motion.div
            key={`first-${testimonial.id}-${index}`}
            whileHover={{
              scale: 1.02,
              zIndex: 10,
              transition: { duration: 0.2 },
            }}
            className='flex-shrink-0 w-80'
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <TestimonialCard testimonial={testimonial} />
          </motion.div>
        ))}
      </motion.div>

      {/* 第二行：从左往右滚动 */}
      <motion.div
        animate={secondRowControls}
        className='flex space-x-6'
        style={{ width: 'fit-content' }}
      >
        {duplicatedSecondRow.map((testimonial, index) => (
          <motion.div
            key={`second-${testimonial.id}-${index}`}
            whileHover={{
              scale: 1.02,
              zIndex: 10,
              transition: { duration: 0.2 },
            }}
            className='flex-shrink-0 w-80'
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <TestimonialCard testimonial={testimonial} />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}

// 多列垂直滚动展示组件
interface TestimonialsVerticalScrollProps {
  testimonials: Testimonial[];
  columns?: number;
  speed?: number;
  className?: string;
}

export function TestimonialsVerticalScroll({
  testimonials,
  columns = 3,
  speed = 50,
  className = '',
}: TestimonialsVerticalScrollProps) {
  const [isHovered, setIsHovered] = useState(false);

  // 将评价分成多列
  const getColumnTestimonials = (columnIndex: number) => {
    return testimonials.filter((_, index) => index % columns === columnIndex);
  };

  return (
    <div
      className={`relative overflow-hidden testimonials-vertical-scroll ${className} ${isHovered ? 'paused' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className='flex space-x-6'>
        {[...Array(columns)].map((_, columnIndex) => {
          const columnTestimonials = getColumnTestimonials(columnIndex);
          return (
            <div key={columnIndex} className='flex-1'>
              <motion.div
                animate={{
                  y: isHovered
                    ? undefined
                    : [0, -100 * columnTestimonials.length],
                }}
                transition={{
                  duration: speed,
                  repeat: isHovered ? 0 : Infinity,
                  ease: 'linear',
                  repeatType: 'loop',
                }}
                className='space-y-6'
              >
                {/* 渲染两遍以实现无缝滚动 */}
                {[...Array(2)].map((_, repeatIndex) =>
                  columnTestimonials.map((testimonial, index) => (
                    <motion.div
                      key={`${repeatIndex}-${testimonial.id}-${index}`}
                      whileHover={{
                        scale: 1.02,
                        zIndex: 10,
                        transition: { duration: 0.2 },
                      }}
                      className='transform transition-transform'
                      onMouseEnter={() => setIsHovered(true)}
                      onMouseLeave={() => setIsHovered(false)}
                    >
                      <TestimonialCard testimonial={testimonial} />
                    </motion.div>
                  )),
                )}
              </motion.div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// 默认导出轮播组件
export default TestimonialsCarousel;
