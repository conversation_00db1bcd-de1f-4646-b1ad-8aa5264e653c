// 缓存策略实现
'use client';

// 缓存配置
export const CACHE_CONFIG = {
  // 默认过期时间 (毫秒)
  DEFAULT_TTL: 5 * 60 * 1000, // 5分钟

  // 不同类型数据的过期时间
  TTL: {
    USER_DATA: 10 * 60 * 1000, // 用户数据 10分钟
    TASKS: 2 * 60 * 1000, // 委托数据 2分钟
    DASHBOARD: 1 * 60 * 1000, // 仪表盘数据 1分钟
    STATIC_DATA: 60 * 60 * 1000, // 静态数据 1小时
    SESSION: 30 * 60 * 1000, // 会话数据 30分钟
    SETTINGS: 15 * 60 * 1000, // 设置数据 15分钟
  },

  // 缓存键前缀
  PREFIXES: {
    USER: 'user:',
    TASK: 'task:',
    DASHBOARD: 'dashboard:',
    SESSION: 'session:',
    API: 'api:',
    STATIC: 'static:',
  },
} as const;

// 缓存项接口
interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  version?: string;
}

// 内存缓存类
class MemoryCache {
  private cache = new Map<string, CacheItem>();
  private timers = new Map<string, NodeJS.Timeout>();

  set<T>(
    key: string,
    data: T,
    ttl: number = CACHE_CONFIG.DEFAULT_TTL,
    version?: string,
  ): void {
    // 清除旧的定时器
    const existingTimer = this.timers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      version,
    };

    this.cache.set(key, item);

    // 设置过期定时器
    const timer = setTimeout(() => {
      this.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.data as T;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): boolean {
    const timer = this.timers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }

    return this.cache.delete(key);
  }

  clear(): void {
    // 清除所有定时器
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // 获取缓存统计信息
  getStats() {
    const items = Array.from(this.cache.values());
    const now = Date.now();

    return {
      total: items.length,
      expired: items.filter(item => now - item.timestamp > item.ttl).length,
      valid: items.filter(item => now - item.timestamp <= item.ttl).length,
      memoryUsage: JSON.stringify(items).length, // 粗略估算
    };
  }

  // 清理过期项
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, item] of Array.from(this.cache.entries())) {
      if (now - item.timestamp > item.ttl) {
        this.delete(key);
        cleaned++;
      }
    }

    return cleaned;
  }
}

// 全局内存缓存实例
const memoryCache = new MemoryCache();

// LocalStorage 缓存类
class LocalStorageCache {
  private prefix = 'refundgo_cache:';

  set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.DEFAULT_TTL): void {
    if (typeof window === 'undefined') return;

    try {
      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl,
      };

      localStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error);
    }
  }

  get<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const itemStr = localStorage.getItem(this.prefix + key);
      if (!itemStr) return null;

      const item: CacheItem<T> = JSON.parse(itemStr);

      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.delete(key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error);
      return null;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.prefix + key);
  }

  clear(): void {
    if (typeof window === 'undefined') return;

    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    });
  }

  // 清理过期项
  cleanup(): number {
    if (typeof window === 'undefined') return 0;

    const keys = Object.keys(localStorage);
    let cleaned = 0;

    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        const cacheKey = key.replace(this.prefix, '');
        if (!this.has(cacheKey)) {
          cleaned++;
        }
      }
    });

    return cleaned;
  }
}

// 全局 localStorage 缓存实例
const localStorageCache = new LocalStorageCache();

// SessionStorage 缓存类
class SessionStorageCache {
  private prefix = 'refundgo_session:';

  set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.DEFAULT_TTL): void {
    if (typeof window === 'undefined') return;

    try {
      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl,
      };

      sessionStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set sessionStorage cache:', error);
    }
  }

  get<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const itemStr = sessionStorage.getItem(this.prefix + key);
      if (!itemStr) return null;

      const item: CacheItem<T> = JSON.parse(itemStr);

      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        this.delete(key);
        return null;
      }

      return item.data;
    } catch (error) {
      console.warn('Failed to get sessionStorage cache:', error);
      return null;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    if (typeof window === 'undefined') return;
    sessionStorage.removeItem(this.prefix + key);
  }

  clear(): void {
    if (typeof window === 'undefined') return;

    const keys = Object.keys(sessionStorage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        sessionStorage.removeItem(key);
      }
    });
  }
}

// 全局 sessionStorage 缓存实例
const sessionStorageCache = new SessionStorageCache();

// 缓存策略枚举
export enum CacheStrategy {
  MEMORY_ONLY = 'memory',
  LOCAL_STORAGE = 'localStorage',
  SESSION_STORAGE = 'sessionStorage',
  MEMORY_FIRST = 'memoryFirst',
  PERSISTENT_FIRST = 'persistentFirst',
}

// 统一缓存接口
export class CacheManager {
  private strategy: CacheStrategy;

  constructor(strategy: CacheStrategy = CacheStrategy.MEMORY_FIRST) {
    this.strategy = strategy;
  }

  set<T>(key: string, data: T, ttl?: number, strategy?: CacheStrategy): void {
    const currentStrategy = strategy || this.strategy;
    const cacheTTL = ttl || CACHE_CONFIG.DEFAULT_TTL;

    switch (currentStrategy) {
      case CacheStrategy.MEMORY_ONLY:
        memoryCache.set(key, data, cacheTTL);
        break;

      case CacheStrategy.LOCAL_STORAGE:
        localStorageCache.set(key, data, cacheTTL);
        break;

      case CacheStrategy.SESSION_STORAGE:
        sessionStorageCache.set(key, data, cacheTTL);
        break;

      case CacheStrategy.MEMORY_FIRST:
        memoryCache.set(key, data, cacheTTL);
        localStorageCache.set(key, data, cacheTTL);
        break;

      case CacheStrategy.PERSISTENT_FIRST:
        localStorageCache.set(key, data, cacheTTL);
        memoryCache.set(key, data, cacheTTL);
        break;
    }
  }

  get<T>(key: string, strategy?: CacheStrategy): T | null {
    const currentStrategy = strategy || this.strategy;

    switch (currentStrategy) {
      case CacheStrategy.MEMORY_ONLY:
        return memoryCache.get<T>(key);

      case CacheStrategy.LOCAL_STORAGE:
        return localStorageCache.get<T>(key);

      case CacheStrategy.SESSION_STORAGE:
        return sessionStorageCache.get<T>(key);

      case CacheStrategy.MEMORY_FIRST: {
        // 先尝试内存缓存
        let data = memoryCache.get<T>(key);
        if (data === null) {
          // 内存中没有，尝试 localStorage
          data = localStorageCache.get<T>(key);
          if (data !== null) {
            // 找到了，同步到内存缓存
            memoryCache.set(key, data, CACHE_CONFIG.DEFAULT_TTL);
          }
        }
        return data;
      }

      case CacheStrategy.PERSISTENT_FIRST: {
        // 先尝试 localStorage
        let persistentData = localStorageCache.get<T>(key);
        if (persistentData === null) {
          // localStorage 中没有，尝试内存缓存
          persistentData = memoryCache.get<T>(key);
        }
        return persistentData;
      }

      default:
        return null;
    }
  }

  has(key: string, strategy?: CacheStrategy): boolean {
    return this.get(key, strategy) !== null;
  }

  delete(key: string): void {
    memoryCache.delete(key);
    localStorageCache.delete(key);
    sessionStorageCache.delete(key);
  }

  clear(): void {
    memoryCache.clear();
    localStorageCache.clear();
    sessionStorageCache.clear();
  }

  // 获取缓存统计
  getStats() {
    return {
      memory: memoryCache.getStats(),
      localStorage: {
        // localStorage 统计需要遍历所有键
        total:
          typeof window !== 'undefined'
            ? Object.keys(localStorage).filter(k =>
                k.startsWith('refundgo_cache:'),
              ).length
            : 0,
      },
    };
  }

  // 清理过期缓存
  cleanup(): { memory: number; localStorage: number } {
    return {
      memory: memoryCache.cleanup(),
      localStorage: localStorageCache.cleanup(),
    };
  }
}

// 默认缓存管理器
export const cache = new CacheManager(CacheStrategy.MEMORY_FIRST);

// 特定用途的缓存管理器
export const userCache = new CacheManager(CacheStrategy.PERSISTENT_FIRST);
export const sessionCache = new CacheManager(CacheStrategy.SESSION_STORAGE);
export const tempCache = new CacheManager(CacheStrategy.MEMORY_ONLY);

// 缓存键生成器
export const CacheKeys = {
  user: (userId: string) => `${CACHE_CONFIG.PREFIXES.USER}${userId}`,
  userTasks: (userId: string) => `${CACHE_CONFIG.PREFIXES.TASK}user:${userId}`,
  dashboard: (userId: string) => `${CACHE_CONFIG.PREFIXES.DASHBOARD}${userId}`,
  session: (sessionId: string) =>
    `${CACHE_CONFIG.PREFIXES.SESSION}${sessionId}`,
  api: (endpoint: string, params?: string) =>
    `${CACHE_CONFIG.PREFIXES.API}${endpoint}${params ? `:${params}` : ''}`,
  static: (key: string) => `${CACHE_CONFIG.PREFIXES.STATIC}${key}`,
};

// 缓存装饰器函数
export function withCache<T extends(...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  ttl: number = CACHE_CONFIG.DEFAULT_TTL,
  strategy: CacheStrategy = CacheStrategy.MEMORY_FIRST,
): T {
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator(...args);
    const cacheManager = new CacheManager(strategy);

    // 尝试从缓存获取
    const cached = cacheManager.get(key);
    if (cached !== null) {
      return cached;
    }

    // 缓存中没有，执行原函数
    const result = await fn(...args);

    // 存储到缓存
    cacheManager.set(key, result, ttl);

    return result;
  }) as T;
}

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  // 每5分钟清理一次过期缓存
  setInterval(
    () => {
      cache.cleanup();
    },
    5 * 60 * 1000,
  );
}

export default cache;
