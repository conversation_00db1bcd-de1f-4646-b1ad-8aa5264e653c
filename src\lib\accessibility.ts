/**
 * 无障碍性工具函数
 * 提供键盘导航、屏幕阅读器支持和RTL语言支持
 */

import { useEffect, useRef } from 'react';

/**
 * 键盘导航支持
 */
export const useKeyboardNavigation = () => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Tab键导航增强
      if (event.key === 'Tab') {
        // 确保焦点可见
        document.body.classList.add('keyboard-navigation');
      }

      // Escape键关闭模态框
      if (event.key === 'Escape') {
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement && activeElement.closest('[role="dialog"]')) {
          const closeButton = activeElement.closest('[role="dialog"]')?.querySelector('[aria-label*="close"], [aria-label*="关闭"]') as HTMLElement;
          closeButton?.click();
        }
      }

      // Enter键和空格键激活按钮
      if ((event.key === 'Enter' || event.key === ' ') &&
          event.target instanceof HTMLElement &&
          event.target.getAttribute('role') === 'button') {
        event.preventDefault();
        event.target.click();
      }
    };

    const handleMouseDown = () => {
      document.body.classList.remove('keyboard-navigation');
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);
};

/**
 * 焦点管理
 */
export const useFocusManagement = (isOpen: boolean) => {
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const containerRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      // 保存当前焦点
      previousFocusRef.current = document.activeElement as HTMLElement;

      // 将焦点移到容器内的第一个可聚焦元素
      const focusableElements = containerRef.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      );

      if (focusableElements && focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
      }
    } else {
      // 恢复之前的焦点
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  return containerRef;
};

/**
 * 屏幕阅读器公告
 */
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // 清理
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

/**
 * RTL语言检测和支持
 */
export const useRTLSupport = (locale: string) => {
  const isRTL = ['ar', 'he', 'fa', 'ur'].includes(locale);

  useEffect(() => {
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = locale;
  }, [locale, isRTL]);

  return { isRTL };
};

/**
 * 颜色对比度检查
 */
export const checkColorContrast = (foreground: string, background: string): boolean => {
  // 简化的对比度检查，实际项目中应使用更完整的算法
  const getLuminance = (color: string): number => {
    // 这里应该实现完整的亮度计算
    // 为了简化，返回一个模拟值
    return 0.5;
  };

  const foregroundLuminance = getLuminance(foreground);
  const backgroundLuminance = getLuminance(background);

  const contrast = (Math.max(foregroundLuminance, backgroundLuminance) + 0.05) /
                  (Math.min(foregroundLuminance, backgroundLuminance) + 0.05);

  // WCAG AA标准要求对比度至少为4.5:1
  return contrast >= 4.5;
};

/**
 * 生成唯一的ARIA ID
 */
export const generateAriaId = (prefix: string = 'aria'): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 无障碍性属性生成器
 */
export const getAccessibilityProps = (options: {
  label?: string;
  description?: string;
  required?: boolean;
  invalid?: boolean;
  expanded?: boolean;
  controls?: string;
  describedBy?: string;
}) => {
  const props: Record<string, any> = {};

  if (options.label) {
    props['aria-label'] = options.label;
  }

  if (options.description) {
    const descId = generateAriaId('desc');
    props['aria-describedby'] = options.describedBy ? `${options.describedBy} ${descId}` : descId;
  }

  if (options.required) {
    props['aria-required'] = true;
  }

  if (options.invalid) {
    props['aria-invalid'] = true;
  }

  if (options.expanded !== undefined) {
    props['aria-expanded'] = options.expanded;
  }

  if (options.controls) {
    props['aria-controls'] = options.controls;
  }

  return props;
};

/**
 * 跳转到主内容的辅助函数
 * 注意：实际的 SkipToMainContent 组件现在位于 src/components/skip-to-main-content.tsx
 */
export const skipToMainContent = () => {
  const mainContent = document.getElementById('main-content');
  if (mainContent) {
    mainContent.focus();
    mainContent.scrollIntoView({ behavior: 'smooth' });
  }
};

/**
 * 减少动画偏好检测
 */
export const useReducedMotion = () => {
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false;

  return prefersReducedMotion;
};

/**
 * 高对比度模式检测
 */
export const useHighContrast = () => {
  const prefersHighContrast = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-contrast: high)').matches
    : false;

  return prefersHighContrast;
};

/**
 * 字体大小偏好检测
 */
export const useFontSizePreference = () => {
  const preferredFontSize = typeof window !== 'undefined'
    ? getComputedStyle(document.documentElement).fontSize
    : '16px';

  return preferredFontSize;
};
