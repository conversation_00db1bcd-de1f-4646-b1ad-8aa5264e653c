'use client';

import {
  Search,
  Plus,
  Users,
  Crown,
  Shield,
  Mail,
  Filter,
  ChevronLeft,
  ChevronRight,
  Ban,
  CheckCircle,
} from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';

import { AddUserDialog } from '@/components/admin/add-user-dialog';
import { UserActionsDropdown } from '@/components/admin/user-actions-dropdown';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { getMemberPlanText, getUserStatusText } from '@/lib/constants';
import { User } from '@/types/user';

interface UsersManagementProps {
  initialUsers: User[];
  stats: {
    totalUsers: number;
    normalUsers: number;
    frozenUsers: number;
    freeUsers: number;
    proUsers: number;
    businessUsers: number;
    totalBalance: number;
    totalCompletedTasks: number;
    totalPublishedTasks: number;
  };
}

export function UsersManagement({
  initialUsers,
  stats: initialStats,
}: UsersManagementProps) {
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [stats, setStats] = useState(initialStats);
  const [addUserOpen, setAddUserOpen] = useState(false);

  // 筛选状态
  const [searchQuery, setSearchQuery] = useState('');
  const [memberPlanFilter, setMemberPlanFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // 筛选后的用户列表
  const [filteredUsers, setFilteredUsers] = useState<User[]>(initialUsers);

  // 获取用户名首字母
  const getInitials = (nickname: string) => {
    return nickname.charAt(0).toUpperCase();
  };

  // 获取会员套餐颜色
  const getMemberPlanColor = (plan: string) => {
    switch (plan) {
      case 'BUSINESS':
        return 'bg-purple-100 text-purple-800';
      case 'PRO':
        return 'bg-blue-100 text-blue-800';
      case 'FREE':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取会员套餐图标
  const getMemberPlanIcon = (plan: string) => {
    switch (plan) {
      case 'BUSINESS':
        return <Crown className='h-3 w-3' />;
      case 'PRO':
        return <Shield className='h-3 w-3' />;
      default:
        return <Users className='h-3 w-3' />;
    }
  };

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    if (status === 'ACTIVE') {
      return {
        color: 'bg-green-100 text-green-800',
        icon: <CheckCircle className='h-3 w-3' />,
        variant: 'default' as const,
      };
    } else {
      return {
        color: 'bg-red-100 text-red-800',
        icon: <Ban className='h-3 w-3' />,
        variant: 'destructive' as const,
      };
    }
  };

  // 计算会员到期时间显示
  const getMemberExpiryDisplay = (expiry: string, memberPlan: string) => {
    // 免费版显示永久
    if (memberPlan === 'FREE') {
      return '永久';
    }

    const expiryDate = new Date(expiry);
    const now = new Date();
    const diffTime = expiryDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return '已过期';
    } else if (diffDays === 0) {
      return '今天到期';
    } else if (diffDays < 30) {
      return `${diffDays}天后到期`;
    } else if (diffDays > 3650) {
      return `${Math.floor(diffDays / 365)}年后到期`;
    } else {
      return `${Math.floor(diffDays / 30)}个月后到期`;
    }
  };

  // 更新统计数据
  const updateStats = (userList: User[]) => {
    const newStats = {
      totalUsers: userList.length,
      normalUsers: userList.filter(u => u.status === 'ACTIVE').length,
      frozenUsers: userList.filter(u => u.status === 'FROZEN').length,
      freeUsers: userList.filter(u => u.memberPlan === 'FREE').length,
      proUsers: userList.filter(u => u.memberPlan === 'PRO').length,
      businessUsers: userList.filter(u => u.memberPlan === 'BUSINESS').length,
      totalBalance: userList.reduce((sum, u) => sum + u.balance, 0),
      totalCompletedTasks: userList.reduce(
        (sum, u) => sum + u.completedTasks,
        0,
      ),
      totalPublishedTasks: userList.reduce(
        (sum, u) => sum + u.publishedTasks,
        0,
      ),
    };
    setStats(newStats);
  };

  // 处理用户更新
  const handleUserUpdate = (updatedUser: User) => {
    const newUsers = users.map(user =>
      user.id === updatedUser.id ? updatedUser : user,
    );
    setUsers(newUsers);
    updateStats(newUsers);
    applyFilters(newUsers); // 重新应用筛选
  };

  // 处理用户添加
  const handleUserAdd = (newUser: User) => {
    const newUsers = [...users, newUser];
    setUsers(newUsers);
    updateStats(newUsers);
    applyFilters(newUsers); // 重新应用筛选
  };

  // 筛选用户列表
  const applyFilters = useCallback(
    (userList: User[] = users) => {
      let filtered = [...userList];

      // 搜索筛选
      if (searchQuery.trim()) {
        const query = searchQuery.trim().toLowerCase();
        filtered = filtered.filter(
          user =>
            user.nickname.toLowerCase().includes(query) ||
            user.email.toLowerCase().includes(query),
        );
      }

      // 会员套餐筛选
      if (memberPlanFilter !== 'all') {
        const planMap: { [key: string]: string } = {
          free: 'FREE',
          pro: 'PRO',
          business: 'BUSINESS',
        };
        filtered = filtered.filter(
          user => user.memberPlan === planMap[memberPlanFilter],
        );
      }

      // 状态筛选
      if (statusFilter !== 'all') {
        const statusMap: { [key: string]: string } = {
          normal: 'ACTIVE',
          frozen: 'FROZEN',
        };
        filtered = filtered.filter(
          user => user.status === statusMap[statusFilter],
        );
      }

      setFilteredUsers(filtered);
    },
    [users, searchQuery, memberPlanFilter, statusFilter],
  );

  // 重置筛选
  const resetFilters = () => {
    setSearchQuery('');
    setMemberPlanFilter('all');
    setStatusFilter('all');
    setFilteredUsers(users);
  };

  // 当筛选条件变化时重新筛选
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  return (
    <div className='space-y-6'>
      {/* 页面标题和操作 */}
      <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
        <div className='space-y-1'>
          <h1 className='text-2xl font-bold tracking-tight'>用户管理</h1>
          <p className='text-sm text-muted-foreground'>
            管理系统中的所有用户，查看用户信息、调整会员套餐和账户余额
          </p>
        </div>
        <div className='flex flex-col sm:flex-row gap-2'>
          <Button size='sm' onClick={() => setAddUserOpen(true)}>
            <Plus className='mr-2 h-4 w-4' />
            添加用户
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>总用户数</CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.totalUsers}</div>
            <p className='text-xs text-muted-foreground'>
              正常 {stats.normalUsers} / 冻结 {stats.frozenUsers}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>免费版</CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.freeUsers}</div>
            <p className='text-xs text-muted-foreground'>
              占比{' '}
              {stats.totalUsers > 0
                ? Math.round((stats.freeUsers / stats.totalUsers) * 100)
                : 0}
              %
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>专业版</CardTitle>
            <Shield className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.proUsers}</div>
            <p className='text-xs text-muted-foreground'>
              占比{' '}
              {stats.totalUsers > 0
                ? Math.round((stats.proUsers / stats.totalUsers) * 100)
                : 0}
              %
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>商业版</CardTitle>
            <Crown className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.businessUsers}</div>
            <p className='text-xs text-muted-foreground'>
              占比{' '}
              {stats.totalUsers > 0
                ? Math.round((stats.businessUsers / stats.totalUsers) * 100)
                : 0}
              %
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Filter className='h-5 w-5' />
            搜索和筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex flex-col lg:flex-row gap-4'>
            <div className='relative flex-1'>
              <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='搜索昵称或邮箱...'
                className='pl-10'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
            <div className='flex flex-col sm:flex-row gap-2'>
              <Select
                value={memberPlanFilter}
                onValueChange={setMemberPlanFilter}
              >
                <SelectTrigger className='w-full sm:w-[140px]'>
                  <SelectValue placeholder='会员套餐' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>全部套餐</SelectItem>
                  <SelectItem value='free'>免费版</SelectItem>
                  <SelectItem value='pro'>专业版</SelectItem>
                  <SelectItem value='business'>商业版</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-full sm:w-[120px]'>
                  <SelectValue placeholder='账号状态' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>全部状态</SelectItem>
                  <SelectItem value='normal'>正常</SelectItem>
                  <SelectItem value='frozen'>冻结</SelectItem>
                </SelectContent>
              </Select>
              <Button variant='outline' size='sm' onClick={resetFilters}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 用户列表 */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='overflow-x-auto'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>用户信息</TableHead>
                  <TableHead>会员套餐</TableHead>
                  <TableHead>会员到期</TableHead>
                  <TableHead>账号余额</TableHead>
                  <TableHead>完成委托</TableHead>
                  <TableHead>发布委托</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>注册日期</TableHead>
                  <TableHead className='text-right'>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map(user => (
                  <TableRow key={user.id} className='hover:bg-muted/50'>
                    <TableCell>
                      <div className='flex items-center gap-3'>
                        <Avatar className='h-8 w-8'>
                          <AvatarImage src={user.avatar} />
                          <AvatarFallback className='bg-primary/10 text-primary'>
                            {getInitials(user.nickname)}
                          </AvatarFallback>
                        </Avatar>
                        <div className='space-y-1'>
                          <p className='font-medium text-sm'>{user.nickname}</p>
                          <div className='flex items-center gap-1 text-xs text-muted-foreground'>
                            <Mail className='h-3 w-3' />
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant='outline'
                        className={`text-xs ${getMemberPlanColor(user.memberPlan)}`}
                      >
                        <span className='mr-1'>
                          {getMemberPlanIcon(user.memberPlan)}
                        </span>
                        {getMemberPlanText(user.memberPlan)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {user.memberPlan === 'FREE' ? (
                        <div className='text-sm text-muted-foreground'>
                          永久
                        </div>
                      ) : (
                        <>
                          <div className='text-sm'>
                            {new Date(user.memberPlanExpiry).toLocaleDateString(
                              'zh-CN',
                            )}
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            {getMemberExpiryDisplay(
                              user.memberPlanExpiry,
                              user.memberPlan,
                            )}
                          </div>
                        </>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className='font-mono text-sm'>
                        ${user.balance.toFixed(2)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='text-sm'>{user.completedTasks}</div>
                    </TableCell>
                    <TableCell>
                      <div className='text-sm'>{user.publishedTasks}</div>
                    </TableCell>
                    <TableCell>
                      {(() => {
                        const statusDisplay = getStatusDisplay(user.status);
                        return (
                          <Badge
                            variant={statusDisplay.variant}
                            className={`text-xs ${statusDisplay.color}`}
                          >
                            <span className='mr-1'>{statusDisplay.icon}</span>
                            {getUserStatusText(user.status)}
                          </Badge>
                        );
                      })()}
                    </TableCell>
                    <TableCell>
                      <div className='text-sm'>
                        {new Date(user.registerDate).toLocaleDateString(
                          'zh-CN',
                        )}
                      </div>
                    </TableCell>
                    <TableCell className='text-right'>
                      <UserActionsDropdown
                        user={user}
                        onUserUpdate={handleUserUpdate}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 分页 */}
      <div className='flex items-center justify-between'>
        <div className='text-sm text-muted-foreground'>
          显示 1-{filteredUsers.length} 条，共 {users.length} 条记录
          {filteredUsers.length !== users.length && (
            <span className='text-blue-600'> (已筛选)</span>
          )}
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='sm' disabled>
            <ChevronLeft className='h-4 w-4' />
            上一页
          </Button>
          <Button variant='outline' size='sm' disabled>
            下一页
            <ChevronRight className='h-4 w-4' />
          </Button>
        </div>
      </div>

      {/* 添加用户对话框 */}
      <AddUserDialog
        open={addUserOpen}
        onOpenChange={setAddUserOpen}
        onUserAdd={handleUserAdd}
      />
    </div>
  );
}
