'use client';

import { <PERSON>, <PERSON> } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

export function FooterModeToggle() {
  const { setTheme } = useTheme();
  const pathname = usePathname();
  const isAdmin = pathname.startsWith('/admin');

  // 硬编码的中文文本用于后台管理系统
  const adminTexts = {
    toggle: '切换主题',
    light: '浅色',
    dark: '深色',
    system: '系统',
  };

  // Always call the hook - React hooks must be called unconditionally
  const t = useTranslations('Common');

  // 获取文本的辅助函数
  const getText = (key: 'toggle' | 'light' | 'dark' | 'system') => {
    if (isAdmin) {
      return adminTexts[key];
    }
    const translationKey = `theme.${key}` as const;
    try {
      return t(translationKey);
    } catch (error) {
      // Fallback to admin texts if translation fails
      return adminTexts[key];
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            // Footer-specific styling for dark background
            "text-gray-400 hover:text-white hover:bg-gray-800 dark:hover:bg-gray-700",
            // Ensure proper contrast and visibility
            "border border-gray-600 dark:border-gray-500",
            // Background that works on dark footer
            "bg-gray-800/50 dark:bg-gray-700/50",
            // Focus states for accessibility
            "focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-900",
            // Transition for smooth interactions
            "transition-all duration-200",
          )}
        >
          <Sun className={cn(
            'h-[1.2rem] w-[1.2rem] transition-all',
            'scale-100 rotate-0 dark:scale-0 dark:-rotate-90',
          )} />
          <Moon className={cn(
            'absolute h-[1.2rem] w-[1.2rem] transition-all',
            'scale-0 rotate-90 dark:scale-100 dark:rotate-0',
          )} />
          <span className='sr-only'>{getText('toggle')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align='end'
        className={cn(
          // Ensure dropdown is visible on dark footer
          "bg-gray-800 dark:bg-gray-900",
          "border-gray-600 dark:border-gray-500",
          "text-gray-200 dark:text-gray-100",
        )}
      >
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className="hover:bg-gray-700 dark:hover:bg-gray-800 focus:bg-gray-700 dark:focus:bg-gray-800"
        >
          <Sun className="mr-2 h-4 w-4" />
          {getText('light')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className="hover:bg-gray-700 dark:hover:bg-gray-800 focus:bg-gray-700 dark:focus:bg-gray-800"
        >
          <Moon className="mr-2 h-4 w-4" />
          {getText('dark')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className="hover:bg-gray-700 dark:hover:bg-gray-800 focus:bg-gray-700 dark:focus:bg-gray-800"
        >
          <div className="mr-2 h-4 w-4 rounded-sm border border-current flex items-center justify-center">
            <div className="h-2 w-2 rounded-full bg-current opacity-60" />
          </div>
          {getText('system')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
