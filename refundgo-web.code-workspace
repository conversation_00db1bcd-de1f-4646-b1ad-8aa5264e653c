{
  "folders": [
    {
      "name": "RefundGo Web",
      "path": ".",
    },
  ],
  "settings": {
    // ===== 工作区特定设置 =====
    "typescript.preferences.includePackageJsonAutoImports": "auto",
    "typescript.suggest.autoImports": true,
    "typescript.updateImportsOnFileMove.enabled": "always",

    // ===== 项目特定路径 =====
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.preferences.importModuleSpecifierEnding": "minimal",

    // ===== Next.js 特定设置 =====
    "emmet.includeLanguages": {
      "javascript": "javascriptreact",
      "typescript": "typescriptreact",
    },

    // ===== Tailwind CSS 设置 =====
    "tailwindCSS.includeLanguages": {
      "typescript": "javascript",
      "typescriptreact": "javascript",
    },
    "tailwindCSS.experimental.classRegex": [
      ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
      ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
      ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
      ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ],

    // ===== 文件关联 =====
    "files.associations": {
      "*.css": "tailwindcss",
    },

    // ===== 搜索排除 =====
    "search.exclude": {
      "**/node_modules": true,
      "**/.next": true,
      "**/coverage": true,
      "**/.nyc_output": true,
      "**/.vercel": true,
      "**/out": true,
      "**/dist": true,
      "**/build": true,
      "**/prisma/migrations": true,
    },

    // ===== 文件监视排除 =====
    "files.watcherExclude": {
      "**/.git/objects/**": true,
      "**/.git/subtree-cache/**": true,
      "**/node_modules/**": true,
      "**/.next/**": true,
      "**/coverage/**": true,
      "**/.nyc_output/**": true,
      "**/.vercel/**": true,
      "**/out/**": true,
      "**/dist/**": true,
      "**/build/**": true,
      "**/prisma/migrations/**": true,
    },

    // ===== 调试设置 =====
    "debug.javascript.autoAttachFilter": "smart",
    "debug.javascript.codelens.npmScripts": "all",

    // ===== Git 设置 =====
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    "git.enableSmartCommit": true,
    "git.confirmSync": false,

    // ===== 终端设置 =====
    "terminal.integrated.cwd": "${workspaceFolder}",
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.defaultProfile.osx": "zsh",
    "terminal.integrated.profiles.osx": {
      "zsh": {
        "path": "/bin/zsh",
        "args": ["-l"],
      },
      "bash": {
        "path": "/bin/bash",
        "args": ["-l"],
      },
      "fish": {
        "path": "/opt/homebrew/bin/fish",
        "args": ["-l"],
      },
    },

    // ===== 委托设置 =====
    "task.autoDetect": "on",
    "task.showDecorations": true,

    // ===== 扩展设置 =====
    "extensions.ignoreRecommendations": false,
    "extensions.showRecommendationsOnlyOnDemand": false,
    "cSpell.words": ["sonner"],

    // ===== i18n Ally 配置 =====
    "i18n-ally.localesPaths": ["messages"],
    "i18n-ally.keystyle": "nested",
    "i18n-ally.pathMatcher": "{locale}/{namespaces}.json",
    "i18n-ally.namespace": true,
    "i18n-ally.enabledParsers": ["json"],
    "i18n-ally.sourceLanguage": "zh",
    "i18n-ally.displayLanguage": "en",
    "i18n-ally.enabledFrameworks": ["next-intl", "react"],
    "i18n-ally.extract.autoDetect": true,
    "i18n-ally.extract.keygenStyle": "camelCase",
    "i18n-ally.sortKeys": true,
    "i18n-ally.annotations.inPlace": true,
  },
  "extensions": {
    "recommendations": [
      "esbenp.prettier-vscode",
      "dbaeumer.vscode-eslint",
      "ms-vscode.vscode-typescript-next",
      "bradlc.vscode-tailwindcss",
      "formulahendry.auto-rename-tag",
      "christian-kohler.path-intellisense",
      "dsznajder.es7-react-js-snippets",
      "eamodio.gitlens",
      "orta.vscode-jest",
      "ms-playwright.playwright",
      "streetsidesoftware.code-spell-checker",
      "usernamehw.errorlens",
      "pkief.material-icon-theme",
      "mikestead.dotenv",
      "lokalise.i18n-ally",
    ],
    "unwantedRecommendations": [
      "ms-vscode.vscode-typescript",
      "hookyqr.beautify",
    ],
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Start Development Server",
        "type": "shell",
        "command": "npm run dev",
        "group": {
          "kind": "build",
          "isDefault": true,
        },
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared",
        },
        "problemMatcher": [],
      },
    ],
  },
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Next.js: debug server-side",
        "type": "node-terminal",
        "request": "launch",
        "command": "npm run dev",
        "serverReadyAction": {
          "pattern": "started server on .+, url: (https?://.+)",
          "uriFormat": "%s",
          "action": "debugWithChrome",
        },
      },
    ],
  },
}
