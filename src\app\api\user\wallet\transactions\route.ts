import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问，请先登录',
    serverError: '服务器内部错误',
  },
  en: {
    unauthorized: 'Unauthorized access, please login first',
    serverError: 'Internal server error',
  },
};

// 强制动态渲染
export const dynamic = 'force-dynamic';

// GET /api/user/wallet/transactions - 获取用户钱包交易记录
export async function GET(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: t.unauthorized },
        { status: 401 },
      );
    }

    // 2. 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
    const limit = Math.min(
      50,
      Math.max(1, parseInt(searchParams.get('limit') || '20', 10)),
    );
    const type = searchParams.get('type'); // 交易类型筛选
    const skip = (page - 1) * limit;

    // 3. 构建查询条件
    const where: any = {
      userId: session.user.id,
    };

    if (
      type &&
      [
        'DEPOSIT',
        'WITHDRAW',
        'TASK_FEE',
        'COMMISSION',
        'MEMBERSHIP',
        'REFUND',
      ].includes(type)
    ) {
      where.type = type;
    }

    // 4. 获取交易记录
    const [transactions, total] = await Promise.all([
      prisma.walletTransaction.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          type: true,
          amount: true,
          status: true,
          description: true,
          reference: true,
          depositMethod: true,
          withdrawMethod: true,
          createdAt: true,
          completedAt: true,
        },
      }),
      prisma.walletTransaction.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}
