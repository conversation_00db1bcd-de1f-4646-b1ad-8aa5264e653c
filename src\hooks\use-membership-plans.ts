import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { ApiResponse } from '@/types/rates';

// 会员套餐接口
export interface MembershipPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  maxTasks: number | null;
  taskTypes: string[];
  platformRate: number;
  whitelistSlots: number;
  supportLevel: string;
  features: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 获取会员套餐列表
export function useMembershipPlans() {
  return useQuery({
    queryKey: ['membership-plans'],
    queryFn: async (): Promise<MembershipPlan[]> => {
      const response = await fetch('/api/user/membership-plans');
      const data: ApiResponse<MembershipPlan[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get membership plans');
      }

      return data.data || [];
    },
  });
}

// 购买/升级会员套餐（返回支付信息）
export function useUpgradeMembership() {
  const queryClient = useQueryClient();
  const t = useTranslations('Membership');

  return useMutation({
    mutationFn: async (data: {
      planId: string;
      planName: string;
    }): Promise<any> => {
      const response = await fetch('/api/user/membership/upgrade', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || t('hooks.upgrade.error'));
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      if (data.needPayment) {
        toast.success(t('hooks.upgrade.confirmed'), {
          description: t('hooks.upgrade.confirmedDesc', {
            planName: variables.planName,
          }),
        });
      } else {
        queryClient.invalidateQueries({ queryKey: ['user-membership'] });
        toast.success(t('hooks.upgrade.success'), {
          description: t('hooks.upgrade.successDesc', {
            planName: variables.planName,
          }),
        });
      }
    },
    onError: (error: Error) => {
      toast.error(t('hooks.upgrade.failed'), {
        description: error.message,
      });
    },
  });
}

// 会员购买支付
export function usePurchaseMembership() {
  const queryClient = useQueryClient();
  const t = useTranslations('Membership');

  return useMutation({
    mutationFn: async (data: {
      planId: string;
      planName: string;
      paymentMethod: string;
      action: 'upgrade' | 'renew';
    }): Promise<any> => {
      const response = await fetch('/api/user/membership/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || t('hooks.purchase.error'));
      }

      return result.data;
    },
    onSuccess: (data, variables) => {
      toast.success(t('hooks.purchase.success'), {
        description: t('hooks.purchase.successDesc', {
          action:
            variables.action === 'upgrade'
              ? t('hooks.purchase.upgrade')
              : t('hooks.purchase.renew'),
          planName: variables.planName,
        }),
      });
    },
    onError: (error: Error) => {
      toast.error(t('hooks.purchase.failed'), {
        description: error.message,
      });
    },
  });
}

// 续费会员套餐（返回支付信息）
export function useRenewMembership() {
  const queryClient = useQueryClient();
  const t = useTranslations('Membership');

  return useMutation({
    mutationFn: async (): Promise<any> => {
      const response = await fetch('/api/user/membership/renew', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || t('hooks.renew.error'));
      }

      return result.data;
    },
    onSuccess: data => {
      if (data.needPayment) {
        toast.success(t('hooks.renew.confirmed'), {
          description: t('hooks.renew.confirmedDesc', {
            planName: data.planName,
          }),
        });
      } else {
        queryClient.invalidateQueries({ queryKey: ['user-membership'] });
        toast.success(t('hooks.renew.success'), {
          description: t('hooks.renew.successDesc'),
        });
      }
    },
    onError: (error: Error) => {
      toast.error(t('hooks.renew.failed'), {
        description: error.message,
      });
    },
  });
}
