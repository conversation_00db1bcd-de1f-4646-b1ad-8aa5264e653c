'use client';

import {
  <PERSON>Circle,
  DollarSign,
  AlertTriangle,
  Package,
  MapPin,
  Clock,
  Banknote,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

interface AcceptTaskDialogProps {
  children?: React.ReactNode;
  task: {
    id: string;
    title?: string;
    productDescription: string;
    productUrl: string;
    quantity: number;
    unitPrice: number;
    totalAmount: number;
    recipientName: string;
    recipientPhone: string;
    shippingAddress: string;
    listingTime: string;
    expiresAt?: string;
    platform: string;
    category: string;
    commission: number;
    deposit: number;
  };
  onAccept?: (taskId: string) => void;
}

export function AcceptTaskDialog({
  children,
  task,
  onAccept,
}: AcceptTaskDialogProps) {
  const t = useTranslations('MyAcceptedTasks');
  const tMessages = useTranslations('Messages');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleAcceptTask = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/user/tasks/${task.id}/accept`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || t('error.accept'));
      }

      toast.success(tMessages('success.accept'), {
        description: t('acceptTask.successDescription'),
        duration: 6000,
      });

      onAccept?.(task.id);
      setIsOpen(false);

      // 刷新页面或跳转到我的委托页面
      window.location.href = '/my-tasks/accepted';
    } catch (error) {
      toast.error(tMessages('error.accept'), {
        description:
          error instanceof Error ? error.message : tMessages('error.network'),
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return t('acceptTask.notSet');
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CheckCircle className='h-5 w-5 text-green-600' />
            {t('acceptTask.title')}
          </DialogTitle>
          <DialogDescription>{t('acceptTask.description')}</DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* 委托基本信息 */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-lg'>
              {t('acceptTask.taskInfo')}
            </h3>

            <div className='p-4 bg-blue-50 border border-blue-200 rounded-lg'>
              <div className='space-y-3'>
                {task.title && (
                  <div>
                    <Label className='text-sm font-medium text-blue-900'>
                      {t('acceptTask.taskTitle')}
                    </Label>
                    <p className='text-blue-800 mt-1'>{task.title}</p>
                  </div>
                )}

                <div>
                  <Label className='text-sm font-medium text-blue-900'>
                    {t('acceptTask.productDescription')}
                  </Label>
                  <p className='text-blue-800 mt-1 line-clamp-2'>
                    {task.productDescription}
                  </p>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <Label className='text-sm font-medium text-blue-900'>
                      {t('acceptTask.platform')}
                    </Label>
                    <p className='text-blue-800 mt-1'>{task.platform}</p>
                  </div>
                  <div>
                    <Label className='text-sm font-medium text-blue-900'>
                      {t('acceptTask.category')}
                    </Label>
                    <p className='text-blue-800 mt-1'>{task.category}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 购买要求 */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-lg flex items-center gap-2'>
              <Package className='h-5 w-5' />
              {t('acceptTask.purchaseRequirements')}
            </h3>

            <div className='grid grid-cols-2 gap-4'>
              <div className='p-3 border rounded-lg'>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.quantity')}
                </Label>
                <div className='text-lg font-semibold mt-1'>
                  {task.quantity} {t('acceptTask.pieces')}
                </div>
              </div>
              <div className='p-3 border rounded-lg'>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.unitPrice')}
                </Label>
                <div className='text-lg font-semibold mt-1'>
                  {formatCurrency(task.unitPrice)}
                </div>
              </div>
              <div className='p-3 border rounded-lg'>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.totalPrice')}
                </Label>
                <div className='text-lg font-semibold text-blue-600 mt-1'>
                  {formatCurrency(task.totalAmount)}
                </div>
              </div>
              <div className='p-3 border rounded-lg'>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.listingDuration')}
                </Label>
                <div className='text-lg font-semibold mt-1'>
                  {task.listingTime} {t('acceptTask.hours')}
                </div>
              </div>
            </div>
          </div>

          {/* 收货信息 */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-lg flex items-center gap-2'>
              <MapPin className='h-5 w-5' />
              {t('acceptTask.shippingInfo')}
            </h3>

            <div className='p-4 bg-gray-50 rounded-lg space-y-2'>
              <div className='flex justify-between'>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.recipient')}
                </Label>
                <span className='font-medium'>{task.recipientName}</span>
              </div>
              <div className='flex justify-between'>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.phone')}
                </Label>
                <span className='font-medium'>{task.recipientPhone}</span>
              </div>
              <div>
                <Label className='text-sm text-muted-foreground'>
                  {t('acceptTask.address')}
                </Label>
                <p className='font-medium mt-1'>{task.shippingAddress}</p>
              </div>
            </div>
          </div>

          {/* 时间要求 */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-lg flex items-center gap-2'>
              <Clock className='h-5 w-5' />
              {t('acceptTask.timeRequirements')}
            </h3>

            <div className='p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>
              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <Label className='text-sm text-yellow-800 dark:text-yellow-100'>
                    {t('acceptTask.deadline')}
                  </Label>
                  <span className='font-medium text-yellow-900'>
                    {formatDateTime(task.expiresAt)}
                  </span>
                </div>
                <div className='text-xs text-yellow-700'>
                  {t('acceptTask.deadlineNote')}
                </div>
              </div>
            </div>
          </div>

          {/* 费用明细 */}
          <div className='space-y-4'>
            <h3 className='font-semibold text-lg flex items-center gap-2'>
              <Banknote className='h-5 w-5' />
              {t('acceptTask.costBreakdown')}
            </h3>

            <div className='p-4 border rounded-lg space-y-3'>
              <div className='flex justify-between text-sm'>
                <span className='text-muted-foreground'>
                  {t('acceptTask.commission')}
                </span>
                <span className='font-semibold text-green-600'>
                  {formatCurrency(task.commission)}
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-muted-foreground'>
                  {t('acceptTask.deposit')}
                </span>
                <span className='font-semibold text-orange-600'>
                  {formatCurrency(task.deposit)}
                </span>
              </div>
              <Separator />
              <div className='flex justify-between'>
                <span className='font-medium'>
                  {t('acceptTask.netCommission')}
                </span>
                <span className='text-lg font-bold text-green-600'>
                  {formatCurrency(task.commission)}
                </span>
              </div>
              <div className='text-xs text-muted-foreground'>
                * {t('acceptTask.depositNote')}
              </div>
            </div>
          </div>

          {/* 重要提醒 */}
          <div className='p-4 bg-red-50 border border-red-200 rounded-lg'>
            <div className='flex items-start gap-2'>
              <AlertTriangle className='h-5 w-5 text-red-600 mt-0.5 flex-shrink-0' />
              <div>
                <h4 className='font-medium text-red-800 mb-2'>
                  {t('acceptTask.importantReminder')}
                </h4>
                <ul className='text-sm text-red-700 space-y-1'>
                  <li>• {t('acceptTask.reminder1')}</li>
                  <li>
                    •{' '}
                    {t('acceptTask.reminder2', {
                      amount: formatCurrency(task.deposit),
                    })}
                  </li>
                  <li>• {t('acceptTask.reminder3')}</li>
                  <li>• {t('acceptTask.reminder4')}</li>
                  <li>• {t('acceptTask.reminder5')}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className='flex gap-2 pt-2'>
            <Button
              variant='outline'
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
              className='flex-1'
            >
              {t('acceptTask.cancel')}
            </Button>

            <Button
              onClick={handleAcceptTask}
              disabled={isLoading}
              className='flex-1 bg-green-600 hover:bg-green-700'
            >
              {isLoading ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                  {t('acceptTask.accepting')}
                </>
              ) : (
                <>
                  <CheckCircle className='h-4 w-4 mr-2' />
                  {t('acceptTask.confirmAccept')}
                </>
              )}
            </Button>
          </div>

          {/* 协议条款 */}
          <div className='text-xs text-muted-foreground text-center'>
            {t('acceptTask.agreementText')}
            <span className='text-blue-600 cursor-pointer hover:underline mx-1'>
              {t('acceptTask.serviceAgreement')}
            </span>
            {t('acceptTask.and')}
            <span className='text-blue-600 cursor-pointer hover:underline mx-1'>
              {t('acceptTask.userGuidelines')}
            </span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
