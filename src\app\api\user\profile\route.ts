import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { hashPassword, verifyPassword } from '@/lib/password';

// 获取用户个人信息
export async function GET() {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        image: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: user.id,
        nickname: user.name || '未设置',
        email: user.email || '',
        emailVerified: user.emailVerified ? new Date(user.emailVerified) : null,
        avatar: user.image || '',
        lastPasswordChange: user.updatedAt, // 使用updatedAt作为最后修改时间
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 更新用户昵称
const updateNicknameSchema = z.object({
  nickname: z
    .string()
    .min(2, '昵称至少需要2个字符')
    .max(20, '昵称最多20个字符'),
});

export async function PATCH(request: NextRequest) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const body = await request.json();
    const { type } = body;

    if (type === 'nickname') {
      const validation = updateNicknameSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            success: false,
            message: '数据验证失败',
            details: validation.error.issues.map(issue => issue.message),
          },
          { status: 400 },
        );
      }

      const { nickname } = validation.data;

      // 更新用户昵称
      const updatedUser = await prisma.user.update({
        where: { id: session.user.id },
        data: { name: nickname },
        select: {
          id: true,
          name: true,
          updatedAt: true,
        },
      });

      return NextResponse.json({
        success: true,
        message: '昵称修改成功',
        data: {
          nickname: updatedUser.name,
          updatedAt: updatedUser.updatedAt,
        },
      });
    }

    return NextResponse.json(
      { success: false, message: '未知的更新类型' },
      { status: 400 },
    );
  } catch (error) {
    console.error('更新用户信息失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
