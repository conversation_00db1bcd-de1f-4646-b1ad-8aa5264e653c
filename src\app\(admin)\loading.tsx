import React from 'react';

/**
 * 管理员区域加载组件
 * 在管理员页面加载时显示
 */
export default function AdminLoading() {
  return (
    <div className='flex min-h-screen items-center justify-center'>
      <div className='flex flex-col items-center space-y-4'>
        {/* 管理员专用加载动画 */}
        <div className='relative'>
          <div className='h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-red-600'></div>
          <div className='absolute inset-0 flex items-center justify-center'>
            <div className='h-6 w-6 rounded-full bg-red-100'></div>
          </div>
        </div>

        {/* 加载文本 */}
        <div className='text-center'>
          <h2 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>
            加载管理面板...
          </h2>
          <p className='text-sm text-gray-500 dark:text-gray-400'>
            正在验证管理员权限并准备数据
          </p>
        </div>

        {/* 加载进度指示器 */}
        <div className='w-48 bg-gray-200 rounded-full h-2 dark:bg-gray-700'>
          <div
            className='bg-red-600 h-2 rounded-full animate-pulse'
            style={{ width: '60%' }}
          ></div>
        </div>
      </div>
    </div>
  );
}
