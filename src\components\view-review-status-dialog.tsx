'use client';

import {
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  RotateCcw,
  Truck,
  Loader2,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ReactNode, useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ViewReviewStatusDialogProps {
  children: ReactNode;
  taskId?: string;
  taskStatus: string;
  orderNumber?: string;
  orderScreenshot?: string;
  trackingNumber?: string;
  logisticsScreenshots?: string[];
  reviewRejectReason?: string;
  reviewedAt?: string;
  logisticsReviewDeadline?: string;
  onResubmit?: () => void;
}

interface TrackingData {
  success: boolean;
  data?: any;
  error?: string;
  isTemporary?: boolean; // 新增属性，用于判断是否是临时无物流信息
}

export function ViewReviewStatusDialog({
  children,
  taskId,
  taskStatus,
  orderNumber,
  orderScreenshot,
  trackingNumber,
  logisticsScreenshots = [],
  reviewRejectReason,
  reviewedAt,
  logisticsReviewDeadline,
  onResubmit,
}: ViewReviewStatusDialogProps) {
  const t = useTranslations('my-accepted-tasks');
  const [isOpen, setIsOpen] = useState(false);
  const [trackingData, setTrackingData] = useState<TrackingData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 获取物流数据
  const fetchTrackingData = useCallback(async () => {
    if (!taskId || !trackingNumber) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/tasks/${taskId}/logistics/register`);
      const result = await response.json();

      if (response.ok) {
        if (result.success) {
          // 成功获取到物流数据
          setTrackingData(result);
        } else if (result.isTemporary) {
          // 临时无物流信息（如"暂无物流信息"）
          setTrackingData({
            success: false,
            error: result.error || t('noLogisticsInfo'),
            isTemporary: true,
          });
        } else {
          // 其他错误
          setTrackingData({
            success: false,
            error: result.error || t('fetchLogisticsFailed'),
          });
        }
      } else {
        setTrackingData({
          success: false,
          error: result.error || t('fetchLogisticsFailed'),
        });
      }
    } catch (error) {
      setTrackingData({
        success: false,
        error: t('networkError'),
      });
    } finally {
      setIsLoading(false);
    }
  }, [taskId, trackingNumber, t]);

  // 当对话框打开且有物流单号时获取数据
  useEffect(() => {
    if (
      isOpen &&
      trackingNumber &&
      (taskStatus === 'PENDING_DELIVERY' || taskStatus === 'COMPLETED')
    ) {
      fetchTrackingData();
    }
  }, [isOpen, trackingNumber, taskId, taskStatus, fetchTrackingData]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch {
      return dateString;
    }
  };

  const formatTrackingStatus = (status: string) => {
    const statusMap: Record<string, { label: string; color: string }> = {
      NotFound: { label: t('status.notFound'), color: 'text-gray-600' },
      InfoReceived: { label: t('status.infoReceived'), color: 'text-blue-600' },
      InTransit: { label: t('status.inTransit'), color: 'text-yellow-600' },
      Expired: { label: t('status.expired'), color: 'text-red-600' },
      AvailableForPickup: {
        label: t('status.availableForPickup'),
        color: 'text-green-600',
      },
      OutForDelivery: {
        label: t('status.outForDelivery'),
        color: 'text-blue-600',
      },
      DeliveryFailure: {
        label: t('status.deliveryFailure'),
        color: 'text-red-600',
      },
      Delivered: { label: t('status.delivered'), color: 'text-green-600' },
      Exception: { label: t('status.exception'), color: 'text-red-600' },
    };

    return (
      statusMap[status] || {
        label: status || t('status.unknown'),
        color: 'text-gray-600',
      }
    );
  };

  // 获取审核状态信息
  const getReviewStatus = () => {
    switch (taskStatus) {
      case 'PENDING_REVIEW':
        return {
          icon: <Clock className='h-5 w-5 text-yellow-500' />,
          title: t('reviewStatus.pending.title'),
          description: t('reviewStatus.pending.description'),
        };
      case 'REVIEW_REJECTED':
        return {
          icon: <XCircle className='h-5 w-5 text-red-500' />,
          title: t('reviewStatus.rejected.title'),
          description:
            reviewRejectReason || t('reviewStatus.rejected.description'),
        };
      case 'PENDING_DELIVERY':
        return {
          icon: <CheckCircle className='h-5 w-5 text-green-500' />,
          title: t('reviewStatus.approved.title'),
          description: t('reviewStatus.approved.description'),
        };
      case 'COMPLETED':
        return {
          icon: <CheckCircle className='h-5 w-5 text-green-500' />,
          title: t('reviewStatus.completed.title'),
          description: t('reviewStatus.completed.description'),
        };
      default:
        return {
          icon: <AlertCircle className='h-5 w-5 text-gray-500' />,
          title: t('reviewStatus.unknown.title'),
          description: t('reviewStatus.unknown.description'),
        };
    }
  };

  const reviewStatus = getReviewStatus();
  const trackInfo = trackingData?.data?.track_info;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-lg max-h-[80vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            {t('reviewStatus.title')}
          </DialogTitle>
          <DialogDescription>{t('reviewStatus.description')}</DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 审核状态 */}
          <div className='flex items-center gap-3 p-3 bg-muted/30 rounded-lg'>
            {reviewStatus.icon}
            <div className='flex-1'>
              <div className='font-medium'>{reviewStatus.title}</div>
              <div className='text-sm text-muted-foreground'>
                {reviewStatus.description}
              </div>
            </div>
          </div>

          {/* 物流信息 */}
          {trackingNumber && (
            <div className='space-y-3'>
              <div className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                {t('reviewStatus.logisticsInfo')}
              </div>

              {/* 物流单号 */}
              <div className='flex items-center gap-2 text-sm'>
                <Truck className='h-4 w-4 text-muted-foreground' />
                <span className='text-muted-foreground'>
                  {t('viewOrder.trackingNumber')}:
                </span>
                <span className='font-mono'>{trackingNumber}</span>
              </div>

              {/* 审核通过前的提示 */}
              {taskStatus === 'PENDING_REVIEW' && (
                <div className='text-sm text-muted-foreground p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800'>
                  <div className='flex items-center gap-2 mb-1'>
                    <Clock className='h-4 w-4 text-blue-600' />
                    <span className='font-medium text-blue-700 dark:text-blue-300'>
                      {t('reviewStatus.waitingApproval')}
                    </span>
                  </div>
                  <div className='text-blue-600 dark:text-blue-400'>
                    {t('reviewStatus.approvalNote')}
                  </div>
                </div>
              )}

              {/* 审核通过后显示物流数据 */}
              {(taskStatus === 'PENDING_DELIVERY' ||
                taskStatus === 'COMPLETED') && (
                <>
                  {/* 加载状态 */}
                  {isLoading && (
                    <div className='flex items-center justify-center py-2'>
                      <Loader2 className='h-4 w-4 animate-spin mr-2' />
                      <span className='text-sm'>{t('messages.loading')}</span>
                    </div>
                  )}

                  {/* 物流基本信息 */}
                  {!isLoading && trackingData?.success && trackInfo && (
                    <>
                      <div className='grid grid-cols-2 gap-4 text-sm'>
                        <div>
                          <span className='text-gray-600 dark:text-gray-400'>
                            {t('reviewStatus.carrier')}
                          </span>
                          <div className='font-medium'>
                            {trackInfo.tracking?.providers?.[0]?.provider
                              ?.name || t('reviewStatus.unknown')}
                          </div>
                        </div>
                        <div>
                          <span className='text-gray-600 dark:text-gray-400'>
                            {t('reviewStatus.currentStatus')}
                          </span>
                          <div
                            className={`font-medium ${formatTrackingStatus(trackInfo.latest_status?.status).color}`}
                          >
                            {
                              formatTrackingStatus(
                                trackInfo.latest_status?.status,
                              ).label
                            }
                          </div>
                        </div>
                        <div>
                          <span className='text-gray-600 dark:text-gray-400'>
                            {t('reviewStatus.estimatedDelivery')}
                          </span>
                          <div className='font-medium'>
                            {trackInfo.time_metrics?.estimated_delivery_date
                              ?.from
                              ? formatDate(
                                  trackInfo.time_metrics.estimated_delivery_date
                                    .from,
                                ).split(' ')[0]
                              : t('reviewStatus.noInfo')}
                          </div>
                        </div>
                        <div>
                          <span className='text-gray-600 dark:text-gray-400'>
                            {t('reviewStatus.lastUpdate')}
                          </span>
                          <div className='font-medium'>
                            {trackInfo.latest_event?.time_utc
                              ? formatDate(
                                  trackInfo.latest_event.time_utc,
                                ).split(' ')[0]
                              : t('reviewStatus.noInfo')}
                          </div>
                        </div>
                      </div>

                      {/* 物流轨迹 */}
                      {trackInfo.tracking?.providers?.[0]?.events &&
                        trackInfo.tracking.providers[0].events.length > 0 && (
                          <div className='space-y-2'>
                            <div className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                              {t('reviewStatus.trackingHistory')}
                            </div>
                            <div className='space-y-2 max-h-32 overflow-y-auto'>
                              {trackInfo.tracking.providers[0].events
                                .slice(0, 5)
                                .map((event: any, index: number) => (
                                  <div
                                    key={index}
                                    className='flex gap-2 p-2 rounded border bg-muted/30'
                                  >
                                    <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                                    <div className='flex-1 min-w-0'>
                                      <div className='text-xs text-gray-500 dark:text-gray-400'>
                                        {formatDate(
                                          event.time_iso || event.time_utc,
                                        )}
                                      </div>
                                      <div className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                                        {event.description}
                                      </div>
                                      {event.location && (
                                        <div className='text-xs text-gray-500 dark:text-gray-400'>
                                          {event.location}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}
                    </>
                  )}

                  {/* 错误状态 */}
                  {!isLoading && trackingData && !trackingData.success && (
                    <div className='text-sm text-muted-foreground p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800'>
                      <div className='font-medium text-orange-800 dark:text-orange-200 mb-1'>
                        {trackingData.error}
                      </div>
                      {trackingData.error?.includes(
                        t('reviewStatus.noLogisticsInfo'),
                      ) && (
                        <div className='text-xs text-orange-600 dark:text-orange-400 space-y-1'>
                          <p>{t('reviewStatus.errorReasons.title')}</p>
                          <ul className='list-disc list-inside space-y-0.5 ml-2'>
                            <li>{t('reviewStatus.errorReasons.reason1')}</li>
                            <li>{t('reviewStatus.errorReasons.reason2')}</li>
                            <li>{t('reviewStatus.errorReasons.reason3')}</li>
                          </ul>
                          <p className='mt-1'>
                            {t('reviewStatus.errorReasons.suggestion')}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 未获取数据时显示简化信息 */}
                  {!isLoading && !trackingData && (
                    <div className='text-sm text-muted-foreground p-2 bg-blue-50 dark:bg-blue-900/20 rounded'>
                      {t('reviewStatus.clickRefresh')}
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          <div className='flex gap-2 pt-2'>
            {taskStatus === 'REVIEW_REJECTED' && onResubmit && (
              <Button onClick={onResubmit} className='flex-1'>
                <RotateCcw className='h-4 w-4 mr-2' />
                {t('reviewStatus.resubmit')}
              </Button>
            )}
            {trackingNumber &&
              !isLoading &&
              (taskStatus === 'PENDING_DELIVERY' ||
                taskStatus === 'COMPLETED') && (
                <Button
                  variant='outline'
                  onClick={fetchTrackingData}
                  className='flex-1'
                >
                  <Truck className='h-4 w-4 mr-2' />
                  {t('reviewStatus.refreshLogistics')}
                </Button>
              )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
