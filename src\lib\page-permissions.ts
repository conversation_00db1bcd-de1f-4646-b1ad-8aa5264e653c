// Page-specific permission utilities
import { redirect } from 'next/navigation';

import { requireAuth, requireRole, requireMembership } from '@/lib/auth';
import { canAccessPage, PERMISSIONS, hasPermission } from '@/lib/rbac';

// 页面权限检查装饰器
export async function withPagePermission(
  requiredPermissions: string[],
  redirectPath: string = '/dashboard',
) {
  const session = await requireAuth();
  const userRole = session.user.role || 'USER';

  const hasAccess = requiredPermissions.some(permission =>
    hasPermission(userRole, permission),
  );

  if (!hasAccess) {
    redirect(redirectPath);
  }

  return session;
}

// 特定页面的权限检查函数
export async function requireDashboardAccess() {
  return await withPagePermission([PERMISSIONS.VIEW_DASHBOARD]);
}

export async function requireTasksAccess() {
  return await withPagePermission([PERMISSIONS.VIEW_TASKS]);
}

export async function requirePublishAccess() {
  return await withPagePermission([PERMISSIONS.PUBLISH_TASK]);
}

export async function requireWalletAccess() {
  return await withPagePermission([PERMISSIONS.VIEW_WALLET]);
}

export async function requireWhitelistAccess() {
  return await withPagePermission(
    [PERMISSIONS.MANAGE_WHITELIST],
    '/membership',
  );
}

export async function requirePremiumAccess() {
  return await withPagePermission([PERMISSIONS.ACCESS_PREMIUM], '/membership');
}

export async function requireAdminAccess() {
  return await requireRole(['ADMIN']);
}

// 会员功能权限检查
export async function requirePremiumMembership() {
  const session = await requireAuth();

  // 检查用户角色
  if (session.user.role === 'ADMIN') {
    return session; // 管理员可以访问所有功能
  }

  // 检查会员等级
  return await requireMembership('PREMIUM');
}

// 批量操作权限检查
export async function requireBulkOperationsAccess() {
  const session = await requireAuth();
  const userRole = session.user.role || 'USER';

  if (!hasPermission(userRole, PERMISSIONS.BULK_OPERATIONS)) {
    redirect('/membership');
  }

  return session;
}

// 提现权限检查
export async function requireWithdrawAccess() {
  return await withPagePermission([PERMISSIONS.WITHDRAW_FUNDS]);
}

// 委托管理权限检查
export async function requireTaskManagementAccess() {
  return await withPagePermission([PERMISSIONS.MANAGE_OWN_TASKS]);
}

// 用户管理权限检查（管理员）
export async function requireUserManagementAccess() {
  return await withPagePermission([PERMISSIONS.MANAGE_USERS], '/dashboard');
}

// 系统设置权限检查（管理员）
export async function requireSystemSettingsAccess() {
  return await withPagePermission([PERMISSIONS.MANAGE_SETTINGS], '/dashboard');
}

// 分析数据权限检查（管理员）
export async function requireAnalyticsAccess() {
  return await withPagePermission([PERMISSIONS.VIEW_ANALYTICS], '/dashboard');
}

// 支付管理权限检查（管理员）
export async function requirePaymentManagementAccess() {
  return await withPagePermission([PERMISSIONS.MANAGE_PAYMENTS], '/dashboard');
}

// 动态权限检查（根据路径）
export async function requirePageAccess(pathname: string) {
  const session = await requireAuth();
  const userRole = session.user.role || 'USER';

  if (!canAccessPage(userRole, pathname)) {
    // 根据页面类型重定向到不同位置
    if (pathname.startsWith('/admin')) {
      redirect('/dashboard');
    } else if (pathname.includes('premium') || pathname.includes('whitelist')) {
      redirect('/membership');
    } else {
      redirect('/dashboard');
    }
  }

  return session;
}

// 条件权限检查（不重定向，返回布尔值）
export async function checkPageAccess(pathname: string): Promise<boolean> {
  try {
    const session = await requireAuth();
    const userRole = session.user.role || 'USER';
    return canAccessPage(userRole, pathname);
  } catch {
    return false;
  }
}

// 检查用户是否可以执行特定操作
export async function checkActionPermission(
  permission: string,
): Promise<boolean> {
  try {
    const session = await requireAuth();
    const userRole = session.user.role || 'USER';
    return hasPermission(userRole, permission);
  } catch {
    return false;
  }
}

// 获取用户权限列表
export async function getUserPermissions(): Promise<string[]> {
  try {
    const session = await requireAuth();
    const userRole = session.user.role || 'USER';

    // 导入 getRolePermissions 函数
    const { getRolePermissions } = await import('@/lib/rbac');
    return [...getRolePermissions(userRole)];
  } catch {
    return [];
  }
}

// 权限错误处理
export class PagePermissionError extends Error {
  constructor(
    message: string,
    public page: string,
    public requiredPermissions: string[],
  ) {
    super(message);
    this.name = 'PagePermissionError';
  }
}

// 权限检查中间件工厂
export function createPermissionMiddleware(
  requiredPermissions: string[],
  redirectPath: string = '/dashboard',
) {
  return async function () {
    try {
      return await withPagePermission(requiredPermissions, redirectPath);
    } catch (error) {
      throw new PagePermissionError(
        `Access denied. Required permissions: ${requiredPermissions.join(', ')}`,
        redirectPath,
        requiredPermissions,
      );
    }
  };
}

// 导出常用的权限检查中间件
export const dashboardPermission = createPermissionMiddleware([
  PERMISSIONS.VIEW_DASHBOARD,
]);
export const tasksPermission = createPermissionMiddleware([
  PERMISSIONS.VIEW_TASKS,
]);
export const walletPermission = createPermissionMiddleware([
  PERMISSIONS.VIEW_WALLET,
]);
export const whitelistPermission = createPermissionMiddleware(
  [PERMISSIONS.MANAGE_WHITELIST],
  '/membership',
);
export const adminPermission = createPermissionMiddleware(
  [PERMISSIONS.ADMIN_DASHBOARD],
  '/dashboard',
);
