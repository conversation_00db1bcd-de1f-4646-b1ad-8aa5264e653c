'use client';

import { ArrowRight, RotateCcw } from 'lucide-react';

import { cn } from '@/lib/utils';

interface RefundGoLogoProps {
  variant?: 'full' | 'compact' | 'icon-only' | 'static';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  showTagline?: boolean;
  className?: string;
  darkMode?: boolean;
  vipLevel?: 'basic' | 'premium' | 'vip' | 'enterprise';
  forceWhiteText?: boolean;
}

export function RefundGoLogo({
  variant = 'full',
  size = 'md',
  animated = true,
  showTagline = true,
  className,
  darkMode = false,
  vipLevel = 'basic',
  forceWhiteText = false,
}: RefundGoLogoProps) {
  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'px-3 py-2 gap-2',
      icon: 'w-6 h-6',
      iconInner: 'w-3 h-3',
      accent: 'w-2.5 h-2.5 -top-0.5 -right-0.5',
      accentInner: 'w-1.5 h-1.5',
      text: 'text-lg',
      tagline: 'text-xs',
    },
    md: {
      container: 'px-4 py-3 gap-3',
      icon: 'w-10 h-10',
      iconInner: 'w-5 h-5',
      accent: 'w-3.5 h-3.5 -top-1 -right-1',
      accentInner: 'w-2 h-2',
      text: 'text-2xl',
      tagline: 'text-sm',
    },
    lg: {
      container: 'px-6 py-4 gap-4',
      icon: 'w-12 h-12',
      iconInner: 'w-6 h-6',
      accent: 'w-4 h-4 -top-1 -right-1',
      accentInner: 'w-2.5 h-2.5',
      text: 'text-3xl',
      tagline: 'text-base',
    },
  };

  const config = sizeConfig[size];

  // VIP level color configurations
  const vipColors = {
    basic: {
      icon: 'from-blue-500 to-blue-600',
      iconHover: 'group-hover:from-blue-600 group-hover:to-blue-700',
      accent: 'bg-green-500',
      text: forceWhiteText ? 'text-white' : (darkMode ? 'text-white' : 'text-gray-900'),
      textBlue: forceWhiteText ? 'text-blue-200' : (darkMode ? 'text-blue-300' : 'text-blue-600'),
    },
    premium: {
      icon: 'from-purple-500 to-purple-600',
      iconHover: 'group-hover:from-purple-600 group-hover:to-purple-700',
      accent: 'bg-yellow-500',
      text: forceWhiteText ? 'text-white' : (darkMode ? 'text-white' : 'text-gray-900'),
      textBlue: forceWhiteText ? 'text-purple-200' : (darkMode ? 'text-purple-300' : 'text-purple-600'),
    },
    vip: {
      icon: 'from-yellow-500 to-orange-500',
      iconHover: 'group-hover:from-yellow-600 group-hover:to-orange-600',
      accent: 'bg-red-500',
      text: forceWhiteText ? 'text-white' : (darkMode ? 'text-white' : 'text-gray-900'),
      textBlue: forceWhiteText ? 'text-yellow-200' : (darkMode ? 'text-yellow-300' : 'text-orange-600'),
    },
    enterprise: {
      icon: 'from-gray-700 to-gray-900',
      iconHover: 'group-hover:from-gray-800 group-hover:to-black',
      accent: 'bg-emerald-500',
      text: forceWhiteText ? 'text-white' : (darkMode ? 'text-white' : 'text-gray-900'),
      textBlue: forceWhiteText ? 'text-gray-200' : (darkMode ? 'text-gray-300' : 'text-gray-700'),
    },
  };

  const colors = vipColors[vipLevel];

  // Icon-only variant
  if (variant === 'icon-only') {
    return (
      <div className={cn('relative', className)}>
        <div
          className={cn(
            config.icon,
            'bg-gradient-to-br rounded-xl flex items-center justify-center',
            colors.icon,
            animated && 'group-hover:shadow-lg transition-all duration-300',
            animated && colors.iconHover,
          )}
        >
          <RotateCcw
            className={cn(
              config.iconInner,
              'text-white',
              animated && 'group-hover:-rotate-45 transition-transform duration-300',
            )}
          />
        </div>
        <div
          className={cn(
            config.accent,
            'absolute rounded-full flex items-center justify-center',
            colors.accent,
            animated && 'group-hover:scale-150 transition-transform duration-300',
          )}
        >
          <ArrowRight className={cn(config.accentInner, 'text-white')} />
        </div>
      </div>
    );
  }

  // Static variant (no animations)
  if (variant === 'static' || !animated) {
    return (
      <div className={cn('refund-go-logo refund-go-logo--static flex items-center', config.container, className)}>
        <div className="relative">
          <div
            className={cn(
              config.icon,
              'bg-gradient-to-br rounded-xl flex items-center justify-center',
              colors.icon,
            )}
          >
            <RotateCcw className={cn(config.iconInner, 'text-white')} />
          </div>
          <div
            className={cn(
              config.accent,
              'absolute rounded-full flex items-center justify-center',
              colors.accent,
            )}
          >
            <ArrowRight className={cn(config.accentInner, 'text-white')} />
          </div>
        </div>
        <div className="flex flex-col">
          <div className="flex items-baseline">
            <span className={cn(config.text, 'font-bold', colors.text)}>
              Refund
            </span>
            <span className={cn(config.text, 'font-bold', colors.textBlue)}>
              Go
            </span>
          </div>
          {showTagline && variant === 'full' && (
            <span className={cn(config.tagline, 'font-medium', forceWhiteText ? 'text-gray-200' : (darkMode ? 'text-gray-300' : 'text-gray-500'))}>
              Fast & Easy Refunds
            </span>
          )}
        </div>
      </div>
    );
  }

  // Full animated version (Gradient Animation Version #4)
  return (
    <div
      className={cn(
        'refund-go-logo group flex items-center rounded-xl transition-all duration-300 cursor-pointer overflow-hidden relative',
        // Remove fixed background, make it transparent by default
        'bg-transparent',
        // Add shadow only if not removing backgrounds
        !className?.includes('shadow-none') && 'shadow-lg hover:shadow-xl',
        config.container,
        className,
      )}
    >
      {/* Sliding gradient animation - made more subtle */}
      <div className={cn(
        "gradient-sweep absolute inset-0 bg-gradient-to-r from-transparent to-transparent opacity-30",
        darkMode ? "via-blue-900/20" : "via-blue-50/50",
      )}></div>

      {/* Icon with accent */}
      <div className="relative z-10">
        <div
          className={cn(
            config.icon,
            'icon-bg bg-gradient-to-br rounded-xl flex items-center justify-center group-hover:shadow-lg transition-all duration-300',
            colors.icon,
            colors.iconHover,
          )}
        >
          <RotateCcw
            className={cn(
              config.iconInner,
              'icon-rotate text-white group-hover:-rotate-45 transition-transform duration-300',
            )}
          />
        </div>
        <div
          className={cn(
            config.accent,
            'accent-bg accent-scale absolute rounded-full flex items-center justify-center group-hover:scale-150 transition-transform duration-300',
            colors.accent,
          )}
        >
          <ArrowRight className={cn(config.accentInner, 'text-white')} />
        </div>
      </div>

      {/* Text content */}
      <div className="flex flex-col relative z-10">
        <div className="flex items-baseline">
          <span
            className={cn(
              config.text,
              'text-color-change font-bold transition-colors duration-500',
              colors.text,
              `group-hover:${colors.textBlue.replace('text-', 'text-')}`,
            )}
          >
            Refund
          </span>
          <span
            className={cn(
              config.text,
              'text-color-change font-bold transition-colors duration-500 delay-100',
              colors.textBlue,
              'group-hover:text-green-500',
            )}
          >
            Go
          </span>
        </div>
        {showTagline && variant === 'full' && (
          <span
            className={cn(
              config.tagline,
              'font-medium transition-colors duration-300',
              forceWhiteText ? 'text-gray-200 group-hover:text-gray-100' : (darkMode ? 'text-gray-300 group-hover:text-gray-200' : 'text-gray-500 group-hover:text-gray-700'),
            )}
          >
            Fast & Easy Refunds
          </span>
        )}
      </div>
    </div>
  );
}

// Compact version specifically for navigation
export function RefundGoLogoCompact({
  className,
  animated = false,
  darkMode = false,
}: {
  className?: string;
  animated?: boolean;
  darkMode?: boolean;
}) {
  return (
    <RefundGoLogo
      variant="compact"
      size="sm"
      animated={animated}
      showTagline={false}
      darkMode={darkMode}
      className={cn('refund-go-logo--compact bg-transparent shadow-none hover:shadow-none', className)}
    />
  );
}

// Icon-only version for sidebar with VIP support
export function RefundGoLogoIcon({
  className,
  size = 'sm',
  vipLevel = 'basic',
  darkMode = false,
}: {
  className?: string;
  size?: 'sm' | 'md';
  vipLevel?: 'basic' | 'premium' | 'vip' | 'enterprise';
  darkMode?: boolean;
}) {
  return (
    <RefundGoLogo
      variant="icon-only"
      size={size}
      animated={false}
      vipLevel={vipLevel}
      darkMode={darkMode}
      className={cn('refund-go-logo--icon-only', className)}
    />
  );
}

// Homepage version with enhanced animations (Gradient Animation Version #4)
export function RefundGoLogoHomepage({
  className,
  darkMode = false,
  variant = 'default',
}: {
  className?: string;
  darkMode?: boolean;
  variant?: 'default' | 'navbar';
}) {
  // Additional theme detection for robustness
  const isDarkMode = darkMode || (typeof window !== 'undefined' && document.documentElement.classList.contains('dark'));

  // Navbar-specific sizing to fit within h-16 navigation bar
  const containerClasses = variant === 'navbar'
    ? 'gap-3 px-4 py-2 max-h-12' // Reduced padding and max height for navbar
    : 'gap-4 px-6 py-4'; // Original sizing for other contexts

  const textSize = variant === 'navbar' ? 'text-xl' : 'text-2xl';
  const iconSize = variant === 'navbar' ? 'w-8 h-8' : 'w-10 h-10';
  const iconInnerSize = variant === 'navbar' ? 'w-4 h-4' : 'w-5 h-5';
  const accentSize = variant === 'navbar' ? 'w-3 h-3 -top-0.5 -right-0.5' : 'w-3.5 h-3.5 -top-1 -right-1';
  const accentInnerSize = variant === 'navbar' ? 'w-1.5 h-1.5' : 'w-2 h-2';
  const taglineSize = variant === 'navbar' ? 'text-xs' : 'text-sm';

  return (
    <div
      className={cn(
        'refund-go-logo group flex items-center rounded-xl transition-all duration-300 cursor-pointer overflow-hidden relative',
        containerClasses,
        // Remove fixed background, make it responsive to theme
        isDarkMode ? 'bg-gray-800/50' : 'bg-white/80',
        // Add backdrop blur for modern effect
        'backdrop-blur-sm',
        // Conditional shadow
        !className?.includes('shadow-none') && 'shadow-lg hover:shadow-xl',
        // Ensure proper containment
        'flex-shrink-0',
        className,
      )}
    >
      {/* Sliding gradient animation - Enhanced for homepage */}
      <div className={cn(
        "gradient-sweep absolute inset-0 bg-gradient-to-r from-transparent to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out",
        isDarkMode ? "via-blue-900/30" : "via-blue-50/60",
      )}></div>

      {/* Icon with accent */}
      <div className="relative z-10 flex-shrink-0">
        <div className={cn(
          iconSize,
          "bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:shadow-lg transition-all duration-300",
        )}>
          <RotateCcw className={cn(iconInnerSize, "text-white group-hover:-rotate-45 transition-transform duration-300")} />
        </div>
        <div className={cn(
          "absolute bg-green-500 rounded-full flex items-center justify-center group-hover:scale-150 transition-transform duration-300",
          accentSize,
        )}>
          <ArrowRight className={cn(accentInnerSize, "text-white")} />
        </div>
      </div>

      {/* Text content with enhanced animations */}
      <div className="flex flex-col relative z-10 min-w-0">
        <div className="flex items-baseline">
          <span className={cn(
            textSize,
            "font-bold transition-colors duration-500",
            // Use !important to override any conflicting CSS and robust theme detection
            isDarkMode ? "!text-white group-hover:!text-blue-300" : "!text-gray-900 group-hover:!text-blue-600",
          )}>
            Refund
          </span>
          <span className={cn(
            textSize,
            "font-bold transition-colors duration-500 delay-100",
            // Use !important to override any conflicting CSS and robust theme detection
            isDarkMode ? "!text-blue-300 group-hover:!text-green-400" : "!text-blue-600 group-hover:!text-green-500",
          )}>
            Go
          </span>
        </div>
        <span className={cn(
          taglineSize,
          "font-medium transition-colors duration-300",
          // Use !important to override any conflicting CSS and robust theme detection
          isDarkMode ? "!text-gray-300 group-hover:!text-gray-200" : "!text-gray-500 group-hover:!text-gray-700",
        )}>
          Fast & Easy Refunds
        </span>
      </div>
    </div>
  );
}
