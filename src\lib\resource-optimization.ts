// 资源优化工具库
'use client';

// 资源类型
export type ResourceType =
  | 'script'
  | 'style'
  | 'font'
  | 'image'
  | 'video'
  | 'audio';

// 预加载优先级
export type PreloadPriority = 'high' | 'low' | 'auto';

// 资源加载策略
export type LoadingStrategy = 'eager' | 'lazy' | 'idle' | 'interaction';

// 资源配置接口
interface ResourceConfig {
  url: string;
  type: ResourceType;
  priority?: PreloadPriority;
  strategy?: LoadingStrategy;
  crossOrigin?: 'anonymous' | 'use-credentials';
  integrity?: string;
  media?: string;
  as?: string;
}

// 资源加载状态
interface ResourceState {
  loaded: boolean;
  loading: boolean;
  error: boolean;
  retryCount: number;
}

// 资源管理器类
class ResourceManager {
  private resources = new Map<string, ResourceState>();
  private loadPromises = new Map<string, Promise<void>>();
  private observers = new Map<string, IntersectionObserver>();

  // 预加载资源
  preload(config: ResourceConfig): Promise<void> {
    const { url, type, priority = 'low', crossOrigin, integrity, as } = config;

    // 检查是否已经加载
    if (this.resources.get(url)?.loaded) {
      return Promise.resolve();
    }

    // 检查是否正在加载
    const existingPromise = this.loadPromises.get(url);
    if (existingPromise) {
      return existingPromise;
    }

    const promise = new Promise<void>((resolve, reject) => {
      // 更新状态
      this.resources.set(url, {
        loaded: false,
        loading: true,
        error: false,
        retryCount: 0,
      });

      // 创建预加载链接
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      link.as = as || this.getAsAttribute(type);

      if (priority !== 'auto') {
        link.setAttribute('fetchpriority', priority);
      }

      if (crossOrigin) {
        link.crossOrigin = crossOrigin;
      }

      if (integrity) {
        link.integrity = integrity;
      }

      link.onload = () => {
        this.resources.set(url, {
          loaded: true,
          loading: false,
          error: false,
          retryCount: 0,
        });
        resolve();
      };

      link.onerror = () => {
        const state = this.resources.get(url);
        if (state && state.retryCount < 3) {
          // 重试
          this.resources.set(url, {
            ...state,
            retryCount: state.retryCount + 1,
          });
          setTimeout(
            () => {
              document.head.appendChild(link);
            },
            1000 * (state.retryCount + 1),
          );
        } else {
          this.resources.set(url, {
            loaded: false,
            loading: false,
            error: true,
            retryCount: state?.retryCount || 0,
          });
          reject(new Error(`Failed to preload resource: ${url}`));
        }
      };

      document.head.appendChild(link);
    });

    this.loadPromises.set(url, promise);
    return promise;
  }

  // 懒加载资源
  lazyLoad(config: ResourceConfig, trigger?: HTMLElement): Promise<void> {
    const { url, strategy = 'lazy' } = config;

    switch (strategy) {
      case 'eager':
        return this.preload(config);

      case 'idle':
        return this.loadOnIdle(config);

      case 'interaction':
        return this.loadOnInteraction(config, trigger);

      case 'lazy':
      default:
        return this.loadOnVisible(config, trigger);
    }
  }

  // 空闲时加载
  private loadOnIdle(config: ResourceConfig): Promise<void> {
    return new Promise(resolve => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          this.preload(config).then(resolve);
        });
      } else {
        setTimeout(() => {
          this.preload(config).then(resolve);
        }, 100);
      }
    });
  }

  // 交互时加载
  private loadOnInteraction(
    config: ResourceConfig,
    trigger?: HTMLElement,
  ): Promise<void> {
    return new Promise(resolve => {
      const target = trigger || document;
      const events = ['click', 'touchstart', 'keydown'];

      const loadResource = () => {
        events.forEach(event => {
          target.removeEventListener(event, loadResource);
        });
        this.preload(config).then(resolve);
      };

      events.forEach(event => {
        target.addEventListener(event, loadResource, {
          once: true,
          passive: true,
        });
      });
    });
  }

  // 可见时加载
  private loadOnVisible(
    config: ResourceConfig,
    trigger?: HTMLElement,
  ): Promise<void> {
    return new Promise(resolve => {
      if (!trigger) {
        this.preload(config).then(resolve);
        return;
      }

      const observer = new IntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              observer.disconnect();
              this.preload(config).then(resolve);
            }
          });
        },
        { threshold: 0.1, rootMargin: '50px' },
      );

      observer.observe(trigger);
      this.observers.set(config.url, observer);
    });
  }

  // 获取 as 属性
  private getAsAttribute(type: ResourceType): string {
    switch (type) {
      case 'script':
        return 'script';
      case 'style':
        return 'style';
      case 'font':
        return 'font';
      case 'image':
        return 'image';
      case 'video':
        return 'video';
      case 'audio':
        return 'audio';
      default:
        return 'fetch';
    }
  }

  // 加载脚本
  loadScript(
    url: string,
    options?: {
      async?: boolean;
      defer?: boolean;
      module?: boolean;
      crossOrigin?: string;
      integrity?: string;
    },
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url;

      if (options?.async) script.async = true;
      if (options?.defer) script.defer = true;
      if (options?.module) script.type = 'module';
      if (options?.crossOrigin) script.crossOrigin = options.crossOrigin;
      if (options?.integrity) script.integrity = options.integrity;

      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`));

      document.head.appendChild(script);
    });
  }

  // 加载样式表
  loadStylesheet(
    url: string,
    options?: {
      media?: string;
      crossOrigin?: string;
      integrity?: string;
    },
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = url;

      if (options?.media) link.media = options.media;
      if (options?.crossOrigin) link.crossOrigin = options.crossOrigin;
      if (options?.integrity) link.integrity = options.integrity;

      link.onload = () => resolve();
      link.onerror = () =>
        reject(new Error(`Failed to load stylesheet: ${url}`));

      document.head.appendChild(link);
    });
  }

  // 预连接到域名
  preconnect(url: string, crossOrigin?: boolean): void {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = url;

    if (crossOrigin) {
      link.crossOrigin = 'anonymous';
    }

    document.head.appendChild(link);
  }

  // DNS 预解析
  dnsPrefetch(url: string): void {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = url;
    document.head.appendChild(link);
  }

  // 获取资源状态
  getResourceState(url: string): ResourceState | undefined {
    return this.resources.get(url);
  }

  // 清理资源
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.loadPromises.clear();
  }
}

// 全局资源管理器实例
export const resourceManager = new ResourceManager();

// 字体优化
export class FontOptimizer {
  // 预加载字体
  static preloadFont(url: string, format: string = 'woff2'): Promise<void> {
    return resourceManager.preload({
      url,
      type: 'font',
      priority: 'high',
      crossOrigin: 'anonymous',
      as: 'font',
    });
  }

  // 字体显示策略
  static setFontDisplay(
    fontFamily: string,
    display: 'auto' | 'block' | 'swap' | 'fallback' | 'optional' = 'swap',
  ): void {
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: '${fontFamily}';
        font-display: ${display};
      }
    `;
    document.head.appendChild(style);
  }

  // 字体加载检测
  static async waitForFont(
    fontFamily: string,
    timeout: number = 3000,
  ): Promise<boolean> {
    if (!('fonts' in document)) {
      return false;
    }

    try {
      await Promise.race([
        document.fonts.load(`1em ${fontFamily}`),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Font load timeout')), timeout),
        ),
      ]);
      return true;
    } catch {
      return false;
    }
  }
}

// 图片优化
export class ImageOptimizer {
  // 预加载关键图片
  static preloadImage(
    url: string,
    priority: PreloadPriority = 'high',
  ): Promise<void> {
    return resourceManager.preload({
      url,
      type: 'image',
      priority,
    });
  }

  // 懒加载图片
  static lazyLoadImage(url: string, trigger?: HTMLElement): Promise<void> {
    return resourceManager.lazyLoad(
      {
        url,
        type: 'image',
        strategy: 'lazy',
      },
      trigger,
    );
  }

  // 生成响应式图片 URL
  static generateResponsiveUrls(baseUrl: string, sizes: number[]): string[] {
    return sizes.map(size => `${baseUrl}?w=${size}&q=75`);
  }

  // WebP 支持检测
  static supportsWebP(): Promise<boolean> {
    return new Promise(resolve => {
      const webP = new Image();
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2);
      };
      webP.src =
        'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
  }

  // AVIF 支持检测
  static supportsAVIF(): Promise<boolean> {
    return new Promise(resolve => {
      const avif = new Image();
      avif.onload = avif.onerror = () => {
        resolve(avif.height === 2);
      };
      avif.src =
        'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
    });
  }
}

// 脚本优化
export class ScriptOptimizer {
  // 动态导入模块
  static async importModule<T = any>(modulePath: string): Promise<T> {
    try {
      const importedModule = await import(modulePath);
      return importedModule.default || importedModule;
    } catch (error) {
      console.error(`Failed to import module: ${modulePath}`, error);
      throw error;
    }
  }

  // 条件加载脚本
  static loadScriptIf(condition: boolean, url: string): Promise<void> | null {
    if (!condition) return null;
    return resourceManager.loadScript(url);
  }

  // 用户交互后加载脚本
  static loadScriptOnInteraction(
    url: string,
    trigger?: HTMLElement,
  ): Promise<void> {
    return resourceManager.lazyLoad(
      {
        url,
        type: 'script',
        strategy: 'interaction',
      },
      trigger,
    );
  }
}

// 性能监控
export class PerformanceMonitor {
  // 监控资源加载时间
  static monitorResourceTiming(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
          if (entry.entryType === 'resource') {
            console.log(
              `Resource: ${entry.name}, Duration: ${entry.duration}ms`,
            );
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
    }
  }

  // 获取页面加载指标
  static getPageMetrics(): Record<string, number> {
    const navigation = performance.getEntriesByType(
      'navigation',
    )[0] as PerformanceNavigationTiming;

    return {
      domContentLoaded:
        navigation.domContentLoadedEventEnd -
        navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint:
        performance.getEntriesByName('first-paint')[0]?.startTime || 0,
      firstContentfulPaint:
        performance.getEntriesByName('first-contentful-paint')[0]?.startTime ||
        0,
    };
  }
}

// 清理函数
export function cleanup(): void {
  resourceManager.cleanup();
}

// 在页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanup);
}

export default resourceManager;
