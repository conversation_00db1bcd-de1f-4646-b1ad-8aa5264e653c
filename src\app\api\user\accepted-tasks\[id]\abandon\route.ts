import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/hooks/useEmailTranslation';
import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendTaskCancelledAccepterEmail } from '@/lib/email';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;
    const userId = session.user.id;

    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
            balance: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
            balance: true,
            frozenAmount: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    if (task.accepterId !== userId) {
      return NextResponse.json(
        { error: '只能放弃自己接受的委托' },
        { status: 403 },
      );
    }

    if (task.status !== TaskStatus.IN_PROGRESS) {
      return NextResponse.json(
        { error: '只能放弃进行中的委托' },
        { status: 400 },
      );
    }

    const systemRate = await prisma.systemRate.findFirst();
    if (!systemRate) {
      return NextResponse.json({ error: '系统配置错误' }, { status: 500 });
    }

    const totalPrice = task.unitPrice * task.quantity;
    const depositAmount = totalPrice * (systemRate.depositRatio / 100);

    // 7. 使用事务处理放弃委托的所有操作
    const result = await prisma.$transaction(async tx => {
      // 7.1 从接受者的冻结金额中扣除押金
      await tx.user.update({
        where: { id: userId },
        data: {
          frozenAmount: {
            decrement: depositAmount,
          },
        },
      });

      // 7.2 将押金转给发布者作为补偿
      await tx.user.update({
        where: { id: task.publisherId },
        data: {
          balance: {
            increment: depositAmount,
          },
          totalIncome: {
            increment: depositAmount,
          },
        },
      });

      // 7.3 创建接受者的违约扣款记录
      await tx.walletTransaction.create({
        data: {
          userId,
          type: 'TASK_FEE',
          amount: -depositAmount, // 负数表示扣款
          status: 'COMPLETED',
          description: `放弃委托违约金 - ${taskId}`,
          reference: taskId,
          completedAt: new Date(),
        },
      });

      // 7.4 创建发布者的补偿收入记录
      await tx.walletTransaction.create({
        data: {
          userId: task.publisherId,
          type: 'COMMISSION',
          amount: depositAmount, // 正数表示收入
          status: 'COMPLETED',
          description: `接单者违约补偿 - ${taskId}`,
          reference: taskId,
          completedAt: new Date(),
        },
      });

      // 7.5 更新委托状态，清除接受者信息，重新回到招募状态
      const updatedTask = await tx.task.update({
        where: { id: taskId },
        data: {
          status: TaskStatus.RECRUITING,
          accepterId: null,
          acceptedAt: null,
          // 保持原有的过期时间，不重新设置
        },
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              name: true,
            },
          },
          category: {
            select: {
              name: true,
            },
          },
        },
      });

      return {
        task: updatedTask,
        depositAmount,
        accepterBalance: task.accepter!.balance,
        publisherNewBalance: task.publisher.balance + depositAmount,
      };
    });

    // 8. 发送邮件通知接单者
    try {
      const accepterEmail = task.accepter!.email;
      if (accepterEmail) {
        // 获取用户的语言偏好
        const userLanguage = await getUserEmailLanguage(accepterEmail);

        await sendTaskCancelledAccepterEmail(accepterEmail, {
          userName: task.accepter!.name || '用户',
          userEmail: accepterEmail,
          taskId: result.task.id,
          taskTitle:
            result.task.title ||
            `${result.task.platform.name} - ${result.task.category.name}`,
          publisherName: task.publisher.name || '用户',
          cancelledAt: new Date().toISOString(),
          penaltyAmount: result.depositAmount,
          currency: 'USD',
          cancellationReason: '主动放弃委托',
          language: userLanguage,
        });
      }
    } catch (emailError) {
      console.error('发送委托取消邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: `委托已放弃，违约金 $${depositAmount.toFixed(2)} 已转给发布者作为补偿`,
      data: {
        task: {
          id: result.task.id,
          status: result.task.status,
          platform: result.task.platform.name,
          category: result.task.category.name,
        },
        penalty: {
          amount: result.depositAmount,
          accepterRemainingBalance: result.accepterBalance,
          publisherNewBalance: result.publisherNewBalance,
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
