'use client';

import { ChevronRight, type LucideIcon } from 'lucide-react';
import NextLink from 'next/link';
import { usePathname } from 'next/navigation';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { useSidebarNavigation } from '@/hooks/use-active-route';
import { Link as I18nLink } from '@/i18n/navigation';
import { cn } from '@/lib/utils';

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
      icon?: LucideIcon;
    }[];
  }[];
}) {
  const { isActive, shouldExpand, getNavItemState } = useSidebarNavigation();
  const pathname = usePathname();

  // 判断是否在后台管理系统中
  const isAdmin = pathname.startsWith('/admin');

  // 动态选择 Link 组件（仅在非后台时使用国际化 Link）
  const Link = isAdmin ? NextLink : I18nLink;

  return (
    <SidebarGroup>
      <SidebarMenu>
        {items.map(item => {
          const itemState = getNavItemState(item.url);
          const hasSubItems = item.items && item.items.length > 0;
          const shouldExpandItem = hasSubItems && shouldExpand(item);

          // 如果有子项，渲染可折叠菜单
          if (hasSubItems) {
            return (
              <Collapsible
                key={item.title}
                asChild
                defaultOpen={shouldExpandItem}
                className='group/collapsible'
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton
                      tooltip={item.title}
                      className={cn(
                        itemState.isActive &&
                          'bg-sidebar-accent text-sidebar-accent-foreground',
                        'transition-colors duration-200',
                      )}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                      <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items!.map(subItem => {
                        const subItemState = getNavItemState(subItem.url);
                        return (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              className={cn(
                                subItemState.isActive &&
                                  'bg-sidebar-accent text-sidebar-accent-foreground',
                                'transition-colors duration-200',
                              )}
                            >
                              <Link href={subItem.url}>
                                {subItem.icon && (
                                  <subItem.icon className='h-4 w-4' />
                                )}
                                <span>{subItem.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            );
          }

          // 如果没有子项，渲染直接链接
          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild
                tooltip={item.title}
                className={cn(
                  itemState.isActive &&
                    'bg-sidebar-accent text-sidebar-accent-foreground',
                  'transition-colors duration-200',
                )}
              >
                <Link href={item.url}>
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
