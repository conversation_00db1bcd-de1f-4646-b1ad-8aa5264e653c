import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import { checkSessionTimeout, SessionStatus } from '@/lib/session-management';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { status: 'expired', message: 'No active session' },
        { status: 401 },
      );
    }

    const sessionStatus = await checkSessionTimeout();

    let timeLeft = 0;
    if (sessionStatus === SessionStatus.WARNING) {
      // 计算剩余时间（这里需要从数据库获取实际的过期时间）
      // 简化实现，返回5分钟
      timeLeft = 5 * 60 * 1000;
    }

    return NextResponse.json({
      status: sessionStatus.toLowerCase(),
      timeLeft,
      userId: session.user.id,
    });
  } catch (error) {
    console.error('Session status check error:', error);
    return NextResponse.json(
      { status: 'error', message: 'Failed to check session status' },
      { status: 500 },
    );
  }
}
