'use client';

import { useQuery } from '@tanstack/react-query';

export interface PaymentMethod {
  id: string;
  name: string;
  provider: string;
  icon: string;
  color: string;
  feeRate: number;
  minAmount?: number;
}

export interface PaymentMethodsResponse {
  methods: PaymentMethod[];
}

// 获取可用的支付方式
export function usePaymentMethods() {
  return useQuery<PaymentMethodsResponse>({
    queryKey: ['payment-methods'],
    queryFn: async () => {
      const response = await fetch('/api/payments/methods');
      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }
      return response.json();
    },
    retry: 3,
    staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
    gcTime: 10 * 60 * 1000, // 10分钟后清除缓存
  });
}

// 根据支付方式ID获取对应的图标
export function getPaymentMethodIcon(methodId: string) {
  const iconMap: Record<string, string> = {
    alipay: 'smartphone',
    wxpay: 'smartphone',
    paypal: 'credit-card',
    crypto: 'bitcoin',
    wechat: 'smartphone',
    bank_card: 'building',
  };
  return iconMap[methodId] || 'credit-card';
}

// 根据支付方式ID获取对应的颜色
export function getPaymentMethodColor(methodId: string) {
  const colorMap: Record<string, string> = {
    alipay: 'blue',
    wxpay: 'green',
    paypal: 'indigo',
    crypto: 'orange',
    wechat: 'green',
    bank_card: 'gray',
  };
  return colorMap[methodId] || 'gray';
}
