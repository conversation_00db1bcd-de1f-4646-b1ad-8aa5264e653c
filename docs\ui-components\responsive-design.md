# RefundGo Responsive Design System

## Overview

RefundGo implements a comprehensive responsive design system using a mobile-first approach with Tailwind CSS. The system ensures optimal user experience across all device sizes from 320px mobile devices to large desktop screens.

## Breakpoint System

### Standard Breakpoints

```css
/* Tailwind CSS breakpoints */
sm: 640px   /* Small tablets and large phones */
md: 768px   /* Tablets */
lg: 1024px  /* Small laptops */
xl: 1280px  /* Large laptops */
2xl: 1536px /* Large desktops */
```

### Custom Breakpoints

```css
/* Additional breakpoints for specific needs */
xs: 480px   /* Large phones */
3xl: 1920px /* Ultra-wide screens */
```

### Device Categories

- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px  
- **Desktop**: 1024px+

## Mobile-First Strategy

### Implementation Approach

```css
/* Base styles for mobile (320px+) */
.component {
  @apply text-sm p-4;
}

/* Tablet enhancements (768px+) */
@screen md {
  .component {
    @apply text-base p-6;
  }
}

/* Desktop enhancements (1024px+) */
@screen lg {
  .component {
    @apply text-lg p-8;
  }
}
```

### Progressive Enhancement

1. **Core Functionality**: Works on smallest screens
2. **Enhanced Layout**: Improved spacing and typography on larger screens
3. **Advanced Features**: Additional functionality on desktop

## Layout Patterns

### Container System

```tsx
// Page-level container
<div className="container mx-auto px-4 sm:px-6 lg:px-8">
  <div className="max-w-7xl mx-auto">
    {/* Content */}
  </div>
</div>

// Section container
<section className="py-8 sm:py-12 lg:py-16">
  <div className="container mx-auto px-4">
    {/* Section content */}
  </div>
</section>
```

### Grid Layouts

#### Responsive Grid System

```tsx
// Auto-responsive grid
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
  {items.map(item => <Card key={item.id} {...item} />)}
</div>

// Content-aware grid
<div className="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-4">
  {items.map(item => <Card key={item.id} {...item} />)}
</div>
```

#### Complex Layouts

```tsx
// Dashboard layout
<div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
  {/* Sidebar */}
  <aside className="lg:col-span-1">
    <Navigation />
  </aside>
  
  {/* Main content */}
  <main className="lg:col-span-3">
    <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
      <TaskList />
      <Statistics />
    </div>
  </main>
</div>
```

### Flexbox Patterns

```tsx
// Responsive navigation
<nav className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
  <Logo />
  <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
    <NavLink href="/dashboard">Dashboard</NavLink>
    <NavLink href="/tasks">Tasks</NavLink>
  </div>
</nav>

// Card layout
<div className="flex flex-col lg:flex-row gap-6">
  <div className="flex-1">
    <MainContent />
  </div>
  <aside className="w-full lg:w-80">
    <Sidebar />
  </aside>
</div>
```

## Component Responsive Patterns

### Navigation Components

#### Mobile Navigation

```tsx
// Hamburger menu for mobile
<div className="md:hidden">
  <Button
    variant="ghost"
    size="sm"
    onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
    aria-label="Toggle menu"
  >
    {mobileMenuOpen ? <X /> : <Menu />}
  </Button>
</div>

// Mobile menu overlay
{mobileMenuOpen && (
  <div className="fixed inset-0 z-50 md:hidden">
    <div className="fixed inset-0 bg-black/50" onClick={() => setMobileMenuOpen(false)} />
    <nav className="fixed top-0 right-0 h-full w-64 bg-white p-6">
      {/* Mobile navigation items */}
    </nav>
  </div>
)}
```

#### Desktop Navigation

```tsx
// Desktop horizontal navigation
<nav className="hidden md:flex items-center space-x-6">
  <NavLink href="/dashboard">Dashboard</NavLink>
  <NavLink href="/tasks">Tasks</NavLink>
  <NavLink href="/wallet">Wallet</NavLink>
</nav>
```

### Filter Tabs Implementation

#### Responsive Tab System

```tsx
// Mobile: Horizontal scroll
<div className="md:hidden">
  <div className="flex overflow-x-auto scrollbar-hide gap-2 pb-2">
    {tabs.map(tab => (
      <Button
        key={tab.id}
        variant={activeTab === tab.id ? 'default' : 'outline'}
        size="sm"
        className="whitespace-nowrap flex-shrink-0"
        onClick={() => setActiveTab(tab.id)}
      >
        {tab.label}
      </Button>
    ))}
  </div>
</div>

// Desktop: Grid layout
<div className="hidden md:grid md:grid-cols-3 lg:grid-cols-6 gap-2">
  {tabs.map(tab => (
    <Button
      key={tab.id}
      variant={activeTab === tab.id ? 'default' : 'outline'}
      onClick={() => setActiveTab(tab.id)}
    >
      {tab.label}
    </Button>
  ))}
</div>
```

#### Progressive Text Display

```tsx
// Responsive text based on screen size
const getTabLabel = (tab: Tab, screenSize: 'xs' | 'sm' | 'md' | 'lg') => {
  switch (screenSize) {
    case 'xs':
      return tab.shortLabel; // "All", "Active"
    case 'sm':
      return tab.mediumLabel; // "All Tasks", "Active Tasks"
    default:
      return tab.fullLabel; // "All Tasks (12)", "Active Tasks (5)"
  }
};
```

### Form Layouts

#### Responsive Forms

```tsx
// Stacked on mobile, side-by-side on desktop
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <FormField name="firstName" />
  <FormField name="lastName" />
</div>

// Full width on mobile, constrained on desktop
<div className="w-full max-w-md mx-auto">
  <FormField name="email" />
  <FormField name="password" />
</div>
```

### Data Tables

#### Mobile Table Strategy

```tsx
// Desktop: Traditional table
<div className="hidden md:block">
  <Table>
    <TableHeader>
      <TableRow>
        <TableHead>Task</TableHead>
        <TableHead>Status</TableHead>
        <TableHead>Reward</TableHead>
        <TableHead>Actions</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      {tasks.map(task => (
        <TableRow key={task.id}>
          <TableCell>{task.title}</TableCell>
          <TableCell>{task.status}</TableCell>
          <TableCell>{task.reward}</TableCell>
          <TableCell>
            <Button size="sm">View</Button>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  </Table>
</div>

// Mobile: Card layout
<div className="md:hidden space-y-4">
  {tasks.map(task => (
    <Card key={task.id} className="p-4">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium">{task.title}</h3>
        <Badge>{task.status}</Badge>
      </div>
      <p className="text-sm text-muted-foreground mb-3">
        Reward: ${task.reward}
      </p>
      <Button size="sm" className="w-full">
        View Details
      </Button>
    </Card>
  ))}
</div>
```

## Typography System

### Responsive Typography

```css
/* Fluid typography */
.heading-xl {
  @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
  @apply font-bold leading-tight;
}

.heading-lg {
  @apply text-xl sm:text-2xl lg:text-3xl;
  @apply font-semibold;
}

.body-text {
  @apply text-sm sm:text-base;
  @apply leading-relaxed;
}
```

### Line Height and Spacing

```css
/* Responsive spacing */
.section-spacing {
  @apply py-8 sm:py-12 lg:py-16 xl:py-20;
}

.content-spacing {
  @apply space-y-4 sm:space-y-6 lg:space-y-8;
}
```

## Image and Media

### Responsive Images

```tsx
// Hero image with responsive sizing
<div className="relative h-64 sm:h-80 lg:h-96">
  <Image
    src="/hero-image.jpg"
    alt="Hero image"
    fill
    className="object-cover"
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    priority
  />
</div>

// Gallery with responsive columns
<div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4">
  {images.map(image => (
    <div key={image.id} className="aspect-square relative">
      <Image
        src={image.src}
        alt={image.alt}
        fill
        className="object-cover rounded-lg"
      />
    </div>
  ))}
</div>
```

### Video Embeds

```tsx
// Responsive video container
<div className="relative aspect-video w-full">
  <iframe
    src={videoUrl}
    className="absolute inset-0 w-full h-full"
    allowFullScreen
  />
</div>
```

## Performance Considerations

### Conditional Rendering

```tsx
// Render different components based on screen size
const isMobile = useMediaQuery('(max-width: 767px)');

return (
  <>
    {isMobile ? (
      <MobileNavigation />
    ) : (
      <DesktopNavigation />
    )}
  </>
);
```

### Lazy Loading

```tsx
// Lazy load components for mobile
const MobileChart = lazy(() => import('./MobileChart'));
const DesktopChart = lazy(() => import('./DesktopChart'));

<Suspense fallback={<ChartSkeleton />}>
  {isMobile ? <MobileChart /> : <DesktopChart />}
</Suspense>
```

## Testing Responsive Design

### Viewport Testing

```bash
# Test different viewport sizes
npm run test:responsive

# Visual regression testing
npm run test:visual
```

### Device Testing

- **Mobile**: iPhone SE (375px), iPhone 12 (390px)
- **Tablet**: iPad (768px), iPad Pro (1024px)
- **Desktop**: MacBook (1280px), iMac (1920px)

### Browser Testing

- Chrome DevTools device simulation
- Firefox responsive design mode
- Safari Web Inspector
- Real device testing

## Best Practices

### Design Principles

1. **Mobile-First**: Start with mobile constraints
2. **Progressive Enhancement**: Add features for larger screens
3. **Touch-Friendly**: Minimum 44px touch targets
4. **Readable Text**: Minimum 16px font size on mobile
5. **Fast Loading**: Optimize for mobile networks

### Implementation Guidelines

1. **Use Semantic HTML**: Proper markup for accessibility
2. **Flexible Units**: Use rem, em, and percentages
3. **Avoid Fixed Widths**: Use max-width instead of width
4. **Test Early**: Test on real devices frequently
5. **Performance Budget**: Monitor bundle size and loading times

---

**Supported Devices**: 320px - 1920px+  
**Framework**: Tailwind CSS + Mobile-First  
**Testing**: Cross-device and cross-browser  
**Last Updated**: 2025-01-29
