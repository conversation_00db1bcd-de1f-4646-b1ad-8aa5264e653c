import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { chargebackTypeSchema } from '@/types/rates';

// PUT /api/admin/chargeback-types/[id] - 更新拒付类型
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = chargebackTypeSchema.parse(body);

    const existingChargebackType = await prisma.chargebackType.findUnique({
      where: { id },
    });

    if (!existingChargebackType) {
      return NextResponse.json(
        { success: false, message: '拒付类型不存在' },
        { status: 404 },
      );
    }

    // 检查名称是否与其他记录冲突
    const duplicateChargebackType = await prisma.chargebackType.findFirst({
      where: {
        name: validatedData.name,
        id: { not: id },
      },
    });

    if (duplicateChargebackType) {
      return NextResponse.json(
        { success: false, message: '拒付类型名称已存在' },
        { status: 400 },
      );
    }

    const updatedChargebackType = await prisma.chargebackType.update({
      where: { id },
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: updatedChargebackType,
      message: '拒付类型更新成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('更新拒付类型失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}

// DELETE /api/admin/chargeback-types/[id] - 删除拒付类型
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;

    // 检查拒付类型是否存在
    const existingChargebackType = await prisma.chargebackType.findUnique({
      where: { id },
    });

    if (!existingChargebackType) {
      return NextResponse.json(
        { success: false, message: '拒付类型不存在' },
        { status: 404 },
      );
    }

    // 删除拒付类型
    await prisma.chargebackType.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: '拒付类型删除成功',
    });
  } catch (error) {
    console.error('删除拒付类型失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}
