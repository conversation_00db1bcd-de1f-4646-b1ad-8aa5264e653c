{"navigation": {"title": "My Accepted Tasks", "description": "Manage and track all tasks you have accepted", "breadcrumb": "Task List"}, "messages": {"submitOrderSuccess": "Order information submitted successfully", "submitOrderError": "Failed to submit order information", "submitLogisticsSuccess": "Tracking number submitted successfully!", "submitLogisticsError": "Failed to submit tracking number", "abandonTaskSuccess": "Task abandoned successfully", "abandonTaskError": "Failed to abandon task", "orderNumberCopied": "Order number copied to clipboard", "loading": "Loading...", "networkError": "Network error, please try again later", "needResubmit": "Need to resubmit", "evidenceRejected": "Evidence rejected"}, "tabs": {"all": "All", "inProgress": "In Progress", "pendingLogistics": "Pending Logistics", "pendingReview": "Pending Review", "pendingDelivery": "Pending Delivery", "completed": "Completed", "expired": "Expired", "cancelled": "Cancelled"}, "status": {"PENDING": "Under Review", "IN_PROGRESS": "In Progress", "PENDING_LOGISTICS": "Pending Logistics", "PENDING_REVIEW": "Pending Review", "PENDING_DELIVERY": "Pending Delivery", "COMPLETED": "Completed", "EXPIRED": "Expired", "CANCELLED": "Cancelled", "ABANDONED": "Abandoned", "orderSubmitted": "Order Submitted", "needResubmit": "Need Resubmit", "waitingPublisherReview": "Waiting for publisher review", "publisherReviewingOrderLogistics": "Publisher is reviewing your submitted order number and logistics information, please wait patiently", "waitingPublisherConfirmDelivery": "Waiting for publisher to confirm delivery", "reviewPassedWaitingDelivery": "Review passed, you will receive commission after publisher confirms delivery", "autoDeliveryTime": "Auto delivery time", "autoDeliveryImminent": "Auto delivery confirmation imminent", "taskCompleted": "Task completed", "taskCompletedRewardSent": "Congratulations! Task completed successfully, commission has been sent to your account", "reviewRejected": "Review rejected", "rejectReason": "Rejection reason", "pleaseResubmitCorrectInfo": "Please resubmit correct order number and logistics information", "reviewTime": "Review time"}, "actions": {"viewDetails": "View Details", "submitOrder": "Submit Order", "abandonTask": "Abandon Task", "viewReviewStatus": "View Review Status", "viewOrder": "View Order", "viewOrderNumber": "View Order Number", "viewLogistics": "View Logistics", "submitLogistics": "Submit Logistics", "confirmDelivery": "Confirm Delivery", "requestArbitration": "Request Arbitration", "cancel": "Cancel", "submitting": "Submitting...", "cancelling": "Cancelling..."}, "card": {"platform": "Platform", "category": "Category", "reward": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deadline": "Deadline", "acceptedAt": "Accepted At", "completedAt": "Completed At", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "publisher": "Publisher", "accepter": "Accepter", "orderNumber": "Order Number", "trackingNumber": "Tracking Number"}, "labels": {"totalPrice": "Total Price", "reward": "<PERSON><PERSON>", "evidenceStatus": "Evidence Status", "logisticsSubmission": "Logistics Submission", "autoDeliveryLimit": "Auto Delivery Limit", "orderSubmissionLimit": "Order Submission Limit", "orderReview": "Order Review Limit", "logisticsReview": "Logistics Review Limit", "orderLogisticsInfo": "Order and Logistics Information", "orderScreenshot": "Order Screenshot", "logisticsScreenshot": "Logistics Screenshot"}, "loading": {"tasks": "Loading task data...", "submitting": "Submitting...", "processing": "Processing..."}, "empty": {"title": "No Tasks", "all": "You haven't accepted any tasks yet", "filtered": "No tasks in current status", "description": "Go to the task hall to find suitable tasks", "browseButton": "Browse Tasks"}, "dialog": {"taskDetail": {"title": "Task Details", "description": "View complete task information and requirements", "close": "Close"}, "acceptTask": {"title": "Accept Task Confirmation", "description": "Please read the task details carefully. Accepting will freeze the corresponding deposit", "successDescription": "Your deposit has been frozen. Please complete the task within the specified time", "notSet": "Not set", "taskInfo": "Task Information", "taskTitle": "Task Title", "productDescription": "Product Description", "platform": "Platform", "category": "Category", "purchaseRequirements": "Purchase Requirements", "quantity": "Quantity", "pieces": "pieces", "unitPrice": "Unit Price", "totalPrice": "Total Price", "listingDuration": "Listing Duration", "hours": "hours", "shippingInfo": "Shipping Information", "recipient": "Recipient", "phone": "Phone", "address": "Address", "timeRequirements": "Time Requirements", "deadline": "Task Deadline", "deadlineNote": "Please complete the purchase and submit relevant proof before the deadline", "costBreakdown": "Cost Breakdown", "commission": "Your Commission", "deposit": "Deposit to Freeze", "netCommission": "Net Commission", "depositNote": "Deposit will be automatically returned after task completion", "importantReminder": "Important Reminder", "reminder1": "After accepting the task, the deposit will be frozen from your account balance", "reminder2": "Please ensure sufficient account balance (at least {amount})", "reminder3": "Deposit will be automatically unfrozen and commission paid after task completion", "reminder4": "If you abandon the task, the deposit will be deducted as penalty", "reminder5": "Please strictly follow requirements to purchase products and submit proof", "cancel": "Cancel", "accepting": "Accepting...", "confirmAccept": "Confirm Accept Task", "agreementText": "By clicking \"Confirm Accept Task\" you agree to the", "serviceAgreement": "Service Agreement", "and": "and", "userGuidelines": "User Guidelines"}, "submitOrder": {"title": "Submit Order", "description": "Please fill in order information and upload order screenshot", "orderNumber": "Order Number", "orderNumberPlaceholder": "Please enter order number", "orderScreenshot": "Order Screenshot", "uploadHint": "Please upload order screenshot, supports JPG, PNG formats", "rejectionReason": "Previous review failed", "resubmitHint": "Please resubmit order and logistics information based on the above reasons", "uploadPlaceholder": "Click to upload or drag order screenshot here", "submit": "Submit Order", "submitting": "Submitting...", "cancel": "Cancel"}, "submitLogistics": {"title": "Submit Logistics Information", "description": "Please fill in tracking number and upload logistics screenshot", "trackingNumber": "Tracking Number", "trackingNumberPlaceholder": "Please enter tracking number", "logisticsScreenshot": "Logistics Screenshot", "logisticsScreenshots": "Logistics Screenshots ({{count}} images)", "submit": "Submit Logistics", "submitting": "Submitting...", "cancel": "Cancel"}, "abandonTask": {"title": "Confirm Abandon Task", "description": "Please read the following prompts carefully before confirming the operation", "warning": "Important Notice", "irreversible": "Abandoning the task cannot be undone", "depositLoss": "Your paid deposit ${amount} will be used as breach compensation and cannot be refunded", "consequences": {"title": "Abandoning the task means:", "admit": "You acknowledge that you cannot complete this task", "compensation": "The deposit will be transferred to the task publisher as compensation", "return": "The task will return to the task hall for others to accept"}, "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "confirm": "Confirm Abandon", "cancel": "Cancel", "processing": "Processing..."}, "viewOrder": {"title": "Order Details", "description": "View submitted order information", "orderNumber": "Order Number", "submittedAt": "Submitted At", "orderScreenshot": "Order Screenshot", "copyOrderNumber": "Copy Order Number", "viewScreenshot": "View Screenshot", "close": "Close"}}, "validation": {"orderNumberRequired": "Please enter order number", "orderNumberMinLength": "Order number must be at least 6 characters", "orderScreenshotRequired": "Please upload order screenshot", "orderScreenshotMaxCount": "Only one screenshot allowed", "trackingNumberRequired": "Please enter tracking number", "trackingNumberMinLength": "Tracking number must be at least 6 characters", "logisticsScreenshotsRequired": "Please upload at least one logistics screenshot", "logisticsScreenshotsMaxCount": "Maximum 5 logistics screenshots allowed"}, "time": {"expired": "Expired", "calculating": "Calculating...", "reviewTimeout": "Review timeout", "autoConfirmDelivery": "Auto confirm delivery", "days": " days", "hours": " hours", "minutes": " minutes", "seconds": " seconds"}, "evidence": {"PENDING_SUBMISSION": "Pending Submission", "UNDER_REVIEW": "Under Review", "NO_EVIDENCE": "No Evidence", "REVIEWED": "Reviewed", "REJECTED": "Rejected"}, "fileUpload": {"placeholder": "Click to upload or drag files here", "description": "Supports PNG, JPG, MP4, MOV and other formats", "uploading": "Uploading...", "uploadedFiles": "Uploaded Files ({{count}})", "clearAll": "Clear All", "maxFilesError": "Maximum {{maxFiles}} files allowed", "fileSizeError": "File {{fileName}} exceeds size limit ({{maxSize}}MB)", "noFilesSelected": "No files selected", "uploadFailed": "Upload failed", "uploadSuccess": "Successfully uploaded {{count}} files", "dragAndDrop": "Drag files here", "selectFiles": "Select Files", "fileTypeError": "Unsupported file format", "networkError": "Network error, please retry"}, "browser": {"confirmLeave": "Confirm Leave", "unsavedChanges": "You have unsaved changes, are you sure you want to leave?", "leave": "Leave", "stay": "Stay", "networkError": "Network Error", "connectionLost": "Network connection lost, please check your network and try again", "retry": "Retry", "sessionExpired": "Session Expired", "sessionExpiredDesc": "Your login session has expired, please log in again", "relogin": "Re-login", "uploadProgress": "Upload Progress", "uploading": "Uploading...", "uploadComplete": "Upload Complete", "uploadFailed": "Upload Failed", "fileTooBig": "File Too Large", "fileTooLarge": "File size exceeds limit, please select a smaller file", "invalidFileType": "Unsupported File Type", "invalidFileTypeDesc": "Please select a supported file format", "confirmDelete": "Confirm Delete", "deleteWarning": "This action cannot be undone, are you sure you want to delete?", "delete": "Delete", "cancel": "Cancel"}, "detail": {"loading": "Loading...", "viewDetails": "View Details", "taskDetails": "Task Details", "taskDetailsDescription": "View complete task information and requirements", "basicInfo": "Basic Information", "taskId": "Task ID", "status": "Status", "platform": "Platform", "category": "Product Category", "productInfo": "Product Information", "quantity": "Product Quantity", "unitPrice": "Unit Price", "productLink": "Product Link", "noProductLink": "No product link", "productDescription": "Product Description", "noProductDescription": "No product description", "chargebackInfo": "Chargeback Information", "chargebackType": "Chargeback Type", "none": "None", "supportedPaymentMethods": "Supported Payment Methods", "amountInfo": "Amount Information", "orderAmount": "Order Amount", "taskReward": "Task Reward", "evidenceReviewed": "✓ Evidence reviewed and approved", "evidenceFee": "Evidence fee available", "evidenceStatusAndRewardExplanation": "Evidence Status and Reward Explanation", "evidenceUnderReview": "Evidence under review", "waitingForEvidence": "Waiting for evidence submission", "evidenceApprovedDesc": "✓ Publisher's chargeback evidence has been approved by the platform", "evidenceFeeRefunded": "✓ Evidence fee has been refunded to the publisher, your commission is the base commission", "finalCommission": "✓ Final commission: ", "finalReward": "Final reward", "evidenceFeeReturnedToPublisher": "Evidence fee has been refunded to the publisher, your commission is the base commission", "evidenceRejectedDesc": "✗ Publisher's evidence failed review or needs resubmission", "evidenceUnderReviewDesc": "⏳ Publisher's evidence is under review or not yet submitted", "evidenceFeeIfNotApproved": "✓ If evidence is still not approved when task completes, you will receive additional evidence fee", "estimatedFinalCommission": "✓ Estimated final commission: ", "evidenceRejectedOrNeedsResubmission": "Publisher's evidence failed review or needs resubmission", "evidenceUnderReviewOrNotSubmitted": "Publisher's evidence is under review or not yet submitted", "willGetExtraEvidenceFeeIfNotApproved": "If evidence is still not approved when task completes, you will receive additional evidence fee", "expectedFinalReward": "Estimated final commission: ", "autoReceiveDeadline": "Auto-receive deadline", "baseCommission": "Base commission", "baseReward": "Base reward", "evidenceFeeAmount": "Evidence fee", "timeInfo": "Time Information", "acceptedAt": "Accepted At", "acceptedTime": "Accepted time", "noTime": "None", "orderSubmissionDeadline": "Order Submission Deadline", "logisticsSubmissionDeadline": "Logistics Submission Deadline", "orderReviewDeadline": "Order Review Deadline", "logisticsReviewDeadline": "Logistics Review Deadline", "reviewDeadline": "Review Deadline", "autoDeliveryDeadline": "Auto Delivery Deadline", "remaining": "Remaining", "orderSubmissionHint": "Please submit order information within the deadline", "logisticsSubmissionHint": "Please submit tracking number within the deadline", "orderReviewHint": "Publisher is reviewing order information", "logisticsReviewHint": "Publisher is reviewing logistics information", "reviewHint": "Waiting for publisher review", "autoDeliveryHint": "Publisher confirms delivery, otherwise system will auto-confirm", "shippingAddress": "Shipping Address", "recipientName": "Recipient Name", "recipientPhone": "Phone Number", "detailedAddress": "Detailed Address", "noShippingAddress": "No shipping address", "demoMaterials": "Demo Materials", "cartScreenshots": "Shopping Cart Demo Screenshots", "chargebackEvidence": "Chargeback Evidence", "evidenceReviewFailed": "Evidence review failed", "evidenceUnderReviewStatus": "Evidence under review", "evidenceApprovedStatus": "Evidence reviewed and approved", "evidenceUnderReviewDesc2": "Evidence is under review, viewable after approval", "evidenceRejectedDesc2": "Evidence review failed, cannot view", "evidenceApprovedDesc2": "Evidence content viewable after approval", "evidenceNeedsReupload": "Evidence needs to be re-uploaded", "noChargebackEvidence": "No chargeback evidence", "evidenceRejectedPublisher": "Publisher's evidence failed review, needs to be resubmitted", "evidenceNotSubmitted": "Publisher has not yet provided chargeback evidence materials", "evidencePendingReview": "Evidence pending review", "evidenceSubmittedPendingReview": "Publisher has submitted evidence, waiting for platform review", "evidenceApprovedByPlatform": "Publisher's chargeback evidence has been approved by the platform", "noEvidenceMaterials": "No chargeback evidence materials", "orderLogisticsInfo": "Order and Logistics Information", "orderNumber": "Order Number", "orderScreenshot": "Order Screenshot", "orderScreenshots": "Order Screenshots", "trackingNumber": "Tracking Number", "logisticsScreenshots": "Logistics Screenshots", "publisherReviewingOrderAndLogistics": "Publisher is reviewing your submitted order number and logistics information, please wait patiently", "publisherReviewingInfo": "Publisher is reviewing your submitted order number and logistics information, please wait patiently", "waitingForDeliveryConfirmation": "Waiting for delivery confirmation", "reviewPassedWaitingDelivery": "Review passed, you will receive commission after publisher confirms delivery", "autoDeliveryTime": "Auto delivery time: ", "autoDeliveryImminent": "Auto delivery confirmation imminent", "daysHours": "days", "hours": "hours later", "minutes": "minutes later", "taskCompleted": "Task completed", "congratulationsCompleted": "Congratulations! Task completed successfully, commission has been sent to your account", "reviewFailed": "Review failed", "rejectionReason": "Rejection reason: ", "resubmitCorrectInfo": "Please resubmit correct order number and logistics information", "reviewTime": "Review time: ", "locale": "en", "noCountdownNeeded": "No countdown needed", "lessThanOneHour": "Less than 1 hour", "pieces": "pieces", "submitOrderInfoWithinDeadline": "Please submit order information within the deadline", "submitLogisticsInfoWithinDeadline": "Please submit logistics tracking number within the deadline", "publisherReviewingOrderInfo": "Publisher is reviewing order information", "publisherReviewingLogisticsInfo": "Publisher is reviewing logistics information", "waitingForPublisherReview": "Waiting for publisher review", "publisherConfirmReceiptOrAutoReceive": "Publisher confirms delivery, otherwise system will auto-confirm", "recipient": "Recipient", "phoneNumber": "Phone Number", "cartDemoScreenshots": "<PERSON>t Demo Screenshots", "evidenceUnderReviewCanViewAfterApproval": "Evidence is under review, can view after approval", "evidenceRejectedCannotView": "Evidence review failed, cannot view", "canViewEvidenceAfterApproval": "Can view evidence content after approval", "publisherEvidenceRejectedNeedsResubmit": "Publisher's evidence failed review, needs to resubmit", "publisherHasNotProvidedEvidence": "Publisher has not provided chargeback evidence materials", "publisherSubmittedEvidenceWaitingReview": "Publisher has submitted evidence, waiting for platform review", "publisherEvidenceApprovedByPlatform": "Publisher's chargeback evidence has been approved by platform", "noChargebackEvidenceMaterials": "No chargeback evidence materials", "aboutToAutoReceive": "About to auto-confirm delivery", "daysLater": " days", "hoursLater": " hours later", "minutesLater": " minutes later", "congratulationsTaskCompleted": "Congratulations! Task completed successfully, commission has been sent to your account", "pleaseResubmitCorrectInfo": "Please resubmit correct order number and logistics information", "rejectReason": "Rejection reason", "evidenceApproved": "Evidence approved", "evidenceRejected": "Evidence rejected", "days": "days", "productUrl": "Product URL", "noProductUrl": "No product URL", "timeExpired": "Expired", "timeCalculating": "Calculating...", "timeReviewExpired": "Review timeout", "timeWaitingReview": "Waiting for review", "timeNoCountdown": "No countdown needed", "timeLessThanOneHour": "Less than 1 hour", "timeDays": " days", "timeHours": " hours", "timeMinutes": " minutes", "timeAfter": "", "timeLoading": "Loading...", "reward": "<PERSON><PERSON>", "canGetEvidenceFee": "Can get evidence fee", "publisherEvidencePending": "Publisher evidence pending", "willGetExtraFee": "Will get extra fee", "estimatedFinalReward": "Estimated final reward", "evidenceWaitingSubmission": "Waiting for evidence submission", "evidenceReviewingCanViewAfter": "Evidence under review, can view after approval", "evidenceCanViewAfterApproval": "Can view evidence after approval", "evidenceNeedReupload": "Evidence needs re-upload", "noEvidence": "No evidence", "publisherEvidenceFailedNeedResubmit": "Publisher evidence failed, needs resubmit", "publisherNotProvidedEvidence": "Publisher has not provided evidence", "publisherSubmittedWaitingReview": "Publisher submitted evidence, waiting for review", "publisherEvidenceApproved": "Publisher evidence approved", "statusInfo": "Status Information", "evidenceStatusAndReward": "Evidence Status and Reward", "orderSubmissionLimit": "Order Submission Deadline", "cartDemoScreenshot": "<PERSON>t Demo Screenshot", "statusPending": "Pending", "statusRejected": "Rejected", "statusRecruiting": "Recruiting", "statusInProgress": "In Progress", "statusPendingLogistics": "Pending Logistics", "statusPendingReview": "Pending Review", "statusPendingDelivery": "Pending Delivery", "statusCompleted": "Completed", "statusExpired": "Expired", "statusCancelled": "Cancelled", "publisherReviewingOrderLogistics": "Publisher is reviewing order and logistics information"}, "hooks": {"acceptTask": {"errors": {"networkError": "Network error, please try again later"}}, "abandonTask": {"error": {"title": "Failed to abandon task"}}}, "submitOrder": {"title": "Submit Order", "description": "Please fill in order information and upload order screenshot as completion proof", "orderNumber": "Order Number *", "orderNumberPlaceholder": "Please enter the complete order number", "orderScreenshot": "Order Screenshot *", "rejectionReason": "Previous review failed", "resubmitHint": "Please resubmit order and logistics information based on the above reasons", "submitButton": "Submit Order", "submitting": "Submitting..."}, "submitLogistics": {"title": "Submit Tracking Number", "description": "Please fill in the logistics tracking number so customers can track package status", "trackingNumber": "Tracking Number", "trackingNumberPlaceholder": "Please enter the complete tracking number", "logisticsScreenshots": "Logistics Screenshots", "logisticsScreenshotsNote": "to prove this tracking number belongs to this order", "submitButton": "Submit Tracking Number"}, "viewOrder": {"title": "View Order", "description": "View submitted order number and related information", "taskId": "Task ID", "orderNumber": "Order Number", "submitted": "Submitted", "noOrderNumber": "No order number", "noOrderScreenshot": "No order screenshot", "orderScreenshot": "Order Screenshot", "viewScreenshot": "View Full Image", "submittedAt": "Submitted At", "nextSteps": "Next Steps", "nextStepsDescription": "Please submit tracking number within 5 days and wait for publisher to confirm delivery", "trackingNumber": "Tracking Number", "trackingNumberCopied": "Tracking number copied to clipboard", "approved": "Approved", "logisticsScreenshots": "Logistics Screenshots ({{count}} images)", "logisticsScreenshotAlt": "Logistics Screenshot {{index}}", "waitingForLogistics": "Waiting for logistics information"}, "reviewStatus": {"title": "View Review Status", "description": "View the review progress of your submitted order and logistics information", "logisticsInfo": "Logistics Information", "pending": {"title": "Pending Review", "description": "Your order and logistics information is under review, please wait patiently"}, "rejected": {"title": "Review Failed", "description": "Review failed, please check the reason and resubmit"}, "approved": {"title": "Review Approved", "description": "Order and logistics information has been approved, package is being delivered"}, "completed": {"title": "Task Completed", "description": "Package has been delivered, task completed"}, "unknown": {"title": "Unknown Status", "description": "Unable to determine current status"}, "waitingApproval": "Waiting for approval", "approvalNote": "After approval, the system will automatically register the tracking number and obtain logistics information", "carrier": "Carrier", "currentStatus": "Current Status", "estimatedDelivery": "Estimated Delivery", "lastUpdate": "Last Update", "noInfo": "No information", "trackingHistory": "Tracking History", "noLogisticsInfo": "No logistics information", "errorReasons": {"title": "Possible reasons:", "reason1": "Tracking number not yet active in the system", "reason2": "Carrier system update delay", "reason3": "Incorrect tracking number entered", "suggestion": "Please try again later or contact customer service"}, "clickRefresh": "Click refresh to get the latest logistics information", "resubmit": "Resubmit", "refreshLogistics": "Refresh Logistics"}, "feedback": {"title": "Feedback & Suggestions", "description": "We'd love to hear your opinions and suggestions!", "placeholder": "Please share your feedback...", "send": "Send", "cancel": "Cancel", "validation": {"required": "Please enter feedback content", "enterContent": "Please enter your feedback content"}, "success": {"send": "<PERSON><PERSON><PERSON> sent successfully", "thankYou": "Thank you for your feedback!"}, "error": {"submitFailed": "Failed to submit feedback, please try again"}, "userFeedback": "User <PERSON>", "feedbackImportant": "Your feedback is important to us and helps us improve the product experience.", "howWasExperience": "How was this experience?", "satisfied": "Satisfied", "unsatisfied": "Unsatisfied", "detailedDescription": "Detailed Description (Optional)", "detailedPlaceholder": "Please tell us your specific thoughts or suggestions...", "submitFeedback": "Submit <PERSON>"}, "notifications": {"notificationCenter": "Notification Center", "markAllRead": "<PERSON>", "noNotifications": "No notifications"}}