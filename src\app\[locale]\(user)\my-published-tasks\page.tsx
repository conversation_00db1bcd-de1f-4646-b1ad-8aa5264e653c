'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { MyPublishedTasksContent } from '@/components/my-published-tasks-content';
import { UserPageLayout } from '@/components/user-page-layout';

export default function MyPublishedTasksPage() {
  const t = useTranslations('my-published-tasks');

  useEffect(() => {
    document.title = `${t('navigation.title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('navigation.title')}
      breadcrumbPage={t('navigation.breadcrumb')}
      href='/my-published-tasks'
    >
      <MyPublishedTasksContent />
    </UserPageLayout>
  );
}
