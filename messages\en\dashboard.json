{"title": "Dashboard", "breadcrumb": "Overview", "description": "View your task overview and statistics", "navigation": {"dashboard": "Dashboard", "overview": "Overview"}, "welcome": {"title": "Welcome back!", "description": "This is your task management dashboard overview"}, "stats": {"totalEarnings": "Total Earnings", "completedTasks": "Completed Tasks", "activeTasks": "Active Tasks", "changeFromLastMonth": "from last month"}, "quickActions": {"title": "Quick Actions", "description": "Quick access to common functions", "publishTask": {"title": "Publish New Task", "description": "Create and publish a new task"}, "browseTasks": {"title": "Browse Tasks", "description": "View all available tasks"}, "wallet": {"title": "My Wallet", "description": "Manage your funds and rewards"}, "tickets": {"title": "Ticket System", "description": "View and manage support tickets"}}, "recentBills": {"title": "Recent Bills", "description": "Overview of your recent transaction records", "viewAll": "View All", "noTransactions": "No transactions yet"}, "recentTransactions": {"title": "Recent Transactions", "description": "Overview of your recent transaction records", "viewAll": "View All", "noTransactions": "No transactions yet", "reference": "Reference", "types": {"DEPOSIT": "Deposit - NOWPayments(Cryptocurrency)(Fee $0.00)", "WITHDRAW": "<PERSON><PERSON><PERSON>", "TASK_FEE": "Task Fee", "COMMISSION": "Commission", "MEMBERSHIP": "Membership Upgrade - Business Plan(Fee $0.00)", "REFUND": "Refund"}, "status": {"PENDING": "Processing", "COMPLETED": "Completed", "FAILED": "Failed", "CANCELLED": "Cancelled"}}, "recommendedTasks": {"title": "Recommended Tasks", "description": "Latest recommended tasks for you", "viewMore": "View More", "empty": "No Recommended Tasks", "emptyDescription": "No recommended tasks available at the moment, please try again later"}, "taskCard": {"platform": "Platform", "category": "Category"}, "sidebar": {"dashboard": "Dashboard", "taskHall": "Task Hall", "publishTask": "Publish Task", "taskManagement": "Task Management", "acceptedTasks": "Accepted Tasks", "publishedTasks": "Published Tasks", "ticketManagement": "Ticket Management", "memberCenter": "Member Center", "myWallet": "My Wallet", "membership": "Membership", "whitelist": "Store Whitelist", "accountSecurity": "Account Security", "backToHome": "Back to Home", "membershipUpgrade": "Membership Upgrade", "ticketSystem": "Ticket System", "userCenter": "User Center", "adminDashboard": "Admin Dashboard", "logout": "Logout"}, "userMenu": {"account": "Account", "profile": "Profile", "billing": "Billing", "notifications": "Notifications", "settings": "Settings", "support": "Support", "logout": "Logout"}, "actions": {"publish": "Publish Task", "viewDetails": "View Details"}, "reviewDialog": {"title": "Review Order Information", "description": "Review order and logistics information submitted by the task acceptor", "orderNumber": "Order Number", "noOrderNumber": "No order number", "orderScreenshot": "Order Screenshot", "trackingNumber": "Tracking Number", "logisticsScreenshots": "Logistics Screenshots ({{count}} images)", "logisticsScreenshotAlt": "Logistics Screenshot {{index}}", "logisticsPreview": "Logistics Information Preview", "logisticsHint": "Tip: Real logistics data needs to be registered in the system first. After approval, the system will automatically retrieve the latest logistics tracking information.", "status": "Status", "waitingRegistration": "Waiting Registration", "logisticsCompany": "Logistics Company", "autoDetect": "Auto Detect", "trackingStatus": "Tracking Status", "pending": "Pending", "featuresAfterApproval": "Features available after approval:", "realtimeStatus": "Real-time logistics status", "estimatedDelivery": "Estimated delivery time", "fullTrackingHistory": "Complete tracking history", "autoStatusUpdate": "Automatic status updates", "approve": "Approve", "reject": "Reject", "confirmApproval": "Confirm approval?", "approvalDescription": "The task will enter the waiting for delivery stage", "back": "Back", "processing": "Processing...", "confirmApprove": "Confirm Approve", "rejectReason": "Rejection Reason", "rejectReasonPlaceholder": "Please specify the reason for rejection...", "confirmReject": "Confirm Reject", "rejectReasonRequired": "Rejection reason is required when review is not approved", "reviewFailed": "Review failed", "reviewApproved": "Review approved!", "reviewRejected": "Review rejected", "networkError": "Network error, please try again later", "orderNumberCopied": "Order number copied", "trackingNumberCopied": "Tracking number copied"}, "countdown": {"expired": "Expired", "days": "d"}, "errors": {"fetchStatsError": "Failed to fetch statistics", "fetchTransactionsError": "Failed to fetch transaction records", "fetchRecommendedTasksError": "Failed to fetch recommended tasks"}, "common": {"loading": "Loading...", "unknownPlatform": "Unknown Platform", "unknownCategory": "Unknown Category"}, "dateFormat": {"month": "/", "day": " "}}