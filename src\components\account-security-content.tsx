'use client';

import {
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Shield,
  CheckCircle,
  AlertCircle,
  Edit2,
  Calendar,
  Loader2,
  ArrowRight,
  ArrowLeft,
} from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export function AccountSecurityContent() {
  const t = useTranslations('AccountSecurity');
  const locale = useLocale();
  const [mounted, setMounted] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isNicknameDialogOpen, setIsNicknameDialogOpen] = useState(false);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);

  // 邮箱更换流程状态
  const [emailChangeStep, setEmailChangeStep] = useState(1); // 1: 验证当前邮箱, 2: 输入新邮箱
  const [currentEmailVerified, setCurrentEmailVerified] = useState(false);
  const [currentEmailCountdown, setCurrentEmailCountdown] = useState(0);
  const [newEmailCountdown, setNewEmailCountdown] = useState(0);
  const [currentEmailVerificationSent, setCurrentEmailVerificationSent] =
    useState(false);
  const [newEmailVerificationSent, setNewEmailVerificationSent] =
    useState(false);

  // 数据状态
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState<any>(null);
  const [submitting, setSubmitting] = useState(false);

  // 不同操作的加载状态
  const [sendingCurrentEmailCode, setSendingCurrentEmailCode] = useState(false);
  const [verifyingCurrentEmail, setVerifyingCurrentEmail] = useState(false);
  const [sendingNewEmailCode, setSendingNewEmailCode] = useState(false);
  const [changingEmail, setChangingEmail] = useState(false);

  // 表单状态
  const [nicknameForm, setNicknameForm] = useState('');
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [emailForm, setEmailForm] = useState({
    currentEmailCode: '',
    newEmail: '',
    newEmailCode: '',
  });

  // 获取用户数据
  const fetchUserData = useCallback(async () => {
    try {
      const response = await fetch('/api/user/profile');
      const result = await response.json();

      if (result.success) {
        setUserData(result.data);
        setNicknameForm(result.data.nickname);
      } else {
        toast.error(t('messages.operationFailed'), {
          description: result.message || t('messages.refreshAndRetry'),
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      toast.error(t('messages.operationFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setLoading(false);
    }
  }, [t]);

  useEffect(() => {
    setMounted(true);
    fetchUserData();
  }, [fetchUserData]);

  // 格式化日期时间的安全函数
  const formatDateTime = (dateString: string) => {
    if (!mounted) {
      return t('messages.loading');
    }
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // 发送当前邮箱验证码
  const sendCurrentEmailVerificationCode = async () => {
    try {
      setSendingCurrentEmailCode(true);
      const response = await fetch('/api/user/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send-current-email-code',
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentEmailVerificationSent(true);
        setCurrentEmailCountdown(60);
        toast.success(t('messages.verificationCodeSent'));

        const timer = setInterval(() => {
          setCurrentEmailCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        toast.error(result.message || t('messages.sendCodeFailed'));
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error(t('messages.sendCodeFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setSendingCurrentEmailCode(false);
    }
  };

  // 验证当前邮箱验证码
  const verifyCurrentEmailCode = async () => {
    if (!emailForm.currentEmailCode) {
      toast.error(t('validation.verificationCodeRequired'));
      return;
    }
    if (emailForm.currentEmailCode.length !== 6) {
      toast.error(t('validation.verificationCodeLength'));
      return;
    }

    try {
      setVerifyingCurrentEmail(true);
      const response = await fetch('/api/user/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'verify-current-email',
          verificationCode: emailForm.currentEmailCode,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentEmailVerified(true);
        setEmailChangeStep(2);
        toast.success(t('messages.emailVerificationSuccess'));
      } else {
        toast.error(result.message || t('messages.verificationFailed'));
      }
    } catch (error) {
      console.error('验证码验证失败:', error);
      toast.error(t('messages.verificationFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setVerifyingCurrentEmail(false);
    }
  };

  // 发送新邮箱验证码
  const sendNewEmailVerificationCode = async () => {
    if (!emailForm.newEmail) {
      toast.error(t('validation.emailRequired'));
      return;
    }

    try {
      setSendingNewEmailCode(true);
      const response = await fetch('/api/user/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send-new-email-code',
          email: emailForm.newEmail,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setNewEmailVerificationSent(true);
        setNewEmailCountdown(60);
        toast.success(t('messages.verificationCodeSentToNewEmail'));

        const timer = setInterval(() => {
          setNewEmailCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        toast.error(result.message || t('messages.sendCodeFailed'));
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      toast.error(t('messages.sendCodeFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setSendingNewEmailCode(false);
    }
  };

  // 完成邮箱更换
  const completeEmailChange = async () => {
    if (!emailForm.newEmail) {
      toast.error(t('validation.emailRequired'));
      return;
    }
    if (!emailForm.newEmailCode) {
      toast.error(t('validation.verificationCodeRequired'));
      return;
    }
    if (emailForm.newEmailCode.length !== 6) {
      toast.error(t('validation.verificationCodeLength'));
      return;
    }

    try {
      setChangingEmail(true);
      const response = await fetch('/api/user/email', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          newEmail: emailForm.newEmail,
          verificationCode: emailForm.newEmailCode,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(t('messages.emailChangeSuccess'));
        handleEmailDialogClose();
        // 更新本地数据
        if (userData) {
          setUserData({
            ...userData,
            email: emailForm.newEmail,
            emailVerified: new Date(),
            updatedAt: new Date(),
          });
        }
      } else {
        toast.error(result.message || t('messages.emailChangeFailed'));
      }
    } catch (error) {
      console.error('更换邮箱失败:', error);
      toast.error(t('messages.emailChangeFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setChangingEmail(false);
    }
  };

  // 关闭邮箱对话框并重置状态
  const handleEmailDialogClose = () => {
    setIsEmailDialogOpen(false);
    setEmailChangeStep(1);
    setCurrentEmailVerified(false);
    setCurrentEmailVerificationSent(false);
    setNewEmailVerificationSent(false);
    setCurrentEmailCountdown(0);
    setNewEmailCountdown(0);
    setEmailForm({
      currentEmailCode: '',
      newEmail: '',
      newEmailCode: '',
    });
  };

  // 返回上一步
  const handleBackToStep1 = () => {
    setEmailChangeStep(1);
    setEmailForm(prev => ({
      ...prev,
      newEmail: '',
      newEmailCode: '',
    }));
    setNewEmailVerificationSent(false);
    setNewEmailCountdown(0);
  };

  // 提交处理函数
  const handleNicknameSubmit = async () => {
    if (!nicknameForm.trim()) {
      toast.error(t('validation.nicknameRequired'));
      return;
    }
    if (nicknameForm.length < 2 || nicknameForm.length > 20) {
      toast.error(t('validation.nicknameLengthInvalid'));
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'nickname',
          nickname: nicknameForm,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message || t('messages.nicknameChangeSuccess'));
        setIsNicknameDialogOpen(false);
        // 更新本地数据
        if (userData) {
          setUserData({
            ...userData,
            nickname: result.data.nickname,
            updatedAt: new Date(result.data.updatedAt),
          });
        }
      } else {
        toast.error(result.message || t('messages.nicknameChangeFailed'));
      }
    } catch (error) {
      console.error('修改昵称失败:', error);
      toast.error(t('messages.nicknameChangeFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handlePasswordSubmit = async () => {
    if (!passwordForm.currentPassword) {
      toast.error(t('validation.currentPasswordRequired'));
      return;
    }
    if (!passwordForm.newPassword) {
      toast.error(t('validation.newPasswordRequired'));
      return;
    }
    if (passwordForm.newPassword.length < 6) {
      toast.error(t('validation.passwordMinLength'));
      return;
    }
    if (passwordForm.newPassword.length > 32) {
      toast.error(t('validation.passwordMaxLength'));
      return;
    }
    if (!/^[a-zA-Z0-9]+$/.test(passwordForm.newPassword)) {
      toast.error(t('validation.passwordFormatInvalid'));
      return;
    }
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error(t('validation.passwordMismatch'));
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch('/api/user/password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(passwordForm),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message || t('messages.passwordChangeSuccess'));
        setIsPasswordDialogOpen(false);
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        // 更新最后修改密码时间
        if (userData) {
          setUserData({
            ...userData,
            lastPasswordChange: new Date(),
            updatedAt: new Date(),
          });
        }
      } else {
        toast.error(result.message || t('messages.passwordChangeFailed'));
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      toast.error(t('messages.passwordChangeFailed'), {
        description: t('messages.networkError'),
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='flex items-center gap-2'>
          <Loader2 className='h-5 w-5 animate-spin' />
          <span>{t('messages.loading')}</span>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Alert className='max-w-md'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>{t('messages.refreshAndRetry')}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div>
        <h1 className='text-2xl font-bold tracking-tight'>
          {t('navigation.title')}
        </h1>
        <p className='text-muted-foreground'>{t('navigation.description')}</p>
      </div>

      {/* 安全状态统计卡片 */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center gap-3'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900'>
                <CheckCircle className='h-5 w-5 text-green-600' />
              </div>
              <div>
                <div className='text-sm font-medium'>
                  {t('security.emailVerification')}
                </div>
                <div className='text-xs text-muted-foreground'>
                  {t('security.emailVerified')}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center gap-3'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900'>
                <Lock className='h-5 w-5 text-blue-600' />
              </div>
              <div>
                <div className='text-sm font-medium'>
                  {t('security.passwordStrength')}
                </div>
                <div className='text-xs text-muted-foreground'>
                  {t('security.passwordGood')}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center gap-3'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900'>
                <Shield className='h-5 w-5 text-purple-600' />
              </div>
              <div>
                <div className='text-sm font-medium'>
                  {t('security.securityStatus')}
                </div>
                <div className='text-xs text-muted-foreground'>
                  {t('security.securityNormal')}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 账号安全设置 */}
      <div className='space-y-4'>
        {/* 昵称设置 */}
        <Card className='hover:shadow-md transition-shadow'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-3'>
                <User className='h-5 w-5 text-muted-foreground' />
                <div>
                  <div className='font-medium'>{t('profile.nickname')}</div>
                  <div className='text-sm text-muted-foreground'>
                    {userData.nickname}
                  </div>
                </div>
              </div>
              <Dialog
                open={isNicknameDialogOpen}
                onOpenChange={setIsNicknameDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button size='sm' variant='outline'>
                    <Edit2 className='h-4 w-4 mr-1' />
                    {t('actions.modify')}
                  </Button>
                </DialogTrigger>
                <DialogContent className='sm:max-w-md'>
                  <DialogHeader>
                    <DialogTitle>{t('nickname.dialogTitle')}</DialogTitle>
                    <DialogDescription>
                      {t('nickname.dialogDescription')}
                    </DialogDescription>
                  </DialogHeader>
                  <div className='space-y-4'>
                    <div>
                      <Label htmlFor='nickname'>
                        {t('nickname.newNickname')}
                      </Label>
                      <Input
                        id='nickname'
                        placeholder={t('nickname.placeholder')}
                        value={nicknameForm}
                        onChange={e => setNicknameForm(e.target.value)}
                        className='mt-2'
                        disabled={submitting}
                      />
                      <div className='text-xs text-muted-foreground mt-1'>
                        {t('nickname.lengthHint')}
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={() => {
                          setIsNicknameDialogOpen(false);
                          setNicknameForm(userData.nickname);
                        }}
                        disabled={submitting}
                      >
                        {t('actions.cancel')}
                      </Button>
                      <Button
                        type='submit'
                        onClick={handleNicknameSubmit}
                        disabled={submitting}
                      >
                        {submitting && (
                          <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                        )}
                        {t('actions.save')}
                      </Button>
                    </DialogFooter>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>

        {/* 邮箱设置 */}
        <Card className='hover:shadow-md transition-shadow'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-3'>
                <Mail className='h-5 w-5 text-muted-foreground' />
                <div>
                  <div className='font-medium'>{t('profile.email')}</div>
                  <div className='flex items-center gap-2 mt-1'>
                    <span className='text-sm text-muted-foreground'>
                      {userData.email}
                    </span>
                    {userData.emailVerified ? (
                      <Badge variant='secondary' className='text-xs'>
                        <CheckCircle className='h-3 w-3 mr-1' />
                        {t('security.emailVerified')}
                      </Badge>
                    ) : (
                      <Badge variant='destructive' className='text-xs'>
                        <AlertCircle className='h-3 w-3 mr-1' />
                        {t('security.emailUnverified')}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              <Dialog
                open={isEmailDialogOpen}
                onOpenChange={setIsEmailDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button size='sm' variant='outline'>
                    <Mail className='h-4 w-4 mr-1' />
                    {t('actions.change')}
                  </Button>
                </DialogTrigger>
                <DialogContent className='sm:max-w-md'>
                  <DialogHeader>
                    <DialogTitle className='flex items-center gap-2'>
                      {t('email.dialogTitle')}
                      <Badge variant='outline' className='text-xs'>
                        {emailChangeStep === 1
                          ? t('email.step1')
                          : t('email.step2')}
                      </Badge>
                    </DialogTitle>
                    <DialogDescription>
                      {emailChangeStep === 1
                        ? t('email.step1Description')
                        : t('email.step2Description')}
                    </DialogDescription>
                  </DialogHeader>
                  <div className='space-y-4'>
                    {emailChangeStep === 1 && (
                      <>
                        <div>
                          <Label htmlFor='currentEmailCode'>
                            {t('email.verificationCode')}
                          </Label>
                          <div className='flex gap-2 mt-2'>
                            <Input
                              id='currentEmailCode'
                              placeholder={t('email.codePlaceholder')}
                              maxLength={6}
                              value={emailForm.currentEmailCode}
                              onChange={e =>
                                setEmailForm(prev => ({
                                  ...prev,
                                  currentEmailCode: e.target.value,
                                }))
                              }
                              disabled={verifyingCurrentEmail}
                            />
                            <Button
                              type='button'
                              variant='outline'
                              onClick={sendCurrentEmailVerificationCode}
                              disabled={
                                currentEmailCountdown > 0 ||
                                sendingCurrentEmailCode
                              }
                              className='whitespace-nowrap'
                            >
                              {sendingCurrentEmailCode ? (
                                <Loader2 className='h-4 w-4 animate-spin' />
                              ) : currentEmailCountdown > 0 ? (
                                `${currentEmailCountdown}s`
                              ) : (
                                t('actions.sendCode')
                              )}
                            </Button>
                          </div>
                          {currentEmailVerificationSent && (
                            <div className='flex items-center gap-2 text-sm text-green-600 mt-2'>
                              <CheckCircle className='h-4 w-4' />
                              {t('email.codeSentCurrent')}
                            </div>
                          )}
                        </div>
                      </>
                    )}
                    {emailChangeStep === 2 && (
                      <>
                        <div>
                          <Label htmlFor='newEmail'>
                            {t('email.newEmail')}
                          </Label>
                          <Input
                            id='newEmail'
                            type='email'
                            placeholder={t('email.newEmailPlaceholder')}
                            value={emailForm.newEmail}
                            onChange={e =>
                              setEmailForm(prev => ({
                                ...prev,
                                newEmail: e.target.value,
                              }))
                            }
                            className='mt-2'
                            disabled={changingEmail}
                          />
                        </div>
                        <div>
                          <Label htmlFor='newEmailCode'>
                            {t('email.verificationCode')}
                          </Label>
                          <div className='flex gap-2 mt-2'>
                            <Input
                              id='newEmailCode'
                              placeholder={t('email.codePlaceholder')}
                              maxLength={6}
                              value={emailForm.newEmailCode}
                              onChange={e =>
                                setEmailForm(prev => ({
                                  ...prev,
                                  newEmailCode: e.target.value,
                                }))
                              }
                              disabled={changingEmail}
                            />
                            <Button
                              type='button'
                              variant='outline'
                              onClick={sendNewEmailVerificationCode}
                              disabled={
                                newEmailCountdown > 0 || sendingNewEmailCode
                              }
                              className='whitespace-nowrap'
                            >
                              {sendingNewEmailCode ? (
                                <Loader2 className='h-4 w-4 animate-spin' />
                              ) : newEmailCountdown > 0 ? (
                                `${newEmailCountdown}s`
                              ) : (
                                t('actions.sendCode')
                              )}
                            </Button>
                          </div>
                          {newEmailVerificationSent && (
                            <div className='flex items-center gap-2 text-sm text-green-600 mt-2'>
                              <CheckCircle className='h-4 w-4' />
                              {t('email.codeSentNew')}
                            </div>
                          )}
                        </div>
                      </>
                    )}
                    <DialogFooter>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={handleEmailDialogClose}
                        disabled={
                          verifyingCurrentEmail ||
                          changingEmail ||
                          sendingCurrentEmailCode ||
                          sendingNewEmailCode
                        }
                      >
                        {t('actions.cancel')}
                      </Button>
                      {emailChangeStep === 1 && (
                        <Button
                          type='button'
                          onClick={verifyCurrentEmailCode}
                          disabled={
                            !currentEmailVerificationSent ||
                            verifyingCurrentEmail
                          }
                          className='flex items-center gap-2'
                        >
                          {verifyingCurrentEmail ? (
                            <Loader2 className='h-4 w-4 animate-spin' />
                          ) : (
                            <>
                              {t('actions.next')}
                              <ArrowRight className='h-4 w-4' />
                            </>
                          )}
                        </Button>
                      )}
                      {emailChangeStep === 2 && (
                        <>
                          <Button
                            type='button'
                            variant='outline'
                            onClick={handleBackToStep1}
                            disabled={changingEmail || sendingNewEmailCode}
                            className='flex items-center gap-2'
                          >
                            <ArrowLeft className='h-4 w-4' />
                            {t('actions.back')}
                          </Button>
                          <Button
                            type='button'
                            onClick={completeEmailChange}
                            disabled={
                              !newEmailVerificationSent || changingEmail
                            }
                            className='flex items-center gap-2'
                          >
                            {changingEmail ? (
                              <Loader2 className='h-4 w-4 animate-spin' />
                            ) : (
                              <>
                                {t('actions.confirmChange')}
                                <CheckCircle className='h-4 w-4' />
                              </>
                            )}
                          </Button>
                        </>
                      )}
                    </DialogFooter>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>

        {/* 密码安全 */}
        <Card className='hover:shadow-md transition-shadow'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-3'>
                <Lock className='h-5 w-5 text-muted-foreground' />
                <div>
                  <div className='font-medium'>{t('profile.password')}</div>
                  <div className='text-sm text-muted-foreground'>
                    {t('profile.passwordDescription')}
                  </div>
                </div>
              </div>
              <Dialog
                open={isPasswordDialogOpen}
                onOpenChange={setIsPasswordDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button size='sm' variant='outline'>
                    <Lock className='h-4 w-4 mr-1' />
                    {t('actions.changePassword')}
                  </Button>
                </DialogTrigger>
                <DialogContent className='sm:max-w-md'>
                  <DialogHeader>
                    <DialogTitle>{t('password.dialogTitle')}</DialogTitle>
                    <DialogDescription>
                      {t('password.dialogDescription')}
                    </DialogDescription>
                  </DialogHeader>
                  <div className='space-y-4'>
                    <div>
                      <Label htmlFor='currentPassword'>
                        {t('password.currentPassword')}
                      </Label>
                      <div className='relative mt-2'>
                        <Input
                          id='currentPassword'
                          type={showCurrentPassword ? 'text' : 'password'}
                          placeholder={t('password.currentPasswordPlaceholder')}
                          value={passwordForm.currentPassword}
                          onChange={e =>
                            setPasswordForm(prev => ({
                              ...prev,
                              currentPassword: e.target.value,
                            }))
                          }
                          disabled={submitting}
                        />
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                          onClick={() =>
                            setShowCurrentPassword(!showCurrentPassword)
                          }
                          disabled={submitting}
                        >
                          {showCurrentPassword ? (
                            <EyeOff className='h-4 w-4' />
                          ) : (
                            <Eye className='h-4 w-4' />
                          )}
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor='newPassword'>
                        {t('password.newPassword')}
                      </Label>
                      <div className='relative mt-2'>
                        <Input
                          id='newPassword'
                          type={showNewPassword ? 'text' : 'password'}
                          placeholder={t('password.newPasswordPlaceholder')}
                          value={passwordForm.newPassword}
                          onChange={e =>
                            setPasswordForm(prev => ({
                              ...prev,
                              newPassword: e.target.value,
                            }))
                          }
                          disabled={submitting}
                        />
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          disabled={submitting}
                        >
                          {showNewPassword ? (
                            <EyeOff className='h-4 w-4' />
                          ) : (
                            <Eye className='h-4 w-4' />
                          )}
                        </Button>
                      </div>
                      <div className='text-xs text-muted-foreground mt-1'>
                        {t('password.passwordHint')}
                      </div>
                    </div>
                    <div>
                      <Label htmlFor='confirmPassword'>
                        {t('password.confirmPassword')}
                      </Label>
                      <div className='relative mt-2'>
                        <Input
                          id='confirmPassword'
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder={t('password.confirmPasswordPlaceholder')}
                          value={passwordForm.confirmPassword}
                          onChange={e =>
                            setPasswordForm(prev => ({
                              ...prev,
                              confirmPassword: e.target.value,
                            }))
                          }
                          disabled={submitting}
                        />
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          disabled={submitting}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className='h-4 w-4' />
                          ) : (
                            <Eye className='h-4 w-4' />
                          )}
                        </Button>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={() => {
                          setIsPasswordDialogOpen(false);
                          setPasswordForm({
                            currentPassword: '',
                            newPassword: '',
                            confirmPassword: '',
                          });
                        }}
                        disabled={submitting}
                      >
                        {t('actions.cancel')}
                      </Button>
                      <Button
                        type='submit'
                        onClick={handlePasswordSubmit}
                        disabled={submitting}
                      >
                        {submitting && (
                          <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                        )}
                        {t('actions.confirm')}
                      </Button>
                    </DialogFooter>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>

        {/* 密码修改时间 */}
        <Card className='hover:shadow-md transition-shadow'>
          <CardContent className='p-6'>
            <div className='flex items-center gap-3'>
              <Calendar className='h-5 w-5 text-muted-foreground' />
              <div>
                <div className='font-medium'>
                  {t('profile.lastPasswordChange')}
                </div>
                <div className='text-sm text-muted-foreground'>
                  {formatDateTime(userData.lastPasswordChange)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 安全提醒 */}
      <Alert>
        <Shield className='h-4 w-4' />
        <AlertDescription>{t('security_alert.description')}</AlertDescription>
      </Alert>
    </div>
  );
}
