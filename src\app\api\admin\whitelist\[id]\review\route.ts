import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 验证管理员权限的辅助函数
async function requireAdmin() {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('请先登录');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (user?.role !== 'ADMIN') {
    throw new Error('权限不足');
  }

  return session;
}

// 审核白名单的验证schema
const reviewWhitelistSchema = z.object({
  action: z.enum(['approve', 'reject']),
  reviewNote: z.string().optional(),
});

// 审核白名单（通过/拒绝）
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    const session = await requireAdmin();

    const { id } = await params;
    const body = await request.json();

    // 验证请求数据
    const validation = reviewWhitelistSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: '请求数据格式错误',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { action, reviewNote } = validation.data;

    // 检查白名单条目是否存在并包含审核状态（临时硬编码字段检查）
    const existingWhitelist = await prisma.shopWhitelist.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingWhitelist) {
      return NextResponse.json({ error: '白名单条目不存在' }, { status: 404 });
    }

    // 更新审核状态和相关信息
    const isActive = action === 'approve'; // 只有通过审核的才生效

    // 使用原生SQL来更新，避免Prisma类型问题
    const statusValue = action === 'approve' ? 'APPROVED' : 'REJECTED';
    await prisma.$executeRaw`
      UPDATE shop_whitelist 
      SET 
        status = ${statusValue}::"WhitelistStatus",
        "isActive" = ${isActive},
        "reviewedBy" = ${session.user!.id},
        "reviewedAt" = NOW(),
        "reviewNote" = ${reviewNote || null},
        "updatedAt" = NOW()
      WHERE id = ${id}
    `;

    // 重新查询更新后的数据
    const updatedWhitelist = await prisma.shopWhitelist.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const actionText = action === 'approve' ? '通过' : '拒绝';

    return NextResponse.json({
      message: `白名单申请已${actionText}`,
      whitelistItem: updatedWhitelist,
    });
  } catch (error) {
    console.error('审核白名单失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' },
      { status: 500 },
    );
  }
}
