module.exports = {
  // 语言环境路径
  localesPaths: ['messages'],
  
  // 路径匹配模式 - 匹配 messages/{locale}/{namespace}.json
  pathMatcher: '{locale}/{namespaces}.json',
  
  // 启用命名空间支持
  namespace: true,
  
  // 键值样式 - 嵌套结构
  keystyle: 'nested',
  
  // 支持的解析器
  enabledParsers: ['json'],
  
  // 源语言（默认语言）
  sourceLanguage: 'zh',
  
  // 显示语言
  displayLanguage: 'en',
  
  // 支持的框架
  enabledFrameworks: ['next-intl', 'react'],
  
  // 自动提取配置
  extract: {
    autoDetect: true,
    keygenStyle: 'camelCase',
    keyMaxLength: 50,
    ignoredByFiles: {
      '**/*.test.{js,ts,jsx,tsx}': ['*'],
      '**/*.spec.{js,ts,jsx,tsx}': ['*'],
      '**/node_modules/**': ['*'],
      '**/.next/**': ['*'],
      '**/dist/**': ['*'],
      '**/build/**': ['*']
    }
  },
  
  // 翻译候选配置
  translate: {
    engines: ['google', 'bing'],
    fallbackLocale: 'en',
    promptSource: true
  },
  
  // 注解配置
  annotations: {
    enabled: true,
    inPlace: true
  },
  
  // 排序配置
  sortKeys: true,
  
  // 文件结构配置
  fileStructure: 'file',
  
  // 忽略的文件和目录
  ignoredLocales: [],
  ignoredNamespaces: [],
  
  // 自定义规则
  rules: {
    'no-missing-keys': 'error',
    'no-unused-keys': 'warn',
    'no-empty-translation': 'warn'
  },
  
  // 编辑器配置
  editor: {
    preferEditor: true,
    autoCompletion: true,
    hover: true
  }
};
