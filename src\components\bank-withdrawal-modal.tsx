'use client';

import { CreditCard } from 'lucide-react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUserSystemRate } from '@/hooks/use-rates-api';
import { useCreateWithdrawalRequest } from '@/hooks/use-withdrawal-api';

interface BankWithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialAmount: string;
  userBalance: number;
  withdrawalToken?: string; // 新增：验证token参数
}

// 国际美元账户验证规则
const bankWithdrawalSchema = z.object({
  // 账户持有人：支持英文字母、空格、连字符、撇号
  accountHolder: z
    .string()
    .min(2, '账户全名至少2个字符')
    .max(100, '账户全名不能超过100个字符')
    .regex(
      /^[A-Za-z\s\-'./]+$/,
      '账户全名只能包含英文字母、空格、连字符、撇号和点号',
    ),

  // 银行账户号码：支持8-34位数字和字母（IBAN标准）
  bankCard: z
    .string()
    .min(8, '账户号码至少8位')
    .max(34, '账户号码不能超过34位')
    .regex(/^[A-Z0-9]+$/, '账户号码只能包含大写字母和数字'),

  // 银行名称：支持英文和常见符号
  bankName: z
    .string()
    .min(2, '银行名称至少2个字符')
    .max(100, '银行名称不能超过100个字符')
    .regex(/^[A-Za-z0-9\s\-&./,]+$/, '银行名称格式不正确'),

  // SWIFT代码：严格按照ISO 13616标准
  bankSwift: z
    .string()
    .regex(
      /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/,
      'SWIFT代码格式不正确（如：CHASUS33XXX）',
    ),

  // 银行地址：支持英文地址格式
  branchAddress: z
    .string()
    .min(10, '银行地址至少10个字符')
    .max(200, '银行地址不能超过200个字符')
    .regex(/^[A-Za-z0-9\s\-,.#/]+$/, '银行地址格式不正确'),

  // 银行国家：使用ISO 3166-1 alpha-2国家代码
  bankCountry: z
    .string()
    .length(2, '请输入2位国家代码（如：US, GB, CA）')
    .regex(/^[A-Z]{2}$/, '国家代码必须是2位大写字母'),
});

type BankWithdrawalFormData = z.infer<typeof bankWithdrawalSchema> & {
  amount: number;
};

// 常见国家代码提示
const countryCodeHints = {
  US: '美国',
  GB: '英国',
  CA: '加拿大',
  AU: '澳大利亚',
  DE: '德国',
  FR: '法国',
  JP: '日本',
  SG: '新加坡',
  HK: '香港',
  CH: '瑞士',
};

export function BankWithdrawalModal({
  isOpen,
  onClose,
  initialAmount,
  userBalance,
  withdrawalToken, // 新增参数
}: BankWithdrawalModalProps) {
  const [formData, setFormData] = useState<BankWithdrawalFormData>({
    amount: parseFloat(initialAmount || '0') || 0,
    accountHolder: '',
    bankCard: '',
    bankName: '',
    bankSwift: '',
    branchAddress: '',
    bankCountry: '',
  });

  const [errors, setErrors] = useState<
    Partial<Record<keyof BankWithdrawalFormData, string>>
  >({});

  const { data: systemRate } = useUserSystemRate();
  const createWithdrawalMutation = useCreateWithdrawalRequest();

  // 当 initialAmount 变化时更新金额
  useEffect(() => {
    const amount = parseFloat(initialAmount || '0') || 0;
    setFormData(prev => ({ ...prev, amount }));
  }, [initialAmount]);

  // 当模态框打开时重新初始化金额
  useEffect(() => {
    if (isOpen) {
      const amount = parseFloat(initialAmount || '0') || 0;
      setFormData(prev => ({ ...prev, amount }));
    }
  }, [isOpen, initialAmount]);

  // 格式化账户号码（转换为大写，移除空格）
  const formatAccountNumber = (value: string): string => {
    return value.replace(/\s/g, '').toUpperCase();
  };

  // 格式化SWIFT代码
  const formatSwiftCode = (value: string): string => {
    return value
      .replace(/[^A-Z0-9]/g, '')
      .toUpperCase()
      .slice(0, 11);
  };

  // 格式化账户持有人姓名
  const formatAccountHolder = (value: string): string => {
    return value.replace(/[^A-Za-z\s\-'./]/g, '').slice(0, 100);
  };

  // 格式化银行名称
  const formatBankName = (value: string): string => {
    return value.replace(/[^A-Za-z0-9\s\-&./,]/g, '').slice(0, 100);
  };

  // 格式化银行地址
  const formatBankAddress = (value: string): string => {
    return value.replace(/[^A-Za-z0-9\s\-,.#/]/g, '').slice(0, 200);
  };

  // 格式化国家代码
  const formatCountryCode = (value: string): string => {
    return value.replace(/[^A-Z]/g, '').slice(0, 2);
  };

  // 表单验证
  const validateForm = (): boolean => {
    try {
      // 验证所有表单字段
      bankWithdrawalSchema.parse({
        accountHolder: formData.accountHolder,
        bankCard: formData.bankCard,
        bankName: formData.bankName,
        bankSwift: formData.bankSwift,
        branchAddress: formData.branchAddress,
        bankCountry: formData.bankCountry,
      });

      // 验证提现金额
      if (
        typeof systemRate?.minimumWithdrawalAmount === 'number' &&
        formData.amount < systemRate.minimumWithdrawalAmount
      ) {
        setErrors({
          amount: `最低提现金额为$${systemRate.minimumWithdrawalAmount.toFixed(2)}`,
        });
        return false;
      }

      if (formData.amount > 50000) {
        setErrors({ amount: '单次提现金额不能超过$50,000' });
        return false;
      }

      // 检查余额
      if (formData.amount > userBalance) {
        setErrors({ amount: '提现金额不能超过可用余额' });
        return false;
      }

      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof BankWithdrawalFormData, string>> =
          {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof BankWithdrawalFormData] =
              err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // 检查是否有有效的验证token
    if (!withdrawalToken) {
      toast.error('请先完成邮箱验证');
      onClose(); // 关闭当前模态框，让用户重新进行验证
      return;
    }

    try {
      await createWithdrawalMutation.mutateAsync({
        amount: formData.amount,
        withdrawMethod: 'BANK_CARD',
        accountHolder: formData.accountHolder,
        bankCard: formData.bankCard,
        bankName: formData.bankName,
        bankSwift: formData.bankSwift,
        branchAddress: formData.branchAddress,
        bankCountry: formData.bankCountry,
        withdrawalToken, // 传递验证token
      });

      onClose();
      // 重置表单（保持金额不变）
      setFormData(prev => ({
        ...prev,
        accountHolder: '',
        bankCard: '',
        bankName: '',
        bankSwift: '',
        branchAddress: '',
        bankCountry: '',
      }));
      setErrors({});
    } catch (error) {
      console.error('提现申请失败:', error);
      // 如果是验证相关的错误，关闭模态框让用户重新验证
      if (error instanceof Error && error.message.includes('验证')) {
        onClose();
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[500px] max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CreditCard className='h-5 w-5 text-blue-600' />
            国际美元账户提现
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 提现金额显示 */}
          <div className='space-y-2'>
            <Label>提现金额</Label>
            <div className='flex items-center justify-between p-3 bg-muted rounded-md'>
              <span className='text-sm text-muted-foreground'>提现金额：</span>
              <span className='text-lg font-semibold'>
                {formData.amount && formData.amount > 0
                  ? `$${formData.amount.toFixed(2)}`
                  : '请先在钱包页面输入金额'}
              </span>
            </div>
            {errors.amount && (
              <p className='text-sm text-red-600'>{errors.amount}</p>
            )}
          </div>

          {/* 账户全名 */}
          <div className='space-y-2'>
            <Label htmlFor='accountHolder'>
              账户全名 (Account Holder Name) *
            </Label>
            <Input
              id='accountHolder'
              placeholder='John Smith 或 Mary Johnson-Brown'
              value={formData.accountHolder}
              onChange={e =>
                setFormData({
                  ...formData,
                  accountHolder: formatAccountHolder(e.target.value),
                })
              }
            />
            <p className='text-xs text-muted-foreground'>
              请输入与银行账户完全一致的英文全名
            </p>
            {errors.accountHolder && (
              <p className='text-sm text-red-600'>{errors.accountHolder}</p>
            )}
          </div>

          {/* 账户号码 */}
          <div className='space-y-2'>
            <Label htmlFor='bankCard'>
              银行账户号码 (Account Number/IBAN) *
            </Label>
            <Input
              id='bankCard'
              placeholder='********************** 或 **********'
              value={formData.bankCard}
              onChange={e =>
                setFormData({
                  ...formData,
                  bankCard: formatAccountNumber(e.target.value),
                })
              }
            />
            <p className='text-xs text-muted-foreground'>
              支持IBAN格式或传统账户号码
            </p>
            {errors.bankCard && (
              <p className='text-sm text-red-600'>{errors.bankCard}</p>
            )}
          </div>

          {/* 银行名称 */}
          <div className='space-y-2'>
            <Label htmlFor='bankName'>银行名称 (Bank Name) *</Label>
            <Input
              id='bankName'
              placeholder='Chase Bank, HSBC, Bank of America'
              value={formData.bankName}
              onChange={e =>
                setFormData({
                  ...formData,
                  bankName: formatBankName(e.target.value),
                })
              }
            />
            <p className='text-xs text-muted-foreground'>
              请输入银行的完整英文名称
            </p>
            {errors.bankName && (
              <p className='text-sm text-red-600'>{errors.bankName}</p>
            )}
          </div>

          {/* SWIFT代码 */}
          <div className='space-y-2'>
            <Label htmlFor='bankSwift'>SWIFT/BIC代码 (SWIFT Code) *</Label>
            <Input
              id='bankSwift'
              placeholder='CHASUS33XXX, HBUKGB4B'
              value={formData.bankSwift}
              onChange={e =>
                setFormData({
                  ...formData,
                  bankSwift: formatSwiftCode(e.target.value),
                })
              }
            />
            <p className='text-xs text-muted-foreground'>
              8-11位国际银行识别代码
            </p>
            {errors.bankSwift && (
              <p className='text-sm text-red-600'>{errors.bankSwift}</p>
            )}
          </div>

          {/* 银行地址 */}
          <div className='space-y-2'>
            <Label htmlFor='branchAddress'>
              银行分行地址 (Branch Address) *
            </Label>
            <Input
              id='branchAddress'
              placeholder='123 Main Street, New York, NY 10001'
              value={formData.branchAddress}
              onChange={e =>
                setFormData({
                  ...formData,
                  branchAddress: formatBankAddress(e.target.value),
                })
              }
            />
            <p className='text-xs text-muted-foreground'>
              银行分行的完整英文地址
            </p>
            {errors.branchAddress && (
              <p className='text-sm text-red-600'>{errors.branchAddress}</p>
            )}
          </div>

          {/* 银行国家 */}
          <div className='space-y-2'>
            <Label htmlFor='bankCountry'>银行国家代码 (Country Code) *</Label>
            <Input
              id='bankCountry'
              placeholder='US, GB, CA, AU'
              value={formData.bankCountry}
              onChange={e =>
                setFormData({
                  ...formData,
                  bankCountry: formatCountryCode(e.target.value),
                })
              }
              maxLength={2}
            />
            <div className='text-xs text-muted-foreground'>
              <p>请输入2位ISO国家代码，常用代码：</p>
              <div className='grid grid-cols-2 gap-1 mt-1'>
                {Object.entries(countryCodeHints).map(([code, name]) => (
                  <span key={code} className='text-xs'>
                    {code} - {name}
                  </span>
                ))}
              </div>
            </div>
            {errors.bankCountry && (
              <p className='text-sm text-red-600'>{errors.bankCountry}</p>
            )}
          </div>

          {/* 重要提示 */}
          <div className='p-3 bg-yellow-50 border border-yellow-200 rounded-md'>
            <h4 className='text-sm font-medium text-yellow-800 mb-2'>
              重要提示：
            </h4>
            <ul className='text-xs text-yellow-700 space-y-1'>
              <li>• 所有信息必须与您的银行账户信息完全一致</li>
              <li>• 账户持有人姓名必须与您的身份证明文件一致</li>
              <li>• 请确保银行支持接收国际美元转账</li>
              <li>• 处理时间通常为3-5个工作日</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={onClose}>
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              createWithdrawalMutation.isPending ||
              !formData.amount ||
              formData.amount <= 0 ||
              !withdrawalToken
            }
          >
            {createWithdrawalMutation.isPending
              ? '提交中...'
              : !withdrawalToken
                ? '请先完成邮箱验证'
                : '确认提现'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
