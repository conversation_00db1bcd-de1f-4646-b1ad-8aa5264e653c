import { z } from 'zod';

// 状态枚举
export enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

// 基础接口
export interface Platform {
  id: string;
  name: string;
  status: Status;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  status: Status;
  createdAt: string;
  updatedAt: string;
}

export interface ChargebackType {
  id: string;
  name: string;
  rate: number;
  status: Status;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  rate: number;
  status: Status;
  createdAt: string;
  updatedAt: string;
}

export interface SystemRate {
  id: string;
  noEvidenceExtraRate: number;
  depositRatio: number;
  bankWithdrawalRate: number;
  erc20WithdrawalRate: number;
  trc20WithdrawalRate: number;
  minimumWithdrawalAmount: number;
  createdAt: string;
  updatedAt: string;
}

// Zod 验证模式
export const platformSchema = z.object({
  name: z
    .string()
    .min(1, '平台名称不能为空')
    .max(50, '平台名称不能超过50个字符'),
  status: z.nativeEnum(Status),
});

export const categorySchema = z.object({
  name: z
    .string()
    .min(1, '分类名称不能为空')
    .max(50, '分类名称不能超过50个字符'),
  status: z.nativeEnum(Status),
});

export const chargebackTypeSchema = z.object({
  name: z
    .string()
    .min(1, '拒付类型名称不能为空')
    .max(50, '拒付类型名称不能超过50个字符'),
  rate: z.number().min(0, '费率不能为负数').max(100, '费率不能超过100'),
  status: z.nativeEnum(Status),
});

export const paymentMethodSchema = z.object({
  name: z
    .string()
    .min(1, '支付方式名称不能为空')
    .max(50, '支付方式名称不能超过50个字符'),
  rate: z.number().min(0, '费率不能为负数').max(100, '费率不能超过100'),
  status: z.nativeEnum(Status),
});

export const systemRateSchema = z.object({
  noEvidenceExtraRate: z
    .number()
    .min(0, '费率不能为负数')
    .max(100, '费率不能超过100'),
  depositRatio: z.number().min(0, '比例不能为负数').max(100, '比例不能超过100'),
  bankWithdrawalRate: z
    .number()
    .min(0, '费率不能为负数')
    .max(100, '费率不能超过100'),
  erc20WithdrawalRate: z
    .number()
    .min(0, '费率不能为负数')
    .max(100, '费率不能超过100'),
  trc20WithdrawalRate: z
    .number()
    .min(0, '费率不能为负数')
    .max(100, '费率不能超过100'),
  minimumWithdrawalAmount: z.number().min(0, '最小提现金额不能为负数'),
});

// 更新验证模式
export const updatePlatformSchema = platformSchema.partial();
export const updateCategorySchema = categorySchema.partial();
export const updateChargebackTypeSchema = chargebackTypeSchema.partial();
export const updatePaymentMethodSchema = paymentMethodSchema.partial();

// 请求/响应类型
export type CreatePlatformRequest = z.infer<typeof platformSchema>;
export type UpdatePlatformRequest = z.infer<typeof updatePlatformSchema>;
export type CreateCategoryRequest = z.infer<typeof categorySchema>;
export type UpdateCategoryRequest = z.infer<typeof updateCategorySchema>;
export type CreateChargebackTypeRequest = z.infer<typeof chargebackTypeSchema>;
export type UpdateChargebackTypeRequest = z.infer<
  typeof updateChargebackTypeSchema
>;
export type CreatePaymentMethodRequest = z.infer<typeof paymentMethodSchema>;
export type UpdatePaymentMethodRequest = z.infer<
  typeof updatePaymentMethodSchema
>;
export type UpdateSystemRateRequest = z.infer<typeof systemRateSchema>;

// API 响应接口
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export type PlatformResponse = ApiResponse<Platform>;
export type PlatformsResponse = ApiResponse<Platform[]>;
export type CategoryResponse = ApiResponse<Category>;
export type CategoriesResponse = ApiResponse<Category[]>;
export type ChargebackTypeResponse = ApiResponse<ChargebackType>;
export type ChargebackTypesResponse = ApiResponse<ChargebackType[]>;
export type PaymentMethodResponse = ApiResponse<PaymentMethod>;
export type PaymentMethodsResponse = ApiResponse<PaymentMethod[]>;
export type SystemRateResponse = ApiResponse<SystemRate>;
