import { MemberPlan } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';

// 处理会员过期的定时委托
export async function POST(request: NextRequest) {
  try {
    const now = new Date();

    // 查找所有过期的会员用户
    const expiredUsers = await prisma.user.findMany({
      where: {
        memberPlan: {
          in: ['PRO', 'BUSINESS'], // 只查询付费用户
        },
        memberPlanExpiry: {
          lt: now, // 过期时间小于当前时间
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        memberPlan: true,
        memberPlanExpiry: true,
      },
    });

    if (expiredUsers.length === 0) {
      return NextResponse.json({
        success: true,
        message: '没有发现过期的会员用户',
        data: {
          expiredCount: 0,
        },
      });
    }

    // 批量更新过期用户为免费版
    const updatePromises = expiredUsers.map(user =>
      prisma.user.update({
        where: { id: user.id },
        data: {
          memberPlan: MemberPlan.FREE,
          memberPlanExpiry: null, // 免费版无过期时间
        },
      }),
    );

    await Promise.all(updatePromises);

    // 创建过期降级记录（可选，用于跟踪历史）
    const expiredUserIds = expiredUsers.map(user => user.id);

    console.log(`[会员过期处理] 处理了 ${expiredUsers.length} 个过期用户：`, {
      userIds: expiredUserIds,
      processedAt: now,
    });

    return NextResponse.json({
      success: true,
      message: `成功处理 ${expiredUsers.length} 个过期会员用户`,
      data: {
        expiredCount: expiredUsers.length,
        processedUsers: expiredUsers.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          previousPlan: user.memberPlan,
          expiredAt: user.memberPlanExpiry,
        })),
      },
    });
  } catch (error) {
    console.error('[会员过期处理] 处理失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '处理会员过期失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

// 允许外部调用（如定时委托系统）
export async function GET() {
  return POST({} as NextRequest);
}
