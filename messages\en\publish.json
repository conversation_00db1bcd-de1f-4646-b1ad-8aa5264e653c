{"navigation": {"title": "Publish Task", "description": "Follow the steps to fill in task information and publish quickly", "breadcrumb": "New Task"}, "steps": {"platformSelection": "Select Platform", "basicInfo": "Basic Information", "evidenceUpload": "Upload Evidence", "summary": "Confirm Submission"}, "platformSelection": {"title": "Select Platform", "selectPlatform": "Select Platform", "selectPlatformPlaceholder": "Please select a platform", "selectCategory": "Product Category", "selectCategoryPlaceholder": "Please select a category", "chargebackTypes": "Chargeback Types", "chargebackTypesDescription": "Multiple selection allowed, system will calculate based on lowest rate", "paymentMethods": "Payment Methods", "paymentMethodsDescription": "Multiple selection allowed, system will calculate based on lowest rate", "loading": "Loading configuration data...", "error": "Failed to load configuration, please refresh and try again", "platformLabels": {"DHgate": "DHgate", "独立站": "Independent Site"}, "categoryLabels": {"女装": "Women's Clothing", "男装": "Men's Clothing", "鞋类": "Shoes", "包袋箱包": "Bags & Luggage", "饰品与配饰": "Jewelry & Accessories", "美容与个人护理": "Beauty & Personal Care", "手机与配件": "Phones & Accessories", "消费电子": "Consumer Electronics", "电脑与办公": "Computers & Office", "家用电器": "Home Appliances", "家居与园艺": "Home & Garden", "厨房与餐饮用品": "Kitchen & Dining", "家纺": "Home Textiles", "灯具与照明": "Lights & Lighting", "婚庆与派对用品": "Wedding & Party Supplies", "玩具与礼品": "Toys & Gifts", "母婴与儿童用品": "Baby & Kids", "宠物用品": "Pet Supplies", "运动与户外用品": "Sports & Outdoor", "汽车与摩托车配件": "Auto & Motorcycle", "手表与眼镜": "Watches & Eyewear", "办公与文具用品": "Office & Stationery", "工具与工业设备": "Tools & Industrial", "安防与监控": "Security & Surveillance", "健康与保健": "Health & Wellness", "节日装饰与定制产品": "Holiday & Custom Products", "宗教与灵修用品": "Religious & Spiritual", "艺术与手工艺": "Arts & Crafts", "乐器与音响设备": "Musical Instruments & Audio", "图书与教育产品": "Books & Education", "虚拟商品与订阅服务": "Virtual & Subscription Services", "二手与再制造商品": "Secondhand & Refurbished", "电子烟与配件": "E-cigarette & Accessories", "成人用品": "Adult Products", "医疗器械与检测设备": "Medical Devices & Testing", "液体与化学品": "Liquids & Chemicals", "粉末类与易制品": "Powders & Controlled Substances", "危险与仿真类物品": "Dangerous & Imitation Items", "敏感设计商品": "Sensitive Design Products", "特殊认证与许可产品": "Special Certification & Licensed Products"}, "chargebackTypeLabels": {"违反信用卡支付政策": "Violates Credit Card Payment Policy", "违反PayPal支付政策": "Violates PayPal Payment Policy", "违反运输守则": "Violates Shipping Rules", "违反健康守则": "Violates Health Guidelines", "仿牌产品": "Counterfeit Products", "禁售产品": "Prohibited Products", "虚假宣传": "False Advertising", "侵权产品": "Infringing Products", "未授权产品": "Unauthorized Products"}, "paymentMethodLabels": {"信用卡": "Credit Card", "PayPal": "PayPal"}, "validation": {"platformRequired": "Please select a platform", "categoryRequired": "Please select a product category", "chargebackTypesRequired": "Please select at least one chargeback type", "paymentMethodsRequired": "Please select at least one payment method", "productUrlRequired": "Please enter product URL", "productUrlInvalid": "Please enter a valid product URL", "urlMustStartWithHttps": "Product URL must start with https://", "invalidUrl": "Please enter a valid URL format", "productDescriptionRequired": "Please enter product description", "englishOnly": "Product description can only contain English characters (letters, numbers, punctuation, and spaces)", "englishOnlyHint": "Hint: Product description can only use English characters including letters, numbers, punctuation, and spaces", "quantityRequired": "Please enter quantity", "quantityMin": "Quantity must be at least 1", "unitPriceRequired": "Please enter unit price", "unitPriceMin": "Unit price must be greater than 0", "recipientNameRequired": "Please enter recipient name", "recipientPhoneRequired": "Please enter recipient phone", "shippingAddressRequired": "Please enter shipping address", "cartScreenshotRequired": "Please upload cart screenshot"}}, "basicInfo": {"title": "Basic Product Information", "productUrl": "Product URL", "productUrlPlaceholder": "https://...", "productDescription": "Product Description", "productDescriptionPlaceholder": "Describe the product information in detail (such as specifications, size, color, etc.) to ensure that the description is accurate and avoid errors in completing the commission", "quantity": "Quantity", "quantityPlaceholder": "Enter quantity", "unitPrice": "Unit Price (USD)", "listingTime": "Listing Duration", "listingTimePlaceholder": "Select listing duration", "listingTimeOptions": {"24": "24 hours", "48": "48 hours", "72": "72 hours", "96": "96 hours (4 days)", "120": "120 hours (5 days)", "144": "144 hours (6 days)", "168": "168 hours (7 days)"}, "listingTimeDescription": "The task will remain listed for the specified duration after publication", "recipientInfo": "Recipient Information", "recipientName": "Recipient Name", "recipientNamePlaceholder": "Please enter recipient name", "recipientPhone": "Recipient Phone", "recipientPhonePlaceholder": "Please enter recipient phone", "shippingAddress": "Shipping Address", "shippingAddressPlaceholder": "Please enter detailed shipping address", "cartScreenshot": "<PERSON>t Screenshot", "cartScreenshotDescription": "Upload cart screenshot with clear product information", "cartScreenshotPlaceholder": "Click to upload or drag cart screenshot here", "cartScreenshotFormats": "Supports PNG, JPG formats, max 10MB per file", "cartScreenshotUpload": {"label": "<PERSON>t Screenshot", "placeholder": "Upload cart screenshot", "description": "Upload shopping cart screenshot showing product details", "uploadText": "Click to upload or drag cart screenshot here", "supportedFormats": "Supports PNG, JPG, JPEG formats, max 10MB per file", "maxFiles": "Maximum 5 files", "uploading": "Uploading cart screenshot...", "uploadSuccess": "Cart screenshot uploaded successfully", "uploadFailed": "Failed to upload cart screenshot", "removeImage": "Remove image", "dragActive": "Drop cart screenshot here", "fileTypeError": "Please upload image files only", "fileSizeError": "Image size exceeds 10MB limit", "fileCountError": "Maximum 5 cart screenshots allowed"}, "whitelistCheck": {"checking": "Checking whitelist...", "whitelisted": "This store is prohibited from publishing tasks, please select another store", "notWhitelisted": "This product is not whitelisted and can be published", "error": "Whitelist check failed, please try again later"}}, "evidenceUpload": {"title": "Evidence Upload Method (Upload valid chargeback evidence to assist executors)", "description": "Choose evidence upload method. All types require evidence fee payment first. Fee will be refunded after approval, executor reward excludes this fee", "types": {"immediate": {"label": "Upload Now", "description": "Upload evidence files immediately, fee refunded after approval", "feeNote": "Refunded after approval"}, "later": {"label": "Upload Later", "description": "Upload evidence after task publication, fee refunded after approval", "feeNote": "Refunded after approval"}, "none": {"label": "No Evidence", "description": "Evidence generated by executor, this fee serves as additional reward", "feeNote": "As additional reward"}}, "fileUpload": {"title": "Upload Evidence Files", "description": "Supports image and video files, max 50MB per file", "placeholder": "Click to upload or drag evidence files here", "formats": "Supports PNG, JPG, MP4, MOV formats, max 50MB per file", "uploading": "Uploading...", "uploadSuccess": "Upload successful", "uploadFailed": "Upload failed", "removeFile": "Remove file", "clearAll": "Clear all", "filesUploaded": "files uploaded", "dragActive": "Drop files here to upload", "fileTypeError": "Unsupported file type", "fileSizeError": "File size exceeds limit", "fileCountError": "Too many files selected", "networkError": "Network error, please try again", "browserNotSupported": "Your browser does not support file upload", "selectFiles": "Select Files", "or": "or", "dragFilesHere": "drag files here"}}, "summary": {"title": "Task Information Summary", "description": "Please carefully review the following information and confirm before submitting the task", "platformInfo": "Platform Information", "targetPlatform": "Target Platform", "productCategory": "Product Category", "chargebackTypes": "Chargeback Types", "paymentMethods": "Payment Methods", "productInfo": "Product Information", "productUrl": "Product URL", "productDescription": "Product Description", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Total Price", "listingTime": "Listing Duration", "recipientInfo": "Recipient Information", "recipientName": "Recipient", "recipientPhone": "Contact Phone", "shippingAddress": "Shipping Address", "evidenceInfo": "Evidence Information", "evidenceUploadType": "Upload Method", "feeBreakdown": "Fee Breakdown", "productPrice": "Product Total", "paymentMethodFee": "Payment Method Fee", "chargebackTypeFee": "Chargeback Type Fee", "evidenceFee": "Evidence Fee", "memberServiceFee": "Member Service Fee", "memberDiscount": "Member Discount", "finalTotal": "Final Total", "hours": "hours", "productUnitPrice": "Product Unit Price", "productDescriptionLabel": "Product Description", "quantityLabel": "Purchase Quantity", "listingDuration": "Listing Duration", "recipientAddress": "Recipient Address", "recipientNameLabel": "Recipient Name", "contactPhone": "Contact Phone", "detailedAddress": "Detailed Address", "evidenceUploadMethod": "Evidence Upload Method", "filesUploaded": "files", "filesUploadedPrefix": "Uploaded", "feeCalculation": "Fee Calculation", "feeCalculationDescription": "The following is the detailed fee breakdown based on your selections", "productTotalPrice": "Product Total Price:", "paymentMethodRate": "Payment Method Fee", "evidenceFeeLabel": "Evidence Fee", "memberServiceFeeLabel": "Member Service Fee", "finalPayment": "Final Payment:", "feeExplanationTitle": "Fee Explanation", "feeExplanation": {"productTotal": "Product Total Price = Unit Price × Quantity", "paymentMethod": "Payment method fee is determined by the selected payment method", "chargebackType": "Chargeback type fee is determined by the selected chargeback type", "evidence": "Evidence fee is only charged when selecting \"Upload Now\"", "memberService": "Member service fee is the platform service fee", "finalAmount": "Final payment amount includes all fees", "paymentMethodRate": "Payment method and chargeback type fees are calculated at the lowest rate", "memberServiceRate": "Member service fee is calculated based on membership plan rate", "evidenceRefund": "Evidence fee: Will be refunded to you after approval", "deductionNotice": "Corresponding fees will be deducted from your account balance after task submission"}, "hoursUnit": "hours", "daysUnit": "days"}, "actions": {"previous": "Previous", "next": "Next", "submit": "Submit Task", "cancel": "Cancel", "confirm": "Confirm", "checkWhitelist": "Check Whitelist"}, "dialog": {"confirmSubmit": {"title": "Confirm Task Submission", "description": "You are about to submit this task. Once submitted, it cannot be modified. Please confirm all information is correct.", "taskFee": "Task Fee:"}, "insufficientBalance": {"title": "Insufficient Balance", "description": "Your account balance is insufficient to pay for this task. Please top up first before submitting.", "currentBalance": "Current Balance:", "requiredAmount": "Required Amount:", "goToWallet": "Go to Wallet"}, "whitelistBlocked": {"title": "<PERSON><PERSON> Continue", "description": ". This store is prohibited from publishing tasks. Please change to a product link from another store before continuing."}}, "messages": {"insufficientBalance": {"title": "Insufficient Balance", "currentBalance": "Current Balance: ", "requiredAmount": "Required Amount: ", "goToWallet": "Go to Wallet"}, "submitSuccess": "Task submitted successfully!", "submitError": "Task submission failed, please try again", "validationError": "Please check if form information is complete", "networkError": "Network error, please check your connection"}, "common": {"recommended": "Recommended"}, "fileUpload": {"common": {"selectFile": "Select File", "selectFiles": "Select Files", "uploading": "Uploading...", "uploadComplete": "Upload Complete", "uploadFailed": "Upload Failed", "retry": "Retry", "cancel": "Cancel", "remove": "Remove", "preview": "Preview", "download": "Download"}, "errors": {"fileRequired": "Please select at least one file", "invalidFileType": "Invalid file type. Please select supported formats.", "fileTooLarge": "File size exceeds the maximum limit", "tooManyFiles": "Too many files selected. Maximum allowed: {max}", "uploadTimeout": "Upload timeout. Please try again.", "uploadInterrupted": "Upload was interrupted. Please try again.", "insufficientStorage": "Insufficient storage space", "browserNotSupported": "Your browser does not support this feature", "permissionDenied": "File access permission denied", "corruptedFile": "File appears to be corrupted"}, "browser": {"dragAndDrop": "Drag and drop files here", "clickToSelect": "Click to select files", "pasteFromClipboard": "Paste from clipboard", "unsupportedBrowser": "Your browser version is too old. Please update your browser.", "enableJavaScript": "Please enable JavaScript to use file upload", "cookiesRequired": "Cookies must be enabled for file upload"}}, "progress": {"step": "Step", "of": "of", "completed": "Completed"}}