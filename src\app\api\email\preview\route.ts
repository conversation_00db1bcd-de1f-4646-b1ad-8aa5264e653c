import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import {
  depositSuccessTemplateI18n,
  depositFailedTemplateI18n,
  type DepositSuccessEmailData,
  type DepositFailedEmailData,
} from '@/lib/email-templates';

// 预览请求验证schema
const previewSchema = z.object({
  type: z.enum(['deposit-success', 'deposit-failed']),
  language: z.enum(['zh', 'en']).optional(),
  data: z.object({}).passthrough().optional(),
});

// 默认测试数据
const getDefaultTestData = (type: string, language: 'zh' | 'en') => {
  const baseData = {
    userName: language === 'zh' ? '张三' : '<PERSON>',
    userEmail: '<EMAIL>',
    amount: 100.0,
    currency: 'USD',
    transactionId: 'TXN123456789',
    paymentMethod: language === 'zh' ? '信用卡' : 'Credit Card',
    language,
  };

  switch (type) {
    case 'deposit-success':
      return {
        ...baseData,
        processedAt: new Date().toISOString(),
        newBalance: 250.0,
      } as DepositSuccessEmailData;

    case 'deposit-failed':
      return {
        ...baseData,
        failureReason:
          language === 'zh'
            ? '银行卡余额不足，请检查您的账户余额后重试'
            : 'Insufficient funds. Please check your account balance and try again.',
        failedAt: new Date().toISOString(),
      } as DepositFailedEmailData;

    default:
      throw new Error(`Unsupported email type: ${type}`);
  }
};

// 生成邮件HTML
const generateEmailHTML = (type: string, data: any) => {
  switch (type) {
    case 'deposit-success':
      return depositSuccessTemplateI18n(data as DepositSuccessEmailData);
    case 'deposit-failed':
      return depositFailedTemplateI18n(data as DepositFailedEmailData);
    default:
      throw new Error(`Unsupported email type: ${type}`);
  }
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const language = (searchParams.get('language') || 'zh') as 'zh' | 'en';

    if (!type) {
      return NextResponse.json({ error: '缺少邮件类型参数' }, { status: 400 });
    }

    const validation = previewSchema.safeParse({
      type,
      language,
    });

    if (!validation.success) {
      return NextResponse.json(
        {
          error: '参数验证失败',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    // 获取测试数据
    const testData = getDefaultTestData(type, language);

    // 生成邮件HTML
    const emailHTML = generateEmailHTML(type, testData);

    // 返回HTML预览
    return new NextResponse(emailHTML, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
    });
  } catch (error) {
    console.error('邮件预览生成失败:', error);
    return NextResponse.json(
      {
        error: '邮件预览生成失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const validation = previewSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: '参数验证失败',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { type, language = 'zh', data } = validation.data;

    // 使用提供的数据或默认测试数据
    const emailData = data || getDefaultTestData(type, language);

    // 确保语言设置正确
    emailData.language = language;

    // 生成邮件HTML
    const emailHTML = generateEmailHTML(type, emailData);

    return NextResponse.json({
      success: true,
      type,
      language,
      data: emailData,
      html: emailHTML,
    });
  } catch (error) {
    console.error('邮件预览生成失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '邮件预览生成失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}
