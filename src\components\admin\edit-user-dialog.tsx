'use client';

import {
  Edit,
  Save,
  User as UserIcon,
  Crown,
  Shield,
  Users,
  Ban,
  CheckCircle,
  Calendar,
  Clock,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  getMemberPlanText,
  getUserStatusText,
  MemberPlan,
  UserStatus,
} from '@/lib/constants';
import { User } from '@/types/user';

interface EditUserDialogProps {
  user: User;
  children?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onUserUpdate?: (user: User) => void;
}

export function EditUserDialog({
  user,
  children,
  open,
  onOpenChange,
  onUserUpdate,
}: EditUserDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const isOpen = open !== undefined ? open : internalOpen;
  const setIsOpen = onOpenChange || setInternalOpen;
  const [isLoading, setIsLoading] = useState(false);

  // 格式化时间为 datetime-local 输入框格式
  const formatDateTimeLocal = (dateString: string) => {
    if (!dateString) {
      // 如果没有时间数据，返回当前时间
      return new Date().toISOString().slice(0, 16);
    }
    return new Date(dateString).toISOString().slice(0, 16);
  };

  const [formData, setFormData] = useState({
    nickname: user.nickname,
    email: user.email,
    memberPlan: user.memberPlan,
    memberPlanExpiry: formatDateTimeLocal(user.memberPlanExpiry),
    status: user.status,
  });

  // 获取用户名首字母
  const getInitials = (nickname: string) => {
    return nickname.charAt(0).toUpperCase();
  };

  // 获取会员套餐图标
  const getMemberPlanIcon = (plan: MemberPlan) => {
    switch (plan) {
      case 'BUSINESS':
        return <Crown className='h-4 w-4' />;
      case 'PRO':
        return <Shield className='h-4 w-4' />;
      case 'FREE':
      default:
        return <Users className='h-4 w-4' />;
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: UserStatus) => {
    return status === 'ACTIVE' ? (
      <CheckCircle className='h-4 w-4' />
    ) : (
      <Ban className='h-4 w-4' />
    );
  };

  // 表单验证
  const validateForm = () => {
    if (!formData.nickname.trim()) {
      toast.error('请输入用户昵称');
      return false;
    }
    if (formData.nickname.length < 2 || formData.nickname.length > 20) {
      toast.error('昵称长度应在2-20个字符之间');
      return false;
    }
    if (!formData.email.trim()) {
      toast.error('请输入邮箱地址');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error('请输入有效的邮箱地址');
      return false;
    }

    return true;
  };

  // 关闭对话框
  const handleClose = () => {
    setIsOpen(false);
    // 重置表单数据
    setFormData({
      nickname: user.nickname,
      email: user.email,
      memberPlan: user.memberPlan,
      memberPlanExpiry: formatDateTimeLocal(user.memberPlanExpiry),
      status: user.status,
    });
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 调用真实的API更新用户
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nickname: formData.nickname,
          email: formData.email,
          memberPlan: formData.memberPlan,
          memberPlanExpiry: formData.memberPlanExpiry,
          status: formData.status,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        toast.error(result.error || '更新失败');
        return;
      }

      // 调用更新回调，使用后端返回的真实用户数据
      onUserUpdate?.(result.user);

      toast.success('用户信息更新成功');
      setIsOpen(false);
    } catch (error) {
      console.error('操作失败:', error);
      toast.error('操作失败', {
        description: '请重试或联系客服',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const dialogContent = (
    <DialogContent className='w-full max-w-lg mx-auto'>
      <DialogHeader>
        <DialogTitle className='flex items-center gap-2'>
          <Edit className='h-5 w-5' />
          编辑用户信息
        </DialogTitle>
        <DialogDescription>修改用户的基本信息和会员状态</DialogDescription>
      </DialogHeader>

      <div className='space-y-4'>
        {/* 用户头像预览 */}
        <div className='flex items-center gap-3 p-3 bg-gray-50 rounded-lg'>
          <Avatar className='h-12 w-12'>
            <AvatarImage src={user.avatar} alt={formData.nickname} />
            <AvatarFallback>{getInitials(formData.nickname)}</AvatarFallback>
          </Avatar>
          <div className='flex-1'>
            <p className='font-medium'>{formData.nickname}</p>
            <p className='text-sm text-gray-600'>用户ID: {user.id}</p>
          </div>
        </div>

        {/* 用户昵称 */}
        <div className='space-y-2'>
          <Label htmlFor='nickname'>用户昵称</Label>
          <Input
            id='nickname'
            placeholder='输入用户昵称'
            value={formData.nickname}
            onChange={e =>
              setFormData(prev => ({ ...prev, nickname: e.target.value }))
            }
            disabled={isLoading}
          />
        </div>

        {/* 邮箱地址 */}
        <div className='space-y-2'>
          <Label htmlFor='email'>邮箱地址</Label>
          <Input
            id='email'
            type='email'
            placeholder='输入邮箱地址'
            value={formData.email}
            onChange={e =>
              setFormData(prev => ({ ...prev, email: e.target.value }))
            }
            disabled={isLoading}
          />
        </div>

        {/* 会员套餐 */}
        <div className='space-y-2'>
          <Label htmlFor='memberPlan'>会员套餐</Label>
          <Select
            value={formData.memberPlan}
            onValueChange={value =>
              setFormData(prev => ({
                ...prev,
                memberPlan: value as MemberPlan,
              }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder='选择会员套餐' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='FREE'>
                <div className='flex items-center gap-2'>
                  <Users className='h-4 w-4' />
                  免费版
                </div>
              </SelectItem>
              <SelectItem value='PRO'>
                <div className='flex items-center gap-2'>
                  <Shield className='h-4 w-4' />
                  专业版
                </div>
              </SelectItem>
              <SelectItem value='BUSINESS'>
                <div className='flex items-center gap-2'>
                  <Crown className='h-4 w-4' />
                  商业版
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 会员到期时间 */}
        <div className='space-y-2'>
          <Label htmlFor='memberPlanExpiry'>会员到期时间</Label>
          <Input
            id='memberPlanExpiry'
            type='datetime-local'
            value={formData.memberPlanExpiry}
            onChange={e =>
              setFormData(prev => ({
                ...prev,
                memberPlanExpiry: e.target.value,
              }))
            }
            disabled={isLoading}
          />
        </div>

        {/* 用户状态 */}
        <div className='space-y-2'>
          <Label htmlFor='status'>用户状态</Label>
          <Select
            value={formData.status}
            onValueChange={value =>
              setFormData(prev => ({ ...prev, status: value as UserStatus }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder='选择用户状态' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='ACTIVE'>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  正常
                </div>
              </SelectItem>
              <SelectItem value='FROZEN'>
                <div className='flex items-center gap-2'>
                  <Ban className='h-4 w-4 text-red-600' />
                  冻结
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 当前状态显示 */}
        <div className='grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg'>
          <div className='space-y-1'>
            <p className='text-sm font-medium'>当前套餐</p>
            <Badge variant='outline' className='gap-1'>
              {getMemberPlanIcon(user.memberPlan)}
              {getMemberPlanText(user.memberPlan)}
            </Badge>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium'>账户状态</p>
            <Badge
              variant={user.status === 'ACTIVE' ? 'default' : 'destructive'}
              className='gap-1'
            >
              {getStatusIcon(user.status)}
              {getUserStatusText(user.status)}
            </Badge>
          </div>
        </div>
      </div>

      <DialogFooter className='flex gap-2'>
        <Button variant='outline' onClick={handleClose} disabled={isLoading}>
          取消
        </Button>
        <Button onClick={handleSubmit} disabled={isLoading} className='gap-2'>
          {isLoading ? (
            <>
              <Clock className='h-4 w-4 animate-spin' />
              保存中...
            </>
          ) : (
            <>
              <Save className='h-4 w-4' />
              保存更改
            </>
          )}
        </Button>
      </DialogFooter>
    </DialogContent>
  );

  if (children) {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        {dialogContent}
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {dialogContent}
    </Dialog>
  );
}
