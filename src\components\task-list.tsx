'use client';

import {
  Clock,
  DollarSign,
  Calendar,
  Tag,
  CreditCard,
  Building2,
  <PERSON>lipboardList,
  FileText,
  MapPin,
  Users,
  Star,
  CheckCircle2,
  AlertCircle,
  XCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { TaskDetailSheet } from '@/components/task-detail-sheet';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import {
  Task,
  Platform,
  PLATFORM_LABELS,
  CHARGEBACK_TYPE_LABELS,
  PAYMENT_METHOD_LABELS,
  EVIDENCE_STATUS_LABELS,
  TaskStatus,
  EvidenceStatus,
  TASK_STATUS_LABELS,
} from '@/lib/types/task';
import { cn } from '@/lib/utils';
import { getTaskBaseCommission } from '@/lib/utils/commission';

interface TaskListProps {
  tasks: Task[];
  loading?: boolean;
}

interface CountdownProps {
  deadline: Date;
}

function Countdown({ deadline }: CountdownProps) {
  const t = useTranslations('tasks');
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    isExpired: boolean;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: false });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const deadlineTime = new Date(deadline).getTime();
      const difference = deadlineTime - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
        );
        const minutes = Math.floor(
          (difference % (1000 * 60 * 60)) / (1000 * 60),
        );
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds, isExpired: false });
      } else {
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true,
        });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [deadline]);

  if (timeLeft.isExpired) {
    return (
      <div className='flex items-center gap-1.5 text-red-500'>
        <XCircle className='h-3.5 w-3.5' />
        <span className='text-xs font-medium'>{t('taskCard.expired')}</span>
      </div>
    );
  }

  const isUrgent = timeLeft.days === 0 && timeLeft.hours < 24;

  return (
    <div
      className={cn(
        'flex items-center gap-1.5',
        isUrgent ? 'text-red-500' : 'text-amber-600 dark:text-amber-400',
      )}
    >
      <Clock className='h-3.5 w-3.5' />
      <span className='text-xs font-medium tabular-nums'>
        {timeLeft.days > 0 && `${timeLeft.days}${t('taskCard.days')} `}
        {String(timeLeft.hours).padStart(2, '0')}:
        {String(timeLeft.minutes).padStart(2, '0')}:
        {String(timeLeft.seconds).padStart(2, '0')}
      </span>
    </div>
  );
}

interface TaskCardProps {
  task: Task;
}

export function TaskCard({ task }: TaskCardProps) {
  const [mounted, setMounted] = useState(false);
  const t = useTranslations('tasks');
  const tPublish = useTranslations('publish');

  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取费率数据
  const { chargebackTypes, paymentMethods, systemRate } = useCommissionRates();

  // 获取证据状态标签和图标
  const getEvidenceStatusInfo = (
    status: EvidenceStatus | string | undefined,
  ) => {
    if (!status || status === 'PENDING_SUBMISSION') {
      return {
        label: t('taskCard.evidenceStatusLabels.PENDING_SUBMISSION'),
        icon: AlertCircle,
        className:
          'bg-slate-100 text-slate-700 border-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-700',
      };
    }

    switch (status) {
      case 'UNDER_REVIEW':
        return {
          label: t('taskCard.evidenceStatusLabels.UNDER_REVIEW'),
          icon: Clock,
          className:
            'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800',
        };
      case 'NO_EVIDENCE':
        return {
          label: t('taskCard.evidenceStatusLabels.NO_EVIDENCE'),
          icon: XCircle,
          className:
            'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800',
        };
      case 'REVIEWED':
        return {
          label: t('taskCard.evidenceStatusLabels.REVIEWED'),
          icon: CheckCircle2,
          className:
            'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800',
        };
      case 'REJECTED':
        return {
          label: t('taskCard.evidenceStatusLabels.REJECTED'),
          icon: XCircle,
          className:
            'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800',
        };
      default:
        return {
          label: t('taskCard.evidenceStatusLabels.PENDING_SUBMISSION'),
          icon: AlertCircle,
          className:
            'bg-slate-100 text-slate-700 border-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-700',
        };
    }
  };

  // 格式化日期时间的安全函数
  const formatDateTime = (date: Date) => {
    if (!mounted) {
      return t('emptyState.loading');
    }
    return date
      .toISOString()
      .slice(5, 16)
      .replace('T', ' ')
      .replace('-', t('taskCard.dateFormat.month'))
      .replace('-', t('taskCard.dateFormat.day'));
  };

  // 计算基础酬金（不包含证据费用）
  const baseCommission = getTaskBaseCommission(
    task,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 获取平台显示名称
  const getPlatformName = (
    platform: Platform | string | { id: string; name: string },
  ) => {
    if (typeof platform === 'object' && platform?.name) {
      // 如果是对象，直接返回name字段，因为API已经返回了正确的显示名称
      return platform.name;
    }
    if (typeof platform === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.platformLabels.${platform}` as any) ||
        platform
      );
    }
    return String(platform);
  };

  // 获取分类显示名称
  const getCategoryName = (category: string | { id: string; name: string }) => {
    if (typeof category === 'object' && category?.name) {
      // 如果是对象，直接返回name字段，因为API已经返回了正确的显示名称
      return category.name;
    }
    if (typeof category === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.categoryLabels.${category}` as any) ||
        category
      );
    }
    return String(category);
  };

  const evidenceInfo = getEvidenceStatusInfo(task.evidenceStatus);
  const EvidenceIcon = evidenceInfo.icon;

  return (
    <Card className='group relative overflow-hidden border-slate-200/60 dark:border-slate-800/60 bg-white/80 dark:bg-slate-950/80 backdrop-blur-sm hover:shadow-xl hover:shadow-slate-200/20 dark:hover:shadow-slate-900/20 transition-all duration-500 hover:-translate-y-1 hover:border-slate-300/80 dark:hover:border-slate-700/80'>
      {/* 顶部装饰条 */}
      <div className='absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-slate-400/50 to-transparent' />

      <CardContent className='p-6 h-full flex flex-col'>
        {/* 第一行：平台和分类 */}
        <div className='flex items-start justify-between mb-4'>
          <div className='flex items-center gap-3'>
            <div className='p-2 rounded-lg bg-slate-50 dark:bg-slate-900/50 border border-slate-200/50 dark:border-slate-800/50'>
              <Building2 className='h-4 w-4 text-slate-600 dark:text-slate-400' />
            </div>
            <div>
              <p className='text-xs text-slate-500 dark:text-slate-400 mb-0.5'>
                {t('taskCard.platform')}
              </p>
              <p className='text-sm font-semibold text-slate-900 dark:text-slate-100 leading-tight'>
                {getPlatformName(task.platform)}
              </p>
            </div>
          </div>

          <div className='flex items-center gap-3'>
            <div className='text-right'>
              <p className='text-xs text-slate-500 dark:text-slate-400 mb-0.5'>
                {t('taskCard.category')}
              </p>
              <p className='text-sm font-semibold text-slate-900 dark:text-slate-100 leading-tight'>
                {getCategoryName(task.category)}
              </p>
            </div>
            <div className='p-2 rounded-lg bg-slate-50 dark:bg-slate-900/50 border border-slate-200/50 dark:border-slate-800/50'>
              <Tag className='h-4 w-4 text-slate-600 dark:text-slate-400' />
            </div>
          </div>
        </div>

        {/* 第二行：酬金和证据状态 */}
        <div className='flex items-center justify-between mb-5'>
          <div className='flex items-center gap-3'>
            <div className='p-2.5 rounded-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900 border border-emerald-200/50 dark:border-emerald-800/50'>
              <DollarSign className='h-5 w-5 text-emerald-600 dark:text-emerald-400' />
            </div>
            <div>
              <p className='text-xs text-slate-500 dark:text-slate-400 mb-0.5'>
                {t('taskCard.reward')}
              </p>
              <p className='text-lg font-bold text-emerald-600 dark:text-emerald-400 leading-tight'>
                ${baseCommission.toFixed(2)}
              </p>
            </div>
          </div>

          <div className='flex items-center gap-3'>
            <div className='text-right'>
              <p className='text-xs text-slate-500 dark:text-slate-400 mb-1'>
                {t('taskCard.evidence')}
              </p>
              <Badge
                className={cn(
                  'text-xs font-medium border inline-flex items-center gap-1.5 px-2.5 py-1',
                  evidenceInfo.className,
                )}
              >
                <EvidenceIcon className='h-3 w-3' />
                {evidenceInfo.label}
              </Badge>
            </div>
          </div>
        </div>

        {/* 第三行：拒付类型和支付方式 */}
        <div className='space-y-3 mb-4 flex-1'>
          <div>
            <div className='flex items-center gap-2 mb-2'>
              <CreditCard className='h-3.5 w-3.5 text-slate-500 dark:text-slate-400' />
              <span className='text-xs font-medium text-slate-600 dark:text-slate-400'>
                {t('taskCard.chargebackType')}
              </span>
            </div>
            <div className='flex flex-wrap gap-1.5'>
              {task.chargebackTypeIds?.length ? (
                task.chargebackTypeIds.slice(0, 2).map((typeId, index) => {
                  const typeData = Array.isArray(chargebackTypes)
                    ? chargebackTypes.find(ct => ct.id === typeId)
                    : null;
                  return (
                    <Badge
                      key={index}
                      variant='outline'
                      className='text-xs px-2.5 py-1 bg-slate-50/80 dark:bg-slate-900/50 border-slate-200/60 dark:border-slate-700/60 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors'
                    >
                      {typeData?.name
                        ? tPublish(
                            `platformSelection.chargebackTypeLabels.${typeData.name}` as any,
                          ) || typeData.name
                        : typeId}
                    </Badge>
                  );
                })
              ) : (
                <span className='text-xs text-slate-400 dark:text-slate-500 italic'>
                  {t('taskCard.none')}
                </span>
              )}
              {task.chargebackTypeIds && task.chargebackTypeIds.length > 2 && (
                <Badge
                  variant='outline'
                  className='text-xs px-2.5 py-1 bg-slate-50/80 dark:bg-slate-900/50 border-slate-200/60 dark:border-slate-700/60 text-slate-500 dark:text-slate-400'
                >
                  +{task.chargebackTypeIds.length - 2}
                </Badge>
              )}
            </div>
          </div>

          <div>
            <div className='flex items-center gap-2 mb-2'>
              <CreditCard className='h-3.5 w-3.5 text-slate-500 dark:text-slate-400' />
              <span className='text-xs font-medium text-slate-600 dark:text-slate-400'>
                {t('taskCard.paymentMethod')}
              </span>
            </div>
            <div className='flex flex-wrap gap-1.5'>
              {task.paymentMethodIds?.length ? (
                task.paymentMethodIds.slice(0, 2).map((methodId, index) => {
                  const methodData = Array.isArray(paymentMethods)
                    ? paymentMethods.find(pm => pm.id === methodId)
                    : null;
                  return (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='text-xs px-2.5 py-1 bg-slate-100/80 dark:bg-slate-800/50 text-slate-700 dark:text-slate-300 border-0 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors'
                    >
                      {methodData?.name
                        ? tPublish(
                            `platformSelection.paymentMethodLabels.${methodData.name}` as any,
                          ) || methodData.name
                        : methodId}
                    </Badge>
                  );
                })
              ) : (
                <span className='text-xs text-slate-400 dark:text-slate-500 italic'>
                  {t('taskCard.none')}
                </span>
              )}
              {task.paymentMethodIds && task.paymentMethodIds.length > 2 && (
                <Badge
                  variant='secondary'
                  className='text-xs px-2.5 py-1 bg-slate-100/80 dark:bg-slate-800/50 text-slate-500 dark:text-slate-400 border-0'
                >
                  +{task.paymentMethodIds.length - 2}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* 第四行：倒计时和截止时间 */}
        {task.expiresAt && (
          <div className='flex items-center justify-between pt-3 border-t border-slate-100/80 dark:border-slate-800/80 mb-4'>
            <Countdown deadline={new Date(task.expiresAt)} />
            <div className='flex items-center gap-1.5 text-slate-500 dark:text-slate-400'>
              <Calendar className='h-3.5 w-3.5' />
              <span className='text-xs font-medium'>
                {formatDateTime(new Date(task.expiresAt))}
              </span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className='mt-auto'>
          <TaskDetailSheet task={task}>
            <Button
              size='sm'
              className='w-full bg-slate-900 hover:bg-slate-800 dark:bg-slate-100 dark:hover:bg-slate-200 dark:text-slate-900 text-white transition-all duration-200 font-medium shadow-sm hover:shadow-md'
            >
              {t('taskCard.viewDetails')}
            </Button>
          </TaskDetailSheet>
        </div>
      </CardContent>
    </Card>
  );
}

export function TaskList({ tasks, loading = false }: TaskListProps) {
  const t = useTranslations('tasks');

  if (loading) {
    return (
      <div className='max-w-7xl mx-auto px-4'>
        <div className='grid grid-cols-2 gap-6'>
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className='aspect-[5/2]'>
              <Card className='animate-pulse h-full border-slate-200/60 dark:border-slate-800/60'>
                <CardContent className='p-6 h-full'>
                  <div className='space-y-4 h-full flex flex-col'>
                    <div className='flex justify-between'>
                      <div className='space-y-2'>
                        <div className='h-3 bg-slate-300 dark:bg-slate-700 rounded w-12'></div>
                        <div className='h-4 bg-slate-300 dark:bg-slate-700 rounded w-20'></div>
                      </div>
                      <div className='space-y-2'>
                        <div className='h-3 bg-slate-300 dark:bg-slate-700 rounded w-12'></div>
                        <div className='h-4 bg-slate-300 dark:bg-slate-700 rounded w-16'></div>
                      </div>
                    </div>
                    <div className='flex justify-between'>
                      <div className='h-8 bg-slate-300 dark:bg-slate-700 rounded w-24'></div>
                      <div className='h-6 bg-slate-300 dark:bg-slate-700 rounded w-16'></div>
                    </div>
                    <div className='space-y-2 flex-1'>
                      <div className='h-3 bg-slate-300 dark:bg-slate-700 rounded w-16'></div>
                      <div className='flex gap-2'>
                        <div className='h-6 bg-slate-300 dark:bg-slate-700 rounded w-12'></div>
                        <div className='h-6 bg-slate-300 dark:bg-slate-700 rounded w-12'></div>
                      </div>
                    </div>
                    <div className='h-8 bg-slate-300 dark:bg-slate-700 rounded mt-auto'></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className='text-center py-20'>
        <div className='mx-auto w-20 h-20 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-6'>
          <ClipboardList className='h-10 w-10 text-slate-400' />
        </div>
        <h3 className='text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2'>
          {t('emptyState.noTasks')}
        </h3>
        <p className='text-slate-500 dark:text-slate-400'>
          {t('emptyState.noTasksDescription')}
        </p>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto px-4'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {tasks.map(task => (
          <div key={task.id} className='aspect-[5/2]'>
            <TaskCard task={task} />
          </div>
        ))}
      </div>
    </div>
  );
}
