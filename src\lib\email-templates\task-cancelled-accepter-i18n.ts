import {
  emailStyles,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface TaskCancelledAccepterEmailData {
  userName: string;
  userEmail: string;
  taskId: string;
  taskTitle: string;
  publisherName: string;
  cancelledAt: string;
  penaltyAmount?: number;
  currency?: string;
  cancellationReason?: string;
  language?: 'zh' | 'en';
}

export const taskCancelledAccepterTemplateI18n = (
  data: TaskCancelledAccepterEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.cancelledAt, language);
  const formattedPenalty =
    data.penaltyAmount && data.currency
      ? formatEmailAmount(data.penaltyAmount, data.currency, language)
      : null;

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
          security: '为了您的账户安全，请勿向任何人透露您的账户信息。',
        },
        buttons: {
          viewTasks: '查看我的任务',
          browseNew: '浏览新任务',
          contactSupport: '联系客服',
        },
      },
      notifications: {
        taskCancelled: {
          title: '委托取消通知',
          greeting: '您接受的委托已被取消',
          description: '很抱歉通知您，您接受的以下委托已被发布者取消。',
          taskDetails: '委托详情',
          taskId: '委托编号：',
          taskTitle: '委托标题：',
          publisherName: '发布者：',
          cancelledAt: '取消时间：',
          cancellationReason: '取消原因：',
          penaltyInfo: '违约金信息',
          penaltyAmount: '违约金：',
          penaltyNotice: '根据平台规则，已从您的账户扣除相应违约金。',
          noPenalty: '本次取消无需支付违约金。',
          nextSteps: '接下来您可以：',
          stepsList: {
            browse: '浏览并接受新的委托任务',
            review: '查看您的任务历史记录',
            improve: '提升服务质量以避免类似情况',
          },
          tips: '温馨提示',
          tipsList: {
            quality: '保持良好的服务质量可避免委托被取消',
            communication: '与发布者保持良好沟通',
            help: '如有疑问，请联系客服支持',
          },
          apology: '我们为给您带来的不便深表歉意。',
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
          security:
            'For your account security, please do not share your account information with anyone.',
        },
        buttons: {
          viewTasks: 'View My Tasks',
          browseNew: 'Browse New Tasks',
          contactSupport: 'Contact Support',
        },
      },
      notifications: {
        taskCancelled: {
          title: 'Task Cancellation Notice',
          greeting: 'The task you accepted has been cancelled',
          description:
            'We regret to inform you that the following task you accepted has been cancelled by the publisher.',
          taskDetails: 'Task Details',
          taskId: 'Task ID:',
          taskTitle: 'Task Title:',
          publisherName: 'Publisher:',
          cancelledAt: 'Cancelled At:',
          cancellationReason: 'Cancellation Reason:',
          penaltyInfo: 'Penalty Information',
          penaltyAmount: 'Penalty Fee:',
          penaltyNotice:
            'According to platform rules, the corresponding penalty fee has been deducted from your account.',
          noPenalty: 'No penalty fee is required for this cancellation.',
          nextSteps: 'What you can do next:',
          stepsList: {
            browse: 'Browse and accept new tasks',
            review: 'Review your task history',
            improve: 'Improve service quality to avoid similar situations',
          },
          tips: 'Important Notes',
          tipsList: {
            quality:
              'Maintaining good service quality can prevent task cancellations',
            communication: 'Maintain good communication with publishers',
            help: 'If you have any questions, please contact customer support',
          },
          apology: 'We sincerely apologize for any inconvenience caused.',
        },
      },
    },
  };

  const t = translations[language];

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.taskCancelled.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .cancel-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .task-card {
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 8px 0;
          border-bottom: 1px solid #fecaca;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #7f1d1d;
          font-weight: 500;
          min-width: 120px;
        }
        .info-value {
          color: #991b1b;
          font-weight: 600;
          flex: 1;
          text-align: right;
        }
        .penalty-card {
          background: #fef3c7;
          border: 1px solid #fcd34d;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          text-align: center;
        }
        .penalty-amount {
          font-size: 24px;
          font-weight: bold;
          color: #d97706;
          margin: 10px 0;
        }
        .no-penalty-card {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          text-align: center;
        }
        .steps-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .steps-list li {
          padding: 8px 0;
          padding-left: 24px;
          position: relative;
          color: #1e40af;
        }
        .steps-list li:before {
          content: "→";
          position: absolute;
          left: 0;
          color: #3b82f6;
          font-weight: bold;
        }
        .tips-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .tips-list li {
          padding: 6px 0;
          padding-left: 20px;
          position: relative;
          color: #f59e0b;
          font-size: 14px;
        }
        .tips-list li:before {
          content: "•";
          position: absolute;
          left: 0;
          color: #f59e0b;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Cancel Icon -->
          <div class="cancel-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}
          </h2>
          
          <h3 style="text-align: center; color: #ef4444; margin-bottom: 16px;">
            ${t.notifications.taskCancelled.greeting}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.taskCancelled.description}
          </p>

          <!-- Task Details -->
          <div class="task-card">
            <h4 style="margin-top: 0; color: #7f1d1d;">
              ${t.notifications.taskCancelled.taskDetails}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.taskId}</span>
              <span class="info-value" style="font-family: monospace;">${data.taskId}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.taskTitle}</span>
              <span class="info-value">${data.taskTitle}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.publisherName}</span>
              <span class="info-value">${data.publisherName}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.cancelledAt}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
            
            ${
              data.cancellationReason
                ? `
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.cancellationReason}</span>
              <span class="info-value">${data.cancellationReason}</span>
            </div>
            `
                : ''
            }
          </div>

          <!-- Penalty Information -->
          ${
            formattedPenalty
              ? `
          <div class="penalty-card">
            <h4 style="margin-top: 0; color: #d97706;">
              ⚠️ ${t.notifications.taskCancelled.penaltyInfo}
            </h4>
            <div class="penalty-amount">
              ${t.notifications.taskCancelled.penaltyAmount} ${formattedPenalty}
            </div>
            <p style="margin-bottom: 0; color: #d97706;">
              ${t.notifications.taskCancelled.penaltyNotice}
            </p>
          </div>
          `
              : `
          <div class="no-penalty-card">
            <h4 style="margin-top: 0; color: #16a34a;">
              ✅ ${t.notifications.taskCancelled.penaltyInfo}
            </h4>
            <p style="margin-bottom: 0; color: #16a34a;">
              ${t.notifications.taskCancelled.noPenalty}
            </p>
          </div>
          `
          }

          <!-- Next Steps -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              ${t.notifications.taskCancelled.nextSteps}
            </h4>
            <ul class="steps-list">
              <li>${t.notifications.taskCancelled.stepsList.browse}</li>
              <li>${t.notifications.taskCancelled.stepsList.review}</li>
              <li>${t.notifications.taskCancelled.stepsList.improve}</li>
            </ul>
          </div>

          <!-- Tips -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              💡 ${t.notifications.taskCancelled.tips}
            </h4>
            <ul class="tips-list">
              <li>${t.notifications.taskCancelled.tipsList.quality}</li>
              <li>${t.notifications.taskCancelled.tipsList.communication}</li>
              <li>${t.notifications.taskCancelled.tipsList.help}</li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/task-hall" class="email-button email-button-primary">
              ${t.common.buttons.browseNew}
            </a>
            <a href="${process.env.DOMAIN}/my-accepted-tasks" class="email-button email-button-secondary">
              ${t.common.buttons.viewTasks}
            </a>
          </div>

          <!-- Apology -->
          <p style="text-align: center; color: #64748b; margin-top: 32px; font-style: italic;">
            ${t.notifications.taskCancelled.apology}
          </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.contact}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.security}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `.trim();
};
