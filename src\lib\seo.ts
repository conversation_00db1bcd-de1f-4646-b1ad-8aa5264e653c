// SEO 优化工具库
import { Metadata } from 'next';

// 网站基础信息
export const SITE_CONFIG = {
  name: 'RefundGo',
  title: 'RefundGo - 专业的退款服务平台',
  description: '提供安全、快速、专业的退款服务，帮助用户轻松处理各类退款需求',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://refundgo.com',
  ogImage: '/images/og-image.jpg',
  creator: 'RefundGo Team',
  keywords: ['退款服务', '退款平台', '消费者权益', '退款申请', '客服代理'],
  authors: [
    {
      name: 'RefundGo Team',
      url: 'https://refundgo.com',
    },
  ],
  locale: 'zh-CN',
  alternateLocales: ['en-US'],
} as const;

// 页面类型定义
export type PageType =
  | 'website'
  | 'article'
  | 'profile'
  | 'product'
  | 'service';

// OpenGraph 支持的类型
export type OpenGraphType = 'website' | 'article' | 'profile';

// SEO 元数据配置接口
export interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  ogType?: PageType;
  ogImage?: string;
  ogImageAlt?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  section?: string;
  tags?: string[];
  locale?: string;
  alternates?: Record<string, string>;
}

// 生成完整的页面标题
export function generatePageTitle(title?: string): string {
  if (!title) return SITE_CONFIG.title;
  return `${title} | ${SITE_CONFIG.name}`;
}

// 生成页面描述
export function generatePageDescription(description?: string): string {
  return description || SITE_CONFIG.description;
}

// 生成关键词
export function generateKeywords(keywords: string[] = []): string[] {
  return Array.from(new Set([...SITE_CONFIG.keywords, ...keywords]));
}

// 生成 Open Graph 图片 URL
export function generateOGImageUrl(path?: string): string {
  const baseUrl = SITE_CONFIG.url;
  const imagePath = path || SITE_CONFIG.ogImage;
  return imagePath.startsWith('http') ? imagePath : `${baseUrl}${imagePath}`;
}

// 生成规范 URL
export function generateCanonicalUrl(path: string): string {
  const baseUrl = SITE_CONFIG.url;
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
}

// 生成结构化数据
export function generateStructuredData(config: {
  type:
    | 'WebSite'
    | 'Organization'
    | 'WebPage'
    | 'Article'
    | 'Service'
    | 'Product';
  data: Record<string, any>;
}) {
  const baseData = {
    '@context': 'https://schema.org',
    '@type': config.type,
    ...config.data,
  };

  // 添加网站基础信息
  if (config.type === 'WebSite') {
    return {
      ...baseData,
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
      description: SITE_CONFIG.description,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${SITE_CONFIG.url}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string',
      },
    };
  }

  // 添加组织信息
  if (config.type === 'Organization') {
    return {
      ...baseData,
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
      logo: generateOGImageUrl('/images/logo.png'),
      description: SITE_CONFIG.description,
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+86-************',
        contactType: 'customer service',
        availableLanguage: ['Chinese', 'English'],
      },
    };
  }

  return baseData;
}

// 主要的 SEO 元数据生成函数
export function generateSEOMetadata(config: SEOConfig = {}): Metadata {
  const title = generatePageTitle(config.title);
  const description = generatePageDescription(config.description);
  const keywords = generateKeywords(config.keywords);
  const canonical = config.canonical
    ? generateCanonicalUrl(config.canonical)
    : undefined;
  const ogImage = generateOGImageUrl(config.ogImage);

  const metadata: Metadata = {
    title,
    description,
    keywords: keywords.join(', '),
    authors: [...SITE_CONFIG.authors],
    creator: SITE_CONFIG.creator,

    // 基础 meta 标签
    metadataBase: new URL(SITE_CONFIG.url),

    // 规范 URL
    ...(canonical && { alternates: { canonical } }),

    // robots 设置
    robots: {
      index: !config.noindex,
      follow: !config.nofollow,
      googleBot: {
        index: !config.noindex,
        follow: !config.nofollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // Open Graph
    openGraph: {
      type:
        config.ogType === 'product' || config.ogType === 'service'
          ? 'website'
          : (config.ogType as OpenGraphType) || 'website',
      locale: config.locale || SITE_CONFIG.locale,
      url: canonical || SITE_CONFIG.url,
      title,
      description,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: config.ogImageAlt || title,
        },
      ],
      ...(config.publishedTime && { publishedTime: config.publishedTime }),
      ...(config.modifiedTime && { modifiedTime: config.modifiedTime }),
      ...(config.authors && { authors: config.authors }),
      ...(config.section && { section: config.section }),
      ...(config.tags && { tags: config.tags }),
    },

    // Twitter Cards
    twitter: {
      card: config.twitterCard || 'summary_large_image',
      title,
      description,
      images: [ogImage],
      creator: '@RefundGo',
      site: '@RefundGo',
    },

    // 多语言支持
    ...(config.alternates && {
      alternates: {
        languages: config.alternates,
      },
    }),

    // 应用相关
    applicationName: SITE_CONFIG.name,
    appleWebApp: {
      capable: true,
      title: SITE_CONFIG.name,
      statusBarStyle: 'default',
    },

    // 格式检测
    formatDetection: {
      telephone: false,
      date: false,
      address: false,
      email: false,
      url: false,
    },

    // 其他
    category: 'business',
  };

  return metadata;
}

// 页面特定的 SEO 配置
export const PAGE_SEO_CONFIGS = {
  // 首页
  home: {
    title: '专业的退款服务平台',
    description:
      '提供安全、快速、专业的退款服务，帮助用户轻松处理各类退款需求，保障消费者权益',
    keywords: ['退款服务', '退款平台', '消费者权益', '退款申请'],
    ogType: 'website' as PageType,
  },

  // 用户仪表盘
  dashboard: {
    title: '用户仪表盘',
    description: '查看您的委托概览、统计信息和最新动态',
    keywords: ['用户中心', '仪表盘', '委托管理'],
    noindex: true, // 用户私人页面不索引
  },

  // 委托中心
  tasks: {
    title: '委托中心',
    description: '查看和管理您的委托列表，包含筛选和排序功能',
    keywords: ['委托管理', '委托列表', '退款委托'],
    noindex: true,
  },

  // 发布委托
  publish: {
    title: '发布委托',
    description: '发布新的退款委托，获得专业的退款服务支持',
    keywords: ['发布委托', '退款申请', '委托服务'],
    noindex: true,
  },

  // 账号安全
  profile: {
    title: '账号安全',
    description: '管理您的账号安全设置，保护您的账户信息',
    keywords: ['账号安全', '个人设置', '隐私保护'],
    noindex: true,
  },

  // 钱包管理
  wallet: {
    title: '我的钱包',
    description: '管理您的账户余额、交易记录和提现设置',
    keywords: ['钱包管理', '余额查询', '交易记录'],
    noindex: true,
  },

  // 会员套餐
  membership: {
    title: '会员套餐',
    description: '选择适合您的会员套餐，享受更多专业服务',
    keywords: ['会员套餐', '高级服务', '套餐升级'],
    ogType: 'product' as PageType,
  },

  // 商铺白名单
  whitelist: {
    title: '商铺白名单',
    description: '管理您的商铺白名单设置，提升委托成功率',
    keywords: ['白名单管理', '商铺设置', '成功率优化'],
    noindex: true,
  },

  // 工单管理
  tickets: {
    title: '工单管理',
    description: '查看和处理您的客服工单，获得及时支持',
    keywords: ['工单管理', '客服支持', '问题反馈'],
    noindex: true,
  },
} as const;

// 获取页面 SEO 配置
export function getPageSEOConfig(
  page: keyof typeof PAGE_SEO_CONFIGS,
): SEOConfig {
  const config = PAGE_SEO_CONFIGS[page] || {};
  return {
    ...config,
    keywords: config.keywords ? [...config.keywords] : undefined,
  };
}

// 生成面包屑结构化数据
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{
    name: string;
    url: string;
  }>,
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

// 生成文章结构化数据
export function generateArticleStructuredData(article: {
  title: string;
  description: string;
  author: string;
  publishedTime: string;
  modifiedTime?: string;
  image?: string;
  url: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      logo: {
        '@type': 'ImageObject',
        url: generateOGImageUrl('/images/logo.png'),
      },
    },
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    image: article.image
      ? generateOGImageUrl(article.image)
      : generateOGImageUrl(),
    url: article.url,
  };
}

export default generateSEOMetadata;
