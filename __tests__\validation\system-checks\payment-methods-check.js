// 测试 paymentMethods.find 错误修复
const fs = require('fs');
const path = require('path');

console.log('=== PaymentMethods.find 错误修复验证 ===\n');

// 需要检查的文件和修复内容
const filesToCheck = [
  {
    name: 'task-list.tsx',
    path: 'src/components/task-list.tsx',
    checks: [
      {
        desc: '支付方式查找已添加数组检查',
        pattern:
          /Array\.isArray\(paymentMethods\)\s*\?\s*paymentMethods\.find\(\(pm\)\s*=>\s*pm\.id\s*===\s*methodId\)/,
        shouldMatch: true,
      },
      {
        desc: '拒付类型查找已添加数组检查',
        pattern:
          /Array\.isArray\(chargebackTypes\)\s*\?\s*chargebackTypes\.find\(\(ct\)\s*=>\s*ct\.id\s*===\s*typeId\)/,
        shouldMatch: true,
      },
      {
        desc: '直接调用 paymentMethods.find 已移除',
        pattern:
          /paymentMethods\.find\(\s*\(pm\)\s*=>\s*pm\.id\s*===\s*methodId\s*\)/,
        shouldMatch: false,
      },
    ],
  },
  {
    name: 'task-detail-sheet.tsx',
    path: 'src/components/task-detail-sheet.tsx',
    checks: [
      {
        desc: '支付方式查找已添加数组检查',
        pattern:
          /Array\.isArray\(paymentMethods\)\s*\?\s*paymentMethods\.find\(\(pm\)\s*=>\s*pm\.id\s*===\s*methodId\)/,
        shouldMatch: true,
      },
    ],
  },
  {
    name: 'tasks-page-content.tsx',
    path: 'src/components/tasks-page-content.tsx',
    checks: [
      {
        desc: '支付方式映射已添加数组检查',
        pattern:
          /Array\.isArray\(paymentMethods\)\s*\?\s*paymentMethods\.reduce/,
        shouldMatch: true,
      },
      {
        desc: '拒付类型映射已添加数组检查',
        pattern:
          /Array\.isArray\(chargebackTypes\)\s*\?\s*chargebackTypes\.reduce/,
        shouldMatch: true,
      },
    ],
  },
];

let totalChecks = 0;
let passedChecks = 0;
let failedFiles = [];

console.log('🔍 **修复验证结果**：\n');

filesToCheck.forEach(file => {
  console.log(`📄 ${file.name}:`);

  try {
    const filePath = path.join(__dirname, '../../../', file.path);
    const content = fs.readFileSync(filePath, 'utf8');

    let filePassed = 0;
    let fileTotal = file.checks.length;

    file.checks.forEach((check, index) => {
      totalChecks++;
      const matches = check.pattern.test(content);
      const result = check.shouldMatch ? matches : !matches;
      const status = result ? '✅' : '❌';

      if (result) {
        passedChecks++;
        filePassed++;
      }

      console.log(`   ${index + 1}. ${check.desc}: ${status}`);

      if (!result) {
        if (check.shouldMatch) {
          console.log(`      ⚠️  期望匹配但未找到模式`);
        } else {
          console.log(`      ⚠️  不应匹配但找到了模式`);
        }
      }
    });

    if (filePassed < fileTotal) {
      failedFiles.push({
        name: file.name,
        passed: filePassed,
        total: fileTotal,
      });
    }

    console.log(
      `   修复进度: ${filePassed}/${fileTotal} (${Math.round((filePassed / fileTotal) * 100)}%)\n`
    );
  } catch (error) {
    console.log(`   ❌ 文件读取失败: ${error.message}\n`);
    failedFiles.push({
      name: file.name,
      passed: 0,
      total: file.checks.length,
      error: error.message,
    });
  }
});

console.log('=== 修复结果总结 ===');
console.log(`总检查项: ${totalChecks}`);
console.log(`通过检查: ${passedChecks}`);
console.log(`修复成功率: ${Math.round((passedChecks / totalChecks) * 100)}%`);

if (failedFiles.length > 0) {
  console.log('\n❌ **需要进一步修复的文件**：');
  failedFiles.forEach(file => {
    if (file.error) {
      console.log(`   • ${file.name}: 文件读取错误 - ${file.error}`);
    } else {
      console.log(
        `   • ${file.name}: ${file.passed}/${file.total} (${Math.round((file.passed / file.total) * 100)}%)`
      );
    }
  });
} else {
  console.log('\n✅ 所有文件修复检查通过！');
}

console.log('\n=== 修复内容说明 ===');
console.log('🎯 **修复的问题**：');
console.log('   • TypeError: paymentMethods.find is not a function');
console.log('   • TypeError: chargebackTypes.find is not a function');
console.log('   • TypeError: paymentMethods.reduce is not a function');

console.log('\n🔧 **修复方法**：');
console.log('   • 添加 Array.isArray() 检查确保变量是数组');
console.log('   • 使用三元运算符提供安全的回退值');
console.log('   • 在调用数组方法前进行类型验证');

console.log('\n💡 **修复模式**：');
console.log('   • 原代码: paymentMethods.find(...)');
console.log(
  '   • 修复后: Array.isArray(paymentMethods) ? paymentMethods.find(...) : null'
);

if (passedChecks === totalChecks) {
  console.log('\n🎉 **PaymentMethods.find 错误修复完成！**');
  console.log('\n✨ **修复效果**：');
  console.log('   • 防止运行时 TypeError 错误');
  console.log('   • 提供安全的数组操作');
  console.log('   • 改善用户体验和应用稳定性');
  console.log('   • 兼容数据加载状态和错误状态');
} else {
  console.log('\n⚠️ **仍需完善**：');
  console.log('   • 检查其他可能存在类似问题的文件');
  console.log('   • 测试修复后的功能是否正常工作');
  console.log('   • 考虑添加更完善的错误处理');
}

console.log('\n=== 验证完成 ===');
console.log(`验证时间: ${new Date().toLocaleString('zh-CN')}`);
console.log('修复已完成，应用应该不再出现 paymentMethods.find 错误！🎊');
