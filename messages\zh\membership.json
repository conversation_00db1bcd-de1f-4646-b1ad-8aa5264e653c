{"title": "会员套餐", "breadcrumb": "套餐选择", "description": "查看和管理您的会员套餐，升级享受更多权益", "navigation": {"title": "会员套餐", "description": "查看和管理您的会员套餐，升级获得更多权益"}, "status": {"title": "当前会员状态", "currentPlan": "当前套餐", "validUntil": "有效期至", "permanent": "永久有效", "taskUsage": "本月委托使用量", "whitelistUsage": "白名单店铺使用量"}, "plans": {"free": "免费版", "pro": "专业版", "business": "商业版", "current": "当前套餐", "unlimited": "无限制", "noData": "无数据", "noQuota": "无名额"}, "features": {"taskLimit": "委托发布", "monthlyTasks": "每月", "times": "次", "platformRate": "平台费率", "whitelistSlots": "白名单店铺名额", "none": "无", "slots": "个", "customerSupport": "客服支持", "basicSupport": "基础客服", "prioritySupport": "优先客服", "dedicatedSupport": "专属客服"}, "actions": {"renew": "续费套餐", "upgrade": "升级到此套餐", "current": "当前套餐", "noDowngrade": "不支持降级", "processing": "处理中..."}, "benefits": {"title": "权益说明", "taskLimitTitle": "委托发布限制", "taskLimitDesc": "不同套餐每月可发布的委托数量不同，超出限制需要升级套餐。", "platformRateTitle": "平台费率", "platformRateDesc": "会员等级越高，享受的平台费率越低，节省更多成本。", "whitelistTitle": "白名单店铺名额", "whitelistDesc": "可以将特定店铺加入白名单，禁止用户发布这些店铺的相关委托。", "supportTitle": "客服支持", "supportDesc": "高级会员享受优先客服支持，商业版用户有专属VIP客服。"}, "messages": {"loading": "正在加载会员数据...", "loadError": "加载会员数据失败，请刷新重试", "noData": "会员数据不存在", "currentPlanInfo": "当前套餐", "currentPlanDesc": "您已经是此套餐的用户", "paymentDialog": "打开支付对话框", "errorHandled": "变更中处理的错误", "paymentSuccess": "支付成功后刷新会员数据", "loadingMembership": "正在加载会员信息...", "membershipLoadError": "加载会员信息失败", "noMembershipData": "暂无会员数据", "upgradeSuccess": "会员升级成功！", "renewSuccess": "会员续费成功！", "operationFailed": "操作失败，请重试", "confirmUpgrade": "确定要升级到{planName}吗？", "confirmRenew": "确定要续费当前会员吗？"}, "payment": {"dialog": {"title": {"upgrade": "升级会员套餐", "renew": "续费会员套餐"}, "description": {"upgrade": "选择支付方式升级到{planName}", "renew": "选择支付方式续费{planName}"}, "planPrice": "套餐价格", "selectPaymentMethod": "选择支付方式", "processingFee": "手续费 ({rate}%)", "total": "总计", "planCost": "套餐费用", "cancel": "取消", "confirmPayment": "确认支付", "creating": "创建中...", "selectMethodFirst": "请选择支付方式", "invalidPaymentMethod": "支付方式无效"}, "result": {"title": "支付订单已创建", "description": {"upgrade": "请完成支付以升级到{planName}", "renew": "请完成支付以续费{planName}"}, "cryptoPaymentNote": "请在新打开的页面中完成加密货币支付", "paymentPageNote": "点击下方按钮跳转到支付页面", "goToPayment": "前往支付", "autoCheckingStatus": "系统正在自动检测支付状态...", "paymentCompleted": "我已完成支付", "checkingPaymentStatus": "检查支付状态..."}, "status": {"orderNotFound": "订单号不存在", "checkFailed": "检查支付状态失败", "upgradeSuccess": "会员升级成功！", "renewSuccess": "会员续费成功！", "paymentExpired": "支付已过期，请重新创建订单", "paymentFailed": "支付失败，请重新尝试", "paymentPending": "支付还未完成", "paymentPendingDescription": "请确保已完成支付，支付成功后再次点击此按钮", "unknownError": "未知错误", "redirectedToPayment": "已跳转到支付页面，请完成支付", "createPaymentFailed": "创建支付失败", "getPaymentMethodsFailed": "获取支付方式失败"}}, "time": {"loading": "加载中..."}, "hooks": {"upgrade": {"error": "升级会员失败", "confirmed": "升级信息已确认", "confirmedDesc": "请选择支付方式完成升级到{planName}", "success": "升级成功", "successDesc": "已成功升级到{planName}", "failed": "升级失败"}, "purchase": {"error": "创建支付订单失败", "success": "支付订单创建成功", "successDesc": "请完成支付以{action}{planName}", "upgrade": "升级到", "renew": "续费", "failed": "创建支付订单失败"}, "renew": {"error": "续费失败", "confirmed": "续费信息已确认", "confirmedDesc": "请选择支付方式完成续费{planName}", "success": "续费成功", "successDesc": "会员套餐已成功续费", "failed": "续费失败"}}}