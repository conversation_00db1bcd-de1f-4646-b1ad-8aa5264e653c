import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import {
  CreateTicketInput,
  ReplyTicketInput,
  UpdateTicketStatusInput,
  TicketQueryParams,
  TicketListResponse,
  TicketWithUser,
} from '@/lib/types/ticket';

// 获取用户工单列表
export function useTickets(params: TicketQueryParams) {
  const t = useTranslations('tickets');

  return useQuery<TicketListResponse>({
    queryKey: ['tickets', params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/tickets?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error(t('messages.loadError'));
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5分钟
  });
}

// 获取工单详情
export function useTicket(ticketId: string) {
  const t = useTranslations('tickets');

  return useQuery<TicketWithUser>({
    queryKey: ['ticket', ticketId],
    queryFn: async () => {
      const response = await fetch(`/api/tickets/${ticketId}`);
      if (!response.ok) {
        throw new Error(t('messages.loadError'));
      }
      return response.json();
    },
    enabled: !!ticketId,
  });
}

// 创建新工单
export function useCreateTicket() {
  const queryClient = useQueryClient();
  const t = useTranslations('tickets');

  return useMutation({
    mutationFn: async (data: CreateTicketInput) => {
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();

        // 如果有具体的验证错误，显示第一个错误信息
        if (
          error.details &&
          Array.isArray(error.details) &&
          error.details.length > 0
        ) {
          throw new Error(error.details[0].message);
        }

        throw new Error(error.error || t('messages.submitError'));
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新工单列表
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
      toast.success(t('messages.submitSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 回复工单
export function useReplyTicket(ticketId: string) {
  const queryClient = useQueryClient();
  const t = useTranslations('tickets');

  return useMutation({
    mutationFn: async (data: ReplyTicketInput) => {
      const response = await fetch(`/api/tickets/${ticketId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || t('messages.replyError'));
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新工单详情和列表
      queryClient.invalidateQueries({ queryKey: ['ticket', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
      toast.success(t('messages.replySuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 更新工单状态
export function useUpdateTicketStatus(ticketId: string) {
  const queryClient = useQueryClient();
  const t = useTranslations('tickets');

  return useMutation({
    mutationFn: async (data: UpdateTicketStatusInput) => {
      const response = await fetch(`/api/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || t('messages.statusUpdateError'));
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新工单详情和列表
      queryClient.invalidateQueries({ queryKey: ['ticket', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
      toast.success(t('messages.statusUpdateSuccess'));
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
