'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { UploadedFile } from './use-file-upload';

interface ResubmitEvidenceData {
  taskId: string;
  files: UploadedFile[];
}

interface ResubmitEvidenceResponse {
  success: boolean;
  message: string;
  data?: any;
}

// 重新提交证据的API函数
async function resubmitEvidence(
  data: ResubmitEvidenceData,
): Promise<ResubmitEvidenceResponse> {
  // 将上传的文件信息转换为文件URL数组
  const evidenceFileUrls = data.files.map(file => file.fileUrl);

  const response = await fetch(`/api/tasks/${data.taskId}/resubmit-evidence`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      evidenceFiles: evidenceFileUrls,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || '重新提交证据失败');
  }

  return response.json();
}

// 使用重新提交证据的Hook
export function useResubmitEvidence() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: resubmitEvidence,
    onSuccess: data => {
      toast.success('证据重新提交成功！', {
        description: data.message || '您的证据已重新提交，请等待审核',
      });

      // 刷新相关查询缓存
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['published-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['admin-tasks'] });
    },
    onError: error => {
      const errorMessage =
        error instanceof Error ? error.message : '重新提交证据失败';
      toast.error('提交失败', {
        description: errorMessage,
      });
    },
  });
}
