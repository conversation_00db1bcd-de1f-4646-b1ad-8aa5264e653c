#!/usr/bin/env node

/**
 * 验证 i18n Ally 配置脚本
 * 
 * 检查：
 * 1. 翻译文件结构
 * 2. 命名空间映射
 * 3. 配置文件正确性
 * 4. 缺失的翻译键
 */

const fs = require('fs');
const path = require('path');

/**
 * 检查翻译文件结构
 */
function checkTranslationFiles() {
  console.log('📁 检查翻译文件结构...');
  
  const messagesDir = path.join(process.cwd(), 'messages');
  const locales = ['zh', 'en'];
  
  if (!fs.existsSync(messagesDir)) {
    console.error('❌ messages 目录不存在');
    return false;
  }
  
  let allFilesExist = true;
  
  locales.forEach(locale => {
    const localeDir = path.join(messagesDir, locale);
    
    if (!fs.existsSync(localeDir)) {
      console.error(`❌ ${locale} 语言目录不存在`);
      allFilesExist = false;
      return;
    }
    
    console.log(`  ✅ ${locale} 目录存在`);
    
    // 检查关键文件
    const keyFiles = [
      'my-published-tasks.json',
      'my-accepted-tasks.json',
      'publish.json',
      'common.json'
    ];
    
    keyFiles.forEach(file => {
      const filePath = path.join(localeDir, file);
      if (fs.existsSync(filePath)) {
        console.log(`    ✅ ${file}`);
      } else {
        console.error(`    ❌ ${file} 不存在`);
        allFilesExist = false;
      }
    });
  });
  
  return allFilesExist;
}

/**
 * 检查配置文件
 */
function checkConfigFiles() {
  console.log('\n⚙️  检查配置文件...');
  
  // 检查 VSCode 设置
  const vscodeSettings = path.join(process.cwd(), '.vscode', 'settings.json');
  if (fs.existsSync(vscodeSettings)) {
    console.log('  ✅ .vscode/settings.json 存在');
    
    try {
      const settings = JSON.parse(fs.readFileSync(vscodeSettings, 'utf8'));
      
      const requiredSettings = [
        'i18n-ally.localesPaths',
        'i18n-ally.pathMatcher',
        'i18n-ally.namespace',
        'i18n-ally.enabledFrameworks'
      ];
      
      requiredSettings.forEach(setting => {
        if (settings[setting] !== undefined) {
          console.log(`    ✅ ${setting}: ${JSON.stringify(settings[setting])}`);
        } else {
          console.error(`    ❌ ${setting} 未配置`);
        }
      });
      
    } catch (error) {
      console.error('    ❌ settings.json 格式错误');
    }
  } else {
    console.error('  ❌ .vscode/settings.json 不存在');
  }
  
  // 检查 i18n-ally.config.js
  const i18nConfig = path.join(process.cwd(), 'i18n-ally.config.js');
  if (fs.existsSync(i18nConfig)) {
    console.log('  ✅ i18n-ally.config.js 存在');
  } else {
    console.error('  ❌ i18n-ally.config.js 不存在');
  }
}

/**
 * 检查命名空间一致性
 */
function checkNamespaceConsistency() {
  console.log('\n🔗 检查命名空间一致性...');
  
  const messagesDir = path.join(process.cwd(), 'messages');
  const zhDir = path.join(messagesDir, 'zh');
  
  if (!fs.existsSync(zhDir)) {
    console.error('❌ zh 目录不存在，无法检查');
    return;
  }
  
  const files = fs.readdirSync(zhDir).filter(f => f.endsWith('.json'));
  
  files.forEach(file => {
    const namespace = file.replace('.json', '');
    console.log(`  📄 文件: ${file} -> 命名空间: ${namespace}`);
    
    // 检查是否为 kebab-case
    if (namespace.includes('_') || /[A-Z]/.test(namespace)) {
      console.warn(`    ⚠️  建议使用 kebab-case: ${namespace}`);
    } else {
      console.log(`    ✅ 命名符合 kebab-case 规范`);
    }
  });
}

/**
 * 生成使用示例
 */
function generateUsageExamples() {
  console.log('\n📖 使用示例：');
  
  console.log(`
// ✅ 正确的使用方式
import { useTranslations } from 'next-intl';

function MyComponent() {
  const t = useTranslations('my-published-tasks');
  
  return (
    <div>
      <h1>{t('navigation.title')}</h1>
      <p>{t('navigation.description')}</p>
    </div>
  );
}

// ✅ 多命名空间使用
function AnotherComponent() {
  const tTasks = useTranslations('my-published-tasks');
  const tCommon = useTranslations('common');
  
  return (
    <div>
      <h1>{tTasks('navigation.title')}</h1>
      <button>{tCommon('actions.save')}</button>
    </div>
  );
}
  `);
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 i18n Ally 配置验证\n');
  
  const checks = [
    checkTranslationFiles(),
    checkConfigFiles(),
  ];
  
  checkNamespaceConsistency();
  generateUsageExamples();
  
  const allPassed = checks.every(check => check !== false);
  
  console.log('\n📋 验证结果：');
  if (allPassed) {
    console.log('✅ 所有检查通过！');
    console.log('\n🚀 下一步：');
    console.log('1. 重启 VSCode');
    console.log('2. 打开任意包含翻译的文件');
    console.log('3. 检查 i18n Ally 侧边栏是否显示翻译');
    console.log('4. 测试自动完成功能');
  } else {
    console.log('❌ 部分检查失败，请修复后重试');
  }
}

main();
