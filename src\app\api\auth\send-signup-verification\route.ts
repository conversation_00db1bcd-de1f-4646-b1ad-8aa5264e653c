import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import prisma from '@/lib/db';
import {
  sendVerificationCodeEmail,
  generateVerificationCode,
} from '@/lib/email';
import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { hashPassword } from '@/lib/password';

// 注册验证码发送请求
const sendSignUpCodeSchema = z.object({
  name: z.string().min(1, '请输入用户名'),
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码长度至少为6位'),
});

// 验证码生成函数已从 @/lib/email 导入

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validation = sendSignUpCodeSchema.safeParse(body);

    if (!validation.success) {
      // Get language for error messages
      const language = await getUserEmailLanguage(undefined, request, 'zh');
      return NextResponse.json(
        {
          error: language === 'zh'
            ? validation.error.errors[0].message
            : 'Invalid request parameters'
        },
        { status: 400 },
      );
    }

    const { name, email, password } = validation.data;

    // 检查邮箱是否已被注册
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      const language = await getUserEmailLanguage(undefined, request, 'zh');
      return NextResponse.json({
        error: language === 'zh'
          ? '该邮箱已被注册'
          : 'This email is already registered'
      }, { status: 409 });
    }

    // 生成验证码
    const code = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

    // 加密密码（为临时存储做准备）
    const hashedPassword = await hashPassword(password);

    // 使用特殊的identifier格式来标识这是注册验证码
    const signupIdentifier = `signup:${email}`;

    // 删除该邮箱的旧注册验证码和临时注册数据
    await prisma.verificationToken.deleteMany({
      where: { identifier: signupIdentifier },
    });

    // 保存验证码和临时注册数据
    // 将注册数据编码到token中，格式为: 验证码|加密的注册数据
    const registrationData = Buffer.from(
      JSON.stringify({
        name,
        email,
        hashedPassword,
      }),
    ).toString('base64');
    const encodedToken = `${code}|${registrationData}`;

    await prisma.verificationToken.create({
      data: {
        identifier: signupIdentifier,
        token: encodedToken,
        expires: expiresAt,
      },
    });

    // 使用内置邮件系统发送验证码 (with language detection)
    const emailResult = await sendVerificationCodeEmail(
      email,
      {
        userName: name,
        userEmail: email,
        verificationCode: code,
        action: 'register',
        expiresIn: 10,
      },
      {
        request, // 传递请求信息用于语言检测
      },
    );

    if (!emailResult.success) {
      console.error('发送邮件失败:', emailResult.error);
      const language = await getUserEmailLanguage(undefined, request, 'zh');
      return NextResponse.json(
        {
          error: language === 'zh'
            ? '邮件发送失败，请稍后重试'
            : 'Failed to send email, please try again later'
        },
        { status: 500 },
      );
    }

    const language = await getUserEmailLanguage(undefined, request, 'zh');
    return NextResponse.json({
      success: true,
      message: language === 'zh'
        ? '验证码已发送，请查收邮件'
        : 'Verification code sent, please check your email',
    });
  } catch (error) {
    console.error('发送注册验证码失败:', error);
    const language = await getUserEmailLanguage(undefined, request, 'zh');
    return NextResponse.json({
      error: language === 'zh'
        ? '服务器内部错误'
        : 'Internal server error'
    }, { status: 500 });
  }
}
