'use client';

import {
  Search,
  Filter,
  ExternalLink,
  CheckCircle,
  XCircle,
  Clock,
  ShieldCheck,
  Store,
  Trash2,
  Loader2,
  AlertCircle,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useAdminWhitelist,
  useAdminUpdateWhitelistStatus,
  useAdminDeleteWhitelist,
  useAdminReviewWhitelist,
  type WhitelistQueryParams,
  type WhitelistStatus,
  type WhitelistItem,
} from '@/hooks/use-whitelist';

export function AdminWhitelistContent() {
  const [queryParams, setQueryParams] = useState<WhitelistQueryParams>({
    page: 1,
    limit: 10,
    search: '',
    platform: 'all',
    status: 'all',
  });

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    type: 'approve' | 'reject' | 'delete' | null;
    item: WhitelistItem | null;
  }>({
    isOpen: false,
    type: null,
    item: null,
  });

  // 使用真实数据hooks
  const {
    data: whitelistData,
    isLoading,
    error,
  } = useAdminWhitelist(queryParams);
  const updateStatusMutation = useAdminUpdateWhitelistStatus();
  const deleteMutation = useAdminDeleteWhitelist();
  const reviewMutation = useAdminReviewWhitelist();

  const whitelistItems = whitelistData?.whitelistItems || [];
  const stats = whitelistData?.stats || {
    total: 0,
    active: 0,
    inactive: 0,
    todayAdded: 0,
  };

  // 获取审核状态显示
  const getStatusDisplay = (status: WhitelistStatus) => {
    switch (status) {
      case 'PENDING':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <Clock className='h-3 w-3' />,
          text: '待审核',
        };
      case 'APPROVED':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className='h-3 w-3' />,
          text: '已通过',
        };
      case 'REJECTED':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <XCircle className='h-3 w-3' />,
          text: '已拒绝',
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Clock className='h-3 w-3' />,
          text: '未知状态',
        };
    }
  };

  // 打开确认对话框
  const openConfirmDialog = (
    type: 'approve' | 'reject' | 'delete',
    item: WhitelistItem,
  ) => {
    setConfirmDialog({
      isOpen: true,
      type,
      item,
    });
  };

  // 关闭确认对话框
  const closeConfirmDialog = () => {
    setConfirmDialog({
      isOpen: false,
      type: null,
      item: null,
    });
  };

  // 确认执行操作
  const confirmAction = () => {
    if (!confirmDialog.item || !confirmDialog.type) return;

    const { item, type } = confirmDialog;

    switch (type) {
      case 'approve':
        reviewMutation.mutate({ id: item.id, action: 'approve' });
        break;
      case 'reject':
        reviewMutation.mutate({ id: item.id, action: 'reject' });
        break;
      case 'delete':
        deleteMutation.mutate(item.id);
        break;
    }

    closeConfirmDialog();
  };

  // 获取确认对话框内容
  const getConfirmContent = () => {
    if (!confirmDialog.type || !confirmDialog.item) return null;

    const { type, item } = confirmDialog;

    switch (type) {
      case 'approve':
        return {
          title: '确认通过申请',
          description:
            '您确定要通过这个白名单申请吗？通过后该店铺将被加入白名单，禁止用户发布相关委托。',
          confirmText: '确认通过',
          confirmClass: 'bg-green-600 hover:bg-green-700',
        };
      case 'reject':
        return {
          title: '确认拒绝申请',
          description:
            '您确定要拒绝这个白名单申请吗？拒绝后用户可以删除记录并重新申请。',
          confirmText: '确认拒绝',
          confirmClass: 'bg-red-600 hover:bg-red-700',
        };
      case 'delete':
        return {
          title: '确认删除条目',
          description:
            '您确定要删除这个白名单条目吗？删除后将无法恢复，用户可以重新申请。',
          confirmText: '确认删除',
          confirmClass: 'bg-red-600 hover:bg-red-700',
        };
      default:
        return null;
    }
  };

  return (
    <div className='space-y-6'>
      {/* 统计卡片 */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>白名单总数</CardTitle>
            <ShieldCheck className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.total}</div>
            <p className='text-xs text-muted-foreground'>所有白名单条目</p>
          </CardContent>
        </Card>
      </div>

      {/* 白名单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>白名单列表</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className='flex items-center justify-center py-8'>
              <Loader2 className='h-8 w-8 animate-spin' />
              <span className='ml-2'>加载中...</span>
            </div>
          ) : error ? (
            <div className='text-center py-8'>
              <AlertCircle className='h-12 w-12 mx-auto mb-4 text-red-500' />
              <p className='text-red-600'>加载白名单失败</p>
            </div>
          ) : whitelistItems.length === 0 ? (
            <div className='text-center py-8 text-muted-foreground'>
              <Store className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>暂无白名单条目</p>
            </div>
          ) : (
            <div className='overflow-x-auto'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户信息</TableHead>
                    <TableHead>店铺信息</TableHead>
                    <TableHead>平台</TableHead>
                    <TableHead>添加时间</TableHead>
                    <TableHead>审核状态</TableHead>
                    <TableHead className='text-right'>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {whitelistItems.map(item => (
                    <TableRow key={item.id} className='hover:bg-muted/50'>
                      <TableCell>
                        <div className='space-y-1'>
                          <p className='text-sm font-medium'>
                            {item.user?.name || '未知用户'}
                          </p>
                          <p className='text-xs text-muted-foreground'>
                            {item.user?.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='space-y-1'>
                          <p className='text-sm font-medium'>{item.shopName}</p>
                          <div className='flex items-center gap-1'>
                            <ExternalLink className='h-3 w-3 text-muted-foreground' />
                            <a
                              href={item.shopUrl}
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-xs text-blue-600 hover:underline truncate max-w-[200px]'
                            >
                              {item.shopUrl}
                            </a>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant='outline' className='text-xs'>
                          {item.platform}
                        </Badge>
                      </TableCell>

                      <TableCell>
                        <span className='text-sm'>
                          {new Date(item.createdAt).toLocaleDateString(
                            'zh-CN',
                            {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                            },
                          )}
                        </span>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const statusDisplay = getStatusDisplay(
                            item.status || 'PENDING',
                          );
                          return (
                            <Badge className={`text-xs ${statusDisplay.color}`}>
                              <span className='mr-1'>{statusDisplay.icon}</span>
                              {statusDisplay.text}
                            </Badge>
                          );
                        })()}
                      </TableCell>
                      <TableCell className='text-right'>
                        <div className='flex items-center gap-2 justify-end'>
                          {item.status === 'PENDING' && (
                            <>
                              <Button
                                size='sm'
                                variant='outline'
                                onClick={() =>
                                  openConfirmDialog('approve', item)
                                }
                                disabled={reviewMutation.isPending}
                                className='text-green-600 hover:text-green-700 hover:bg-green-50'
                              >
                                {reviewMutation.isPending ? (
                                  <Loader2 className='mr-1 h-4 w-4 animate-spin' />
                                ) : (
                                  <CheckCircle className='mr-1 h-4 w-4' />
                                )}
                                通过
                              </Button>
                              <Button
                                size='sm'
                                variant='outline'
                                onClick={() =>
                                  openConfirmDialog('reject', item)
                                }
                                disabled={reviewMutation.isPending}
                                className='text-red-600 hover:text-red-700 hover:bg-red-50'
                              >
                                {reviewMutation.isPending ? (
                                  <Loader2 className='mr-1 h-4 w-4 animate-spin' />
                                ) : (
                                  <XCircle className='mr-1 h-4 w-4' />
                                )}
                                拒绝
                              </Button>
                            </>
                          )}
                          {item.status === 'APPROVED' && (
                            <Button
                              size='sm'
                              variant='outline'
                              onClick={() =>
                                updateStatusMutation.mutate({
                                  id: item.id,
                                  isActive: !item.isActive,
                                })
                              }
                              disabled={updateStatusMutation.isPending}
                              className={
                                item.isActive
                                  ? 'text-red-600 hover:text-red-700'
                                  : 'text-green-600 hover:text-green-700'
                              }
                            >
                              {updateStatusMutation.isPending ? (
                                <Loader2 className='mr-1 h-4 w-4 animate-spin' />
                              ) : item.isActive ? (
                                <XCircle className='mr-1 h-4 w-4' />
                              ) : (
                                <CheckCircle className='mr-1 h-4 w-4' />
                              )}
                              {item.isActive ? '禁用' : '启用'}
                            </Button>
                          )}
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() => openConfirmDialog('delete', item)}
                            disabled={deleteMutation.isPending}
                            className='text-red-600 hover:text-red-700'
                          >
                            {deleteMutation.isPending ? (
                              <Loader2 className='mr-1 h-4 w-4 animate-spin' />
                            ) : (
                              <Trash2 className='mr-1 h-4 w-4' />
                            )}
                            删除
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 确认对话框 */}
      <Dialog open={confirmDialog.isOpen} onOpenChange={closeConfirmDialog}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>{getConfirmContent()?.title}</DialogTitle>
            <DialogDescription>
              {getConfirmContent()?.description}
            </DialogDescription>
          </DialogHeader>

          {confirmDialog.item && (
            <div className='space-y-3'>
              <div className='p-3 bg-gray-50 rounded-lg'>
                <p className='font-medium'>{confirmDialog.item.shopName}</p>
                <p className='text-sm text-muted-foreground'>
                  {confirmDialog.item.platform}
                </p>
                <p className='text-sm text-muted-foreground truncate'>
                  {confirmDialog.item.shopUrl}
                </p>
                <div className='flex items-center gap-2 mt-2'>
                  <span className='text-sm text-muted-foreground'>
                    申请用户：
                  </span>
                  <span className='text-sm'>
                    {confirmDialog.item.user?.name || '未知用户'}
                  </span>
                </div>
                <div className='flex items-center gap-2 mt-1'>
                  <span className='text-sm text-muted-foreground'>
                    当前状态：
                  </span>
                  {(() => {
                    const statusDisplay = getStatusDisplay(
                      confirmDialog.item.status || 'PENDING',
                    );
                    return (
                      <Badge className={`text-xs ${statusDisplay.color}`}>
                        <span className='mr-1'>{statusDisplay.icon}</span>
                        {statusDisplay.text}
                      </Badge>
                    );
                  })()}
                </div>
              </div>
            </div>
          )}

          <div className='flex gap-2 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={closeConfirmDialog}
              className='flex-1'
              disabled={reviewMutation.isPending || deleteMutation.isPending}
            >
              取消
            </Button>
            <Button
              type='button'
              onClick={confirmAction}
              className={`flex-1 text-white ${getConfirmContent()?.confirmClass || 'bg-blue-600 hover:bg-blue-700'}`}
              disabled={reviewMutation.isPending || deleteMutation.isPending}
            >
              {reviewMutation.isPending || deleteMutation.isPending ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  处理中...
                </>
              ) : (
                getConfirmContent()?.confirmText || '确认'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
