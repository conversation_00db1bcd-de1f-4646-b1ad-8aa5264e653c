'use client';

import {
  UserPlus,
  Save,
  Mail,
  Lock,
  User as UserIcon,
  Wallet,
  Crown,
  Shield,
  Users,
  DollarSign,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { getMemberPlanText } from '@/lib/constants';
import { User } from '@/types/user';

interface AddUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserAdd?: (user: User) => void;
}

export function AddUserDialog({
  open,
  onOpenChange,
  onUserAdd,
}: AddUserDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  // 获取一个月后的日期
  const getOneMonthLater = () => {
    const now = new Date();
    const oneMonthLater = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      now.getDate(),
    );
    return oneMonthLater.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
  };

  const [formData, setFormData] = useState({
    nickname: '',
    email: '',
    password: '',
    confirmPassword: '',
    memberPlan: 'FREE' as any,
    memberPlanExpiry: '',
    balance: '',
    status: 'ACTIVE' as any,
  });

  // 获取会员套餐颜色
  const getMemberPlanColor = (plan: string) => {
    switch (plan) {
      case 'BUSINESS':
        return 'bg-purple-100 text-purple-800';
      case 'PRO':
        return 'bg-blue-100 text-blue-800';
      case 'FREE':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取会员套餐图标
  const getMemberPlanIcon = (plan: string) => {
    switch (plan) {
      case 'BUSINESS':
        return <Crown className='h-3 w-3' />;
      case 'PRO':
        return <Shield className='h-3 w-3' />;
      default:
        return <Users className='h-3 w-3' />;
    }
  };

  // 表单验证
  const validateForm = () => {
    if (!formData.nickname.trim()) {
      toast.error('请输入用户昵称');
      return false;
    }
    if (formData.nickname.length < 2 || formData.nickname.length > 20) {
      toast.error('昵称长度应在2-20个字符之间');
      return false;
    }
    if (!formData.email.trim()) {
      toast.error('请输入邮箱地址');
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('请输入有效的邮箱地址');
      return false;
    }
    if (!formData.password.trim()) {
      toast.error('请输入密码');
      return false;
    }
    if (formData.password.length < 6) {
      toast.error('密码长度至少6位');
      return false;
    }
    if (formData.password.length > 32) {
      toast.error('密码长度不能超过32位');
      return false;
    }
    if (!/^[a-zA-Z0-9]+$/.test(formData.password)) {
      toast.error('密码只能包含字母和数字');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      toast.error('两次输入的密码不一致');
      return false;
    }
    if (
      formData.balance &&
      (isNaN(parseFloat(formData.balance)) || parseFloat(formData.balance) < 0)
    ) {
      toast.error('请输入有效的初始余额');
      return false;
    }
    // 移除会员到期时间的验证，因为现在会自动设置
    return true;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // 调用真实的API创建用户
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nickname: formData.nickname,
          email: formData.email,
          password: formData.password,
          memberPlan: formData.memberPlan,
          memberPlanExpiry: formData.memberPlanExpiry,
          balance: parseFloat(formData.balance) || 0,
          status: formData.status,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        toast.error(result.error || '添加用户失败');
        return;
      }

      // 调用添加回调，使用后端返回的真实用户数据
      onUserAdd?.(result.user);

      toast.success(`用户 ${formData.nickname} 添加成功`);
      handleClose();
    } catch (error) {
      console.error('操作失败:', error);
      toast.error('操作失败', {
        description: '请重试或联系客服',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 关闭对话框并重置表单
  const handleClose = () => {
    setFormData({
      nickname: '',
      email: '',
      password: '',
      confirmPassword: '',
      memberPlan: 'FREE' as any,
      memberPlanExpiry: '',
      balance: '',
      status: 'ACTIVE' as any,
    });
    onOpenChange(false);
  };

  // 处理输入框变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // 当会员套餐改变时，自动设置到期时间
      if (field === 'memberPlan' && value !== 'FREE') {
        newData.memberPlanExpiry = getOneMonthLater();
      } else if (field === 'memberPlan' && value === 'FREE') {
        newData.memberPlanExpiry = '';
      }

      return newData;
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-full max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <UserPlus className='h-5 w-5' />
            添加新用户
          </DialogTitle>
          <DialogDescription>
            创建一个新的用户账户，填写以下信息
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6 py-4'>
          {/* 基本信息 */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='nickname' className='flex items-center gap-2'>
                <UserIcon className='h-4 w-4' />
                昵称 <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='nickname'
                value={formData.nickname}
                onChange={e => handleInputChange('nickname', e.target.value)}
                placeholder='输入用户昵称'
                className='w-full h-10'
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='email' className='flex items-center gap-2'>
                <Mail className='h-4 w-4' />
                邮箱 <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='email'
                type='email'
                value={formData.email}
                onChange={e => handleInputChange('email', e.target.value)}
                placeholder='输入邮箱地址'
                className='w-full h-10'
              />
            </div>
          </div>

          {/* 密码设置 */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='password' className='flex items-center gap-2'>
                <Lock className='h-4 w-4' />
                密码 <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='password'
                type='password'
                value={formData.password}
                onChange={e => handleInputChange('password', e.target.value)}
                placeholder='输入密码'
                className='w-full h-10'
              />
            </div>

            <div className='space-y-2'>
              <Label
                htmlFor='confirmPassword'
                className='flex items-center gap-2'
              >
                <Lock className='h-4 w-4' />
                确认密码 <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='confirmPassword'
                type='password'
                value={formData.confirmPassword}
                onChange={e =>
                  handleInputChange('confirmPassword', e.target.value)
                }
                placeholder='再次输入密码'
                className='w-full h-10'
              />
            </div>
          </div>

          {/* 会员设置 */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='memberPlan' className='flex items-center gap-2'>
                <Crown className='h-4 w-4' />
                会员套餐
              </Label>
              <Select
                value={formData.memberPlan}
                onValueChange={value => handleInputChange('memberPlan', value)}
              >
                <SelectTrigger className='w-full h-10'>
                  <SelectValue placeholder='选择会员套餐' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='FREE'>
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant='outline'
                        className={getMemberPlanColor('FREE')}
                      >
                        <Users className='h-3 w-3 mr-1' />
                        免费版
                      </Badge>
                    </div>
                  </SelectItem>
                  <SelectItem value='PRO'>
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant='outline'
                        className={getMemberPlanColor('PRO')}
                      >
                        <Shield className='h-3 w-3 mr-1' />
                        专业版
                      </Badge>
                    </div>
                  </SelectItem>
                  <SelectItem value='BUSINESS'>
                    <div className='flex items-center gap-2'>
                      <Badge
                        variant='outline'
                        className={getMemberPlanColor('BUSINESS')}
                      >
                        <Crown className='h-3 w-3 mr-1' />
                        商业版
                      </Badge>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.memberPlan !== 'FREE' && (
              <div className='space-y-2'>
                <Label htmlFor='memberPlanExpiry'>会员到期时间</Label>
                <Input
                  id='memberPlanExpiry'
                  type='date'
                  value={formData.memberPlanExpiry}
                  onChange={e =>
                    handleInputChange('memberPlanExpiry', e.target.value)
                  }
                  className='w-full h-10'
                />
                <p className='text-xs text-gray-500'>
                  默认为当前时间+一个月，您可以手动调整
                </p>
              </div>
            )}
          </div>

          {/* 账户设置 */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='balance' className='flex items-center gap-2'>
                <DollarSign className='h-4 w-4' />
                初始余额
              </Label>
              <Input
                id='balance'
                type='number'
                step='0.01'
                min='0'
                value={formData.balance}
                onChange={e => handleInputChange('balance', e.target.value)}
                placeholder='0.00'
                className='w-full h-10'
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='status'>账户状态</Label>
              <Select
                value={formData.status}
                onValueChange={value => handleInputChange('status', value)}
              >
                <SelectTrigger className='w-full h-10'>
                  <SelectValue placeholder='选择账户状态' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='ACTIVE'>
                    <Badge
                      variant='outline'
                      className='bg-green-100 text-green-800'
                    >
                      正常
                    </Badge>
                  </SelectItem>
                  <SelectItem value='SUSPENDED'>
                    <Badge
                      variant='outline'
                      className='bg-yellow-100 text-yellow-800'
                    >
                      暂停
                    </Badge>
                  </SelectItem>
                  <SelectItem value='BANNED'>
                    <Badge
                      variant='outline'
                      className='bg-red-100 text-red-800'
                    >
                      封禁
                    </Badge>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <DialogFooter className='flex flex-col sm:flex-row gap-2'>
          <Button
            type='button'
            variant='outline'
            onClick={handleClose}
            disabled={isLoading}
            className='w-full sm:w-auto'
          >
            取消
          </Button>
          <Button
            type='button'
            onClick={handleSubmit}
            disabled={isLoading}
            className='flex items-center gap-2 w-full sm:w-auto'
          >
            {isLoading ? (
              <>
                <div className='h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                创建中...
              </>
            ) : (
              <>
                <Save className='h-4 w-4' />
                创建用户
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
