'use client';

import {
  Search,
  Edit,
  MoreHorizontal,
  ClipboardList,
  Eye,
  Filter,
  ChevronLeft,
  ChevronRight,
  Ban,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Loader2,
  XCircle,
  Upload,
  Truck,
} from 'lucide-react';
import { useState } from 'react';

import { AdminEditTaskDialog } from '@/components/admin/admin-edit-task-dialog';
import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { AdminTaskDetailSheet } from '@/components/admin/admin-task-detail-sheet';
import { AdminViewEvidenceSheet } from '@/components/admin/admin-view-evidence-sheet';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useAdminAllTasks,
  type AdminTaskFilters,
} from '@/hooks/use-admin-all-tasks';
import { useActivePlatforms } from '@/hooks/use-publish-config';

export default function TasksPage() {
  // 筛选状态
  const [filters, setFilters] = useState<AdminTaskFilters>({
    search: '',
    platform: 'all',
    taskStatus: 'all',
    page: 1,
    limit: 20,
  });

  // 获取数据
  const { data: tasksData, isLoading, error } = useAdminAllTasks(filters);
  const { data: platforms = [] } = useActivePlatforms();

  const tasks = tasksData?.data?.tasks || [];
  const stats = tasksData?.data?.stats || {
    totalTasks: 0,
    pendingTasks: 0,
    recruitingTasks: 0,
    activeTasks: 0,
    completedTasks: 0,
    rejectedTasks: 0,
    expiredTasks: 0,
    totalValue: 0,
    pendingSubmissionEvidence: 0,
    underReviewEvidence: 0,
    reviewedEvidence: 0,
    noEvidence: 0,
  };
  const pagination = tasksData?.data?.pagination;

  // 处理筛选变化
  const handleFilterChange = (key: keyof AdminTaskFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value,
      page: 1, // 重置页码
    }));
  };

  // 处理搜索
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      search: value || undefined,
      page: 1,
    }));
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({
      search: '',
      platform: 'all',
      taskStatus: 'all',
      page: 1,
      limit: 20,
    });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // 获取委托状态显示
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <AlertTriangle className='h-3 w-3' />,
          variant: 'outline' as const,
          label: '审核中',
        };
      case 'RECRUITING':
        return {
          color: 'bg-blue-100 text-blue-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'secondary' as const,
          label: '招募中',
        };
      case 'IN_PROGRESS':
        return {
          color: 'bg-purple-100 text-purple-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'secondary' as const,
          label: '进行中',
        };
      case 'PENDING_LOGISTICS':
        return {
          color: 'bg-orange-100 text-orange-800',
          icon: <Upload className='h-3 w-3' />,
          variant: 'secondary' as const,
          label: '等待物流单号',
        };
      case 'PENDING_REVIEW':
        return {
          color: 'bg-indigo-100 text-indigo-800',
          icon: <Eye className='h-3 w-3' />,
          variant: 'secondary' as const,
          label: '等待审核',
        };
      case 'PENDING_DELIVERY':
        return {
          color: 'bg-cyan-100 text-cyan-800',
          icon: <Truck className='h-3 w-3' />,
          variant: 'secondary' as const,
          label: '等待收货',
        };
      case 'COMPLETED':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className='h-3 w-3' />,
          variant: 'default' as const,
          label: '已完成',
        };
      case 'REJECTED':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <Ban className='h-3 w-3' />,
          variant: 'destructive' as const,
          label: '已拒绝',
        };
      case 'EXPIRED':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'outline' as const,
          label: '已过期',
        };
      case 'CANCELLED':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Ban className='h-3 w-3' />,
          variant: 'outline' as const,
          label: '已取消',
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'outline' as const,
          label: status,
        };
    }
  };

  // 获取证据状态显示
  const getEvidenceDisplay = (evidenceStatus: string | null) => {
    switch (evidenceStatus) {
      case 'REVIEWED':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className='h-3 w-3' />,
          variant: 'default' as const,
          label: '已审核',
        };
      case 'UNDER_REVIEW':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'outline' as const,
          label: '待审核',
        };
      case 'PENDING_SUBMISSION':
        return {
          color: 'bg-blue-100 text-blue-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'secondary' as const,
          label: '待上传',
        };
      case 'REJECTED':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <XCircle className='h-3 w-3' />,
          variant: 'destructive' as const,
          label: '未通过',
        };
      case 'NO_EVIDENCE':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <FileText className='h-3 w-3' />,
          variant: 'outline' as const,
          label: '无证据',
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <FileText className='h-3 w-3' />,
          variant: 'outline' as const,
          label: '未知',
        };
    }
  };

  if (error) {
    return (
      <AdminPageLayout title='管理后台' breadcrumbPage='委托列表' href='/admin'>
        <div className='flex items-center justify-center py-12'>
          <p className='text-sm text-red-600'>
            加载委托数据失败，请刷新页面重试
          </p>
        </div>
      </AdminPageLayout>
    );
  }

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='委托列表' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题和操作 */}
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
          <div className='space-y-1'>
            <h1 className='text-2xl font-bold tracking-tight'>委托列表</h1>
            <p className='text-sm text-muted-foreground'>
              管理系统中的所有委托，查看委托详情、审核状态和拒付证据
            </p>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>总委托数</CardTitle>
              <ClipboardList className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.totalTasks}</div>
              <p className='text-xs text-muted-foreground'>
                进行中 {stats.activeTasks} / 已完成 {stats.completedTasks}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>待审核委托</CardTitle>
              <AlertTriangle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.pendingTasks}</div>
              <p className='text-xs text-muted-foreground'>
                招募中 {stats.recruitingTasks} 个委托
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>总价值</CardTitle>
              <div className='h-4 w-4 text-muted-foreground'>$</div>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                ${stats.totalValue.toLocaleString()}
              </div>
              <p className='text-xs text-muted-foreground'>所有委托总价值</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>拒付证据</CardTitle>
              <FileText className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.reviewedEvidence}</div>
              <p className='text-xs text-muted-foreground'>
                已审核 / 待审核 {stats.underReviewEvidence}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Filter className='h-5 w-5' />
              搜索和筛选
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex flex-col lg:flex-row gap-4'>
              <div className='relative flex-1'>
                <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='搜索委托ID或发布者...'
                  className='pl-10'
                  value={filters.search || ''}
                  onChange={e => handleSearchChange(e.target.value)}
                />
              </div>
              <div className='flex flex-col sm:flex-row gap-2'>
                <Select
                  value={filters.platform || 'all'}
                  onValueChange={value => handleFilterChange('platform', value)}
                >
                  <SelectTrigger className='w-full sm:w-[120px]'>
                    <SelectValue placeholder='平台' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部平台</SelectItem>
                    {platforms.map(platform => (
                      <SelectItem key={platform.id} value={platform.id}>
                        {platform.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={filters.taskStatus || 'all'}
                  onValueChange={value =>
                    handleFilterChange('taskStatus', value)
                  }
                >
                  <SelectTrigger className='w-full sm:w-[120px]'>
                    <SelectValue placeholder='委托状态' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部状态</SelectItem>
                    <SelectItem value='pending'>审核中</SelectItem>
                    <SelectItem value='recruiting'>招募中</SelectItem>
                    <SelectItem value='in_progress'>进行中</SelectItem>
                    <SelectItem value='completed'>已完成</SelectItem>
                    <SelectItem value='rejected'>已拒绝</SelectItem>
                    <SelectItem value='expired'>已过期</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 委托列表 */}
        <Card>
          <CardHeader>
            <CardTitle>委托列表</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className='flex items-center justify-center py-12'>
                <Loader2 className='h-8 w-8 animate-spin' />
                <span className='ml-2 text-sm text-muted-foreground'>
                  加载委托数据...
                </span>
              </div>
            ) : tasks.length === 0 ? (
              <div className='text-center py-12'>
                <ClipboardList className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                <p className='text-muted-foreground'>暂无委托数据</p>
              </div>
            ) : (
              <>
                <div className='overflow-x-auto'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>委托ID</TableHead>
                        <TableHead>平台</TableHead>
                        <TableHead>拒付类型</TableHead>
                        <TableHead>数量</TableHead>
                        <TableHead>单价</TableHead>
                        <TableHead>酬金</TableHead>
                        <TableHead>发布者</TableHead>
                        <TableHead>接单者</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>拒付证据</TableHead>
                        <TableHead className='text-right'>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tasks.map(task => (
                        <TableRow key={task.id} className='hover:bg-muted/50'>
                          <TableCell>
                            <span className='font-mono text-sm font-medium text-blue-600'>
                              {task.id}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline' className='text-xs'>
                              {task.platform}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant='ghost'
                                  className='h-auto p-0 hover:bg-transparent'
                                >
                                  <Badge
                                    variant='outline'
                                    className='text-xs bg-orange-100 text-orange-800 cursor-pointer hover:bg-orange-200'
                                  >
                                    {task.chargebackTypes[0] || '未知'}
                                    {task.chargebackTypes.length > 1 && (
                                      <span className='ml-1 text-orange-600'>
                                        +{task.chargebackTypes.length - 1}
                                      </span>
                                    )}
                                  </Badge>
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className='w-80'>
                                <div className='space-y-2'>
                                  <h4 className='font-medium text-sm'>
                                    拒付类型
                                  </h4>
                                  <div className='space-y-2'>
                                    {task.chargebackTypes.map((type, index) => (
                                      <div
                                        key={index}
                                        className='flex items-center gap-2'
                                      >
                                        <Badge
                                          variant='outline'
                                          className='text-xs bg-orange-100 text-orange-800'
                                        >
                                          {type}
                                        </Badge>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          </TableCell>
                          <TableCell>
                            <span className='text-sm font-medium'>
                              {task.quantity}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className='text-sm font-medium text-green-600'>
                              ${task.unitPrice.toFixed(2)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className='text-sm font-medium text-green-600'>
                              ${(task.commission || 0).toFixed(2)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className='space-y-1'>
                              <p className='text-sm font-medium'>
                                {task.publisher.nickname}
                              </p>
                              <p className='text-xs text-muted-foreground'>
                                {task.publisher.email}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            {task.accepter ? (
                              <div className='space-y-1'>
                                <p className='text-sm font-medium'>
                                  {task.accepter.nickname}
                                </p>
                                <p className='text-xs text-muted-foreground'>
                                  {task.accepter.email}
                                </p>
                              </div>
                            ) : (
                              <span className='text-sm text-muted-foreground'>
                                未接单
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            {(() => {
                              const statusDisplay = getStatusDisplay(
                                task.status,
                              );
                              return (
                                <Badge
                                  variant={statusDisplay.variant}
                                  className={`text-xs ${statusDisplay.color}`}
                                >
                                  <span className='mr-1'>
                                    {statusDisplay.icon}
                                  </span>
                                  {statusDisplay.label}
                                </Badge>
                              );
                            })()}
                          </TableCell>
                          <TableCell>
                            {(() => {
                              const evidenceDisplay = getEvidenceDisplay(
                                task.evidenceStatus,
                              );
                              return (
                                <Badge
                                  variant={evidenceDisplay.variant}
                                  className={`text-xs ${evidenceDisplay.color}`}
                                >
                                  <span className='mr-1'>
                                    {evidenceDisplay.icon}
                                  </span>
                                  {evidenceDisplay.label}
                                </Badge>
                              );
                            })()}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <span className='sr-only'>打开菜单</span>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuLabel>委托操作</DropdownMenuLabel>
                                <AdminTaskDetailSheet task={task}>
                                  <DropdownMenuItem
                                    onSelect={e => e.preventDefault()}
                                  >
                                    <Eye className='mr-2 h-4 w-4' />
                                    查看详情
                                  </DropdownMenuItem>
                                </AdminTaskDetailSheet>
                                <AdminEditTaskDialog task={task}>
                                  <DropdownMenuItem
                                    onSelect={e => e.preventDefault()}
                                  >
                                    <Edit className='mr-2 h-4 w-4' />
                                    编辑委托
                                  </DropdownMenuItem>
                                </AdminEditTaskDialog>
                                <DropdownMenuSeparator />
                                <AdminViewEvidenceSheet task={task}>
                                  <DropdownMenuItem
                                    onSelect={e => e.preventDefault()}
                                  >
                                    <FileText className='mr-2 h-4 w-4' />
                                    查看证据
                                  </DropdownMenuItem>
                                </AdminViewEvidenceSheet>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* 分页 */}
                {pagination && pagination.totalPages > 1 && (
                  <div className='flex items-center justify-between space-x-2 py-4'>
                    <div className='text-sm text-muted-foreground'>
                      显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
                      {Math.min(
                        pagination.page * pagination.limit,
                        pagination.total,
                      )}{' '}
                      条， 共 {pagination.total} 条记录
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page <= 1}
                      >
                        <ChevronLeft className='h-4 w-4' />
                        上一页
                      </Button>
                      <div className='text-sm'>
                        第 {pagination.page} 页，共 {pagination.totalPages} 页
                      </div>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        下一页
                        <ChevronRight className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminPageLayout>
  );
}
