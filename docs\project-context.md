# Refundgo Web 2 - Project Context

## Application Purpose

Refundgo is a comprehensive multilingual task publishing and completion platform with integrated
payment processing, user management, and administrative features. Built with Next.js 14+ and modern
React patterns for optimal performance and developer experience. Features complete
internationalization support for Chinese and English users with automatic language detection and
user preference tracking.

## Key Features

- **User Authentication**: NextAuth v5 (beta) with OAuth providers
- **Task Management**: Publishing, accepting, completing, and reviewing tasks
- **Payment System**: Multi-provider payment processing with wallet system
- **Admin Dashboard**: Comprehensive user, task, and system management
- **Membership System**: Tiered user subscriptions with privileges
- **Ticket System**: Customer support and issue tracking
- **Whitelist Management**: User access control and verification
- **File Uploads**: Secure document and media handling
- **3D Components**: Interactive UI elements with React Three Fiber
- **Internationalization**: Complete multi-language support with next-intl (Chinese/English)
- **User Language Tracking**: Automatic language detection and preference storage
- **Multilingual Email System**: Language-specific email templates and notifications
- **AI Integration**: Personalized message generation
- **Logistics Tracking**: Order and delivery management
- **Evidence System**: Task completion verification

## Tech Stack

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **UI & Styling**: Tailwind CSS, shadcn/ui, Radix UI, Framer Motion, next-themes
- **3D Graphics**: React Three Fiber + Drei
- **State Management**: TanStack Query (server state), Zustand (client state)
- **Forms**: React Hook Form + Zod validation
- **Backend & Data**: Prisma ORM (PostgreSQL), NextAuth v5 (beta), Server Actions
- **Email**: Resend with multilingual templates
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Vitest, Playwright
- **Dev Tools**: ESLint, Prettier, Husky, lint-staged, Augment

## User Roles

1. **Admin**: Full system access and management
2. **Publisher**: Can create and manage tasks
3. **User**: Can accept and complete tasks
4. **Guest**: Limited access to public content

## Route Structure

### User-Facing Routes (`src/app/[locale]`)

- **(main)**: Public pages like `/`, `/sign-in`, `/sign-up`, `/privacy`, `/terms`.
- **(user)**: Protected user-centric pages like `/dashboard`, `/tasks`, `/profile`, `/wallet`,
  `/membership`.
- **payment**: Payment flows for success and cancellation.

### Admin Routes (`src/app/(admin)`)

- A dedicated group for all admin panel functionality, such as `/admin/users`, `/admin/tasks`,
  `/admin/settings`. This group has its own layout and is not internationalized.

## API Endpoints

### Authentication (`/api/auth`)

- `/[...nextauth]`: NextAuth.js handlers for login, logout, session.
- `/register`, `/complete-signup`: User registration flow.
- `/send-verification-code`: Email verification.

### User (`/api/user`)

- `/profile`, `/password`, `/email`: User account management.
- `/wallet/*`: Wallet operations (deposit, withdraw, transactions).
- `/membership/*`: Membership subscription management.
- `/tasks/*`, `/accepted-tasks/*`, `/published-tasks/*`: User-level task management.
- `/tickets/*`: User support tickets.

### Task Management (`/api/tasks`)

- `/`: Browse and search tasks.
- `/[id]/accept`: Accept a task.
- `/[id]/submit-evidence`: Submit proof of completion.
- `/publish`: Publish a new task.

### Publisher (`/api/publisher`)

- `/tasks/[id]/review`: Review submitted evidence.
- `/tasks/[id]/confirm-delivery`: Confirm task completion.

### Admin (`/api/admin`)

- `/dashboard/stats`: Platform statistics.
- `/users/*`: User management.
- `/tasks/*`: Task administration and review.
- `/tickets/*`: Support ticket management.
- `/withdrawals/*`: Payout processing.
- `/settings/*`: System configuration (rates, plans, etc.).

### Payments (`/api/payments`)

- `/create`: Create a new payment order.
- `/status/[orderNo]`: Check payment status.
- `/notify/[provider]`: Webhook for payment notifications.

### Utilities

- `/api/upload`: File uploads.
- `/api/cron/*`: Scheduled jobs (e.g., expiring tasks).
- `/api/ai/*`: AI-powered features.

## Database Schema (Prisma)

### Core Models

- **User** - Authentication, profile, and account management
- **Account** - OAuth provider account linking (NextAuth)
- **Session** - User session management (NextAuth)
- **VerificationToken** - Email verification tokens (NextAuth)

### Task System

- **Task** - Job postings with details, requirements, and status
- **TaskApplication** - User applications to tasks
- **TaskEvidence** - Submission evidence and verification
- **TaskReview** - Task completion reviews and ratings
- **Category** - Task categorization
- **Platform** - E-commerce platforms for tasks
- **ChargebackType** - Types of chargeback/refund scenarios

### Payment & Finance

- **Wallet** - User wallet and balance management
- **Transaction** - Financial transaction records
- **Withdrawal** - Withdrawal requests and processing
- **PaymentMethod** - Available payment options
- **PaymentConfig** - Payment provider configurations
- **SystemRate** - Commission and fee structures

### Membership & Privileges

- **Membership** - User subscription records
- **MembershipPlan** - Available membership tiers
- **MembershipBenefit** - Benefits associated with plans

### Support & Communication

- **Ticket** - Customer support ticket system
- **TicketMessage** - Ticket conversation messages
- **Whitelist** - User verification and access control

### System Configuration

- **SystemSetting** - Application configuration
- **EmailTemplate** - Dynamic email templates
- **AuditLog** - System activity logging

## Environment Variables

### Database

```env
DATABASE_URL="postgresql://..."  # PostgreSQL connection string
```

### Authentication (NextAuth v5)

```env
NEXTAUTH_SECRET="your-secret-key"          # JWT signing secret
NEXTAUTH_URL="http://localhost:3000"       # Application URL
AUTH_TRUST_HOST="true"                     # Trust host for production

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-secret"
```

### Email Service (Resend)

```env
RESEND_API_KEY="re_..."                     # Resend API key
FROM_EMAIL="<EMAIL>"        # Sender email address
```

### Payment Processing

```env
STRIPE_SECRET_KEY="sk_..."                 # Stripe secret key
STRIPE_PUBLISHABLE_KEY="pk_..."            # Stripe publishable key
STRIPE_WEBHOOK_SECRET="whsec_..."          # Stripe webhook secret

# Additional payment providers
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-secret"
```

### File Storage

```env
UPLOAD_MAX_SIZE="10485760"                 # Max upload size (10MB)
UPLOAD_ALLOWED_TYPES="image/*,application/pdf"  # Allowed file types
STORAGE_PATH="./public/uploads"            # Local storage path
```

### AI Services

```env
OPENAI_API_KEY="sk-..."                    # OpenAI API key for AI features
```

### External Services

```env
CRISP_WEBSITE_ID="your-crisp-id"           # Crisp chat widget ID
IP_GEOLOCATION_API_KEY="your-api-key"      # IP geolocation service
```

### Application Configuration

```env
NEXT_PUBLIC_APP_URL="http://localhost:3000" # Public app URL
NEXT_PUBLIC_DEFAULT_LOCALE="en"            # Default locale
ADMIN_EMAIL="<EMAIL>"         # Admin contact email
```

## Development Workflow

### 1. Setup Development Environment

```bash
# Clone and install dependencies
git clone <repository-url>
cd refundgo
npm install

# Setup database
npx prisma generate
npx prisma db push
npm run db:seed

# Start development server
npm run dev
```

### 2. Development Process

1. **Feature Branches**: Create feature branches from `main`
2. **Code Quality**: Use ESLint + Prettier for consistent formatting
3. **Type Safety**: Leverage TypeScript strict mode
4. **Testing**: Write tests for new features and bug fixes
5. **Commit Standards**: Use conventional commit messages
6. **Pre-commit Hooks**: Husky + lint-staged for quality gates

### 3. Code Quality Commands

```bash
npm run lint          # Check linting issues
npm run lint:fix      # Auto-fix linting issues
npm run format        # Format code with Prettier
npm run fix           # Run both lint:fix and format
npm test              # Run test suite
npm run test:coverage # Generate coverage report
```

### 4. Database Management

```bash
npx prisma studio     # Database GUI
npx prisma generate   # Generate Prisma client
npx prisma db push    # Push schema changes
npm run db:seed       # Seed database
npm run db:reset      # Reset and reseed database
```

## Deployment

### Production Build

```bash
npm run build         # Create optimized production build
npm run start         # Start production server
```

### Database Deployment

```bash
npx prisma migrate deploy  # Run pending migrations
npx prisma generate        # Generate client for production
```

### Environment Configuration

- Separate `.env` files for different environments
- Production environment variables in deployment platform
- Database migrations run automatically on deploy
- Static assets served via CDN for optimal performance

### Deployment Checklist

1. ✅ Environment variables configured
2. ✅ Database migrations applied
3. ✅ Build process successful
4. ✅ Tests passing
5. ✅ Security headers configured
6. ✅ Performance optimizations enabled
7. ✅ Monitoring and logging setup
