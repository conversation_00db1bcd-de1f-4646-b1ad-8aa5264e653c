@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入RTL支持样式 */
@import '../styles/rtl-support.css';

/* 导入RefundGo Logo样式 */
@import '../styles/refund-go-logo.css';

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* 无障碍性增强样式 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus,
.sr-only:active {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 键盘导航焦点样式 */
.keyboard-navigation *:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced theme variables for homepage */
    --hero-background: 220 14% 96%;
    --hero-gradient-from: 220 60% 97%;
    --hero-gradient-via: 0 0% 100%;
    --hero-gradient-to: 270 60% 97%;
    --nav-background: 0 0% 100%;
    --nav-background-scrolled: 0 0% 100%;
    --nav-border: 214.3 31.8% 91.4%;
    --nav-text: 215.4 16.3% 46.9%;
    --nav-text-hover: 221.2 83.2% 53.3%;
    --nav-hover-bg: 210 40% 96%;
    --text-primary: 222.2 84% 4.9%;
    --text-secondary: 215.4 16.3% 46.9%;
    --text-muted: 215.4 16.3% 65.1%;
    --gradient-primary-from: 221.2 83.2% 53.3%;
    --gradient-primary-to: 262.1 83.3% 57.8%;
    --success: 142.1 76.2% 36.3%;
    --warning: 47.9 95.8% 53.1%;
    --info: 199.89 89.47% 49.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced dark theme variables for homepage */
    --hero-background: 240 10% 3.9%;
    --hero-gradient-from: 240 10% 3.9%;
    --hero-gradient-via: 240 5% 6%;
    --hero-gradient-to: 240 10% 3.9%;
    --nav-background: 240 10% 3.9%;
    --nav-background-scrolled: 240 10% 3.9%;
    --nav-border: 217.2 32.6% 17.5%;
    --nav-text: 215 20.2% 65.1%;
    --nav-text-hover: 217.2 91.2% 59.8%;
    --nav-hover-bg: 217.2 32.6% 17.5%;
    --text-primary: 0 0% 98%;
    --text-secondary: 215 20.2% 65.1%;
    --text-muted: 215.4 16.3% 46.9%;
    --gradient-primary-from: 217.2 91.2% 59.8%;
    --gradient-primary-to: 262.1 83.3% 57.8%;
    --success: 142.1 70.6% 45.3%;
    --warning: 47.9 95.8% 53.1%;
    --info: 199.89 89.47% 49.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* 隐藏滚动条 */
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  /* 文本截断 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 自定义加载动画 */
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out;
  }

  .animate-fade-in-delay {
    animation: fade-in 0.6s ease-out 0.2s both;
  }

  /* 响应式标签页优化 */
  .responsive-tabs-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .responsive-tabs-mobile::-webkit-scrollbar {
    display: none;
  }

  .responsive-tab-trigger {
    transition: all 0.2s ease-in-out;
  }

  .responsive-tab-trigger:active {
    transform: scale(0.98);
  }

  /* 移动端触摸优化 */
  @media (max-width: 768px) {
    .responsive-tab-trigger {
      min-height: 44px;
      min-width: 44px;
    }
  }
}
