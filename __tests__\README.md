# Test Directory Structure

This directory contains all tests for the RefundGo web application, organized by test type and
feature area.

## Directory Structure

```
__tests__/
├── unit/                    # Unit tests for individual components/functions
│   ├── components/          # React component unit tests
│   ├── lib/                 # Library/utility unit tests
│   ├── hooks/               # Custom React hooks tests
│   ├── utils/               # Utility function tests
│   └── i18n/                # Basic internationalization unit tests
├── integration/             # Integration tests
│   ├── api/                 # API integration tests
│   ├── email/               # Email system integration tests
│   ├── i18n/                # Internationalization integration tests
│   ├── payments/            # Payment system integration tests
│   ├── tickets/             # Ticket system integration tests
│   └── ui/                  # UI integration tests
├── e2e/                     # End-to-end tests (Playwright, etc.)
│   └── dashboard/           # Dashboard E2E tests
├── validation/              # System validation and health check scripts
│   ├── system-checks/       # System integrity checks
│   └── translation-checks/  # Translation validation scripts
└── fixtures/                # Test data and fixtures
    ├── mock-data/           # Mock data for tests
    └── test-helpers/        # Test utility functions
```

## Naming Conventions

- **Unit & Integration Tests**: Use `.test.js` or `.test.ts` extension
- **End-to-End Tests**: Use `.spec.js` or `.spec.ts` extension (Playwright convention)
- **Validation Scripts**: Use descriptive names without "test-" prefix
- **File Names**: Use kebab-case (e.g., `user-authentication.test.js`)

## Test Types

### Unit Tests (`unit/`)

Test individual components, functions, or modules in isolation.

- Fast execution
- No external dependencies
- Mock all dependencies

### Integration Tests (`integration/`)

Test how different parts of the system work together.

- Test API endpoints
- Test component interactions
- Test data flow between modules

### End-to-End Tests (`e2e/`)

Test complete user workflows from start to finish.

- Use Playwright for browser automation
- Test real user scenarios
- Slower execution but high confidence

### Validation Scripts (`validation/`)

System health checks and validation utilities.

- File existence checks
- Translation completeness
- System integrity verification

## Running Tests

```bash
# Run all Jest tests (unit + integration)
npm test

# Run specific test types
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:validation    # System validation scripts
npm run test:e2e          # End-to-end tests (requires Playwright)

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## Writing Tests

### Unit Test Example

```javascript
// __tests__/unit/utils/format-currency.test.js
import { formatCurrency } from '@/lib/utils/format-currency';

describe('formatCurrency', () => {
  it('should format USD currency correctly', () => {
    expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56');
  });
});
```

### Integration Test Example

```javascript
// __tests__/integration/api/user-auth.test.js
import { POST } from '@/app/api/auth/login/route';

describe('/api/auth/login', () => {
  it('should authenticate valid user', async () => {
    const request = new Request('http://localhost:3000/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>', password: 'password' }),
    });

    const response = await POST(request);
    expect(response.status).toBe(200);
  });
});
```

### E2E Test Example

```javascript
// __tests__/e2e/dashboard/user-login.spec.js
import { test, expect } from '@playwright/test';

test('user can login and access dashboard', async ({ page }) => {
  await page.goto('/login');
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'password');
  await page.click('[data-testid="login-button"]');

  await expect(page).toHaveURL('/dashboard');
});
```

## Best Practices

1. **Test Organization**: Group related tests in the same directory
2. **Descriptive Names**: Use clear, descriptive test and file names
3. **Test Data**: Use fixtures for consistent test data
4. **Mocking**: Mock external dependencies in unit tests
5. **Cleanup**: Clean up after tests (database, files, etc.)
6. **Documentation**: Document complex test scenarios

## Configuration

Tests are configured in:

- `jest.config.ts` - Jest configuration
- `playwright.config.ts` - Playwright configuration (if exists)
- `jest.setup.ts` - Jest setup file

## Troubleshooting

### Common Issues

- **Import Errors**: Check path mappings in `jest.config.ts`
- **Mock Issues**: Ensure mocks are in `__mocks__/` directory
- **Timeout Errors**: Increase timeout in test configuration
- **Environment Issues**: Check test environment setup in `jest.setup.ts`

### Getting Help

- Check existing tests for examples
- Review Jest documentation: https://jestjs.io/
- Review Playwright documentation: https://playwright.dev/
- Ask team members for guidance on testing patterns
