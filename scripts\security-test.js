#!/usr/bin/env node

/**
 * Security Testing Script
 * Automated security tests for the RefundGo application
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n🔍 ${description}`, 'blue');
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} - PASSED`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} - FAILED`, 'red');
    log(error.message, 'red');
    return { success: false, error: error.message };
  }
}

function checkFileExists(filePath, description) {
  log(`\n📁 ${description}`, 'blue');
  if (fs.existsSync(filePath)) {
    log(`✅ ${description} - EXISTS`, 'green');
    return true;
  } else {
    log(`❌ ${description} - MISSING`, 'red');
    return false;
  }
}

function checkFileContent(filePath, pattern, description) {
  log(`\n📄 ${description}`, 'blue');
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    if (pattern.test(content)) {
      log(`✅ ${description} - FOUND`, 'green');
      return true;
    } else {
      log(`❌ ${description} - NOT FOUND`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ ${description} - ERROR: ${error.message}`, 'red');
    return false;
  }
}

async function runSecurityTests() {
  log('🔒 Starting Security Tests for RefundGo', 'cyan');
  log('=' * 50, 'cyan');

  const results = {
    passed: 0,
    failed: 0,
    tests: [],
  };

  // 1. Authentication Tests
  log('\n🔐 AUTHENTICATION TESTS', 'magenta');

  const authTests = [
    {
      name: 'Authentication middleware exists',
      test: () =>
        checkFileExists('src/lib/auth.ts', 'Authentication middleware file'),
    },
    {
      name: 'RBAC system exists',
      test: () => checkFileExists('src/lib/rbac.ts', 'RBAC system file'),
    },
    {
      name: 'Page permissions exist',
      test: () =>
        checkFileExists('src/lib/page-permissions.ts', 'Page permissions file'),
    },
    {
      name: 'User layout has auth protection',
      test: () =>
        checkFileContent(
          'src/app/[locale]/(user)/layout.tsx',
          /requireAuth/,
          'User layout authentication check'
        ),
    },
  ];

  for (const test of authTests) {
    const result = test.test();
    results.tests.push({ name: test.name, passed: result });
    if (result) results.passed++;
    else results.failed++;
  }

  // 2. Session Management Tests
  log('\n⏰ SESSION MANAGEMENT TESTS', 'magenta');

  const sessionTests = [
    {
      name: 'Session management system exists',
      test: () =>
        checkFileExists(
          'src/lib/session-management.ts',
          'Session management file'
        ),
    },
    {
      name: 'Session monitor component exists',
      test: () =>
        checkFileExists(
          'src/components/session-monitor.tsx',
          'Session monitor component'
        ),
    },
    {
      name: 'Session API routes exist',
      test: () =>
        checkFileExists(
          'src/app/api/auth/session-status/route.ts',
          'Session status API'
        ),
    },
  ];

  for (const test of sessionTests) {
    const result = test.test();
    results.tests.push({ name: test.name, passed: result });
    if (result) results.passed++;
    else results.failed++;
  }

  // 3. CSRF Protection Tests
  log('\n🛡️ CSRF PROTECTION TESTS', 'magenta');

  const csrfTests = [
    {
      name: 'CSRF protection system exists',
      test: () => checkFileExists('src/lib/csrf.ts', 'CSRF protection file'),
    },
    {
      name: 'CSRF hooks exist',
      test: () => checkFileExists('src/hooks/use-csrf.ts', 'CSRF hooks file'),
    },
    {
      name: 'CSRF form component exists',
      test: () =>
        checkFileExists('src/components/csrf-form.tsx', 'CSRF form component'),
    },
    {
      name: 'CSRF token API exists',
      test: () =>
        checkFileExists(
          'src/app/api/auth/csrf-token/route.ts',
          'CSRF token API'
        ),
    },
  ];

  for (const test of csrfTests) {
    const result = test.test();
    results.tests.push({ name: test.name, passed: result });
    if (result) results.passed++;
    else results.failed++;
  }

  // 4. Middleware Security Tests
  log('\n🚧 MIDDLEWARE SECURITY TESTS', 'magenta');

  const middlewareTests = [
    {
      name: 'Middleware file exists',
      test: () => checkFileExists('src/middleware.ts', 'Middleware file'),
    },
    {
      name: 'Protected routes are configured',
      test: () =>
        checkFileContent(
          'src/middleware.ts',
          /protectedPaths/,
          'Protected paths configuration'
        ),
    },
    {
      name: 'Admin routes are protected',
      test: () =>
        checkFileContent(
          'src/middleware.ts',
          /adminOnlyPaths/,
          'Admin-only paths configuration'
        ),
    },
  ];

  for (const test of middlewareTests) {
    const result = test.test();
    results.tests.push({ name: test.name, passed: result });
    if (result) results.passed++;
    else results.failed++;
  }

  // 5. Environment Security Tests
  log('\n🌍 ENVIRONMENT SECURITY TESTS', 'magenta');

  const envTests = [
    {
      name: 'Environment example exists',
      test: () => checkFileExists('.env.example', 'Environment example file'),
    },
    {
      name: 'NextAuth secret is configured',
      test: () =>
        checkFileContent(
          '.env.example',
          /NEXTAUTH_SECRET/,
          'NextAuth secret configuration'
        ),
    },
  ];

  for (const test of envTests) {
    const result = test.test();
    results.tests.push({ name: test.name, passed: result });
    if (result) results.passed++;
    else results.failed++;
  }

  // 6. Unit Tests
  log('\n🧪 SECURITY UNIT TESTS', 'magenta');

  const unitTestResult = runCommand(
    'npm test -- --testPathPattern=security',
    'Running security unit tests'
  );

  results.tests.push({
    name: 'Security unit tests',
    passed: unitTestResult.success,
  });
  if (unitTestResult.success) results.passed++;
  else results.failed++;

  // 7. TypeScript Compilation
  log('\n📝 TYPESCRIPT COMPILATION TESTS', 'magenta');

  const tscResult = runCommand(
    'npx tsc --noEmit',
    'TypeScript compilation check'
  );

  results.tests.push({
    name: 'TypeScript compilation',
    passed: tscResult.success,
  });
  if (tscResult.success) results.passed++;
  else results.failed++;

  // 8. Linting Security Rules
  log('\n🔍 LINTING SECURITY RULES', 'magenta');

  const lintResult = runCommand(
    'npx eslint src --ext .ts,.tsx --quiet',
    'ESLint security rules check'
  );

  results.tests.push({
    name: 'ESLint security rules',
    passed: lintResult.success,
  });
  if (lintResult.success) results.passed++;
  else results.failed++;

  // Results Summary
  log('\n📊 SECURITY TEST RESULTS', 'cyan');
  log('=' * 50, 'cyan');
  log(`Total Tests: ${results.passed + results.failed}`, 'blue');
  log(`Passed: ${results.passed}`, 'green');
  log(`Failed: ${results.failed}`, results.failed > 0 ? 'red' : 'green');
  log(
    `Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`,
    results.failed > 0 ? 'yellow' : 'green'
  );

  if (results.failed > 0) {
    log('\n❌ FAILED TESTS:', 'red');
    results.tests
      .filter(test => !test.passed)
      .forEach(test => log(`  - ${test.name}`, 'red'));
  }

  // Security Recommendations
  log('\n💡 SECURITY RECOMMENDATIONS:', 'yellow');
  log('  1. Regularly update dependencies', 'yellow');
  log('  2. Monitor for security vulnerabilities', 'yellow');
  log('  3. Implement rate limiting', 'yellow');
  log('  4. Use HTTPS in production', 'yellow');
  log('  5. Regular security audits', 'yellow');
  log('  6. Implement proper logging', 'yellow');
  log('  7. Use security headers', 'yellow');

  return results.failed === 0;
}

// Run the security tests
if (require.main === module) {
  runSecurityTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`\n💥 Security test runner failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { runSecurityTests };
