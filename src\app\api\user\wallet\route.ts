import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问，请先登录',
    userNotFound: '用户不存在',
    serverError: '服务器内部错误',
  },
  en: {
    unauthorized: 'Unauthorized access, please login first',
    userNotFound: 'User not found',
    serverError: 'Internal server error',
  },
};

// 强制动态渲染
export const dynamic = 'force-dynamic';

// GET /api/user/wallet - 获取用户钱包信息
export async function GET(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: t.unauthorized },
        { status: 401 },
      );
    }

    // 2. 获取用户钱包信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        balance: true,
        frozenAmount: true,
        totalIncome: true,
        totalExpense: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: t.userNotFound },
        { status: 404 },
      );
    }

    // 3. 构造钱包数据
    const walletData = {
      id: `wallet-${user.id}`,
      userId: user.id,
      balance: user.balance,
      frozenAmount: user.frozenAmount,
      totalIncome: user.totalIncome,
      totalExpense: user.totalExpense,
      updatedAt: user.updatedAt,
    };

    return NextResponse.json({
      success: true,
      data: walletData,
    });
  } catch (error) {
    console.error('API错误:', error);
    // 在错误情况下使用默认语言
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}
