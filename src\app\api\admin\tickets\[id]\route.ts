import { TicketStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import {
  updateTicketStatusSchema,
  assignTicketSchema,
} from '@/lib/types/ticket';

// 验证管理员权限的辅助函数
async function requireAdmin() {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('请先登录');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (user?.role !== 'ADMIN') {
    throw new Error('权限不足');
  }

  return session;
}

// 获取工单详情（管理员端）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    await requireAdmin();

    const { id: ticketId } = await params;

    // 查找工单
    const ticket = await prisma.ticket.findUnique({
      where: { id: ticketId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        replies: {
          orderBy: {
            createdAt: 'asc',
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            replies: true,
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: '工单不存在' }, { status: 404 });
    }

    return NextResponse.json(ticket);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 更新工单状态或分配工单（管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    const session = await requireAdmin();

    const { id: ticketId } = await params;
    const body = await request.json();

    // 检查工单是否存在
    const existingTicket = await prisma.ticket.findUnique({
      where: { id: ticketId },
    });

    if (!existingTicket) {
      return NextResponse.json({ error: '工单不存在' }, { status: 404 });
    }

    // 判断是更新状态还是分配操作
    if (body.action === 'assign') {
      // 分配工单
      const validation = assignTicketSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            error: '请求数据格式错误',
            details: validation.error.errors,
          },
          { status: 400 },
        );
      }

      const { assignedTo } = validation.data;

      // 如果分配给某人，验证该用户是否为管理员
      if (assignedTo) {
        const assignee = await prisma.user.findUnique({
          where: { id: assignedTo },
          select: { role: true },
        });

        if (!assignee || assignee.role !== 'ADMIN') {
          return NextResponse.json(
            { error: '只能分配给管理员用户' },
            { status: 400 },
          );
        }
      }

      // 更新工单分配
      const updatedTicket = await prisma.ticket.update({
        where: { id: ticketId },
        data: {
          assignedTo,
          updatedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return NextResponse.json({
        message: assignedTo ? '工单已分配' : '工单分配已取消',
        ticket: updatedTicket,
      });
    } else {
      // 更新工单状态
      const validation = updateTicketStatusSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            error: '请求数据格式错误',
            details: validation.error.errors,
          },
          { status: 400 },
        );
      }

      const { status } = validation.data;

      // 准备更新数据
      const updateData: any = {
        status,
        updatedAt: new Date(),
      };

      // 设置相关时间戳
      if (status === TicketStatus.RESOLVED) {
        updateData.resolvedAt = new Date();
      } else if (status === TicketStatus.CLOSED) {
        updateData.closedAt = new Date();
        // 如果还没有解决时间，设置解决时间
        if (!existingTicket.resolvedAt) {
          updateData.resolvedAt = new Date();
        }
      }

      // 更新工单状态
      const updatedTicket = await prisma.ticket.update({
        where: { id: ticketId },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return NextResponse.json({
        message: '工单状态已更新',
        ticket: updatedTicket,
      });
    }
  } catch (error) {
    console.error('更新工单失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' },
      { status: 500 },
    );
  }
}
