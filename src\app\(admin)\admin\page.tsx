import { AdminDashboardContent } from '@/components/admin/admin-dashboard-content';
import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { auth } from '@/lib/auth';
import { constructMetadata } from '@/lib/utils';

export const metadata = constructMetadata({
  title: '管理后台',
  description: '管理系统仪表盘 - 查看平台运营数据和用户统计信息',
});

export default async function AdminDashboard() {
  const session = await auth();

  return (
    <AdminPageLayout
      title='管理后台'
      breadcrumbPage='仪表盘'
      href='/admin'
      showBackButton={true}
    >
      <div className='space-y-6'>
        {/* 欢迎区域 */}
        <div className='flex flex-col space-y-1'>
          <h1 className='text-2xl font-bold tracking-tight'>
            欢迎回来，{session?.user?.name}！
          </h1>
          <p className='text-sm text-muted-foreground'>
            这是您的管理员控制面板，查看平台运营数据和统计信息。
          </p>
        </div>

        {/* 使用真实数据的仪表盘内容 */}
        <AdminDashboardContent />
      </div>
    </AdminPageLayout>
  );
}
