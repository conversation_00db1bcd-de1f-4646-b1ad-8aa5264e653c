'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Upload,
  FileImage,
  X,
  CheckCircle,
  AlertTriangle,
  Video,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { FileUpload } from '@/components/ui/file-upload';
import { Label } from '@/components/ui/label';
import { UploadedFile } from '@/hooks/use-file-upload';

const createResubmitEvidenceSchema = (t: any) =>
  z.object({
    evidenceFiles: z
      .array(z.any())
      .min(1, t('dialog.resubmitEvidence.validation.filesRequired')),
  });

type ResubmitEvidenceForm = z.infer<
  ReturnType<typeof createResubmitEvidenceSchema>
>;

interface ResubmitEvidenceDialogProps {
  children: React.ReactNode;
  taskId: string;
  rejectionReason?: string;
  onSubmit?: (data: { taskId: string; files: UploadedFile[] }) => void;
}

export function ResubmitEvidenceDialog({
  children,
  taskId,
  rejectionReason,
  onSubmit,
}: ResubmitEvidenceDialogProps) {
  const t = useTranslations('MyPublishedTasks');
  const [isOpen, setIsOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const resubmitEvidenceSchema = createResubmitEvidenceSchema(t);

  const {
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ResubmitEvidenceForm>({
    resolver: zodResolver(resubmitEvidenceSchema),
  });

  const handleFileChange = (files: UploadedFile[]) => {
    setUploadedFiles(files);
    setValue('evidenceFiles', files);
  };

  const onFormSubmit = async (data: ResubmitEvidenceForm) => {
    setIsSubmitting(true);

    try {
      // 调用父组件的提交处理函数，传递已上传的文件信息
      onSubmit?.({ taskId, files: uploadedFiles });

      // 重置表单和状态
      reset();
      setUploadedFiles([]);
      setIsOpen(false);

      toast.success(t('messages.resubmitEvidenceSuccess'), {
        description: t('messages.resubmitEvidenceSuccessDesc'),
      });
    } catch (error) {
      toast.error(t('messages.resubmitEvidenceError'), {
        description: t('messages.submitEvidenceErrorDesc'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    reset();
    setUploadedFiles([]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-[600px]'>
        <DialogHeader>
          <DialogTitle>{t('dialog.resubmitEvidence.title')}</DialogTitle>
          <DialogDescription>
            {t('dialog.resubmitEvidence.description')}
          </DialogDescription>
        </DialogHeader>

        {rejectionReason && (
          <Alert>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>
              <strong>{t('dialog.resubmitEvidence.rejectionReason')}</strong>
              {rejectionReason}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-6'>
          <div className='space-y-4'>
            <Label>{t('dialog.resubmitEvidence.evidenceFiles')}</Label>
            <FileUpload
              onChange={handleFileChange}
              accept='image/*,video/*'
              maxFiles={10}
              maxSize={50 * 1024 * 1024} // 50MB
            />
            <p className='text-sm text-muted-foreground mt-2'>
              {t('dialog.resubmitEvidence.uploadHint')}
            </p>
            {errors.evidenceFiles && (
              <p className='text-sm text-red-500'>
                {errors.evidenceFiles.message}
              </p>
            )}
          </div>

          <div className='flex justify-end space-x-2'>
            <Button type='button' variant='outline' onClick={handleClose}>
              {t('dialog.resubmitEvidence.cancel')}
            </Button>
            <Button
              type='submit'
              disabled={isSubmitting || uploadedFiles.length === 0}
            >
              {isSubmitting
                ? t('dialog.resubmitEvidence.submitting')
                : t('dialog.resubmitEvidence.submit')}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
