'use client';

import {
  Keyboard,
  <PERSON>Up,
  <PERSON>Down,
  <PERSON><PERSON>ef<PERSON>,
  ArrowR<PERSON>,
  Command,
} from 'lucide-react';
import React, { ReactNode, useEffect } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  useKeyboardNavigation,
  useShortcuts,
  useFocusTrap,
  useSkipLinks,
  type ShortcutConfig,
} from '@/hooks/use-keyboard-navigation';
import { cn } from '@/lib/utils';

// 键盘导航容器组件
interface KeyboardNavigationContainerProps {
  children: ReactNode;
  shortcuts?: ShortcutConfig[];
  autoFocus?: boolean;
  trapFocus?: boolean;
  className?: string;
}

export function KeyboardNavigationContainer({
  children,
  shortcuts = [],
  autoFocus = false,
  trapFocus = false,
  className,
}: KeyboardNavigationContainerProps) {
  const { containerRef } = useKeyboardNavigation<HTMLDivElement>(shortcuts, {
    autoFocus,
    trapFocus,
    restoreFocus: true,
  });

  return (
    <div
      ref={containerRef}
      className={cn('outline-none', className)}
      tabIndex={-1}
      role='region'
      aria-label='键盘导航区域'
    >
      {children}
    </div>
  );
}

// 焦点陷阱组件
interface FocusTrapProps {
  children: ReactNode;
  isActive?: boolean;
  className?: string;
}

export function FocusTrap({
  children,
  isActive = true,
  className,
}: FocusTrapProps) {
  const containerRef = useFocusTrap(isActive);

  if (isActive) {
    return (
      <div
        ref={containerRef}
        className={cn('outline-none', className)}
        role='dialog'
        aria-modal='true'
      >
        {children}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn('outline-none', className)}
      role='dialog'
      aria-modal='false'
    >
      {children}
    </div>
  );
}

// 跳转链接组件
interface SkipLinksProps {
  links: Array<{
    id: string;
    label: string;
    target: string;
  }>;
  className?: string;
}

export function SkipLinks({ links, className }: SkipLinksProps) {
  const { handleSkipToContent } = useSkipLinks();

  if (links.length === 0) return null;

  return (
    <div className={cn('sr-only focus-within:not-sr-only', className)}>
      <div className='fixed top-0 left-0 z-50 p-2 bg-background border rounded-br-lg shadow-lg'>
        <div className='flex flex-col gap-1'>
          {links.map(link => (
            <Button
              key={link.id}
              variant='outline'
              size='sm'
              onClick={() => handleSkipToContent(link.target)}
              className='text-left justify-start'
            >
              {link.label}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}

// 快捷键帮助组件
interface ShortcutHelpProps {
  shortcuts: ShortcutConfig[];
  trigger?: ReactNode;
  className?: string;
}

export function ShortcutHelp({
  shortcuts,
  trigger,
  className,
}: ShortcutHelpProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const formatShortcut = (shortcut: ShortcutConfig) => {
    const keys = [];
    if (shortcut.ctrl) keys.push('Ctrl');
    if (shortcut.alt) keys.push('Alt');
    if (shortcut.shift) keys.push('Shift');
    if (shortcut.meta) keys.push('Cmd');
    keys.push(shortcut.key.toUpperCase());
    return keys.join(' + ');
  };

  useShortcuts([
    {
      key: '?',
      handler: () => setIsOpen(!isOpen),
      description: '显示/隐藏快捷键帮助',
    },
    {
      key: 'Escape',
      handler: () => setIsOpen(false),
      description: '关闭快捷键帮助',
    },
  ]);

  return (
    <>
      {trigger && <div onClick={() => setIsOpen(!isOpen)}>{trigger}</div>}

      {isOpen && (
        <div className='fixed inset-0 z-50 flex items-center justify-center'>
          <div
            className='absolute inset-0 bg-black/50'
            onClick={() => setIsOpen(false)}
          />
          <FocusTrap isActive={isOpen}>
            <div
              className={cn(
                'relative bg-background border rounded-lg shadow-lg max-w-md w-full mx-4 p-6',
                className,
              )}
            >
              <div className='flex items-center gap-2 mb-4'>
                <Keyboard className='h-5 w-5' />
                <h2 className='text-lg font-semibold'>键盘快捷键</h2>
              </div>

              <div className='space-y-3 max-h-96 overflow-y-auto'>
                {shortcuts.map((shortcut, index) => (
                  <div
                    key={index}
                    className='flex items-center justify-between'
                  >
                    <span className='text-sm'>
                      {shortcut.description || '未知操作'}
                    </span>
                    <Badge variant='outline' className='font-mono text-xs'>
                      {formatShortcut(shortcut)}
                    </Badge>
                  </div>
                ))}

                {/* 默认快捷键 */}
                <div className='border-t pt-3 mt-3'>
                  <h3 className='text-sm font-medium mb-2'>通用快捷键</h3>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>显示快捷键帮助</span>
                      <Badge variant='outline' className='font-mono text-xs'>
                        ?
                      </Badge>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>关闭对话框</span>
                      <Badge variant='outline' className='font-mono text-xs'>
                        Esc
                      </Badge>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>切换焦点</span>
                      <Badge variant='outline' className='font-mono text-xs'>
                        Tab
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex justify-end mt-4'>
                <Button onClick={() => setIsOpen(false)}>关闭</Button>
              </div>
            </div>
          </FocusTrap>
        </div>
      )}
    </>
  );
}

// 方向键导航指示器
interface ArrowNavigationIndicatorProps {
  direction: 'horizontal' | 'vertical' | 'grid';
  className?: string;
}

export function ArrowNavigationIndicator({
  direction,
  className,
}: ArrowNavigationIndicatorProps) {
  const getArrows = () => {
    switch (direction) {
      case 'horizontal':
        return [
          { icon: ArrowLeft, label: '上一个' },
          { icon: ArrowRight, label: '下一个' },
        ];
      case 'vertical':
        return [
          { icon: ArrowUp, label: '上一个' },
          { icon: ArrowDown, label: '下一个' },
        ];
      case 'grid':
        return [
          { icon: ArrowUp, label: '向上' },
          { icon: ArrowDown, label: '向下' },
          { icon: ArrowLeft, label: '向左' },
          { icon: ArrowRight, label: '向右' },
        ];
      default:
        return [];
    }
  };

  const arrows = getArrows();

  return (
    <div
      className={cn(
        'flex items-center gap-1 text-xs text-muted-foreground',
        className,
      )}
    >
      <span>使用</span>
      {arrows.map((arrow, index) => (
        <React.Fragment key={index}>
          {index > 0 && <span>/</span>}
          <div className='flex items-center gap-1'>
            <arrow.icon className='h-3 w-3' />
            <span className='sr-only'>{arrow.label}</span>
          </div>
        </React.Fragment>
      ))}
      <span>导航</span>
    </div>
  );
}

// 焦点指示器组件
interface FocusIndicatorProps {
  children: ReactNode;
  showIndicator?: boolean;
  className?: string;
}

export function FocusIndicator({
  children,
  showIndicator = true,
  className,
}: FocusIndicatorProps) {
  return (
    <div
      className={cn(
        'relative',
        showIndicator &&
          'focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2',
        className,
      )}
    >
      {children}
    </div>
  );
}

// 键盘导航菜单组件
interface KeyboardMenuProps {
  children: ReactNode;
  orientation?: 'horizontal' | 'vertical';
  className?: string;
}

export function KeyboardMenu({
  children,
  orientation = 'vertical',
  className,
}: KeyboardMenuProps) {
  const { containerRef } = useKeyboardNavigation<HTMLDivElement>([], {
    autoFocus: false,
    trapFocus: false,
  });

  return (
    <div
      ref={containerRef}
      role='group'
      aria-label={`${orientation} navigation menu`}
      className={cn('outline-none', className)}
      tabIndex={-1}
    >
      {children}
    </div>
  );
}

// 键盘导航菜单项组件
interface KeyboardMenuItemProps {
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}

export function KeyboardMenuItem({
  children,
  onClick,
  disabled = false,
  className,
}: KeyboardMenuItemProps) {
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick?.();
    }
  };

  if (disabled) {
    return (
      <div
        role='button'
        tabIndex={-1}
        aria-disabled='true'
        className={cn(
          'px-3 py-2 cursor-pointer outline-none',
          'focus:bg-accent focus:text-accent-foreground',
          'hover:bg-accent hover:text-accent-foreground',
          'opacity-50 cursor-not-allowed',
          className,
        )}
      >
        {children}
      </div>
    );
  }

  return (
    <div
      role='button'
      tabIndex={0}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      aria-disabled='false'
      className={cn(
        'px-3 py-2 cursor-pointer outline-none',
        'focus:bg-accent focus:text-accent-foreground',
        'hover:bg-accent hover:text-accent-foreground',
        className,
      )}
    >
      {children}
    </div>
  );
}

// 全局快捷键提供者
interface GlobalShortcutsProviderProps {
  children: ReactNode;
  shortcuts: ShortcutConfig[];
}

export function GlobalShortcutsProvider({
  children,
  shortcuts,
}: GlobalShortcutsProviderProps) {
  useShortcuts(shortcuts);
  return <>{children}</>;
}

// 键盘导航状态显示组件 (开发用)
export function KeyboardNavigationDebug() {
  const [focusedElement, setFocusedElement] = React.useState<string>('');

  useEffect(() => {
    const handleFocusChange = () => {
      const element = document.activeElement;
      if (element) {
        const tag = element.tagName.toLowerCase();
        const id = element.id ? `#${element.id}` : '';
        const classes = element.className
          ? `.${element.className.split(' ').join('.')}`
          : '';
        setFocusedElement(`${tag}${id}${classes}`);
      }
    };

    document.addEventListener('focusin', handleFocusChange);
    return () => document.removeEventListener('focusin', handleFocusChange);
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className='fixed top-4 right-4 p-2 bg-background border rounded text-xs z-50'>
      <div className='flex items-center gap-2'>
        <Keyboard className='h-3 w-3' />
        <span>焦点:</span>
      </div>
      <div className='font-mono text-xs mt-1 max-w-48 truncate'>
        {focusedElement || '无'}
      </div>
    </div>
  );
}

export default KeyboardNavigationContainer;
