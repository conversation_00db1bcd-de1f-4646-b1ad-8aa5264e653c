'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { CheckCircle, XCircle, Eye, Package2, Truck, Copy } from 'lucide-react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

const reviewSchema = z
  .object({
    approved: z.boolean(),
    rejectReason: z.string().optional(),
  })
  .refine(
    data => {
      // 如果被拒绝，必须提供拒绝原因
      if (!data.approved && !data.rejectReason?.trim()) {
        return false;
      }
      return true;
    },
    {
      message: '审核不通过时必须提供拒绝原因',
      path: ['rejectReason'],
    },
  );

type ReviewForm = z.infer<typeof reviewSchema>;

interface ReviewOrderDialogProps {
  children?: React.ReactNode;
  taskId: string;
  orderNumber: string;
  orderScreenshot: string;
  trackingNumber: string;
  logisticsScreenshots: string[];
  onReview?: (approved: boolean) => void;
}

export function ReviewOrderDialog({
  children,
  taskId,
  orderNumber,
  orderScreenshot,
  trackingNumber,
  logisticsScreenshots,
  onReview,
}: ReviewOrderDialogProps) {
  const t = useTranslations('logistics-info');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [reviewState, setReviewState] = useState<
    'pending' | 'approve' | 'reject'
  >('pending');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ReviewForm>({
    resolver: zodResolver(reviewSchema),
  });

  const onApprove = () => {
    setReviewState('approve');
    setValue('approved', true);
    setValue('rejectReason', '');
  };

  const onReject = () => {
    setReviewState('reject');
    setValue('approved', false);
  };

  const onFormSubmit = async (data: ReviewForm) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/publisher/tasks/${taskId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approved: data.approved,
          rejectReason: data.approved ? undefined : data.rejectReason,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || '审核失败');
      }

      toast.success(data.approved ? '审核通过！' : '审核不通过', {
        description: result.message,
        duration: 6000,
      });

      // 调用父组件的审核处理函数
      onReview?.(data.approved);

      // 重置表单和状态
      reset();
      setReviewState('pending');
      setIsOpen(false);

      // 刷新页面或重新获取数据
      window.location.reload();
    } catch (error) {
      toast.error('审核失败', {
        description:
          error instanceof Error ? error.message : '网络错误，请稍后重试',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyOrderNumber = () => {
    if (orderNumber) {
      navigator.clipboard.writeText(orderNumber);
      toast.success('订单号已复制');
    }
  };

  const copyTrackingNumber = () => {
    if (trackingNumber) {
      navigator.clipboard.writeText(trackingNumber);
      toast.success('物流单号已复制');
    }
  };

  const openImage = (imageUrl: string) => {
    window.open(imageUrl, '_blank');
  };

  const handleDialogClose = (open: boolean) => {
    setIsOpen(open);
    if (!open && !isLoading) {
      reset();
      setReviewState('pending');
    }
  };

  const goBack = () => {
    setReviewState('pending');
    reset();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='w-full max-w-lg mx-4 sm:mx-auto max-h-[85vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Eye className='h-5 w-5' />
            审核订单信息
          </DialogTitle>
          <DialogDescription>审核接单者提交的订单和物流信息</DialogDescription>
        </DialogHeader>

        {reviewState === 'pending' && (
          <div className='space-y-4'>
            {/* 订单信息 */}
            <div className='space-y-2'>
              <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                <Package2 className='h-4 w-4' />
                <span>订单号</span>
              </div>
              <div className='flex items-center gap-2'>
                <div className='flex-1 p-2 bg-muted rounded text-sm font-mono'>
                  {orderNumber || '暂无订单号'}
                </div>
                {orderNumber && (
                  <Button size='sm' variant='outline' onClick={copyOrderNumber}>
                    <Copy className='h-4 w-4' />
                  </Button>
                )}
              </div>
            </div>

            {/* 订单截图 */}
            {orderScreenshot && (
              <div className='space-y-2'>
                <div className='text-sm text-muted-foreground'>
                  {t('orderScreenshot')}
                </div>
                <div
                  className='w-20 h-20 cursor-pointer hover:opacity-80 transition-opacity'
                  onClick={() => openImage(orderScreenshot)}
                >
                  <Image
                    src={orderScreenshot}
                    alt={t('orderScreenshot')}
                    width={80}
                    height={80}
                    className='w-full h-full rounded border object-cover'
                  />
                </div>
              </div>
            )}

            {/* 物流信息 */}
            {trackingNumber && (
              <div className='space-y-2'>
                <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                  <Truck className='h-4 w-4' />
                  <span>{t('trackingNumber')}</span>
                </div>
                <div className='flex items-center gap-2'>
                  <div className='flex-1 p-2 bg-muted rounded text-sm font-mono'>
                    {trackingNumber}
                  </div>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={copyTrackingNumber}
                  >
                    <Copy className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            )}

            {/* 物流截图 */}
            {logisticsScreenshots.length > 0 && (
              <div className='space-y-2'>
                <div className='text-sm text-muted-foreground'>
                  {t('logisticsScreenshots', {
                    count: logisticsScreenshots.length,
                  })}
                </div>
                <div className='flex flex-wrap gap-2'>
                  {logisticsScreenshots.map((screenshot, index) => (
                    <div
                      key={index}
                      className='w-20 h-20 cursor-pointer hover:opacity-80 transition-opacity'
                      onClick={() => openImage(screenshot)}
                    >
                      <Image
                        src={screenshot}
                        alt={`${t('logisticsScreenshots')} ${index + 1}`}
                        width={80}
                        height={80}
                        className='w-full h-full rounded border object-cover'
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 物流信息详情 */}
            {trackingNumber && (
              <div className='space-y-3 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800'>
                <div className='flex items-center gap-2 text-sm font-medium text-blue-700 dark:text-blue-300'>
                  <Truck className='h-4 w-4' />
                  <span>{t('title')}</span>
                </div>

                <div className='text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 p-2 rounded'>
                  💡
                  {t('logisticsDataTip')}
                </div>

                {/* 基础物流状态显示 */}
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('trackingNumber')}
                    </span>
                    <div className='font-medium font-mono text-xs'>
                      {trackingNumber}
                    </div>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('status.title')}
                    </span>
                    <div className='font-medium text-blue-600 dark:text-blue-400'>
                      {t('waitingRegistration')}
                    </div>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('carrier')}
                    </span>
                    <div className='font-medium'>{t('autoDetect')}</div>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('currentStatus')}
                    </span>
                    <div className='font-medium'>{t('pending')}</div>
                  </div>
                </div>

                {/* 功能说明 */}
                <div className='space-y-2'>
                  <div className='text-xs font-medium text-blue-700 dark:text-blue-300'>
                    {t('afterApprovalProvide')}:
                  </div>
                  <div className='grid grid-cols-2 gap-2 text-xs text-blue-600 dark:text-blue-400'>
                    <div>• {t('realTimeStatus')}</div>
                    <div>• {t('estimatedDelivery')}</div>
                    <div>• {t('fullTrackingHistory')}</div>
                    <div>• {t('autoStatusUpdate')}</div>
                  </div>
                </div>
              </div>
            )}

            {/* 审核按钮 */}
            <div className='flex gap-3 pt-2'>
              <Button
                onClick={onApprove}
                className='flex-1 bg-green-600 hover:bg-green-700'
                disabled={isLoading}
              >
                <CheckCircle className='h-4 w-4 mr-2' />
                {t('approve')}
              </Button>
              <Button
                onClick={onReject}
                variant='destructive'
                className='flex-1'
                disabled={isLoading}
              >
                <XCircle className='h-4 w-4 mr-2' />
                {t('reject')}
              </Button>
            </div>
          </div>
        )}

        {/* 审核通过确认 */}
        {reviewState === 'approve' && (
          <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-4'>
            <div className='text-center p-4 bg-green-50 rounded border border-green-200'>
              <CheckCircle className='h-8 w-8 text-green-600 mx-auto mb-2' />
              <h3 className='font-semibold text-green-800'>
                {t('confirmApproval')}
              </h3>
              <p className='text-sm text-green-700 mt-1'>
                {t('taskWillEnterDeliveryStage')}
              </p>
            </div>

            <div className='flex gap-3'>
              <Button
                type='button'
                variant='outline'
                onClick={goBack}
                className='flex-1'
              >
                {t('back')}
              </Button>
              <Button
                type='submit'
                className='flex-1 bg-green-600 hover:bg-green-700'
                disabled={isLoading}
              >
                {isLoading ? t('processing') : t('confirmApprove')}
              </Button>
            </div>
          </form>
        )}

        {/* 审核驳回原因 */}
        {reviewState === 'reject' && (
          <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-4'>
            <div className='p-4 bg-red-50 rounded border border-red-200'>
              <div className='flex items-center gap-2 mb-2'>
                <XCircle className='h-5 w-5 text-red-600' />
                <h3 className='font-semibold text-red-800'>
                  {t('rejectReason')}
                </h3>
              </div>

              <div className='space-y-2'>
                <Textarea
                  placeholder={t('rejectReasonPlaceholder')}
                  className='min-h-[80px]'
                  {...register('rejectReason')}
                />
                {errors.rejectReason && (
                  <p className='text-sm text-red-600'>
                    {errors.rejectReason.message}
                  </p>
                )}
              </div>
            </div>

            <div className='flex gap-3'>
              <Button
                type='button'
                variant='outline'
                onClick={goBack}
                className='flex-1'
              >
                返回
              </Button>
              <Button
                type='submit'
                variant='destructive'
                className='flex-1'
                disabled={isLoading}
              >
                {isLoading ? t('processing') : t('confirmReject')}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
