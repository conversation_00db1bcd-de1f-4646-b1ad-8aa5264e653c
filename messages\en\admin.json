{"evidence": {"uploadType": "Upload Type", "uploadTypes": {"immediate": "Upload Immediately", "delayed": "Delayed Upload", "later": "Upload Later", "none": "No Upload", "unknown": "Unknown"}, "status": {"pendingSubmission": "Pending Upload", "underReview": "Under Review", "reviewed": "Reviewed", "rejected": "Rejected", "noEvidence": "No Evidence", "unknown": "Unknown"}, "viewEvidence": "View Evidence", "viewEvidenceDescription": "View chargeback evidence information and uploaded files for the task", "basicInfo": "Basic Information", "taskId": "Task ID", "platform": "Platform", "productUrl": "Product URL", "evidenceStatus": "Evidence Status", "rejectReason": "Rejection Reason", "uploadTime": "Upload Time", "chargebackTypes": "Chargeback Types", "evidenceFiles": "Evidence Files", "filesCount": "files", "view": "View", "noEvidence": "This task has no evidence", "noEvidenceFiles": "No evidence files", "noEvidenceForTask": "This task has no evidence", "publisherNotUploaded": "Publisher has not uploaded evidence files yet", "evidenceRequirements": "Evidence Requirements", "uploadRequirements": "Upload Requirements", "delayedUpload": "Delayed Upload", "publisherCanUploadLater": "Publisher can upload evidence during task progress", "confirmEvidence": "Confirm Evidence Approval"}, "messages": {"reviewFailed": "Review Failed", "fetchTasksFailed": "Failed to fetch tasks", "fetchStatsFailed": "Failed to fetch statistics", "updateDeliverySuccess": "Delivery information updated successfully", "updateDeliveryFailed": "Failed to update delivery information", "linkCopied": "Link copied to clipboard", "copyFailed": "Co<PERSON> failed"}, "validation": {"recipientNameRequired": "Recipient name is required", "recipientNameTooLong": "Recipient name cannot exceed 50 characters", "phoneRequired": "Recipient phone is required", "phoneInvalid": "Please enter a valid phone number", "addressRequired": "Shipping address is required", "addressTooLong": "Shipping address cannot exceed 500 characters"}, "editTask": {"title": "Edit Delivery Information", "description": "Modify recipient information and shipping address for the task", "recipientName": "Recipient Name", "recipientNamePlaceholder": "Enter recipient name", "recipientPhone": "Recipient Phone", "recipientPhonePlaceholder": "Enter recipient phone", "shippingAddress": "Shipping Address", "shippingAddressPlaceholder": "Enter detailed shipping address", "cancel": "Cancel", "save": "Save Changes", "saving": "Saving..."}, "taskDetail": {"title": "Task Details", "description": "View complete task information and configuration", "basicInfo": "Basic Information", "taskId": "Task ID", "taskStatus": "Task Status", "platform": "Platform", "category": "Category", "publishTime": "Publish Time", "publisherInfo": "Publisher Information", "username": "Username", "email": "Email", "productInfo": "Product Information", "productUrl": "Product URL", "productDescription": "Product Description", "quantity": "Purchase Quantity", "pieces": "pieces", "unitPrice": "Unit Price", "totalPrice": "Total Price", "taskConfig": "Task Configuration", "listingDuration": "Listing Duration", "hours": "hours", "days": "days", "evidenceUploadType": "Evidence Upload Type", "shippingInfo": "Shipping Information", "recipientName": "Recipient Name", "contactPhone": "Contact Phone", "shippingAddress": "Shipping Address", "chargebackTypes": "Chargeback Types", "paymentMethods": "Payment Methods", "costDetails": "Cost Details", "userPayment": "User Payment", "commission": "Commission", "accepterInfo": "Accepter Information", "evidenceRejectReason": "Evidence Rejection Reason", "timeInfo": "Time Information", "createTime": "Create Time", "updateTime": "Update Time", "expiresAt": "Expires At", "completedAt": "Completed At", "unknownPlatform": "Unknown Platform", "unknownCategory": "Unknown Category", "unknownUser": "Unknown User", "unknownEmail": "Unknown Email", "noUrl": "No URL", "noDescription": "No Description", "noInfo": "No Information", "noAddress": "No Address", "noChargebackTypes": "No Chargeback Types", "noPaymentMethods": "No Payment Methods", "status": {"pending": "Pending", "recruiting": "Recruiting", "inProgress": "In Progress", "completed": "Completed", "rejected": "Rejected"}, "evidenceStatus": {"label": "Evidence Status", "pendingSubmission": "Pending Submission", "underReview": "Under Review", "reviewed": "Reviewed", "rejected": "Rejected", "noEvidence": "No Evidence"}, "uploadType": {"immediate": "Immediate Upload", "delayed": "Delayed Upload", "none": "No Upload", "unknown": "Unknown"}}}