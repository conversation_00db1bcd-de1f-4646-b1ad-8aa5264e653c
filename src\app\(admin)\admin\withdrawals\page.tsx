'use client';

import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  CreditCard,
  Wallet,
  RefreshCw,
  Loader2,
  RotateCcw,
  Search,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import {
  useWithdrawalRequests,
  useUpdateWithdrawalStatus,
} from '@/hooks/use-withdrawal-api';
import {
  WithdrawalStatus,
  WithdrawMethod,
  withdrawMethodConfigs,
  withdrawalStatusConfigs,
} from '@/types/withdrawal';

// 提现方式映射
const getWithdrawMethodLabel = (method: WithdrawMethod) => {
  const config = withdrawMethodConfigs.find(c => c.key === method);
  return config?.label || method;
};

// 提现方式类型判断
const getWithdrawMethodType = (method: WithdrawMethod) => {
  const config = withdrawMethodConfigs.find(c => c.key === method);
  return config?.isCrypto ? 'crypto' : 'bank';
};

// 格式化美元账户号 - 移除隐藏效果
const formatBankCard = (cardNumber: string) => {
  if (!cardNumber) return '-';
  // 直接返回完整账户号，不进行隐藏
  return cardNumber;
};

// 格式化钱包地址 - 移除隐藏效果
const formatWalletAddress = (address: string) => {
  if (!address) return '-';
  // 直接返回完整钱包地址，不进行隐藏
  return address;
};

// 获取状态配置
const getStatusConfig = (status: WithdrawalStatus) => {
  const config = withdrawalStatusConfigs.find(c => c.key === status);
  return config || { label: status, color: 'gray', description: '' };
};

export default function WithdrawalsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [rejectReason, setRejectReason] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // 构建查询参数
  const queryParams = {
    search: searchTerm || undefined,
    status:
      statusFilter !== 'all' ? (statusFilter as WithdrawalStatus) : undefined,
    page: currentPage,
    pageSize,
  };

  // 获取提现申请列表
  const {
    data: withdrawalsData,
    isLoading,
    error,
    refetch,
  } = useWithdrawalRequests(queryParams);

  // 更新提现申请状态
  const updateStatusMutation = useUpdateWithdrawalStatus();

  // 获取数据
  const withdrawals = withdrawalsData?.withdrawalRequests || [];
  const stats = withdrawalsData?.stats || {
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    completed: 0,
    bankCard: 0,
    crypto: 0,
  };
  const pagination = withdrawalsData?.pagination || {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // 处理筛选
  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  // 重置筛选
  const resetFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setCurrentPage(1);
  };

  // 处理通过申请
  const handleApprove = async (id: string) => {
    try {
      await updateStatusMutation.mutateAsync({
        id,
        data: { status: 'APPROVED' },
      });
    } catch (error) {
      console.error('审核通过失败:', error);
    }
  };

  // 处理拒绝申请
  const handleReject = async (id: string, reason: string) => {
    if (!reason.trim()) {
      toast.error('请填写拒绝原因');
      return;
    }

    try {
      await updateStatusMutation.mutateAsync({
        id,
        data: { status: 'REJECTED', rejectionReason: reason },
      });
      setRejectReason('');
    } catch (error) {
      console.error('审核拒绝失败:', error);
    }
  };

  // 渲染状态徽章
  const renderStatusBadge = (status: WithdrawalStatus) => {
    const config = getStatusConfig(status);
    let colorClass = '';

    switch (config.color) {
      case 'orange':
        colorClass = 'bg-orange-100 text-orange-800';
        break;
      case 'green':
        colorClass = 'bg-green-100 text-green-800';
        break;
      case 'red':
        colorClass = 'bg-red-100 text-red-800';
        break;
      case 'blue':
        colorClass = 'bg-blue-100 text-blue-800';
        break;
      default:
        colorClass = 'bg-gray-100 text-gray-800';
    }

    return <Badge className={colorClass}>{config.label}</Badge>;
  };

  // 渲染提现方式
  const renderWithdrawMethod = (withdrawal: any) => {
    const methodLabel = getWithdrawMethodLabel(withdrawal.withdrawMethod);
    const methodType = getWithdrawMethodType(withdrawal.withdrawMethod);

    return (
      <div className='flex items-center space-x-2'>
        {methodType === 'crypto' ? (
          <Wallet className='h-4 w-4 text-purple-600' />
        ) : (
          <CreditCard className='h-4 w-4 text-blue-600' />
        )}
        <span className='text-sm font-medium'>{methodLabel}</span>
      </div>
    );
  };

  // 渲染提现详情
  const renderWithdrawDetails = (withdrawal: any) => {
    const methodType = getWithdrawMethodType(withdrawal.withdrawMethod);

    if (methodType === 'crypto') {
      return (
        <div className='space-y-1'>
          <div className='text-sm'>
            <span className='text-muted-foreground'>钱包地址：</span>
            <span className='font-mono'>
              {formatWalletAddress(withdrawal.walletAddress)}
            </span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>币种：</span>
            <span>{withdrawal.cryptoCurrency}</span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>网络：</span>
            <span>{withdrawal.blockchain}</span>
          </div>
        </div>
      );
    } else {
      return (
        <div className='space-y-1'>
          <div className='text-sm'>
            <span className='text-muted-foreground'>持卡人：</span>
            <span>{withdrawal.accountHolder || '-'}</span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>美元账户：</span>
            <span className='font-mono'>
              {formatBankCard(withdrawal.bankCard)}
            </span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>开户行：</span>
            <span>{withdrawal.bankName || '-'}</span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>SWIFT代码：</span>
            <span className='font-mono'>{withdrawal.bankSwift || '-'}</span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>银行地址：</span>
            <span>{withdrawal.branchAddress || '-'}</span>
          </div>
          <div className='text-sm'>
            <span className='text-muted-foreground'>银行国家：</span>
            <span>{withdrawal.bankCountry || '-'}</span>
          </div>
        </div>
      );
    }
  };

  if (error) {
    return (
      <AdminPageLayout title='管理后台' breadcrumbPage='提现审核' href='/admin'>
        <div className='flex flex-col items-center justify-center min-h-[400px] space-y-4'>
          <XCircle className='h-12 w-12 text-red-500' />
          <h3 className='text-lg font-medium'>加载失败</h3>
          <p className='text-muted-foreground'>
            {error instanceof Error ? error.message : '未知错误'}
          </p>
          <Button onClick={() => refetch()} variant='outline'>
            <RefreshCw className='mr-2 h-4 w-4' />
            重试
          </Button>
        </div>
      </AdminPageLayout>
    );
  }

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='提现审核' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题 */}
        <div className='flex flex-col space-y-1'>
          <h1 className='text-2xl font-bold tracking-tight'>提现审核</h1>
          <p className='text-sm text-muted-foreground'>
            管理用户提现申请，审核通过或拒绝提现请求
          </p>
        </div>

        {/* 统计卡片 */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>总申请数</CardTitle>
              <DollarSign className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.total}</div>
              <p className='text-xs text-muted-foreground'>所有提现申请</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>待审核</CardTitle>
              <Clock className='h-4 w-4 text-orange-500' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-orange-600'>
                {stats.pending}
              </div>
              <p className='text-xs text-muted-foreground'>等待审核的申请</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>美元提现</CardTitle>
              <CreditCard className='h-4 w-4 text-blue-500' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-blue-600'>
                {stats.bankCard}
              </div>
              <p className='text-xs text-muted-foreground'>美元提现申请</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                加密货币提现
              </CardTitle>
              <Wallet className='h-4 w-4 text-purple-500' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold text-purple-600'>
                {stats.crypto}
              </div>
              <p className='text-xs text-muted-foreground'>加密货币提现申请</p>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和状态筛选 */}
        <Card>
          <CardHeader>
            <CardTitle>搜索和筛选</CardTitle>
            <CardDescription>
              通过用户名、邮箱或申请ID搜索，或按状态筛选查看
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='search'>搜索</Label>
                <div className='relative'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                  <Input
                    id='search'
                    placeholder='用户名、邮箱或申请ID...'
                    value={searchTerm}
                    onChange={e => handleSearch(e.target.value)}
                    className='pl-10'
                  />
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='status-filter'>状态</Label>
                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                  <SelectTrigger id='status-filter'>
                    <SelectValue placeholder='选择状态' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部状态</SelectItem>
                    <SelectItem value='PENDING'>待审核</SelectItem>
                    <SelectItem value='APPROVED'>已通过</SelectItem>
                    <SelectItem value='REJECTED'>已拒绝</SelectItem>
                    <SelectItem value='COMPLETED'>已完成</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label>&nbsp;</Label>
                <div className='flex space-x-2'>
                  <Button
                    variant='outline'
                    onClick={resetFilters}
                    className='flex-1'
                  >
                    <RotateCcw className='h-4 w-4 mr-2' />
                    重置
                  </Button>
                  <Button
                    variant='outline'
                    onClick={() => refetch()}
                    disabled={isLoading}
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                    />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提现申请列表 */}
        <Card>
          <CardHeader>
            <CardTitle>提现申请列表</CardTitle>
            <CardDescription>
              共 {pagination.total} 条记录，第 {pagination.page} 页
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className='flex items-center justify-center py-8'>
                <Loader2 className='h-8 w-8 animate-spin text-muted-foreground' />
                <span className='ml-2 text-muted-foreground'>加载中...</span>
              </div>
            ) : withdrawals.length === 0 ? (
              <div className='text-center py-8'>
                <DollarSign className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                <h3 className='text-lg font-medium text-muted-foreground'>
                  暂无提现申请
                </h3>
                <p className='text-sm text-muted-foreground mt-2'>
                  {searchTerm || statusFilter !== 'all'
                    ? '没有找到符合条件的申请'
                    : '还没有用户提交提现申请'}
                </p>
              </div>
            ) : (
              <div className='space-y-4'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户信息</TableHead>
                      <TableHead>提现方式</TableHead>
                      <TableHead>提现详情</TableHead>
                      <TableHead>金额信息</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>申请时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {withdrawals.map(withdrawal => (
                      <TableRow key={withdrawal.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <div className='font-medium'>
                              {withdrawal.user.name ||
                                withdrawal.user.username ||
                                '未知用户'}
                            </div>
                            <div className='text-sm text-muted-foreground'>
                              {withdrawal.user.email}
                            </div>
                            <div className='text-xs text-muted-foreground font-mono'>
                              ID: {withdrawal.id}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {renderWithdrawMethod(withdrawal)}
                        </TableCell>
                        <TableCell>
                          {renderWithdrawDetails(withdrawal)}
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <div className='text-sm font-medium'>
                              申请：${withdrawal.amount}
                            </div>
                            <div className='text-sm text-muted-foreground'>
                              手续费：${withdrawal.fee}
                            </div>
                            <div className='text-sm text-green-600 font-medium'>
                              到账：${withdrawal.actualAmount}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            {renderStatusBadge(withdrawal.status)}
                            {withdrawal.rejectionReason && (
                              <div className='text-xs text-red-600'>
                                {withdrawal.rejectionReason}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <div className='text-sm'>
                              {new Date(
                                withdrawal.createdAt,
                              ).toLocaleDateString('zh-CN')}
                            </div>
                            <div className='text-xs text-muted-foreground'>
                              {formatDistanceToNow(
                                new Date(withdrawal.createdAt),
                                {
                                  addSuffix: true,
                                  locale: zhCN,
                                },
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className='text-right'>
                          {withdrawal.status === 'PENDING' ? (
                            <div className='flex items-center space-x-2'>
                              {/* 通过按钮 */}
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    size='sm'
                                    variant='outline'
                                    className='text-green-600 border-green-600 hover:bg-green-50'
                                  >
                                    <CheckCircle className='h-4 w-4 mr-1' />
                                    通过
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      确认通过提现申请
                                    </AlertDialogTitle>
                                    <AlertDialogDescription asChild>
                                      <div className='space-y-4'>
                                        <div className='p-4 bg-gray-50 rounded-lg space-y-2'>
                                          <div className='flex justify-between'>
                                            <span className='text-sm text-muted-foreground'>
                                              用户：
                                            </span>
                                            <span className='text-sm font-medium'>
                                              {withdrawal.user.name ||
                                                withdrawal.user.email}
                                            </span>
                                          </div>
                                          <div className='flex justify-between'>
                                            <span className='text-sm text-muted-foreground'>
                                              提现方式：
                                            </span>
                                            <span className='text-sm'>
                                              {getWithdrawMethodLabel(
                                                withdrawal.withdrawMethod,
                                              )}
                                            </span>
                                          </div>
                                          <div className='flex justify-between'>
                                            <span className='text-sm text-muted-foreground'>
                                              申请金额：
                                            </span>
                                            <span className='text-sm font-medium'>
                                              ${withdrawal.amount}
                                            </span>
                                          </div>
                                          <div className='flex justify-between'>
                                            <span className='text-sm text-muted-foreground'>
                                              手续费：
                                            </span>
                                            <span className='text-sm'>
                                              ${withdrawal.fee}
                                            </span>
                                          </div>
                                          <div className='flex justify-between'>
                                            <span className='text-sm text-muted-foreground'>
                                              实际到账：
                                            </span>
                                            <span className='text-sm font-medium text-green-600'>
                                              ${withdrawal.actualAmount}
                                            </span>
                                          </div>
                                        </div>
                                        <p className='text-sm text-muted-foreground'>
                                          确认通过此提现申请？通过后将无法撤销。
                                        </p>
                                      </div>
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>取消</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() =>
                                        handleApprove(withdrawal.id)
                                      }
                                      disabled={updateStatusMutation.isPending}
                                      className='bg-green-600 hover:bg-green-700'
                                    >
                                      {updateStatusMutation.isPending ? (
                                        <>
                                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                          处理中...
                                        </>
                                      ) : (
                                        <>
                                          <CheckCircle className='mr-2 h-4 w-4' />
                                          确认通过
                                        </>
                                      )}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>

                              {/* 拒绝按钮 */}
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    size='sm'
                                    variant='outline'
                                    className='text-red-600 border-red-600 hover:bg-red-50'
                                  >
                                    <XCircle className='h-4 w-4 mr-1' />
                                    拒绝
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      拒绝提现申请
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      请填写拒绝原因，用户将能看到此原因。
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <div className='space-y-4'>
                                    <div className='space-y-2'>
                                      <Label htmlFor='reject-reason'>
                                        拒绝原因
                                      </Label>
                                      <Textarea
                                        id='reject-reason'
                                        placeholder='请输入拒绝原因...'
                                        value={rejectReason}
                                        onChange={e =>
                                          setRejectReason(e.target.value)
                                        }
                                        className='min-h-[100px]'
                                      />
                                    </div>
                                  </div>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel
                                      onClick={() => setRejectReason('')}
                                    >
                                      取消
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() =>
                                        handleReject(
                                          withdrawal.id,
                                          rejectReason,
                                        )
                                      }
                                      disabled={
                                        updateStatusMutation.isPending ||
                                        !rejectReason.trim()
                                      }
                                      className='bg-red-600 hover:bg-red-700'
                                    >
                                      {updateStatusMutation.isPending ? (
                                        <>
                                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                          处理中...
                                        </>
                                      ) : (
                                        <>
                                          <XCircle className='mr-2 h-4 w-4' />
                                          确认拒绝
                                        </>
                                      )}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          ) : (
                            <div className='flex items-center space-x-2'>
                              <Badge variant='outline' className='text-xs'>
                                {withdrawal.status === 'APPROVED'
                                  ? '已处理'
                                  : '已拒绝'}
                              </Badge>
                              {withdrawal.reviewer && (
                                <span className='text-xs text-muted-foreground'>
                                  by{' '}
                                  {withdrawal.reviewer.name ||
                                    withdrawal.reviewer.email}
                                </span>
                              )}
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* 分页 */}
                {pagination.totalPages > 1 && (
                  <div className='flex items-center justify-between'>
                    <div className='text-sm text-muted-foreground'>
                      显示 {(pagination.page - 1) * pagination.pageSize + 1} 到{' '}
                      {Math.min(
                        pagination.page * pagination.pageSize,
                        pagination.total,
                      )}{' '}
                      条， 共 {pagination.total} 条记录
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                        disabled={currentPage === 1}
                      >
                        上一页
                      </Button>
                      <div className='flex items-center space-x-1'>
                        {Array.from(
                          { length: Math.min(5, pagination.totalPages) },
                          (_, i) => {
                            const pageNumber = Math.max(1, currentPage - 2) + i;
                            if (pageNumber > pagination.totalPages) return null;
                            return (
                              <Button
                                key={pageNumber}
                                variant={
                                  currentPage === pageNumber
                                    ? 'default'
                                    : 'outline'
                                }
                                size='sm'
                                onClick={() => setCurrentPage(pageNumber)}
                              >
                                {pageNumber}
                              </Button>
                            );
                          },
                        )}
                      </div>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          setCurrentPage(
                            Math.min(pagination.totalPages, currentPage + 1),
                          )
                        }
                        disabled={currentPage === pagination.totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminPageLayout>
  );
}
