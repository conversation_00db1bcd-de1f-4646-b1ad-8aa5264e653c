'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

import { cache, CacheManager, CacheStrategy, CACHE_CONFIG } from '@/lib/cache';

// 缓存状态接口
interface CacheState<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  isStale: boolean;
  lastUpdated: number | null;
}

// 缓存选项接口
interface CacheOptions {
  ttl?: number;
  strategy?: CacheStrategy;
  staleWhileRevalidate?: boolean;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
  refreshInterval?: number;
  retryCount?: number;
  retryDelay?: number;
}

// 使用缓存的 Hook
export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {},
) {
  const {
    ttl = CACHE_CONFIG.DEFAULT_TTL,
    strategy = CacheStrategy.MEMORY_FIRST,
    staleWhileRevalidate = true,
    revalidateOnFocus = true,
    revalidateOnReconnect = true,
    refreshInterval,
    retryCount = 3,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<CacheState<T>>({
    data: null,
    isLoading: true,
    error: null,
    isStale: false,
    lastUpdated: null,
  });

  const cacheManager = useRef(new CacheManager(strategy));
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const currentRetryCount = useRef(0);

  // 获取数据
  const fetchData = useCallback(
    async (forceRefresh = false) => {
      try {
        // 如果不是强制刷新，先尝试从缓存获取
        if (!forceRefresh) {
          const cachedData = cacheManager.current.get<T>(key);
          if (cachedData !== null) {
            setState(prev => ({
              ...prev,
              data: cachedData,
              isLoading: false,
              error: null,
              isStale: false,
              lastUpdated: Date.now(),
            }));
            return cachedData;
          }
        }

        // 如果有缓存数据且启用了 staleWhileRevalidate，先返回旧数据
        if (staleWhileRevalidate && !forceRefresh) {
          const staleData = cacheManager.current.get<T>(key);
          if (staleData !== null) {
            setState(prev => ({
              ...prev,
              data: staleData,
              isLoading: true,
              isStale: true,
            }));
          }
        }

        setState(prev => ({ ...prev, isLoading: true, error: null }));

        const data = await fetcher();

        // 存储到缓存
        cacheManager.current.set(key, data, ttl);

        setState({
          data,
          isLoading: false,
          error: null,
          isStale: false,
          lastUpdated: Date.now(),
        });

        currentRetryCount.current = 0;
        return data;
      } catch (error) {
        const err = error instanceof Error ? error : new Error('Unknown error');

        // 重试逻辑
        if (currentRetryCount.current < retryCount) {
          currentRetryCount.current++;
          retryTimeoutRef.current = setTimeout(() => {
            fetchData(forceRefresh);
          }, retryDelay * currentRetryCount.current);
          return;
        }

        setState(prev => ({
          ...prev,
          isLoading: false,
          error: err,
        }));

        throw err;
      }
    },
    [key, fetcher, ttl, staleWhileRevalidate, retryCount, retryDelay],
  );

  // 手动刷新
  const refresh = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // 使数据失效
  const invalidate = useCallback(() => {
    cacheManager.current.delete(key);
    setState(prev => ({ ...prev, isStale: true }));
  }, [key]);

  // 设置数据
  const setData = useCallback(
    (data: T) => {
      cacheManager.current.set(key, data, ttl);
      setState({
        data,
        isLoading: false,
        error: null,
        isStale: false,
        lastUpdated: Date.now(),
      });
    },
    [key, ttl],
  );

  // 初始化数据获取
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 设置刷新间隔
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        fetchData(true);
      }, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [refreshInterval, fetchData]);

  // 页面焦点重新验证
  useEffect(() => {
    if (!revalidateOnFocus) return;

    const handleFocus = () => {
      if (document.visibilityState === 'visible') {
        fetchData();
      }
    };

    document.addEventListener('visibilitychange', handleFocus);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleFocus);
      window.removeEventListener('focus', handleFocus);
    };
  }, [revalidateOnFocus, fetchData]);

  // 网络重连重新验证
  useEffect(() => {
    if (!revalidateOnReconnect) return;

    const handleOnline = () => {
      fetchData();
    };

    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, [revalidateOnReconnect, fetchData]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  return {
    ...state,
    refresh,
    invalidate,
    setData,
  };
}

// 用户数据缓存 Hook
export function useUserCache<T>(
  userId: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {},
) {
  const key = `user:${userId}`;
  return useCache(key, fetcher, {
    ttl: CACHE_CONFIG.TTL.USER_DATA,
    strategy: CacheStrategy.PERSISTENT_FIRST,
    ...options,
  });
}

// 委托数据缓存 Hook
export function useTasksCache<T>(
  userId: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {},
) {
  const key = `tasks:${userId}`;
  return useCache(key, fetcher, {
    ttl: CACHE_CONFIG.TTL.TASKS,
    strategy: CacheStrategy.MEMORY_FIRST,
    refreshInterval: 30000, // 30秒自动刷新
    ...options,
  });
}

// 仪表盘数据缓存 Hook
export function useDashboardCache<T>(
  userId: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {},
) {
  const key = `dashboard:${userId}`;
  return useCache(key, fetcher, {
    ttl: CACHE_CONFIG.TTL.DASHBOARD,
    strategy: CacheStrategy.MEMORY_FIRST,
    refreshInterval: 60000, // 1分钟自动刷新
    ...options,
  });
}

// 静态数据缓存 Hook
export function useStaticCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: CacheOptions = {},
) {
  return useCache(`static:${key}`, fetcher, {
    ttl: CACHE_CONFIG.TTL.STATIC_DATA,
    strategy: CacheStrategy.PERSISTENT_FIRST,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    ...options,
  });
}

// API 缓存 Hook
export function useAPICache<T>(
  endpoint: string,
  params: Record<string, any> = {},
  fetcher: () => Promise<T>,
  options: CacheOptions = {},
) {
  const key = `api:${endpoint}:${JSON.stringify(params)}`;
  return useCache(key, fetcher, {
    ttl: CACHE_CONFIG.DEFAULT_TTL,
    strategy: CacheStrategy.MEMORY_FIRST,
    ...options,
  });
}

// 缓存预加载 Hook
export function useCachePreload() {
  const preload = useCallback(
    <T>(key: string, fetcher: () => Promise<T>, options: CacheOptions = {}) => {
      const {
        ttl = CACHE_CONFIG.DEFAULT_TTL,
        strategy = CacheStrategy.MEMORY_FIRST,
      } = options;
      const cacheManager = new CacheManager(strategy);

      // 检查是否已缓存
      if (cacheManager.has(key)) {
        return Promise.resolve();
      }

      // 预加载数据
      return fetcher()
        .then(data => {
          cacheManager.set(key, data, ttl);
        })
        .catch(error => {
          console.warn('Cache preload failed:', error);
        });
    },
    [],
  );

  return { preload };
}

// 缓存状态管理 Hook
export function useCacheManager() {
  const [stats, setStats] = useState(cache.getStats());

  const updateStats = useCallback(() => {
    setStats(cache.getStats());
  }, []);

  const clearAll = useCallback(() => {
    cache.clear();
    updateStats();
  }, [updateStats]);

  const cleanup = useCallback(() => {
    const result = cache.cleanup();
    updateStats();
    return result;
  }, [updateStats]);

  useEffect(() => {
    // 定期更新统计信息
    const interval = setInterval(updateStats, 10000); // 10秒更新一次
    return () => clearInterval(interval);
  }, [updateStats]);

  return {
    stats,
    clearAll,
    cleanup,
    updateStats,
  };
}

// 缓存同步 Hook (用于多标签页同步)
export function useCacheSync() {
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key?.startsWith('refundgo_cache:')) {
        // 存储变化时，清除内存缓存中对应的项
        const cacheKey = event.key.replace('refundgo_cache:', '');
        cache.delete(cacheKey);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);
}

export default useCache;
