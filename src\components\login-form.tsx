'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2, Eye, EyeOff } from 'lucide-react';
import { signIn } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { AuthBrandPanel } from '@/components/auth-brand-panel';
import { AuthHeader } from '@/components/auth-header';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useRouter, Link } from '@/i18n/navigation';
import { cn } from '@/lib/utils';
import {
  signInSchema,
  signUpSchema,
  type SignInInput,
  type SignUpInput,
} from '@/lib/validations/auth';

interface LoginFormProps extends React.ComponentProps<'div'> {
  mode?: 'signin' | 'signup';
}

export function LoginForm({
  className,
  mode = 'signin',
  ...props
}: LoginFormProps) {
  const router = useRouter();
  const t = useTranslations('Auth');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isVerificationLogin, setIsVerificationLogin] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [isCodeSending, setIsCodeSending] = useState(false);
  const [canResendCode, setCanResendCode] = useState(true);
  const [countdown, setCountdown] = useState(0);

  // 注册验证状态
  const [isSignUpEmailVerification, setIsSignUpEmailVerification] =
    useState(false);
  const [signUpVerificationCode, setSignUpVerificationCode] = useState('');
  const [isSignUpCodeSending, setIsSignUpCodeSending] = useState(false);
  const [canResendSignUpCode, setCanResendSignUpCode] = useState(true);
  const [signUpCountdown, setSignUpCountdown] = useState(0);

  // 登录倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (countdown === 0 && !canResendCode) {
      setCanResendCode(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, canResendCode]);

  // 注册倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (signUpCountdown > 0) {
      timer = setTimeout(() => {
        setSignUpCountdown(signUpCountdown - 1);
      }, 1000);
    } else if (signUpCountdown === 0 && !canResendSignUpCode) {
      setCanResendSignUpCode(true);
    }
    return () => clearTimeout(timer);
  }, [signUpCountdown, canResendSignUpCode]);

  // 重置验证状态
  useEffect(() => {
    if (!isVerificationLogin) {
      setVerificationCode('');
      setCanResendCode(true);
      setCountdown(0);
    }
  }, [isVerificationLogin]);

  // 表单配置
  const signInForm = useForm<SignInInput>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const signUpForm = useForm<SignUpInput>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // 表单数据
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    confirmPassword: '',
  });

  // 处理输入
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 登录处理
  const handleSignIn = async () => {
    // 表单验证
    if (!formData.email || !formData.password) {
      toast.error(t('signin.errors.fillAllFields'));
      return;
    }

    if (!formData.email.includes('@')) {
      toast.error(t('signin.errors.invalidEmail'));
      return;
    }

    setIsLoading(true);
    try {
      const result = await signIn('credentials', {
        email: formData.email,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        toast.error(t('signin.errors.loginFailed'), {
          description: t('signin.errors.wrongCredentials'),
        });
      } else {
        toast.success(t('signin.success.loginSuccess'), {
          description: t('signin.success.redirecting'),
        });

        // 等待一小段时间让认证状态更新，然后重定向
        setTimeout(() => {
          // 使用 router.push 进行重定向，支持国际化路由
          router.push('/dashboard');
        }, 1000);
      }
    } catch (error) {
      toast.error(t('signin.errors.loginFailed'), {
        description: t('signin.errors.networkError'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 发送验证码
  const handleSendSignUpCode = async () => {
    // 表单验证
    if (
      !formData.name ||
      !formData.email ||
      !formData.password ||
      !formData.confirmPassword
    ) {
      toast.error(t('signup.errors.fillAllFields'));
      return;
    }

    if (!formData.email.includes('@')) {
      toast.error(t('signup.errors.invalidEmail'));
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error(t('signup.errors.passwordMismatch'));
      return;
    }

    if (formData.password.length < 6) {
      toast.error(t('signup.errors.passwordTooShort'));
      return;
    }

    setIsSignUpCodeSending(true);
    try {
      const response = await fetch('/api/auth/send-signup-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(t('signup.success.codeSent'), {
          description: t('signup.success.checkEmail'),
        });
        setIsSignUpEmailVerification(true);
        setCanResendSignUpCode(false);
        setSignUpCountdown(60); // 60秒
      } else {
        toast.error(t('signup.errors.sendCodeFailed'), {
          description: result.error || '请重试',
        });
      }
    } catch (error) {
      toast.error(t('signup.errors.sendCodeFailed'), {
        description: t('signup.errors.networkError'),
      });
    } finally {
      setIsSignUpCodeSending(false);
    }
  };

  // 完成注册
  const handleCompleteSignUp = async () => {
    if (!signUpVerificationCode) {
      toast.error(t('signup.errors.enterVerificationCode'));
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/complete-signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          verificationCode: signUpVerificationCode,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(t('signup.success.signUpSuccess'), {
          description: t('signup.success.pleaseLogin'),
        });
        // 切换到登录页面
        router.push('/sign-in');
      } else {
        toast.error(t('signup.errors.signUpFailed'), {
          description: result.error || t('signup.errors.verificationCodeError'),
        });
      }
    } catch (error) {
      toast.error(t('signup.errors.signUpFailed'), {
        description: t('signup.errors.networkError'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 发送验证码
  const handleSendVerificationCode = async () => {
    if (!formData.email) {
      toast.error(t('signin.errors.enterEmail'));
      return;
    }

    if (!formData.email.includes('@')) {
      toast.error(t('signin.errors.invalidEmail'));
      return;
    }

    setIsCodeSending(true);
    try {
      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(t('signin.success.codeSent'), {
          description: t('signin.success.checkEmail'),
        });
        setCanResendCode(false);
        setCountdown(60); // 60秒
      } else {
        toast.error(t('signin.errors.sendCodeFailed'), {
          description: result.error || '请重试',
        });
      }
    } catch (error) {
      toast.error(t('signin.errors.sendCodeFailed'), {
        description: t('signin.errors.networkError'),
      });
    } finally {
      setIsCodeSending(false);
    }
  };

  // 验证码登录
  const handleVerificationLogin = async () => {
    if (!formData.email) {
      toast.error(t('signin.errors.enterEmailAddress'));
      return;
    }

    if (!verificationCode) {
      toast.error(t('signin.errors.enterVerificationCode'));
      return;
    }

    setIsLoading(true);
    try {
      // 验证码登录
      const signInResult = await signIn('credentials', {
        email: formData.email,
        verificationCode,
        redirect: false,
      });

      if (signInResult?.error) {
        toast.error(t('signin.errors.loginFailed'), {
          description: t('signin.errors.verificationCodeError'),
        });
      } else {
        toast.success(t('signin.success.loginSuccess'), {
          description: t('signin.success.redirecting'),
        });

        // 等待一小段时间让认证状态更新，然后重定向
        setTimeout(() => {
          // 使用 router.push 进行重定向，支持国际化路由
          router.push('/dashboard');
        }, 1000);
      }
    } catch (error) {
      toast.error(t('signin.errors.loginFailed'), {
        description: t('signin.errors.networkError'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isSignUp = mode === 'signup';

  // 模式切换时重置表单
  useEffect(() => {
    setFormData({
      email: '',
      password: '',
      name: '',
      confirmPassword: '',
    });
  }, [isSignUp]);

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      {/* 头部导航 - 仅在移动端显示 */}
      <div className='md:hidden'>
        <AuthHeader />
      </div>

      <Card className='overflow-hidden'>
        <CardContent className='grid p-0 md:grid-cols-2'>
          <form className='p-6 md:p-8' onSubmit={e => e.preventDefault()}>
            <div className='flex flex-col gap-6'>
              <div className='flex flex-col items-center text-center'>
                <h1 className='text-2xl font-bold'>
                  {isSignUp ? t('signup.pageTitle') : t('signin.pageTitle')}
                </h1>
                <p className='text-balance text-muted-foreground'>
                  {isSignUp
                    ? t('signup.pageDescription')
                    : t('signin.pageDescription')}
                </p>
              </div>

              {/* 注册时显示用户名字段 */}
              {isSignUp && (
                <div className='grid gap-2'>
                  <Label htmlFor='name'>{t('signup.username')}</Label>
                  <Input
                    id='name'
                    type='text'
                    placeholder={t('signup.usernamePlaceholder')}
                    value={formData.name}
                    onChange={e => handleInputChange('name', e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              )}

              {/* 邮箱字段 */}
              <div className='grid gap-2'>
                <Label htmlFor='email'>
                  {isSignUp ? t('signup.email') : t('signin.email')}
                </Label>
                <Input
                  id='email'
                  type='email'
                  placeholder={
                    isSignUp
                      ? t('signup.emailPlaceholder')
                      : t('signin.emailPlaceholder')
                  }
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  disabled={isLoading}
                />
              </div>

              {/* 密码字段或验证码字段 */}
              {isSignUp || !isVerificationLogin ? (
                <div className='grid gap-2'>
                  <div className='flex items-center'>
                    <Label htmlFor='password'>
                      {isSignUp ? t('signup.password') : t('signin.password')}
                    </Label>
                    {!isSignUp && (
                      <button
                        type='button'
                        className='ml-auto text-sm underline-offset-2 hover:underline'
                        onClick={() => {
                          // TODO: Implement forgot password functionality
                          toast.info(t('signin.errors.forgotPasswordSoon'));
                        }}
                      >
                        {t('signin.forgotPassword')}
                      </button>
                    )}
                  </div>
                  <div className='relative'>
                    <Input
                      id='password'
                      type={showPassword ? 'text' : 'password'}
                      placeholder={
                        isSignUp
                          ? t('signup.passwordPlaceholder')
                          : t('signin.passwordPlaceholder')
                      }
                      value={formData.password}
                      onChange={e =>
                        handleInputChange('password', e.target.value)
                      }
                      disabled={isLoading}
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                  {(isSignUp
                    ? signUpForm.formState.errors.password
                    : signInForm.formState.errors.password) && (
                    <p className='text-sm text-red-600'>
                      {
                        (isSignUp
                          ? signUpForm.formState.errors.password
                          : signInForm.formState.errors.password
                        )?.message
                      }
                    </p>
                  )}
                </div>
              ) : (
                <div className='grid gap-2'>
                  <Label htmlFor='verificationCode'>
                    {t('signin.verificationCode')}
                  </Label>
                  <div className='flex gap-2'>
                    <Input
                      id='verificationCode'
                      type='text'
                      placeholder={t('signin.verificationCodePlaceholder')}
                      value={verificationCode || ''}
                      onChange={e => setVerificationCode(e.target.value)}
                      disabled={isLoading}
                      maxLength={6}
                      className='flex-1'
                    />
                    <Button
                      type='button'
                      variant='outline'
                      onClick={handleSendVerificationCode}
                      disabled={isCodeSending || !canResendCode || isLoading}
                      className='whitespace-nowrap'
                    >
                      {isCodeSending ? (
                        <>
                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          {t('signin.sending')}
                        </>
                      ) : canResendCode ? (
                        t('signin.sendCode')
                      ) : (
                        `${countdown}${t('signin.resendAfter')}`
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {/* 注册时显示确认密码字段 */}
              {isSignUp && !isSignUpEmailVerification && (
                <div className='grid gap-2'>
                  <Label htmlFor='confirmPassword'>
                    {t('signup.confirmPassword')}
                  </Label>
                  <div className='relative'>
                    <Input
                      id='confirmPassword'
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder={t('signup.confirmPasswordPlaceholder')}
                      value={formData.confirmPassword}
                      onChange={e =>
                        handleInputChange('confirmPassword', e.target.value)
                      }
                      disabled={isLoading || isSignUpCodeSending}
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      disabled={isLoading || isSignUpCodeSending}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {/* 注册邮箱验证码输入 */}
              {isSignUp && isSignUpEmailVerification && (
                <div className='grid gap-2'>
                  <Label htmlFor='signUpVerificationCode'>
                    {t('signup.emailVerificationCode')}
                  </Label>
                  <div className='flex gap-2'>
                    <Input
                      id='signUpVerificationCode'
                      type='text'
                      placeholder={t('signup.verificationCodePlaceholder')}
                      value={signUpVerificationCode || ''}
                      onChange={e => setSignUpVerificationCode(e.target.value)}
                      disabled={isLoading}
                      maxLength={6}
                      className='flex-1'
                    />
                    <Button
                      type='button'
                      variant='outline'
                      onClick={handleSendSignUpCode}
                      disabled={
                        isSignUpCodeSending || !canResendSignUpCode || isLoading
                      }
                      className='whitespace-nowrap'
                    >
                      {isSignUpCodeSending ? (
                        <>
                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          {t('signup.sending')}
                        </>
                      ) : canResendSignUpCode ? (
                        t('signup.resendCode')
                      ) : (
                        `${signUpCountdown}${t('signup.resendAfter')}`
                      )}
                    </Button>
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    {t('signup.codeSentTo')} <strong>{formData.email}</strong>
                  </p>
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => {
                      setIsSignUpEmailVerification(false);
                      setSignUpVerificationCode('');
                      setCanResendSignUpCode(true);
                      setSignUpCountdown(0);
                    }}
                    className='mt-2 text-sm text-muted-foreground hover:text-foreground'
                  >
                    {t('signup.reenterInfo')}
                  </Button>
                </div>
              )}

              {/* 按钮组 */}
              <div className='flex flex-col gap-2 -mb-3'>
                {/* 提交按钮 */}
                <Button
                  type='button'
                  className='w-full'
                  disabled={isLoading || isSignUpCodeSending}
                  onClick={
                    isSignUp
                      ? isSignUpEmailVerification
                        ? handleCompleteSignUp
                        : handleSendSignUpCode
                      : isVerificationLogin
                        ? handleVerificationLogin
                        : handleSignIn
                  }
                >
                  {(isLoading || isSignUpCodeSending) && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  {isSignUp
                    ? isSignUpEmailVerification
                      ? t('signup.completeSignUp')
                      : isSignUpCodeSending
                        ? t('signup.sending')
                        : t('signup.signUpButton')
                    : t('signin.loginButton')}
                </Button>

                {/* 验证码登录切换按钮 */}
                {!isSignUp && (
                  <div className='text-center'>
                    <Button
                      type='button'
                      variant='link'
                      className='text-sm text-muted-foreground hover:text-primary'
                      onClick={() => {
                        setIsVerificationLogin(!isVerificationLogin);
                        setVerificationCode('');
                        setCanResendCode(true);
                        setCountdown(0);
                      }}
                      disabled={isLoading}
                    >
                      {isVerificationLogin
                        ? t('signin.passwordLogin')
                        : t('signin.verificationLogin')}
                    </Button>
                  </div>
                )}
              </div>

              {/* 登录/注册切换链接 */}
              <div className='text-center text-sm'>
                {isSignUp ? (
                  <>
                    {t('signup.hasAccount')}{' '}
                    <Link
                      href='/sign-in'
                      className='underline underline-offset-4'
                    >
                      {t('signup.signInNow')}
                    </Link>
                  </>
                ) : (
                  <>
                    {t('signin.noAccount')}{' '}
                    <Link
                      href='/sign-up'
                      className='underline underline-offset-4'
                    >
                      {t('signin.signUpNow')}
                    </Link>
                  </>
                )}
              </div>
            </div>
          </form>

          {/* 右侧品牌展示区域 */}
          <AuthBrandPanel mode={mode} />
        </CardContent>
      </Card>

      <div className='text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary'>
        {t('footer.agreement')} <Link href='/terms'>{t('footer.terms')}</Link>{' '}
        {t('footer.and')} <Link href='/privacy'>{t('footer.privacy')}</Link>
        {t('footer.period')}
      </div>
    </div>
  );
}
