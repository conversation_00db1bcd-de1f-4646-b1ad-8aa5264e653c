import currency from 'currency.js';

export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  source: string;
  timestamp: number;
  expiresAt: number;
}

export interface ConversionResult {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  source: string;
}

export interface ExchangeRateProvider {
  name: string;
  getRate(from: string, to: string): Promise<number>;
}

// 免费汇率API提供商
class FawazExchangeProvider implements ExchangeRateProvider {
  name = 'fawaz-exchange-api';
  private baseUrl =
    'https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies';

  async getRate(from: string, to: string): Promise<number> {
    try {
      const response = await fetch(
        `${this.baseUrl}/${from.toLowerCase()}.json`,
        {
          next: { revalidate: 3600 }, // 缓存1小时
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const rate = data[from.toLowerCase()]?.[to.toLowerCase()];

      if (typeof rate !== 'number') {
        throw new Error(`Rate not found for ${from} to ${to}`);
      }

      return rate;
    } catch (error) {
      throw new Error(
        `Fawaz API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }
}

// 备用汇率API提供商（使用Cloudflare镜像）
class FawazCloudflareProvider implements ExchangeRateProvider {
  name = 'fawaz-cloudflare';
  private baseUrl = 'https://latest.currency-api.pages.dev/v1/currencies';

  async getRate(from: string, to: string): Promise<number> {
    try {
      const response = await fetch(
        `${this.baseUrl}/${from.toLowerCase()}.json`,
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const rate = data[from.toLowerCase()]?.[to.toLowerCase()];

      if (typeof rate !== 'number') {
        throw new Error(`Rate not found for ${from} to ${to}`);
      }

      return rate;
    } catch (error) {
      throw new Error(
        `Fawaz Cloudflare API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }
}

export class CurrencyConverter {
  private cache = new Map<string, ExchangeRate>();
  private providers: ExchangeRateProvider[] = [
    new FawazExchangeProvider(),
    new FawazCloudflareProvider(),
  ];
  private defaultCacheTime = 60 * 60 * 1000; // 1小时缓存
  private fallbackRates = new Map([
    ['USD-CNY', 7.2], // 默认汇率作为最后的降级方案
  ]);

  /**
   * 获取汇率，支持缓存和多提供商降级
   */
  async getExchangeRate(from: string, to: string): Promise<ExchangeRate> {
    const cacheKey = `${from.toUpperCase()}-${to.toUpperCase()}`;

    // 检查缓存
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() < cached.expiresAt) {
      return cached;
    }

    // 尝试从各个提供商获取汇率
    for (const provider of this.providers) {
      try {
        const rate = await provider.getRate(from, to);
        const exchangeRate: ExchangeRate = {
          from: from.toUpperCase(),
          to: to.toUpperCase(),
          rate,
          source: provider.name,
          timestamp: Date.now(),
          expiresAt: Date.now() + this.defaultCacheTime,
        };

        // 缓存汇率
        this.cache.set(cacheKey, exchangeRate);
        return exchangeRate;
      } catch (error) {
        console.warn(`Provider ${provider.name} failed:`, error);
        continue;
      }
    }

    // 如果所有提供商都失败，使用降级汇率
    const fallbackRate = this.fallbackRates.get(cacheKey);
    if (fallbackRate) {
      console.warn(`Using fallback rate for ${cacheKey}: ${fallbackRate}`);
      const exchangeRate: ExchangeRate = {
        from: from.toUpperCase(),
        to: to.toUpperCase(),
        rate: fallbackRate,
        source: 'fallback',
        timestamp: Date.now(),
        expiresAt: Date.now() + this.defaultCacheTime,
      };

      this.cache.set(cacheKey, exchangeRate);
      return exchangeRate;
    }

    throw new Error(
      `Unable to get exchange rate for ${from} to ${to} from any provider`,
    );
  }

  /**
   * 转换货币金额
   */
  async convertCurrency(
    amount: number,
    from: string,
    to: string,
  ): Promise<ConversionResult> {
    if (from.toUpperCase() === to.toUpperCase()) {
      return {
        originalAmount: amount,
        originalCurrency: from.toUpperCase(),
        convertedAmount: amount,
        convertedCurrency: to.toUpperCase(),
        exchangeRate: 1,
        source: 'same-currency',
      };
    }

    const exchangeRate = await this.getExchangeRate(from, to);

    // 使用currency.js进行精确计算
    const originalCurrency = currency(amount);
    const convertedCurrency = originalCurrency.multiply(exchangeRate.rate);

    return {
      originalAmount: originalCurrency.value,
      originalCurrency: from.toUpperCase(),
      convertedAmount: convertedCurrency.value,
      convertedCurrency: to.toUpperCase(),
      exchangeRate: exchangeRate.rate,
      source: exchangeRate.source,
    };
  }

  /**
   * 专门用于USD到CNY的转换（YunPay使用）
   */
  async convertUSDToCNY(usdAmount: number): Promise<ConversionResult> {
    return this.convertCurrency(usdAmount, 'USD', 'CNY');
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存状态
   */
  getCacheInfo(): { key: string; rate: ExchangeRate }[] {
    return Array.from(this.cache.entries()).map(([key, rate]) => ({
      key,
      rate,
    }));
  }
}

// 全局单例实例
export const currencyConverter = new CurrencyConverter();
