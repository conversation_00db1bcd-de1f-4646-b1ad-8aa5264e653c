export interface WithdrawalApprovedEmailData {
  userName: string;
  userEmail: string;
  amount: number;
  fee: number; // 手续费字段
  actualAmount: number; // 到账金额字段
  currency: string;
  withdrawalMethod: string;
  processedAt: string;
  transactionId?: string;
  estimatedArrival?: string;
}

export const withdrawalApprovedTemplate = (
  data: WithdrawalApprovedEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>提现到账通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #333; margin-bottom: 20px; text-align: center;">✅ 提现审核通过 - 已到账</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.userName}！您的提现申请已通过审核，资金已成功到账。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">申请金额：</span>
          <span style="color: #333; font-weight: bold; font-size: 16px; margin-left: 10px;">
            ${data.amount} ${data.currency}
          </span>
        </div>
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">手续费：</span>
          <span style="color: #dc3545; font-weight: bold; font-size: 16px; margin-left: 10px;">
            ${data.fee} ${data.currency}
          </span>
        </div>
        <div style="margin-bottom: 15px; border-top: 1px solid #eee; padding-top: 15px;">
          <span style="color: #666; font-size: 14px;">实际到账：</span>
          <span style="color: #28a745; font-weight: bold; font-size: 18px; margin-left: 10px;">
            ${data.actualAmount} ${data.currency}
          </span>
        </div>
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">提现方式：</span>
          <span style="color: #333; margin-left: 10px;">${data.withdrawalMethod}</span>
        </div>
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">到账时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.processedAt}</span>
        </div>
        ${
          data.transactionId
            ? `
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">交易ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.transactionId}</span>
        </div>
        `
            : ''
        }
      </div>
      
      <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #155724; margin: 0; font-size: 14px;">
          💡 <strong>温馨提示：</strong><br>
          • 资金已成功到账，请查收<br>
          • 如有疑问，请联系客服支持<br>
          • 感谢您使用我们的服务
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
