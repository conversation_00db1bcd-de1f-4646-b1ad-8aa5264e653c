export enum MembershipTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  BUSINESS = 'BUSINESS',
}

export const MEMBERSHIP_TIER_LABELS: Record<MembershipTier, string> = {
  [MembershipTier.FREE]: '免费版',
  [MembershipTier.PROFESSIONAL]: '专业版',
  [MembershipTier.BUSINESS]: '商业版',
};

export const MEMBERSHIP_TIER_PRICES: Record<MembershipTier, number> = {
  [MembershipTier.FREE]: 0,
  [MembershipTier.PROFESSIONAL]: 10,
  [MembershipTier.BUSINESS]: 30,
};

export interface MembershipBenefits {
  maxTasksPerMonth: number | null; // null表示无限制
  taskTypes: string[];
  feeRate: number; // 费率百分比
  whitelistSlots: number; // 白名单店铺名额（用于禁止发布委托）
  supportLevel: string;
}

export const MEMBERSHIP_BENEFITS: Record<MembershipTier, MembershipBenefits> = {
  [MembershipTier.FREE]: {
    maxTasksPerMonth: 3,
    taskTypes: ['基本委托类型'],
    feeRate: 2.0,
    whitelistSlots: 0,
    supportLevel: '普通客服支持',
  },
  [MembershipTier.PROFESSIONAL]: {
    maxTasksPerMonth: 30,
    taskTypes: ['基本委托类型'],
    feeRate: 1.0,
    whitelistSlots: 3,
    supportLevel: '优先客服支持',
  },
  [MembershipTier.BUSINESS]: {
    maxTasksPerMonth: null,
    taskTypes: ['基本委托类型', '纠纷委托类型'],
    feeRate: 0.2,
    whitelistSlots: 30,
    supportLevel: 'VIP专属客服支持',
  },
};

export interface UserMembership {
  id: string;
  userId: string;
  tier: MembershipTier;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  tasksUsedThisMonth: number;
  whitelistSlotsUsed: number; // 已使用的白名单名额
}

export enum TransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAW = 'WITHDRAW',
  TASK_FEE = 'TASK_FEE',
  COMMISSION = 'COMMISSION',
  MEMBERSHIP = 'MEMBERSHIP',
  REFUND = 'REFUND',
}

// Transaction type labels are now handled by translation files
// Use useTranslations('wallet') or useTranslations('transactions') to get labels

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

// Transaction status labels are now handled by translation files
// Use useTranslations('wallet') or useTranslations('transactions') to get labels

export interface WalletTransaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  description: string;
  createdAt: Date;
  completedAt?: Date;
  reference?: string; // 关联的委托ID或订单ID
}

export interface UserWallet {
  id: string;
  userId: string;
  balance: number;
  frozenAmount: number; // 冻结金额（用于押金等）
  totalIncome: number;
  totalExpense: number;
  updatedAt: Date;
}

// 商铺白名单接口（用于禁止发布委托）
export interface ShopWhitelist {
  id: string;
  userId: string;
  shopName: string;
  shopUrl: string;
  platform: string;
  reason: string; // 禁止原因
  createdAt: Date;
  isActive: boolean; // 是否生效（禁止发布委托）
}

export enum DepositMethod {
  ALIPAY = 'ALIPAY',
  WECHAT = 'WECHAT',
  BANK_CARD = 'BANK_CARD',
  PAYPAL = 'PAYPAL',
}

export const DEPOSIT_METHOD_LABELS: Record<DepositMethod, string> = {
  [DepositMethod.ALIPAY]: 'Alipay',
  [DepositMethod.WECHAT]: 'WeChat Pay',
  [DepositMethod.BANK_CARD]: '美元账户',
  [DepositMethod.PAYPAL]: 'PayPal',
};

export enum WithdrawMethod {
  ALIPAY = 'ALIPAY',
  WECHAT = 'WECHAT',
  BANK_CARD = 'BANK_CARD',
}

export const WITHDRAW_METHOD_LABELS: Record<WithdrawMethod, string> = {
  [WithdrawMethod.ALIPAY]: 'Alipay',
  [WithdrawMethod.WECHAT]: 'WeChat Pay',
  [WithdrawMethod.BANK_CARD]: '美元账户',
};
