import { UserRole } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { chargebackTypeSchema } from '@/types/rates';

// GET /api/admin/chargeback-types - 获取所有拒付类型
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const chargebackTypes = await prisma.chargebackType.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({
      success: true,
      data: chargebackTypes,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// POST /api/admin/chargeback-types - 创建新拒付类型
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validatedData = chargebackTypeSchema.parse(body);

    // 检查拒付类型名称是否已存在
    const existingChargebackType = await prisma.chargebackType.findFirst({
      where: { name: validatedData.name },
    });

    if (existingChargebackType) {
      return NextResponse.json(
        { success: false, message: '拒付类型名称已存在' },
        { status: 400 },
      );
    }

    const chargebackType = await prisma.chargebackType.create({
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: chargebackType,
      message: '拒付类型创建成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
