import { useQuery } from '@tanstack/react-query';

import {
  EvidenceUploadType,
  TaskStatus,
  EvidenceStatus,
} from '@/lib/types/task';

interface AcceptedTaskFilters {
  status?: string;
  page?: number;
  limit?: number;
}

interface AcceptedTask {
  id: string;
  platform: string;
  category: string;
  chargebackTypes: string[];
  paymentMethods: string[];
  // 原始ID字段用于酬金计算
  chargebackTypeIds: string[];
  paymentMethodIds: string[];
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  finalTotal: number;
  productUrl: string;
  productDescription: string;
  recipientName: string;
  recipientPhone: string;
  shippingAddress: string;
  listingTime: string;
  status: TaskStatus | string;
  evidenceStatus: EvidenceStatus | string;
  evidenceRejectReason?: string;
  evidenceUploadType: EvidenceUploadType | string;
  evidenceFiles: string[];
  cartScreenshots: string[];
  publisher: {
    id: string;
    nickname: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  acceptedAt?: string;
  expiresAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  deadline: string;
  logisticsDeadline?: string;

  // 审核时间相关
  orderReviewDeadline?: string; // 订单审核截止时间（提交订单后5小时）
  logisticsReviewDeadline?: string; // 物流审核截止时间（提交物流后24小时）
  deliveryDeadline?: string; // 确认收货截止时间（审核通过后30天）

  trackingNumber?: string;
  logisticsScreenshots: string[];
  reviewedAt?: string;
  reviewRejectReason?: string;
  orderNumber?: string;
  orderScreenshot?: string;
}

interface AcceptedTaskStats {
  total: number;
  inProgress: number;
  pendingLogistics: number;
  pendingReview: number;
  pendingDelivery: number;
  completed: number;
  expired: number;
  cancelled: number;
}

interface AcceptedTaskResponse {
  success: boolean;
  data: {
    tasks: AcceptedTask[];
    stats: AcceptedTaskStats;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export function useAcceptedTasks(filters: AcceptedTaskFilters = {}) {
  return useQuery<AcceptedTaskResponse>({
    queryKey: ['accepted-tasks', filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(
        `/api/user/accepted-tasks?${params.toString()}`,
      );

      if (!response.ok) {
        throw new Error('获取接受委托失败');
      }

      return response.json();
    },
    staleTime: 30 * 1000, // 30秒
    refetchOnWindowFocus: false,
  });
}

export type { AcceptedTask, AcceptedTaskStats, AcceptedTaskFilters };
