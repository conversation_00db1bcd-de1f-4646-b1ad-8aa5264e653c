'use client';

import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUpload } from '@/components/ui/file-upload';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  useWhitelistCheck,
  WhitelistCheckResult,
} from '@/hooks/use-whitelist-check';
import { PublishTaskForm } from '@/lib/types/publish';

interface BasicInfoStepProps {
  formData: PublishTaskForm;
  updateFormData: (updates: Partial<PublishTaskForm>) => void;
  onWhitelistResult?: (result: WhitelistCheckResult | null) => void;
}

export function BasicInfoStep({
  formData,
  updateFormData,
  onWhitelistResult,
}: BasicInfoStepProps) {
  const t = useTranslations('Publish');
  const tMessages = useTranslations('Messages');
  const [whitelistResult, setWhitelistResult] =
    useState<WhitelistCheckResult | null>(null);
  const [isCheckingWhitelist, setIsCheckingWhitelist] = useState(false);
  const [lastCheckedUrl, setLastCheckedUrl] = useState<string>('');

  // 验证状态
  const [urlValidationError, setUrlValidationError] = useState<string>('');
  const [descriptionValidationError, setDescriptionValidationError] =
    useState<string>('');

  const whitelistCheckMutation = useWhitelistCheck();

  // 检测白名单
  const checkWhitelist = useCallback(
    (url: string, forceCheck: boolean = false) => {
      if (url && isValidUrl(url)) {
        // 显示检测状态
        setIsCheckingWhitelist(true);

        // 如果URL内容没有变化且不强制检测，快速显示之前的结果
        if (url === lastCheckedUrl && !forceCheck && whitelistResult) {
          setTimeout(() => {
            setIsCheckingWhitelist(false);
          }, 300); // 短暂显示检测状态
          return;
        }

        setLastCheckedUrl(url);

        whitelistCheckMutation.mutate(
          { productUrl: url },
          {
            onSuccess: result => {
              setWhitelistResult(result);
              setIsCheckingWhitelist(false);

              if (result.isWhitelisted) {
                toast.error(tMessages(result.message), {
                  description: t('basicInfo.whitelistCheck.whitelisted'),
                });
              }
            },
            onError: () => {
              setIsCheckingWhitelist(false);
              setWhitelistResult(null);
            },
          },
        );
      }
    },
    [lastCheckedUrl, whitelistCheckMutation, whitelistResult, t, tMessages],
  );

  // 处理输入框失去焦点
  const handleProductUrlBlur = () => {
    const url = formData.productUrl?.trim() || '';

    // 先进行URL格式验证
    if (url && validateProductUrl(url)) {
      // 验证通过后再进行白名单检测
      checkWhitelist(url);
    } else if (!url) {
      // 如果输入框为空，清除结果
      setWhitelistResult(null);
      setLastCheckedUrl('');
      setUrlValidationError('');
    }
  };

  // 处理输入框内容变化
  const handleProductUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    updateFormData({ productUrl: value });

    // 清除之前的验证错误
    setUrlValidationError('');

    // 如果输入框为空，立即清除白名单检测结果
    if (!value || !value.trim()) {
      setWhitelistResult(null);
      setLastCheckedUrl('');
    }
  };

  // 通知父组件白名单检测结果
  useEffect(() => {
    if (onWhitelistResult) {
      onWhitelistResult(whitelistResult);
    }
  }, [whitelistResult, onWhitelistResult]);

  // 检查URL有效性
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // 验证商品链接是否以 https:// 开头
  const validateProductUrl = (url: string) => {
    if (!url.trim()) {
      setUrlValidationError('');
      return true;
    }

    if (!url.startsWith('https://')) {
      setUrlValidationError(
        t('platformSelection.validation.urlMustStartWithHttps'),
      );
      return false;
    }

    if (!isValidUrl(url)) {
      setUrlValidationError(t('platformSelection.validation.invalidUrl'));
      return false;
    }

    setUrlValidationError('');
    return true;
  };

  // 验证商品描述是否只包含英文字符
  const validateProductDescription = (description: string) => {
    if (!description.trim()) {
      setDescriptionValidationError('');
      return true;
    }

    // 只允许英文字母、数字、标点符号、空格
    const englishOnlyRegex =
      /^[a-zA-Z0-9\s.,!?;:'"()\-_@#$%&*+=<>{}[\]|\\/~`]*$/;

    if (!englishOnlyRegex.test(description)) {
      setDescriptionValidationError(
        t('platformSelection.validation.englishOnly'),
      );
      return false;
    }

    setDescriptionValidationError('');
    return true;
  };

  // 处理商品描述输入变化
  const handleProductDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    const value = e.target.value;

    // 实时验证输入内容
    if (validateProductDescription(value)) {
      updateFormData({ productDescription: value });
    } else {
      // 如果验证失败，仍然更新数据但显示错误信息
      updateFormData({ productDescription: value });
    }
  };

  return (
    <div className='space-y-6'>
      {/* 商品基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('basicInfo.title')}</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='productUrl'>{t('basicInfo.productUrl')} *</Label>
              <div className='relative'>
                <Input
                  id='productUrl'
                  type='url'
                  placeholder={t('basicInfo.productUrlPlaceholder')}
                  value={formData.productUrl || ''}
                  onChange={handleProductUrlChange}
                  onBlur={handleProductUrlBlur}
                  className={
                    whitelistResult?.isWhitelisted || urlValidationError
                      ? 'border-red-500'
                      : ''
                  }
                />
                {isCheckingWhitelist && (
                  <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
                    <Loader2 className='h-4 w-4 animate-spin text-muted-foreground' />
                  </div>
                )}
              </div>

              {/* URL验证错误 */}
              {urlValidationError && (
                <p className='mt-1 text-sm text-red-600'>
                  {urlValidationError}
                </p>
              )}

              {/* 白名单检测结果 */}
              {whitelistResult &&
                whitelistResult.isWhitelisted &&
                !urlValidationError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {tMessages(whitelistResult.message)}
                  </p>
                )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='unitPrice'>{t('basicInfo.unitPrice')} *</Label>
              <div className='relative'>
                <span className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground'>
                  $
                </span>
                <Input
                  id='unitPrice'
                  type='number'
                  step='0.01'
                  min='0'
                  placeholder='0.00'
                  className='pl-8'
                  value={formData.unitPrice || ''}
                  onChange={e =>
                    updateFormData({
                      unitPrice: parseFloat(e.target.value) || 0,
                    })
                  }
                />
              </div>
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='productDescription'>
              {t('basicInfo.productDescription')} *
            </Label>
            <Textarea
              id='productDescription'
              placeholder={t('basicInfo.productDescriptionPlaceholder')}
              rows={4}
              value={formData.productDescription || ''}
              onChange={handleProductDescriptionChange}
              className={descriptionValidationError ? 'border-red-500' : ''}
            />

            {/* 商品描述验证错误 */}
            {descriptionValidationError && (
              <p className='mt-1 text-sm text-red-600'>
                {descriptionValidationError}
              </p>
            )}

            {/* 提示信息 */}
            <p className='mt-1 text-sm text-muted-foreground'>
              {t('platformSelection.validation.englishOnlyHint')}
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='quantity'>{t('basicInfo.quantity')} *</Label>
              <Input
                id='quantity'
                type='number'
                min='1'
                placeholder={t('basicInfo.quantityPlaceholder')}
                value={formData.quantity || ''}
                onChange={e =>
                  updateFormData({
                    quantity: parseInt(e.target.value, 10) || 1,
                  })
                }
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='listingTime'>
                {t('basicInfo.listingTime')} *
              </Label>
              <Select
                value={formData.listingTime || '48'}
                onValueChange={value => updateFormData({ listingTime: value })}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t('basicInfo.listingTimePlaceholder')}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='24'>
                    {t('basicInfo.listingTimeOptions.24')}
                  </SelectItem>
                  <SelectItem value='48'>
                    {t('basicInfo.listingTimeOptions.48')}
                  </SelectItem>
                  <SelectItem value='72'>
                    {t('basicInfo.listingTimeOptions.72')}
                  </SelectItem>
                  <SelectItem value='96'>
                    {t('basicInfo.listingTimeOptions.96')}
                  </SelectItem>
                  <SelectItem value='120'>
                    {t('basicInfo.listingTimeOptions.120')}
                  </SelectItem>
                  <SelectItem value='144'>
                    {t('basicInfo.listingTimeOptions.144')}
                  </SelectItem>
                  <SelectItem value='168'>
                    {t('basicInfo.listingTimeOptions.168')}
                  </SelectItem>
                </SelectContent>
              </Select>
              <p className='text-xs text-muted-foreground'>
                {t('basicInfo.listingTimeDescription')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 收货地址 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('basicInfo.recipientInfo')}</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='recipientName'>
                {t('basicInfo.recipientName')} *
              </Label>
              <Input
                id='recipientName'
                placeholder={t('basicInfo.recipientNamePlaceholder')}
                value={formData.recipientName || ''}
                onChange={e =>
                  updateFormData({ recipientName: e.target.value })
                }
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='recipientPhone'>
                {t('basicInfo.recipientPhone')} *
              </Label>
              <Input
                id='recipientPhone'
                type='tel'
                placeholder={t('basicInfo.recipientPhonePlaceholder')}
                value={formData.recipientPhone || ''}
                onChange={e =>
                  updateFormData({ recipientPhone: e.target.value })
                }
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='shippingAddress'>
              {t('basicInfo.shippingAddress')} *
            </Label>
            <Textarea
              id='shippingAddress'
              placeholder={t('basicInfo.shippingAddressPlaceholder')}
              rows={3}
              value={formData.shippingAddress || ''}
              onChange={e =>
                updateFormData({ shippingAddress: e.target.value })
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* 购物车示范截图 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('basicInfo.cartScreenshot')} *</CardTitle>
          <p className='text-sm text-muted-foreground'>
            {t('basicInfo.cartScreenshotDescription')}
          </p>
        </CardHeader>
        <CardContent>
          <FileUpload
            value={formData.cartScreenshot || []}
            onChange={files => updateFormData({ cartScreenshot: files })}
            accept='image/*'
            multiple={true}
            maxFiles={5}
            maxSize={10}
            placeholder={t('basicInfo.cartScreenshotPlaceholder')}
            description={t('basicInfo.cartScreenshotFormats')}
          />
        </CardContent>
      </Card>
    </div>
  );
}
