'use client';

import { Edit, MoreHorizontal, CreditCard, Wallet } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { User } from '@/types/user';

import { BalanceDialog } from './balance-dialog';
import { EditUserDialog } from './edit-user-dialog';

interface UserActionsDropdownProps {
  user: User;
  onUserUpdate?: (user: User) => void;
}

export function UserActionsDropdown({
  user,
  onUserUpdate,
}: UserActionsDropdownProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isChargeDialogOpen, setIsChargeDialogOpen] = useState(false);
  const [isDeductDialogOpen, setIsDeductDialogOpen] = useState(false);

  const handleEditClick = () => {
    setIsEditDialogOpen(true);
  };

  const handleChargeClick = () => {
    setIsChargeDialogOpen(true);
  };

  const handleDeductClick = () => {
    setIsDeductDialogOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' className='h-8 w-8 p-0'>
            <span className='sr-only'>打开菜单</span>
            <MoreHorizontal className='h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuLabel>用户操作</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleEditClick}>
            <Edit className='mr-2 h-4 w-4' />
            编辑信息
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleChargeClick}>
            <CreditCard className='mr-2 h-4 w-4' />
            充值
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleDeductClick}>
            <Wallet className='mr-2 h-4 w-4' />
            扣款
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <EditUserDialog
        user={user}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onUserUpdate={onUserUpdate}
      />

      <BalanceDialog
        user={user}
        type='charge'
        open={isChargeDialogOpen}
        onOpenChange={setIsChargeDialogOpen}
        onUserUpdate={onUserUpdate}
      />

      <BalanceDialog
        user={user}
        type='deduct'
        open={isDeductDialogOpen}
        onOpenChange={setIsDeductDialogOpen}
        onUserUpdate={onUserUpdate}
      />
    </>
  );
}
