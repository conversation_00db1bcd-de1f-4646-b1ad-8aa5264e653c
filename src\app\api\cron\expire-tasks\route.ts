import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';

// 过期委托处理逻辑
async function processExpiredTasks() {
  const now = new Date();
  const results = {
    expiredRecruiting: 0,
    expiredInProgress: 0,
    expiredLogistics: 0,
    recruitingReturned: 0, // 放回委托大厅的数量
    total: 0,
  };

  // 1. 处理招募中的过期委托 (RECRUITING -> EXPIRED)
  const expiredRecruitingTasks = await prisma.task.findMany({
    where: {
      status: TaskStatus.RECRUITING,
      expiresAt: {
        lt: now,
      },
    },
    include: {
      publisher: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (expiredRecruitingTasks.length > 0) {
    await prisma.$transaction(async tx => {
      // 获取系统费率配置
      const systemRate = await tx.systemRate.findFirst();
      const evidenceRate = systemRate?.noEvidenceExtraRate || 2.0;

      for (const task of expiredRecruitingTasks) {
        // 更新委托状态为已过期
        await tx.task.update({
          where: { id: task.id },
          data: {
            status: TaskStatus.EXPIRED,
          },
        });

        // 计算证据费和非证据费
        const evidenceFee = (task.totalAmount * evidenceRate) / 100;
        const nonEvidenceFee = task.finalTotal - evidenceFee;

        // 退还非证据费到可用余额
        if (nonEvidenceFee > 0) {
          await tx.user.update({
            where: { id: task.publisherId },
            data: {
              balance: {
                increment: nonEvidenceFee,
              },
            },
          });

          // 创建非证据费退款记录
          await tx.walletTransaction.create({
            data: {
              userId: task.publisherId,
              type: 'REFUND',
              amount: nonEvidenceFee,
              status: 'COMPLETED',
              description: `委托过期退款（发布费用）- ${task.id}`,
              reference: task.id,
              completedAt: new Date(),
            },
          });
        }

        // 退还证据费：从冻结金额释放到可用余额
        if (evidenceFee > 0) {
          await tx.user.update({
            where: { id: task.publisherId },
            data: {
              frozenAmount: {
                decrement: evidenceFee,
              },
              balance: {
                increment: evidenceFee,
              },
            },
          });

          // 创建证据费退款记录
          await tx.walletTransaction.create({
            data: {
              userId: task.publisherId,
              type: 'REFUND',
              amount: evidenceFee,
              status: 'COMPLETED',
              description: `委托过期退款（证据费）- ${task.id}`,
              reference: task.id,
              completedAt: new Date(),
            },
          });
        }
      }
    });

    results.expiredRecruiting = expiredRecruitingTasks.length;
  }

  // 2. 处理进行中的过期委托 (IN_PROGRESS -> EXPIRED)
  // 查询所有进行中的委托，然后在代码中计算是否过期（支持驳回后的新deadline）
  const inProgressTasks = await prisma.task.findMany({
    where: {
      status: TaskStatus.IN_PROGRESS,
    },
    include: {
      publisher: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      accepter: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  // 筛选出真正过期的委托（考虑驳回后的新deadline）
  const expiredInProgressTasks = inProgressTasks.filter(task => {
    if (!task.acceptedAt) return false;

    // 检查是否被驳回，如果被驳回且有审核时间，使用审核时间+24小时作为deadline
    const taskData = task as any;
    let deadline: Date;

    if (taskData.reviewRejectReason && taskData.reviewedAt) {
      deadline = new Date(taskData.reviewedAt.getTime() + 24 * 60 * 60 * 1000);
    } else {
      deadline = new Date(task.acceptedAt.getTime() + 24 * 60 * 60 * 1000);
    }

    return now > deadline;
  });

  if (expiredInProgressTasks.length > 0) {
    // 获取系统费率配置
    const systemRate = await prisma.systemRate.findFirst();
    if (!systemRate) {
      throw new Error('系统配置错误：未找到费率配置');
    }

    // 分类处理：根据招募时间是否过期决定处理方式
    const tasksToRecruit = expiredInProgressTasks.filter(
      task => task.expiresAt && task.expiresAt > now,
    );
    const tasksToExpire = expiredInProgressTasks.filter(
      task => !task.expiresAt || task.expiresAt <= now,
    );

    await prisma.$transaction(async tx => {
      // 处理招募还有剩余时间的委托：放回委托大厅
      for (const task of tasksToRecruit) {
        if (!task.accepterId) continue;

        // 计算押金金额
        const totalPrice = task.unitPrice * task.quantity;
        const depositAmount = totalPrice * (systemRate.depositRatio / 100);

        // 更新委托状态为招募中，清除接单者信息
        await tx.task.update({
          where: { id: task.id },
          data: {
            status: TaskStatus.RECRUITING,
            accepterId: null,
            acceptedAt: null,
            // 清除相关的审核和物流信息
            reviewRejectReason: null,
            reviewedAt: null,
            logisticsDeadline: null,
          },
        });

        // 释放接单者押金（返还到可用余额）
        await tx.user.update({
          where: { id: task.accepterId },
          data: {
            frozenAmount: {
              decrement: depositAmount,
            },
            balance: {
              increment: depositAmount,
            },
          },
        });

        // 创建押金释放记录
        await tx.walletTransaction.create({
          data: {
            userId: task.accepterId,
            type: 'DEPOSIT',
            amount: depositAmount,
            status: 'COMPLETED',
            description: `委托超时押金释放 - ${task.id}`,
            reference: task.id,
            completedAt: new Date(),
          },
        });
      }

      // 处理招募时间也过期的委托：完全过期
      for (const task of tasksToExpire) {
        if (!task.accepterId) continue;

        // 计算押金金额
        const totalPrice = task.unitPrice * task.quantity;
        const depositAmount = totalPrice * (systemRate.depositRatio / 100);

        // 更新委托状态为已过期
        await tx.task.update({
          where: { id: task.id },
          data: {
            status: TaskStatus.EXPIRED,
          },
        });

        // 扣除接单者押金（押金已冻结，现在正式扣除）
        await tx.user.update({
          where: { id: task.accepterId },
          data: {
            frozenAmount: {
              decrement: depositAmount,
            },
          },
        });

        // 退款给发布者
        await tx.user.update({
          where: { id: task.publisherId },
          data: {
            balance: {
              increment: task.finalTotal,
            },
          },
        });

        // 创建押金扣除记录
        await tx.walletTransaction.create({
          data: {
            userId: task.accepterId,
            type: 'TASK_FEE',
            amount: -depositAmount,
            status: 'COMPLETED',
            description: `委托过期押金扣除 - ${task.id}`,
            reference: task.id,
            completedAt: new Date(),
          },
        });

        // 创建发布者退款记录
        await tx.walletTransaction.create({
          data: {
            userId: task.publisherId,
            type: 'REFUND',
            amount: task.finalTotal,
            status: 'COMPLETED',
            description: `委托过期退款 - ${task.id}`,
            reference: task.id,
            completedAt: new Date(),
          },
        });
      }
    });

    const inProgressTasksToExpire = tasksToExpire.length;
    const inProgressTasksToRecruit = tasksToRecruit.length;
    results.expiredInProgress = inProgressTasksToExpire;
    results.recruitingReturned += inProgressTasksToRecruit;
  }

  // 3. 处理等待物流单号的过期委托 (PENDING_LOGISTICS -> EXPIRED)
  const expiredLogisticsTasks = await prisma.task.findMany({
    where: {
      status: TaskStatus.PENDING_LOGISTICS,
      logisticsDeadline: {
        lt: now,
      },
    },
    include: {
      publisher: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      accepter: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (expiredLogisticsTasks.length > 0) {
    // 获取系统费率配置
    const systemRate = await prisma.systemRate.findFirst();
    if (!systemRate) {
      throw new Error('系统配置错误：未找到费率配置');
    }

    // 分类处理：根据招募时间是否过期决定处理方式
    const tasksToRecruit = expiredLogisticsTasks.filter(
      task => task.expiresAt && task.expiresAt > now,
    );
    const tasksToExpire = expiredLogisticsTasks.filter(
      task => !task.expiresAt || task.expiresAt <= now,
    );

    await prisma.$transaction(async tx => {
      // 处理招募还有剩余时间的委托：放回委托大厅
      for (const task of tasksToRecruit) {
        if (!task.accepterId) continue;

        // 计算押金金额
        const totalPrice = task.unitPrice * task.quantity;
        const depositAmount = totalPrice * (systemRate.depositRatio / 100);

        // 更新委托状态为招募中，清除接单者信息
        await tx.task.update({
          where: { id: task.id },
          data: {
            status: TaskStatus.RECRUITING,
            accepterId: null,
            acceptedAt: null,
            // 清除相关的审核和物流信息
            reviewRejectReason: null,
            reviewedAt: null,
            logisticsDeadline: null,
          },
        });

        // 释放接单者押金（返还到可用余额）
        await tx.user.update({
          where: { id: task.accepterId },
          data: {
            frozenAmount: {
              decrement: depositAmount,
            },
            balance: {
              increment: depositAmount,
            },
          },
        });

        // 创建押金释放记录
        await tx.walletTransaction.create({
          data: {
            userId: task.accepterId,
            type: 'DEPOSIT',
            amount: depositAmount,
            status: 'COMPLETED',
            description: `委托超时押金释放 - ${task.id}`,
            reference: task.id,
            completedAt: new Date(),
          },
        });
      }

      // 处理招募时间也过期的委托：完全过期
      for (const task of tasksToExpire) {
        if (!task.accepterId) continue;

        // 计算押金金额
        const totalPrice = task.unitPrice * task.quantity;
        const depositAmount = totalPrice * (systemRate.depositRatio / 100);

        // 更新委托状态为已过期
        await tx.task.update({
          where: { id: task.id },
          data: {
            status: TaskStatus.EXPIRED,
          },
        });

        // 扣除接单者押金
        await tx.user.update({
          where: { id: task.accepterId },
          data: {
            frozenAmount: {
              decrement: depositAmount,
            },
          },
        });

        // 退款给发布者
        await tx.user.update({
          where: { id: task.publisherId },
          data: {
            balance: {
              increment: task.finalTotal,
            },
          },
        });

        // 创建押金扣除记录
        await tx.walletTransaction.create({
          data: {
            userId: task.accepterId,
            type: 'TASK_FEE',
            amount: -depositAmount,
            status: 'COMPLETED',
            description: `委托过期押金扣除 - ${task.id}`,
            reference: task.id,
            completedAt: new Date(),
          },
        });

        // 创建发布者退款记录
        await tx.walletTransaction.create({
          data: {
            userId: task.publisherId,
            type: 'REFUND',
            amount: task.finalTotal,
            status: 'COMPLETED',
            description: `委托过期退款 - ${task.id}`,
            reference: task.id,
            completedAt: new Date(),
          },
        });
      }
    });

    const logisticsTasksToExpire = tasksToExpire.length;
    const logisticsTasksToRecruit = tasksToRecruit.length;
    results.expiredLogistics = logisticsTasksToExpire;
    results.recruitingReturned += logisticsTasksToRecruit;
  }

  results.total =
    results.expiredRecruiting +
    results.expiredInProgress +
    results.expiredLogistics +
    results.recruitingReturned;

  // 记录处理结果
  console.log(`委托过期处理完成：`, results);

  return {
    success: true,
    message: '委托过期处理完成',
    data: results,
  };
}

// 开发环境的GET请求支持（手动触发）
export async function GET(request: NextRequest) {
  try {
    // 只在开发环境允许GET请求
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: '生产环境不支持GET请求' },
        { status: 405 },
      );
    }

    const result = await processExpiredTasks();
    return NextResponse.json(result);
  } catch (error) {
    console.error('委托过期处理失败:', error);
    return NextResponse.json({ error: '委托过期处理失败' }, { status: 500 });
  }
}

// 定时委托：处理过期委托
export async function POST(request: NextRequest) {
  try {
    // 验证请求来源（简单的安全检查）
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const result = await processExpiredTasks();
    return NextResponse.json(result);
  } catch (error) {
    console.error('委托过期处理失败:', error);
    return NextResponse.json({ error: '委托过期处理失败' }, { status: 500 });
  }
}
