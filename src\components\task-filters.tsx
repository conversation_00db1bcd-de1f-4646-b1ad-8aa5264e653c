'use client';

import { Search, Filter, X, ChevronDown, Settings2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  useActivePlatforms,
  useActiveChargebackTypes,
  useActivePaymentMethods,
} from '@/hooks/use-publish-config';
import { TaskFilters } from '@/lib/types/task';

interface TaskFiltersProps {
  filters: TaskFilters;
  onFiltersChange: (filters: TaskFilters) => void;
}

export function TaskFiltersComponent({
  filters,
  onFiltersChange,
}: TaskFiltersProps) {
  const t = useTranslations('tasks');
  const [searchValue, setSearchValue] = useState(filters.search || '');

  // 获取数据库数据
  const { data: platforms = [], isLoading: platformsLoading } =
    useActivePlatforms();
  const { data: chargebackTypes = [], isLoading: chargebackTypesLoading } =
    useActiveChargebackTypes();
  const { data: paymentMethods = [], isLoading: paymentMethodsLoading } =
    useActivePaymentMethods();

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onFiltersChange({
      ...filters,
      search: value || undefined,
    });
  };

  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split('-') as [
      TaskFilters['sortBy'],
      TaskFilters['sortOrder'],
    ];
    onFiltersChange({
      ...filters,
      sortBy,
      sortOrder,
    });
  };

  const handlePlatformChange = (platformId: string, checked: boolean) => {
    const currentPlatforms = filters.platform || [];
    const newPlatforms = checked
      ? [...currentPlatforms, platformId]
      : currentPlatforms.filter(p => p !== platformId);

    onFiltersChange({
      ...filters,
      platform: newPlatforms.length > 0 ? newPlatforms : undefined,
    });
  };

  const handleChargebackTypeChange = (typeId: string, checked: boolean) => {
    const currentTypes = filters.chargebackType || [];
    const newTypes = checked
      ? [...currentTypes, typeId]
      : currentTypes.filter(t => t !== typeId);

    onFiltersChange({
      ...filters,
      chargebackType: newTypes.length > 0 ? newTypes : undefined,
    });
  };

  const handlePaymentMethodChange = (methodId: string, checked: boolean) => {
    const currentMethods = filters.paymentMethod || [];
    const newMethods = checked
      ? [...currentMethods, methodId]
      : currentMethods.filter(m => m !== methodId);

    onFiltersChange({
      ...filters,
      paymentMethod: newMethods.length > 0 ? newMethods : undefined,
    });
  };

  const clearFilters = () => {
    setSearchValue('');
    onFiltersChange({
      search: undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      platform: undefined,
      chargebackType: undefined,
      paymentMethod: undefined,
    });
  };

  const currentSort = `${filters.sortBy || 'createdAt'}-${filters.sortOrder || 'desc'}`;
  const hasActiveFilters = !!(
    filters.search ||
    filters.platform?.length ||
    filters.chargebackType?.length ||
    filters.paymentMethod?.length
  );

  return (
    <Card className='w-full'>
      <CardContent className='p-4'>
        <div className='flex flex-col md:flex-row gap-4 items-start md:items-center'>
          {/* 搜索框 */}
          <div className='flex-1 min-w-0 w-full md:w-auto'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              <Input
                placeholder={t('search.placeholder')}
                value={searchValue}
                onChange={e => handleSearchChange(e.target.value)}
                className='pl-10 w-full'
              />
            </div>
          </div>

          <div className='flex flex-wrap gap-2 items-center w-full md:w-auto'>
            {/* 排序选择器 */}
            <Select value={currentSort} onValueChange={handleSortChange}>
              <SelectTrigger className='w-full md:w-40'>
                <SelectValue placeholder={t('sorting.title')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='createdAt-desc'>
                  {t('sorting.latest')}
                </SelectItem>
                <SelectItem value='commission-desc'>
                  {t('sorting.rewardHighToLow')}
                </SelectItem>
                <SelectItem value='deadline-asc'>
                  {t('sorting.expiringSoon')}
                </SelectItem>
              </SelectContent>
            </Select>

            {/* 平台筛选 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant='outline' className='min-w-0'>
                  <Settings2 className='h-4 w-4 mr-2' />
                  {t('filters.platform')}
                  {filters.platform?.length ? (
                    <Badge
                      variant='secondary'
                      className='ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs'
                    >
                      {filters.platform.length}
                    </Badge>
                  ) : null}
                  <ChevronDown className='h-4 w-4 ml-2' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-56' align='start'>
                <div className='space-y-3'>
                  <h4 className='font-medium leading-none'>
                    {t('filters.platform')}
                  </h4>
                  <Separator />
                  <div className='space-y-2 max-h-60 overflow-y-auto'>
                    {platformsLoading ? (
                      <div className='text-sm text-muted-foreground'>
                        {t('messages.loading')}
                      </div>
                    ) : (
                      platforms.map(platform => (
                        <div
                          key={platform.id}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`platform-${platform.id}`}
                            checked={(filters.platform || []).includes(
                              platform.id,
                            )}
                            onCheckedChange={checked =>
                              handlePlatformChange(
                                platform.id,
                                checked as boolean,
                              )
                            }
                          />
                          <Label
                            htmlFor={`platform-${platform.id}`}
                            className='text-sm font-normal flex-1'
                          >
                            {platform.name}
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* 拒付类型筛选 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant='outline' className='min-w-0'>
                  {t('filters.chargebackType')}
                  {filters.chargebackType?.length ? (
                    <Badge
                      variant='secondary'
                      className='ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs'
                    >
                      {filters.chargebackType.length}
                    </Badge>
                  ) : null}
                  <ChevronDown className='h-4 w-4 ml-2' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-56' align='start'>
                <div className='space-y-3'>
                  <h4 className='font-medium leading-none'>
                    {t('filters.chargebackType')}
                  </h4>
                  <Separator />
                  <div className='space-y-2 max-h-60 overflow-y-auto'>
                    {chargebackTypesLoading ? (
                      <div className='text-sm text-muted-foreground'>
                        {t('messages.loading')}
                      </div>
                    ) : (
                      chargebackTypes.map(type => (
                        <div
                          key={type.id}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`chargeback-${type.id}`}
                            checked={(filters.chargebackType || []).includes(
                              type.id,
                            )}
                            onCheckedChange={checked =>
                              handleChargebackTypeChange(
                                type.id,
                                checked as boolean,
                              )
                            }
                          />
                          <Label
                            htmlFor={`chargeback-${type.id}`}
                            className='text-sm font-normal flex-1'
                          >
                            {type.name}
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* 支付方式筛选 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant='outline' className='min-w-0'>
                  {t('filters.paymentMethod')}
                  {filters.paymentMethod?.length ? (
                    <Badge
                      variant='secondary'
                      className='ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs'
                    >
                      {filters.paymentMethod.length}
                    </Badge>
                  ) : null}
                  <ChevronDown className='h-4 w-4 ml-2' />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-56' align='start'>
                <div className='space-y-3'>
                  <h4 className='font-medium leading-none'>
                    {t('filters.paymentMethod')}
                  </h4>
                  <Separator />
                  <div className='space-y-2 max-h-60 overflow-y-auto'>
                    {paymentMethodsLoading ? (
                      <div className='text-sm text-muted-foreground'>
                        {t('messages.loading')}
                      </div>
                    ) : (
                      paymentMethods.map(method => (
                        <div
                          key={method.id}
                          className='flex items-center space-x-2'
                        >
                          <Checkbox
                            id={`payment-${method.id}`}
                            checked={(filters.paymentMethod || []).includes(
                              method.id,
                            )}
                            onCheckedChange={checked =>
                              handlePaymentMethodChange(
                                method.id,
                                checked as boolean,
                              )
                            }
                          />
                          <Label
                            htmlFor={`payment-${method.id}`}
                            className='text-sm font-normal flex-1'
                          >
                            {method.name}
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {/* 清除筛选 */}
            {hasActiveFilters && (
              <Button
                variant='ghost'
                size='sm'
                onClick={clearFilters}
                className='text-muted-foreground hover:text-foreground'
              >
                <X className='h-4 w-4 mr-1' />
                {t('filters.clear')}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
