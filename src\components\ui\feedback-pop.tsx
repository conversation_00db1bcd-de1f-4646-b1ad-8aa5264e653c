'use client';
import { MessageCircle, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from './button';

export function FeedbackPop() {
  const t = useTranslations('Feedback');
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [feedback, setFeedback] = useState<string>('');

  const handleSendFeedback = () => {
    if (!feedback.trim()) {
      toast.error(t('validation.required'), {
        description: t('validation.enterContent'),
      });
      return;
    }

    // 模拟发送反馈
    toast.success(t('success.send'), {
      description: t('success.thankYou'),
    });

    setFeedback('');
    setIsOpen(false);
  };

  return (
    <div className='fixed bottom-4 right-4 z-50'>
      {!isOpen ? (
        <Button
          onClick={() => setIsOpen(true)}
          className='rounded-full h-12 w-12 p-0 shadow-lg'
        >
          <MessageCircle className='h-6 w-6' />
        </Button>
      ) : (
        <div className='bg-white dark:bg-gray-800 rounded-lg shadow-xl border p-4 w-80'>
          <div className='flex justify-between items-center mb-3'>
            <h3 className='font-semibold text-lg'>{t('title')}</h3>
            <Button
              variant='ghost'
              size='icon'
              onClick={() => setIsOpen(false)}
              className='h-6 w-6 p-0'
            >
              <X className='h-4 w-4' />
            </Button>
          </div>
          <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
            {t('description')}
          </p>
          <textarea
            value={feedback}
            onChange={e => setFeedback(e.target.value)}
            className='w-full p-2 border rounded-md resize-none dark:bg-gray-700 dark:border-gray-600 dark:text-white'
            rows={3}
            placeholder={t('placeholder')}
          />
          <div className='flex gap-2 mt-3'>
            <Button size='sm' className='flex-1' onClick={handleSendFeedback}>
              {t('send')}
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setIsOpen(false)}
            >
              {t('cancel')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
