import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import { currencyConverter } from '@/lib/payment/currency-converter';

const convertSchema = z.object({
  amount: z.number().positive('金额必须大于0'),
  from: z.string().min(3).max(3, '货币代码必须是3位字符'),
  to: z.string().min(3).max(3, '货币代码必须是3位字符'),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const data = convertSchema.parse(body);

    const result = await currencyConverter.convertCurrency(
      data.amount,
      data.from,
      data.to,
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Currency conversion error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : 'Currency conversion failed',
      },
      { status: 500 },
    );
  }
}
