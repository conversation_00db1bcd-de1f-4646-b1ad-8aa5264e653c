/**
 * 财务邮件集成工具
 * 提供充值相关邮件发送的便捷方法和错误处理
 */

import {
  sendDepositSuccessEmail,
  sendDepositFailedEmail,
  type DepositSuccessEmailData,
  type DepositFailedEmailData,
} from './email';

// 充值结果类型
export interface DepositResult {
  success: boolean;
  transactionId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  processedAt: string;
  newBalance?: number;
  failureReason?: string;
}

// 用户信息类型
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  registrationLanguage?: 'zh' | 'en';
}

/**
 * 发送充值成功邮件的便捷方法
 */
export async function notifyDepositSuccess(
  user: UserInfo,
  depositResult: DepositResult,
  options?: {
    retryCount?: number;
    retryDelay?: number;
  },
): Promise<{ success: boolean; error?: string }> {
  if (!depositResult.success || !depositResult.newBalance) {
    throw new Error('Invalid deposit result for success notification');
  }

  const emailData: DepositSuccessEmailData = {
    userName: user.name,
    userEmail: user.email,
    amount: depositResult.amount,
    currency: depositResult.currency,
    transactionId: depositResult.transactionId,
    paymentMethod: depositResult.paymentMethod,
    processedAt: depositResult.processedAt,
    newBalance: depositResult.newBalance,
    language: user.registrationLanguage || 'zh',
  };

  return await sendEmailWithRetry(
    () => sendDepositSuccessEmail(user.email, emailData, { userId: user.id }),
    options?.retryCount || 3,
    options?.retryDelay || 1000,
  );
}

/**
 * 发送充值失败邮件的便捷方法
 */
export async function notifyDepositFailure(
  user: UserInfo,
  depositResult: DepositResult,
  options?: {
    retryCount?: number;
    retryDelay?: number;
  },
): Promise<{ success: boolean; error?: string }> {
  if (depositResult.success || !depositResult.failureReason) {
    throw new Error('Invalid deposit result for failure notification');
  }

  const emailData: DepositFailedEmailData = {
    userName: user.name,
    userEmail: user.email,
    amount: depositResult.amount,
    currency: depositResult.currency,
    transactionId: depositResult.transactionId,
    paymentMethod: depositResult.paymentMethod,
    failureReason: depositResult.failureReason,
    failedAt: depositResult.processedAt,
    language: user.registrationLanguage || 'zh',
  };

  return await sendEmailWithRetry(
    () => sendDepositFailedEmail(user.email, emailData, { userId: user.id }),
    options?.retryCount || 3,
    options?.retryDelay || 1000,
  );
}

/**
 * 带重试机制的邮件发送
 */
async function sendEmailWithRetry(
  emailSender: () => Promise<{ success: boolean; error?: string }>,
  maxRetries: number,
  retryDelay: number,
): Promise<{ success: boolean; error?: string }> {
  let lastError: string | undefined;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await emailSender();

      if (result.success) {
        // 如果之前有失败，记录恢复日志
        if (attempt > 1) {
          console.log(`邮件发送在第 ${attempt} 次尝试后成功`);
        }
        return result;
      }

      lastError = result.error;

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        console.warn(
          `邮件发送失败，第 ${attempt} 次尝试，${retryDelay}ms 后重试:`,
          result.error,
        );
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        // 指数退避：每次重试延迟时间翻倍
        retryDelay *= 2;
      }
    } catch (error) {
      lastError = error instanceof Error ? error.message : '未知错误';

      if (attempt < maxRetries) {
        console.warn(
          `邮件发送异常，第 ${attempt} 次尝试，${retryDelay}ms 后重试:`,
          lastError,
        );
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryDelay *= 2;
      }
    }
  }

  // 所有重试都失败了
  console.error(`邮件发送失败，已重试 ${maxRetries} 次:`, lastError);
  return {
    success: false,
    error: `邮件发送失败（已重试 ${maxRetries} 次）: ${lastError}`,
  };
}

/**
 * 批量发送充值通知邮件
 */
export async function batchNotifyDeposits(
  notifications: Array<{
    user: UserInfo;
    depositResult: DepositResult;
    type: 'success' | 'failure';
  }>,
  options?: {
    concurrency?: number;
    retryCount?: number;
    retryDelay?: number;
  },
): Promise<{
  success: number;
  failed: number;
  results: Array<{ user: UserInfo; success: boolean; error?: string }>;
}> {
  const concurrency = options?.concurrency || 5;
  const results: Array<{ user: UserInfo; success: boolean; error?: string }> =
    [];

  // 分批处理，避免同时发送太多邮件
  for (let i = 0; i < notifications.length; i += concurrency) {
    const batch = notifications.slice(i, i + concurrency);

    const batchPromises = batch.map(async ({ user, depositResult, type }) => {
      try {
        const result =
          type === 'success'
            ? await notifyDepositSuccess(user, depositResult, options)
            : await notifyDepositFailure(user, depositResult, options);

        return { user, ...result };
      } catch (error) {
        return {
          user,
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // 批次间稍作延迟，避免邮件服务过载
    if (i + concurrency < notifications.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  const successCount = results.filter(r => r.success).length;
  const failedCount = results.length - successCount;

  console.log(`批量邮件发送完成: 成功 ${successCount}, 失败 ${failedCount}`);

  return {
    success: successCount,
    failed: failedCount,
    results,
  };
}

/**
 * 验证邮件数据完整性
 */
export function validateDepositEmailData(
  user: UserInfo,
  depositResult: DepositResult,
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证用户信息
  if (!user.name?.trim()) {
    errors.push('用户姓名不能为空');
  }

  if (!user.email?.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)) {
    errors.push('用户邮箱格式无效');
  }

  // 验证充值结果
  if (!depositResult.transactionId?.trim()) {
    errors.push('交易ID不能为空');
  }

  if (depositResult.amount <= 0) {
    errors.push('充值金额必须大于0');
  }

  if (!depositResult.currency?.trim()) {
    errors.push('货币类型不能为空');
  }

  if (!depositResult.paymentMethod?.trim()) {
    errors.push('支付方式不能为空');
  }

  if (!depositResult.processedAt?.trim()) {
    errors.push('处理时间不能为空');
  }

  // 成功时验证余额
  if (
    depositResult.success &&
    (depositResult.newBalance === undefined || depositResult.newBalance < 0)
  ) {
    errors.push('充值成功时必须提供有效的新余额');
  }

  // 失败时验证失败原因
  if (!depositResult.success && !depositResult.failureReason?.trim()) {
    errors.push('充值失败时必须提供失败原因');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
