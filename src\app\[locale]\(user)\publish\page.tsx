'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { PublishTaskContent } from '@/components/publish-task-content';
import { UserPageLayout } from '@/components/user-page-layout';

export default function PublishPage() {
  const t = useTranslations('Publish');

  useEffect(() => {
    document.title = `${t('navigation.title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('navigation.title')}
      breadcrumbPage={t('navigation.breadcrumb')}
      href='/publish'
    >
      <PublishTaskContent />
    </UserPageLayout>
  );
}
