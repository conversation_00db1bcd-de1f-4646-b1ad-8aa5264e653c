import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { PaymentManager } from '@/lib/payment/payment-manager';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问，请先登录',
    invalidAmount: '充值金额必须大于0',
    selectPaymentMethod: '请选择支付方式',
    paymentMethodUnavailable: '选择的支付方式不可用',
    minAmountError: '最低充值金额为$',
    providerUnavailable: '支付提供商不可用',
    orderCreated: '充值订单创建成功',
    serverError: '服务器内部错误',
  },
  en: {
    unauthorized: 'Unauthorized access, please login first',
    invalidAmount: 'Deposit amount must be greater than 0',
    selectPaymentMethod: 'Please select a payment method',
    paymentMethodUnavailable: 'Selected payment method is unavailable',
    minAmountError: 'Minimum deposit amount is $',
    providerUnavailable: 'Payment provider unavailable',
    orderCreated: 'Deposit order created successfully',
    serverError: 'Internal server error',
  },
};

export async function POST(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 基础充值请求验证Schema
    const depositSchema = z.object({
      amount: z.number().positive(t.invalidAmount),
      method: z.string().min(1, t.selectPaymentMethod),
    });

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: t.unauthorized,
        },
        { status: 401 },
      );
    }

    const body = await request.json();
    const data = depositSchema.parse(body);
    const userId = (session.user as any).id;

    // 验证支付方式是否可用
    const paymentConfigs = await prisma.paymentConfig.findMany({
      where: { isEnabled: true },
    });

    let selectedProvider: string = '';
    let isValidMethod = false;
    let feeRate: number = 0;
    let minAmount: number = 0;

    // 检查支付方式是否在已启用的配置中，并获取手续费率和最低金额
    for (const config of paymentConfigs) {
      if (
        config.provider === 'yunpay' &&
        (data.method === 'alipay' ||
          data.method === 'wxpay' ||
          data.method === 'paypal')
      ) {
        const settings = config.settings as any;
        const paymentMethods = settings.paymentMethods || {};
        if (paymentMethods[data.method]?.enabled) {
          selectedProvider = 'yunpay';
          isValidMethod = true;
          feeRate = paymentMethods[data.method].feeRate || 0;
          minAmount = paymentMethods[data.method].minAmount || 1;
          break;
        }
      } else if (
        config.provider === 'nowpayments' &&
        data.method === 'crypto'
      ) {
        const settings = config.settings as any;
        selectedProvider = 'nowpayments';
        isValidMethod = true;
        feeRate = settings.feeRate || 0;
        minAmount = settings.minAmount || 1;
        break;
      }
    }

    if (!isValidMethod) {
      return NextResponse.json(
        {
          success: false,
          message: t.paymentMethodUnavailable,
        },
        { status: 400 },
      );
    }

    // 验证最低充值金额
    if (data.amount < minAmount) {
      return NextResponse.json(
        {
          success: false,
          message: `${t.minAmountError}${minAmount.toFixed(2)}`,
        },
        { status: 400 },
      );
    }

    // 计算手续费和总支付金额
    const depositAmount = data.amount; // 用户想要充值的金额
    const feeAmount = (depositAmount * feeRate) / 100; // 手续费金额
    const totalPaymentAmount = depositAmount + feeAmount; // 用户需要支付的总金额

    // 生成订单号
    const orderNo = `DEPOSIT_${Date.now()}_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // 构建回调URL
    const notifyUrl = `${process.env.NEXTAUTH_URL}/api/payments/notify/${selectedProvider}`;
    const returnUrl = `${process.env.NEXTAUTH_URL}/wallet?status=success`;

    // 创建支付订单（统一使用美元）
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        orderNo,
        provider: selectedProvider!,
        paymentMethod: data.method,
        amount: totalPaymentAmount, // 总支付金额（美元）
        currency: 'USD', // 系统统一使用美元
        description: `钱包充值 - ${depositAmount}（含手续费${feeAmount.toFixed(2)}）`,
        userId,
        status: 'PENDING',
        expiredAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟过期
        notifyUrl,
        returnUrl,
        metadata: {
          depositAmount, // 实际充值金额（美元）
          feeAmount: feeAmount.toFixed(2), // 手续费金额（美元）
          feeRate, // 手续费率
        },
      },
    });

    // 创建钱包交易记录（记录实际充值金额）
    const walletTransaction = await prisma.walletTransaction.create({
      data: {
        userId,
        type: 'DEPOSIT',
        amount: depositAmount, // 实际充值金额
        status: 'PENDING',
        description: `充值 - ${data.method === 'alipay' ? 'Alipay' : data.method === 'wxpay' ? 'WeChat Pay' : data.method === 'paypal' ? 'PayPal' : 'NOWPayments (Cryptocurrency)'}（手续费${feeAmount.toFixed(2)}）`,
        reference: orderNo,
        depositMethod:
          data.method === 'alipay'
            ? 'ALIPAY'
            : data.method === 'wxpay'
              ? 'WECHAT'
              : data.method === 'paypal'
                ? 'PAYPAL'
                : undefined,
      },
    });

    // 初始化支付管理器
    const paymentManager = new PaymentManager();

    try {
      // 获取支付提供商
      const provider = await paymentManager.getProvider(selectedProvider);
      if (!provider) {
        throw new Error(`${t.providerUnavailable} ${selectedProvider}`);
      }

      // 创建支付（统一使用美元）
      const paymentParams = {
        orderNo,
        amount: totalPaymentAmount, // 美元金额
        currency: 'USD', // 统一使用美元
        paymentMethod: data.method,
        description: `钱包充值 - ${depositAmount}（含手续费${feeAmount.toFixed(2)}）`,
        returnUrl,
        notifyUrl,
        userId,
      };

      const paymentResult = await provider.createPayment(paymentParams);

      // 更新支付订单信息，包括货币转换信息
      const updateData: any = {
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        thirdOrderNo: paymentResult.thirdOrderNo,
      };

      // 如果有货币转换信息，记录到数据库
      if (paymentResult.currencyConversion) {
        const conversion = paymentResult.currencyConversion;
        updateData.originalAmount = conversion.originalAmount;
        updateData.originalCurrency = conversion.originalCurrency;
        updateData.convertedAmount = conversion.convertedAmount;
        updateData.convertedCurrency = conversion.convertedCurrency;
        updateData.exchangeRate = conversion.exchangeRate;
        updateData.exchangeRateSource = conversion.source;
      }

      await prisma.paymentOrder.update({
        where: { orderNo },
        data: updateData,
      });

      // 记录支付日志
      await prisma.paymentLog.create({
        data: {
          orderNo,
          action: 'CREATE_DEPOSIT',
          request: { ...data, feeRate, feeAmount, totalPaymentAmount },
          response: JSON.parse(JSON.stringify(paymentResult)),
          status: 'SUCCESS',
          message: 'Deposit payment created successfully',
        },
      });

      return NextResponse.json({
        success: true,
        message: t.orderCreated,
        data: {
          orderNo,
          depositAmount, // 实际充值金额
          feeAmount: parseFloat(feeAmount.toFixed(2)), // 手续费金额
          feeRate, // 手续费率
          totalPaymentAmount: parseFloat(totalPaymentAmount.toFixed(2)), // 总支付金额
          paymentUrl: paymentResult.paymentUrl,
          qrCode: paymentResult.qrCode,
          expiredAt: paymentOrder.expiredAt,
          method: data.method,
        },
      });
    } catch (paymentError: any) {
      // 更新订单和交易状态为失败
      await Promise.all([
        prisma.paymentOrder.update({
          where: { orderNo },
          data: { status: 'FAILED' },
        }),
        prisma.walletTransaction.updateMany({
          where: { reference: orderNo },
          data: { status: 'FAILED' },
        }),
      ]);

      // 记录错误日志
      await prisma.paymentLog.create({
        data: {
          orderNo,
          action: 'CREATE_DEPOSIT',
          request: { ...data, feeRate, feeAmount, totalPaymentAmount },
          response: { error: paymentError.message },
          status: 'FAILED',
          message: paymentError.message,
        },
      });

      throw paymentError;
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        success: false,
        message:
          error instanceof Error ? error.message : messages.zh.serverError,
      },
      { status: 500 },
    );
  }
}
