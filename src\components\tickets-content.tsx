'use client';

import {
  Ticket,
  Plus,
  Search,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  Calendar,
  User,
  Tag,
  Loader2,
} from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useMemo } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useResponsive } from '@/hooks/use-responsive';
import {
  useTickets,
  useCreateTicket,
  useTicket,
  useReplyTicket,
  useUpdateTicketStatus,
} from '@/hooks/use-tickets';
import {
  TICKET_TYPE_LABELS,
  TICKET_STATUS_LABELS,
  TICKET_PRIORITY_LABELS,
  TicketType,
  TicketStatus,
  TicketPriority,
} from '@/lib/types/ticket';

// 响应式工单筛选标签组件
interface ResponsiveTicketTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  t: (key: string) => string;
}

function ResponsiveTicketTabs({
  activeTab,
  onTabChange,
  t,
}: ResponsiveTicketTabsProps) {
  const { isMobile, isTablet, breakpoint } = useResponsive();

  const tabItems = [
    { value: 'all', label: t('filters.all'), shortLabel: 'All' },
    { value: 'PENDING', label: t('status.PENDING'), shortLabel: 'Pending' },
    {
      value: 'IN_PROGRESS',
      label: t('status.IN_PROGRESS'),
      shortLabel: 'Progress',
    },
    { value: 'RESOLVED', label: t('status.RESOLVED'), shortLabel: 'Resolved' },
    { value: 'CLOSED', label: t('status.CLOSED'), shortLabel: 'Closed' },
    { value: 'CANCELLED', label: t('status.CANCELLED'), shortLabel: 'Cancel' },
  ];

  // 移动端：使用水平滚动
  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block'>{item.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  // 平板端：使用2行3列布局
  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label}
            </button>
          ))}
        </div>
      </Tabs>
    );
  }

  // 桌面端：保持原有的6列网格布局
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
      <TabsList className='grid w-full grid-cols-6'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

export function TicketsContent() {
  const t = useTranslations('tickets');
  const locale = useLocale();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [replyContent, setReplyContent] = useState('');

  // 新工单表单状态
  const [newTicket, setNewTicket] = useState({
    title: '',
    description: '',
    type: TicketType.OTHER,
    priority: TicketPriority.MEDIUM,
  });

  // API调用
  const {
    data: ticketsData,
    isLoading,
    error,
  } = useTickets({
    page: 1,
    limit: 50,
    search: searchQuery || undefined,
    status: activeTab !== 'all' ? (activeTab as TicketStatus) : undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { data: selectedTicket } = useTicket(selectedTicketId || '');
  const createTicketMutation = useCreateTicket();
  const replyTicketMutation = useReplyTicket(selectedTicketId || '');
  const updateStatusMutation = useUpdateTicketStatus(selectedTicketId || '');

  // 筛选工单（前端额外筛选）
  const filteredTickets = useMemo(() => {
    const tickets = ticketsData?.tickets || [];
    let filtered = tickets;

    // 按状态筛选
    if (activeTab !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === activeTab);
    }

    return filtered.sort(
      (a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    );
  }, [ticketsData?.tickets, activeTab]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case TicketStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300';
      case TicketStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300';
      case TicketStatus.RESOLVED:
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300';
      case TicketStatus.CLOSED:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
      case TicketStatus.CANCELLED:
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case TicketPriority.LOW:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
      case TicketPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300';
      case TicketPriority.HIGH:
        return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300';
      case TicketPriority.URGENT:
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case TicketStatus.PENDING:
        return <Clock className='h-4 w-4' />;
      case TicketStatus.IN_PROGRESS:
        return <AlertCircle className='h-4 w-4' />;
      case TicketStatus.RESOLVED:
        return <CheckCircle className='h-4 w-4' />;
      case TicketStatus.CLOSED:
        return <XCircle className='h-4 w-4' />;
      case TicketStatus.CANCELLED:
        return <XCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  // 格式化日期时间
  const formatDateTime = (date: string | Date) => {
    return new Date(date).toLocaleString(locale === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 处理创建工单
  const handleCreateTicket = () => {
    if (!newTicket.title.trim() || !newTicket.description.trim()) {
      toast.error(t('messages.completeInfoRequired'), {
        description: t('messages.titleDescriptionRequired'),
      });
      return;
    }

    createTicketMutation.mutate(newTicket, {
      onSuccess: () => {
        setNewTicket({
          title: '',
          description: '',
          type: TicketType.OTHER,
          priority: TicketPriority.MEDIUM,
        });
        setIsCreateDialogOpen(false);
      },
    });
  };

  // 查看工单详情
  const handleViewTicket = (ticketId: string) => {
    setSelectedTicketId(ticketId);
    setIsDetailDialogOpen(true);
  };

  // 发送回复
  const handleSendReply = () => {
    if (!replyContent.trim()) {
      toast.error(t('messages.replyContentRequired'));
      return;
    }

    replyTicketMutation.mutate(
      { content: replyContent },
      {
        onSuccess: () => {
          setReplyContent('');
        },
      },
    );
  };

  // 关闭工单
  const handleCloseTicket = () => {
    if (!selectedTicketId) return;

    updateStatusMutation.mutate(
      { status: TicketStatus.CLOSED },
      {
        onSuccess: () => {
          // 关闭详情对话框
          setIsDetailDialogOpen(false);
          // 清空选中的工单
          setSelectedTicketId(null);
        },
        onError: (error: Error) => {
          // 错误处理已在钩子中处理，这里可以添加额外的UI反馈
          console.error('Failed to close ticket:', error);
        },
      },
    );
  };

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-start justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>{t('title')}</h1>
          <p className='text-muted-foreground'>{t('description')}</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className='h-4 w-4 mr-2' />
              {t('actions.submit')}
            </Button>
          </DialogTrigger>
          <DialogContent className='sm:max-w-[600px]'>
            <DialogHeader>
              <DialogTitle>{t('form.dialogTitle')}</DialogTitle>
              <DialogDescription>
                {t('form.dialogDescription')}
              </DialogDescription>
            </DialogHeader>
            <div className='grid gap-4 py-4'>
              <div className='grid gap-2'>
                <Label htmlFor='title'>{t('form.title')}</Label>
                <Input
                  id='title'
                  placeholder={t('form.titlePlaceholder')}
                  value={newTicket.title}
                  onChange={e =>
                    setNewTicket(prev => ({ ...prev, title: e.target.value }))
                  }
                />
              </div>
              <div className='grid gap-2'>
                <Label htmlFor='type'>{t('form.type')}</Label>
                <Select
                  value={newTicket.type}
                  onValueChange={value =>
                    setNewTicket(prev => ({
                      ...prev,
                      type: value as TicketType,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(TICKET_TYPE_LABELS).map(([key, label]) => (
                      <SelectItem key={key} value={key}>
                        {t(`types.${key}`)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='grid gap-2'>
                <Label htmlFor='priority'>{t('form.priority')}</Label>
                <Select
                  value={newTicket.priority}
                  onValueChange={value =>
                    setNewTicket(prev => ({
                      ...prev,
                      priority: value as TicketPriority,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(TICKET_PRIORITY_LABELS).map(
                      ([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {t(`priority.${key}`)}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className='grid gap-2'>
                <Label htmlFor='description'>{t('form.description')}</Label>
                <Textarea
                  id='description'
                  placeholder={t('form.descriptionPlaceholder')}
                  className='min-h-[120px]'
                  value={newTicket.description}
                  onChange={e =>
                    setNewTicket(prev => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant='outline'
                onClick={() => setIsCreateDialogOpen(false)}
              >
                {t('form.cancel')}
              </Button>
              <Button
                onClick={handleCreateTicket}
                disabled={createTicketMutation.isPending}
              >
                {createTicketMutation.isPending && (
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                )}
                {createTicketMutation.isPending
                  ? t('form.submitting')
                  : t('form.submit')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 搜索和筛选 */}
      <div className='flex flex-col sm:flex-row gap-4'>
        <div className='relative flex-1'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder={t('search.placeholder')}
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className='pl-10'
          />
        </div>
      </div>

      {/* 工单列表 - 响应式设计 */}
      <ResponsiveTicketTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        t={t}
      />

      {/* 工单内容 */}
      <div className='mt-6'>
        <div className='space-y-4'>
          {isLoading ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <Loader2 className='h-8 w-8 animate-spin text-muted-foreground mb-4' />
                <p className='text-sm text-muted-foreground'>
                  {t('messages.loading')}
                </p>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <XCircle className='h-12 w-12 text-red-500 mb-4' />
                <h3 className='text-lg font-medium text-red-600 mb-2'>
                  {t('messages.loadError')}
                </h3>
                <p className='text-sm text-muted-foreground'>
                  {t('messages.loadError')}
                </p>
              </CardContent>
            </Card>
          ) : filteredTickets.length === 0 ? (
            <Card>
              <CardContent className='flex flex-col items-center justify-center py-12'>
                <Ticket className='h-12 w-12 text-muted-foreground mb-4' />
                <h3 className='text-lg font-medium text-muted-foreground mb-2'>
                  {t('list.empty')}
                </h3>
                <p className='text-sm text-muted-foreground'>
                  {searchQuery
                    ? t('search.noResults')
                    : t('list.emptyDescription')}
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredTickets.map(ticket => (
              <Card
                key={ticket.id}
                className='cursor-pointer hover:shadow-md transition-shadow'
                onClick={() => handleViewTicket(ticket.id)}
              >
                <CardHeader className='pb-3'>
                  <div className='flex items-start justify-between gap-4'>
                    <div className='flex-1 min-w-0'>
                      <div className='flex items-center gap-2 mb-2'>
                        <span className='text-sm text-muted-foreground'>
                          #{ticket.id}
                        </span>
                        <Badge
                          variant='outline'
                          className={getPriorityColor(ticket.priority)}
                        >
                          {t(`priority.${ticket.priority}`) || ticket.priority}
                        </Badge>
                        <Badge variant='outline' className='text-xs'>
                          <Tag className='h-3 w-3 mr-1' />
                          {t(`types.${ticket.type}`) || ticket.type}
                        </Badge>
                      </div>
                      <CardTitle className='text-lg leading-tight'>
                        {ticket.title}
                      </CardTitle>
                    </div>
                    <Badge className={getStatusColor(ticket.status)}>
                      {getStatusIcon(ticket.status)}
                      <span className='ml-1'>
                        {t(`status.${ticket.status}`) || ticket.status}
                      </span>
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className='pt-0'>
                  <p className='text-sm text-muted-foreground mb-4 line-clamp-2'>
                    {ticket.description}
                  </p>
                  <div className='flex items-center justify-between text-xs text-muted-foreground'>
                    <div className='flex items-center gap-4'>
                      <div className='flex items-center gap-1'>
                        <Calendar className='h-3 w-3' />
                        <span>
                          {t('details.createdLabel')}
                          {formatDateTime(ticket.createdAt)}
                        </span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Clock className='h-3 w-3' />
                        <span>
                          {t('details.updatedLabel')}
                          {formatDateTime(ticket.updatedAt)}
                        </span>
                      </div>
                    </div>
                    {ticket._count && ticket._count.replies > 0 && (
                      <div className='flex items-center gap-1'>
                        <MessageSquare className='h-3 w-3' />
                        <span>
                          {ticket._count.replies}
                          {t('details.repliesCount')}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* 工单详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className='sm:max-w-[700px] max-h-[80vh] overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>
              {selectedTicket ? selectedTicket.title : t('details.title')}
            </DialogTitle>
          </DialogHeader>

          {selectedTicket && (
            <>
              <div className='flex items-start justify-between gap-4 mb-4'>
                <div className='flex-1 min-w-0'>
                  <div className='flex items-center gap-2 mb-2'>
                    <span className='text-sm text-muted-foreground'>
                      #{selectedTicket.id}
                    </span>
                    <Badge
                      variant='outline'
                      className={getPriorityColor(selectedTicket.priority)}
                    >
                      {t(`priority.${selectedTicket.priority}`) ||
                        selectedTicket.priority}
                    </Badge>
                    <Badge variant='outline' className='text-xs'>
                      <Tag className='h-3 w-3 mr-1' />
                      {t(`types.${selectedTicket.type}`) || selectedTicket.type}
                    </Badge>
                  </div>
                </div>
                <Badge className={getStatusColor(selectedTicket.status)}>
                  {getStatusIcon(selectedTicket.status)}
                  <span className='ml-1'>
                    {t(`status.${selectedTicket.status}`) ||
                      selectedTicket.status}
                  </span>
                </Badge>
              </div>

              <div className='flex items-center gap-4 text-sm text-muted-foreground mb-6'>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>
                    {t('details.createdAtLabel')}
                    {formatDateTime(selectedTicket.createdAt)}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Clock className='h-4 w-4' />
                  <span>
                    {t('details.lastUpdatedLabel')}
                    {formatDateTime(selectedTicket.updatedAt)}
                  </span>
                </div>
              </div>

              <div className='space-y-6'>
                {/* 工单描述 */}
                <div>
                  <h4 className='font-medium mb-2'>
                    {t('details.description')}
                  </h4>
                  <div className='bg-muted/50 p-4 rounded-lg'>
                    <p className='text-sm whitespace-pre-wrap'>
                      {selectedTicket.description}
                    </p>
                  </div>
                </div>

                {/* 回复列表 */}
                {selectedTicket.replies &&
                  selectedTicket.replies.length > 0 && (
                    <div>
                      <h4 className='font-medium mb-4'>
                        {t('details.replies')}
                      </h4>
                      <div className='space-y-4'>
                        {selectedTicket.replies.map(reply => (
                          <div
                            key={reply.id}
                            className={`flex gap-3 ${reply.isStaff ? 'flex-row' : 'flex-row-reverse'}`}
                          >
                            <div
                              className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                                reply.isStaff
                                  ? 'bg-blue-100 text-blue-600'
                                  : 'bg-green-100 text-green-600'
                              }`}
                            >
                              <User className='h-4 w-4' />
                            </div>
                            <div
                              className={`flex-1 ${reply.isStaff ? 'mr-12' : 'ml-12'}`}
                            >
                              <div className='flex items-center gap-2 mb-1'>
                                <span className='text-sm font-medium'>
                                  {reply.authorName}
                                </span>
                                {reply.isStaff && (
                                  <Badge
                                    variant='secondary'
                                    className='text-xs'
                                  >
                                    {t('details.staff')}
                                  </Badge>
                                )}
                                <span className='text-xs text-muted-foreground'>
                                  {formatDateTime(reply.createdAt)}
                                </span>
                              </div>
                              <div
                                className={`p-3 rounded-lg text-sm ${
                                  reply.isStaff
                                    ? 'bg-blue-50 text-blue-900 dark:bg-blue-900/20 dark:text-blue-100'
                                    : 'bg-green-50 text-green-900 dark:bg-green-900/20 dark:text-green-100'
                                }`}
                              >
                                <p className='whitespace-pre-wrap'>
                                  {reply.content}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* 如果工单状态允许，显示回复区域 */}
                {selectedTicket.status !== TicketStatus.CLOSED &&
                  selectedTicket.status !== TicketStatus.CANCELLED && (
                    <div>
                      <h4 className='font-medium mb-2'>
                        {t('details.addReply')}
                      </h4>
                      <div className='space-y-4'>
                        <Textarea
                          placeholder={t('details.replyPlaceholder')}
                          value={replyContent}
                          onChange={e => setReplyContent(e.target.value)}
                          className='min-h-[100px]'
                        />
                        <div className='flex gap-2'>
                          <Button
                            onClick={handleSendReply}
                            disabled={replyTicketMutation.isPending}
                            className='flex-1'
                          >
                            {replyTicketMutation.isPending && (
                              <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                            )}
                            {t('details.sendReply')}
                          </Button>
                          {selectedTicket.status !== TicketStatus.CLOSED &&
                            selectedTicket.status !==
                              TicketStatus.CANCELLED && (
                              <Button
                                variant='outline'
                                onClick={handleCloseTicket}
                                disabled={updateStatusMutation.isPending}
                              >
                                {updateStatusMutation.isPending && (
                                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                                )}
                                {t('details.closeTicket')}
                              </Button>
                            )}
                        </div>
                      </div>
                    </div>
                  )}
              </div>
            </>
          )}

          {!selectedTicket && (
            <div className='text-center py-8'>
              <Loader2 className='h-8 w-8 animate-spin mx-auto mb-2' />
              <p className='text-sm text-muted-foreground'>
                {t('details.loadingDetails')}
              </p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
