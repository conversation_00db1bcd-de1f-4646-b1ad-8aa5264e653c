const fs = require('fs');
const path = require('path');

// 验证JSON文件是否有效且无重复键
function validateJSONFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // 尝试解析JSON
    const parsed = JSON.parse(content);

    // 重新序列化并比较，如果有重复键，解析后的对象会丢失重复的键
    const serialized = JSON.stringify(parsed, null, 2);

    return {
      valid: true,
      hasContent: Object.keys(parsed).length > 0,
      keyCount: countAllKeys(parsed),
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message,
    };
  }
}

// 递归计算所有键的数量
function countAllKeys(obj) {
  let count = 0;

  function traverse(current) {
    if (
      typeof current === 'object' &&
      current !== null &&
      !Array.isArray(current)
    ) {
      for (const key in current) {
        if (current.hasOwnProperty(key)) {
          count++;
          traverse(current[key]);
        }
      }
    }
  }

  traverse(obj);
  return count;
}

// 检查两个文件的键结构一致性
function checkKeyConsistency(enFile, zhFile) {
  try {
    const enContent = JSON.parse(fs.readFileSync(enFile, 'utf8'));
    const zhContent = JSON.parse(fs.readFileSync(zhFile, 'utf8'));

    const enKeys = getAllKeyPaths(enContent).sort();
    const zhKeys = getAllKeyPaths(zhContent).sort();

    const missingInZh = enKeys.filter(key => !zhKeys.includes(key));
    const missingInEn = zhKeys.filter(key => !enKeys.includes(key));

    return {
      consistent: missingInZh.length === 0 && missingInEn.length === 0,
      enKeyCount: enKeys.length,
      zhKeyCount: zhKeys.length,
      missingInZh: missingInZh.length,
      missingInEn: missingInEn.length,
      missingInZhKeys: missingInZh.slice(0, 5),
      missingInEnKeys: missingInEn.slice(0, 5),
    };
  } catch (error) {
    return {
      error: error.message,
    };
  }
}

// 获取所有键路径
function getAllKeyPaths(obj, prefix = '') {
  const paths = [];

  function traverse(current, currentPath) {
    if (
      typeof current === 'object' &&
      current !== null &&
      !Array.isArray(current)
    ) {
      for (const key in current) {
        if (current.hasOwnProperty(key)) {
          const newPath = currentPath ? `${currentPath}.${key}` : key;
          paths.push(newPath);
          traverse(current[key], newPath);
        }
      }
    }
  }

  traverse(obj, prefix);
  return paths;
}

// 最终验证所有翻译文件
function finalValidation() {
  const messagesDir = path.join(__dirname, 'messages');
  const results = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: 0,
      validFiles: 0,
      invalidFiles: 0,
      consistentPairs: 0,
      inconsistentPairs: 0,
      totalKeys: 0,
    },
    fileValidation: [],
    consistencyCheck: [],
    errors: [],
  };

  console.log('🔍 开始最终验证所有翻译文件...\n');

  // 验证所有JSON文件
  ['en', 'zh'].forEach(lang => {
    const langDir = path.join(messagesDir, lang);
    if (fs.existsSync(langDir)) {
      const files = fs
        .readdirSync(langDir)
        .filter(file => file.endsWith('.json'));

      files.forEach(file => {
        const filePath = path.join(langDir, file);
        const relativePath = path.relative(__dirname, filePath);
        results.summary.totalFiles++;

        const validation = validateJSONFile(filePath);

        if (validation.valid) {
          results.summary.validFiles++;
          results.summary.totalKeys += validation.keyCount;
          console.log(
            `✅ ${relativePath} - 有效 (${validation.keyCount} 个键)`
          );
        } else {
          results.summary.invalidFiles++;
          results.errors.push({ file: relativePath, error: validation.error });
          console.log(`❌ ${relativePath} - 无效: ${validation.error}`);
        }

        results.fileValidation.push({
          file: relativePath,
          ...validation,
        });
      });
    }
  });

  console.log('\n🔄 检查中英文键结构一致性...');

  // 检查键结构一致性
  const enDir = path.join(messagesDir, 'en');
  const zhDir = path.join(messagesDir, 'zh');

  if (fs.existsSync(enDir) && fs.existsSync(zhDir)) {
    const enFiles = fs
      .readdirSync(enDir)
      .filter(file => file.endsWith('.json'));

    enFiles.forEach(file => {
      const enFile = path.join(enDir, file);
      const zhFile = path.join(zhDir, file);

      if (fs.existsSync(zhFile)) {
        const consistency = checkKeyConsistency(enFile, zhFile);

        if (consistency.error) {
          console.log(`❌ ${file}: ${consistency.error}`);
          results.errors.push({ file, error: consistency.error });
        } else if (consistency.consistent) {
          results.summary.consistentPairs++;
          console.log(
            `✅ ${file}: 键结构一致 (${consistency.enKeyCount} 个键)`
          );
        } else {
          results.summary.inconsistentPairs++;
          console.log(`⚠️  ${file}: 键结构不一致`);
          console.log(
            `   英文: ${consistency.enKeyCount} 个键, 中文: ${consistency.zhKeyCount} 个键`
          );

          if (consistency.missingInZh > 0) {
            console.log(
              `   中文缺失 ${consistency.missingInZh} 个键: ${consistency.missingInZhKeys.join(', ')}${consistency.missingInZh > 5 ? '...' : ''}`
            );
          }

          if (consistency.missingInEn > 0) {
            console.log(
              `   英文缺失 ${consistency.missingInEn} 个键: ${consistency.missingInEnKeys.join(', ')}${consistency.missingInEn > 5 ? '...' : ''}`
            );
          }
        }

        results.consistencyCheck.push({
          file,
          ...consistency,
        });
      }
    });
  }

  console.log('\n📊 最终验证结果总结:');
  console.log(`总文件数: ${results.summary.totalFiles}`);
  console.log(`有效文件: ${results.summary.validFiles}`);
  console.log(`无效文件: ${results.summary.invalidFiles}`);
  console.log(`键结构一致的文件对: ${results.summary.consistentPairs}`);
  console.log(`键结构不一致的文件对: ${results.summary.inconsistentPairs}`);
  console.log(`总键数: ${results.summary.totalKeys}`);
  console.log(`错误数: ${results.errors.length}`);

  // 保存结果
  fs.writeFileSync(
    path.join(__dirname, 'final-validation-results.json'),
    JSON.stringify(results, null, 2)
  );

  console.log('\n📄 详细结果已保存到 final-validation-results.json');

  if (
    results.summary.invalidFiles === 0 &&
    results.summary.inconsistentPairs === 0
  ) {
    console.log('\n🎉 所有翻译文件验证通过！重复键问题已完全解决！');
  } else {
    console.log('\n⚠️  仍有问题需要解决，请查看详细结果文件');
  }

  return results;
}

// 运行最终验证
if (require.main === module) {
  finalValidation();
}

module.exports = { finalValidation, validateJSONFile, checkKeyConsistency };
