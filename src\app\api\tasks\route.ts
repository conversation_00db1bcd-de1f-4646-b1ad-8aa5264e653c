import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';
// 翻译消息
const messages = {
  zh: {
    serverError: '服务器内部错误',
  },
  en: {
    serverError: 'Internal server error',
  },
};

// 强制动态渲染
export const dynamic = 'force-dynamic';

// 获取委托列表API
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const search = searchParams.get('search');
    const platformIds = searchParams
      .get('platform')
      ?.split(',')
      .filter(Boolean);
    const chargebackTypeIds = searchParams
      .get('chargebackType')
      ?.split(',')
      .filter(Boolean);
    const paymentMethodIds = searchParams
      .get('paymentMethod')
      ?.split(',')
      .filter(Boolean);
    const statusList = searchParams
      .get('status')
      ?.split(',')
      .filter(Boolean) as TaskStatus[];
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // 构建where条件
    const where: any = {
      // 只显示正在招募的委托（可以被接受的委托）
      status: TaskStatus.RECRUITING,
      // 过滤掉已过期的委托
      expiresAt: {
        gt: new Date(),
      },
    };

    // 搜索筛选
    if (search) {
      where.OR = [
        {
          title: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          productDescription: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // 平台筛选
    if (platformIds?.length) {
      where.platformId = {
        in: platformIds,
      };
    }

    // 拒付类型筛选
    if (chargebackTypeIds?.length) {
      where.chargebackTypeIds = {
        hasSome: chargebackTypeIds,
      };
    }

    // 支付方式筛选
    if (paymentMethodIds?.length) {
      where.paymentMethodIds = {
        hasSome: paymentMethodIds,
      };
    }

    // 状态筛选（委托大厅只能显示RECRUITING状态的委托）
    if (statusList?.length) {
      // 只允许筛选RECRUITING状态，忽略其他状态请求
      const validStatuses = statusList.filter(
        status => status === TaskStatus.RECRUITING,
      );
      if (validStatuses.length > 0) {
        where.status = TaskStatus.RECRUITING; // 保持原有逻辑
      } else {
        // 如果请求的状态都不是RECRUITING，返回空结果
        where.status = 'INVALID_STATUS';
      }
    }

    // 构建排序
    const orderBy: any = {};
    if (sortBy === 'commission') {
      // 酬金排序需要自定义逻辑
      orderBy.finalTotal = sortOrder;
    } else if (sortBy === 'deadline') {
      orderBy.expiresAt = sortOrder;
    } else {
      orderBy.createdAt = sortOrder;
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询委托
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        select: {
          id: true,
          title: true,
          productUrl: true,
          productDescription: true,
          quantity: true,
          unitPrice: true,
          totalAmount: true,
          finalTotal: true,
          listingTime: true,
          expiresAt: true,
          chargebackTypeIds: true,
          paymentMethodIds: true,
          evidenceUploadType: true,
          evidenceStatus: true,
          evidenceFiles: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          accepter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.task.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        tasks,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}
