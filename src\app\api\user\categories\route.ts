import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问，请先登录',
    serverError: '服务器内部错误',
  },
  en: {
    unauthorized: 'Unauthorized access, please login first',
    serverError: 'Internal server error',
  },
};

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: t.unauthorized },
        { status: 401 },
      );
    }

    // 获取启用的分类列表
    const categories = await prisma.category.findMany({
      where: { status: 'ACTIVE' },
      select: {
        id: true,
        name: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { name: 'asc' },
    });

    return NextResponse.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}
