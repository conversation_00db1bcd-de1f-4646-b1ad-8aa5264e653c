{"signin": {"title": "Sign In", "description": "Sign in to your RefundGo account", "pageTitle": "Welcome Back", "pageDescription": "Sign in to your task management account", "email": "Email", "emailPlaceholder": "<EMAIL>", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot password?", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Enter 6-digit code", "sendCode": "Send Code", "sending": "Sending", "resendAfter": "s to resend", "loginButton": "Sign In", "verificationLogin": "Code Login", "passwordLogin": "Password Login", "noAccount": "Don't have an account?", "signUpNow": "Sign up now", "errors": {"fillAllFields": "Please fill in all required fields", "invalidEmail": "Please enter a valid email address", "enterEmail": "Please enter your email address first", "enterEmailAddress": "Please enter your email address", "enterVerificationCode": "Please enter verification code", "loginFailed": "<PERSON><PERSON> failed", "wrongCredentials": "Incorrect email or password, please try again", "networkError": "Network error, please try again", "verificationCodeError": "Verification code is incorrect or expired, please try again", "sendCodeFailed": "Failed to send verification code", "forgotPasswordSoon": "Forgot password feature coming soon"}, "success": {"loginSuccess": "Login successful", "redirecting": "Redirecting...", "codeSent": "Verification code sent", "checkEmail": "Please check your email for the verification code"}}, "signup": {"title": "Sign Up", "description": "Create your RefundGo account", "pageTitle": "Create Account", "pageDescription": "Register your task management account", "username": "Username", "usernamePlaceholder": "Enter your username", "email": "Email", "emailPlaceholder": "<EMAIL>", "password": "Password", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter your password", "emailVerificationCode": "Email Verification Code", "verificationCodePlaceholder": "Enter 6-digit code", "sendCode": "Send Code", "resendCode": "Resend Code", "sending": "Sending", "resendAfter": "s to resend", "signUpButton": "Send Verification Code", "completeSignUp": "Complete Registration", "codeSentTo": "Verification code sent to", "reenterInfo": "Re-enter registration information", "hasAccount": "Already have an account?", "signInNow": "Sign in now", "errors": {"fillAllFields": "Please fill in all required fields", "invalidEmail": "Please enter a valid email address", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters", "enterVerificationCode": "Please enter verification code", "signUpFailed": "Registration failed", "networkError": "Network error, please try again", "verificationCodeError": "Verification code is incorrect or expired", "sendCodeFailed": "Failed to send verification code"}, "success": {"signUpSuccess": "Registration successful", "pleaseLogin": "Please sign in with your email and password", "codeSent": "Verification code sent", "checkEmail": "Please check your email for the verification code"}}, "navigation": {"backToHome": "Back to Home", "switchLanguage": "Switch Language"}, "rightPanel": {"signin": {"title": "Welcome Back to RefundGo", "subtitle": "World's First Chargeback Task Platform"}, "signup": {"title": "Start Your Task Journey", "subtitle": "Secure and Efficient Task Platform"}, "features": {"privacy": {"title": "Privacy Protection", "description": "Task information security"}, "efficiency": {"title": "Efficient Processing", "description": "Massive user acceptance"}}, "stats": {"users": "Users", "acceptanceRate": "Acceptance Rate", "volume": "Transaction Volume"}}, "footer": {"agreement": "By continuing, you agree to our", "terms": "Terms of Service", "and": "and", "privacy": "Privacy Policy", "period": "."}}