'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { UserPageLayout } from '@/components/user-page-layout';
import { WalletContent } from '@/components/wallet-content';

export default function WalletPage() {
  const t = useTranslations('wallet');

  useEffect(() => {
    document.title = `${t('navigation.title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('navigation.title')}
      breadcrumbPage={t('navigation.breadcrumb')}
      href='/wallet'
    >
      <WalletContent />
    </UserPageLayout>
  );
}
