'use client';

import {
  CheckSquare,
  Home,
  Shield,
  LayoutDashboard,
  ClipboardList,
  Plus,
  CheckCircle,
  Upload,
  Crown,
  Wallet,
  Store,
  Ticket,
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import * as React from 'react';


import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useUserMembership } from '@/hooks/use-user-membership';
import { MEMBER_PLAN_MAP } from '@/lib/constants';

import { RefundGoLogoIcon } from './refund-go-logo';

// 导航数据
const getAppData = (user: any, t: any) => ({
  user: {
    name: user?.name || t('defaultUser'),
    email: user?.email || '<EMAIL>',
    avatar: user?.image || '/avatars/default.jpg',
  },
  navMain: [
    {
      title: t('navigation.dashboard'),
      url: '/dashboard',
      icon: Home,
      isActive: true,
    },
    {
      title: t('navigation.taskHall'),
      url: '/tasks',
      icon: ClipboardList,
    },
    {
      title: t('navigation.publishTask'),
      url: '/publish',
      icon: Plus,
    },
    {
      title: t('navigation.taskManagement'),
      url: '#',
      icon: CheckSquare,
      items: [
        {
          title: t('navigation.acceptedTasks'),
          url: '/my-accepted-tasks',
          icon: CheckCircle,
        },
        {
          title: t('navigation.publishedTasks'),
          url: '/my-published-tasks',
          icon: Upload,
        },
      ],
    },
    {
      title: t('navigation.ticketManagement'),
      url: '/tickets',
      icon: Ticket,
    },
    {
      title: t('navigation.memberCenter'),
      url: '#',
      icon: Crown,
      items: [
        {
          title: t('navigation.myWallet'),
          url: '/wallet',
          icon: Wallet,
        },
        {
          title: t('navigation.membership'),
          url: '/membership',
          icon: Crown,
        },
        {
          title: t('navigation.shopWhitelist'),
          url: '/whitelist',
          icon: Store,
        },
      ],
    },
    {
      title: t('navigation.accountSecurity'),
      url: '/profile',
      icon: Shield,
    },
    {
      title: t('navigation.backToHome'),
      url: '/',
      icon: Home,
    },
  ],
});

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  const { data: membership } = useUserMembership();
  const t = useTranslations('Navigation');
  const data = getAppData(session?.user, t);

  // 获取会员套餐名称
  const getMembershipDisplayName = () => {
    if (!membership?.memberPlan) return t('personalPlan');

    const planType = membership.memberPlan as keyof typeof MEMBER_PLAN_MAP;
    // 使用翻译而不是硬编码的中文
    switch (planType) {
      case 'FREE':
        return t('personalPlan');
      case 'PRO':
        return t('proPlan');
      case 'BUSINESS':
        return t('businessPlan');
      default:
        return t('personalPlan');
    }
  };

  // 获取VIP等级用于logo颜色
  const getVipLevel = (): 'basic' | 'premium' | 'vip' | 'enterprise' => {
    if (!membership?.memberPlan) return 'basic';

    const planType = membership.memberPlan as keyof typeof MEMBER_PLAN_MAP;
    switch (planType) {
      case 'FREE':
        return 'basic';
      case 'PRO':
        return 'premium';
      case 'BUSINESS':
        return 'vip';
      default:
        return 'basic';
    }
  };

  return (
    <Sidebar collapsible='icon' {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size='lg'
              className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
            >
              <RefundGoLogoIcon
                size="sm"
                className="mr-2"
                vipLevel={getVipLevel()}
              />
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>RefundGo</span>
                <span className='truncate text-xs'>
                  {getMembershipDisplayName()}
                </span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
