import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { UploadedFile } from '@/hooks/use-file-upload';

interface SubmitEvidenceData {
  taskId: string;
  files: UploadedFile[];
}

interface SubmitEvidenceResponse {
  success: boolean;
  message: string;
}

// 提交证据的API函数
async function submitEvidence(
  data: SubmitEvidenceData,
): Promise<SubmitEvidenceResponse> {
  // 将上传的文件信息转换为文件URL数组
  const evidenceFileUrls = data.files.map(file => file.fileUrl);

  const response = await fetch(`/api/tasks/${data.taskId}/submit-evidence`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      evidenceFiles: evidenceFileUrls,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || '提交证据失败');
  }

  return response.json();
}

// 使用提交证据的hook
export function useSubmitEvidence() {
  const t = useTranslations('messages');
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: submitEvidence,
    onSuccess: data => {
      toast.success(t('success.submit'), {
        description: '您的证据已提交，请等待审核',
      });

      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: ['published-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
    onError: (error: Error) => {
      toast.error(t('error.submit'), {
        description: error.message || '请重试或联系客服',
      });
    },
  });
}
