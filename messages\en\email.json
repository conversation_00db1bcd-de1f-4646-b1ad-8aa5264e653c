{"common": {"brandName": "RefundGo", "greeting": "Hello", "regards": "Best regards", "team": "RefundGo Team", "footer": {"copyright": "© 2024 RefundGo. All rights reserved.", "unsubscribe": "If you no longer wish to receive these emails, please", "unsubscribeLink": "click here to unsubscribe", "contact": "If you have any questions, please contact us: <EMAIL>"}, "buttons": {"viewDetails": "View Details", "login": "Login Now", "contact": "Contact Support", "goToWebsite": "Visit Website"}}, "subjects": {"verification": {"register": "Email Verification Code - Complete Registration", "login": "Login Verification Code - RefundGo", "resetPassword": "Password Reset Verification Code - RefundGo", "verifyCurrentEmail": "Current Email Verification Code - RefundGo", "changeEmail": "Change Email Verification Code - RefundGo", "default": "Verification Code - RefundGo"}, "notification": {"newUser": "New User Registration Notification - RefundGo", "taskCompleted": "Task Completed Notification - RefundGo", "taskCancelled": "Task Cancelled Notification - RefundGo", "taskAccepted": "Task Accepted Notification - RefundGo", "taskReviewApproved": "Task Review Approved Notification - RefundGo", "taskReviewRejected": "Task Review Rejected Notification - RefundGo", "evidenceReviewApproved": "Evidence Review Approved Notification - RefundGo", "evidenceReviewRejected": "Evidence Review Rejected Notification - RefundGo", "logisticsReview": "Order Logistics Information Review Required - RefundGo", "withdrawalApproved": "Withdrawal Approved Notification - RefundGo", "withdrawalRejected": "Withdrawal Request Rejected Notification - RefundGo"}}, "verification": {"title": "Verification Code", "actions": {"register": {"title": "Complete Your Registration", "description": "Welcome to RefundGo! Please use the verification code below to complete your account registration:"}, "login": {"title": "Login Verification", "description": "You are attempting to log into your RefundGo account. Please use the verification code below:"}, "resetPassword": {"title": "Password Reset", "description": "You have requested to reset your password. Please use the verification code below to proceed:"}, "verifyCurrentEmail": {"title": "Email Verification", "description": "Please use the verification code below to verify your current email address:"}, "changeEmail": {"title": "Email Change Verification", "description": "You are updating your email address. Please use the verification code below to confirm this change:"}}, "codeLabel": "Your verification code:", "expiryNotice": "This verification code will expire in {minutes} minutes.", "securityNotice": "If you did not request this verification code, please ignore this email and ensure your account is secure.", "noReply": "This is an automated message. Please do not reply to this email."}, "notifications": {"newUser": {"title": "New User Registration Notification", "content": "A new user has registered an account:", "userInfo": "User Information:", "email": "Email:", "signInTime": "Registration Time:", "action": "Please monitor the new user's usage in a timely manner."}, "taskCompleted": {"publisher": {"title": "Task Completed Notification", "greeting": "Congratulations! Your published task has been successfully completed.", "taskInfo": "Task Information:", "taskId": "Task ID:", "accepter": "Accepter:", "completedAt": "Completed At:", "action": "You can log in to view detailed information."}, "accepter": {"title": "Task Completed Notification", "greeting": "Congratulations! The task you accepted has been successfully completed.", "taskInfo": "Task Information:", "taskId": "Task ID:", "publisher": "Publisher:", "completedAt": "Completed At:", "reward": "Reward Earned:", "action": "The reward has been sent to your account, please log in to check."}}, "taskCancelled": {"accepter": {"title": "Task Cancelled Notification", "greeting": "We're sorry, the task you accepted has been cancelled.", "taskInfo": "Task Information:", "taskId": "Task ID:", "publisher": "Publisher:", "cancelledAt": "Cancelled At:", "reason": "Cancellation Reason:", "compensation": "Compensation Details:", "action": "If you have any questions, please contact customer service."}, "publisher": {"title": "Task Cancelled Notification", "greeting": "Your task has been successfully cancelled.", "taskInfo": "Task Information:", "taskId": "Task ID:", "cancelledAt": "Cancelled At:", "refund": "Refund Details:", "action": "The refund will arrive within 3-5 business days."}}, "taskAccepted": {"title": "Task Accepted Notification", "greeting": "Good news! Your published task has been accepted.", "taskInfo": "Task Information:", "taskId": "Task ID:", "accepter": "Accepter:", "acceptedAt": "Accepted At:", "nextSteps": "Next Steps:", "steps": ["The accepter will complete the task within the specified time", "You can check the task progress at any time", "Please confirm receipt promptly after completion"], "action": "Please log in to view detailed information."}}, "taskReview": {"approved": {"accepter": {"title": "Task Review Approved Notification", "greeting": "Good news! Your submitted task information has been approved.", "taskInfo": "Task Information:", "taskId": "Task ID:", "publisher": "Publisher:", "reviewedAt": "Reviewed At:", "nextSteps": "Next, you need to:", "steps": ["Wait for the publisher to confirm receipt", "The reward will be automatically distributed after confirmation"], "action": "Please log in to view detailed information."}}, "rejected": {"accepter": {"title": "Task Review Rejected Notification", "greeting": "We're sorry, your submitted task information was not approved.", "taskInfo": "Task Information:", "taskId": "Task ID:", "publisher": "Publisher:", "reviewedAt": "Reviewed At:", "reason": "Rejection Reason:", "nextSteps": "You need to:", "steps": ["Modify the information based on feedback", "Resubmit for review"], "action": "Please log in to resubmit."}, "publisher": {"title": "Task Review Result Notification", "greeting": "The review result for your published task is as follows:", "taskInfo": "Task Information:", "taskId": "Task ID:", "reviewedAt": "Reviewed At:", "result": "Review Result:", "approved": "Approved", "rejected": "Rejected", "reason": "Reason:", "action": "Please log in to view detailed information."}}}, "evidenceReview": {"approved": {"title": "Evidence Review Approved Notification", "greeting": "Your submitted evidence has been approved.", "taskInfo": "Task Information:", "taskId": "Task ID:", "reviewedAt": "Reviewed At:", "action": "Please log in to view detailed information."}, "rejected": {"title": "Evidence Review Rejected Notification", "greeting": "We're sorry, your submitted evidence was not approved.", "taskInfo": "Task Information:", "taskId": "Task ID:", "reviewedAt": "Reviewed At:", "reason": "Rejection Reason:", "nextSteps": "You need to resubmit evidence that meets the requirements.", "action": "Please log in to resubmit."}}, "logistics": {"review": {"title": "Order Logistics Information Review Required", "greeting": "The accepter has submitted order logistics information, please review it promptly.", "taskInfo": "Task Information:", "taskId": "Task ID:", "accepter": "Accepter:", "submittedAt": "Submitted At:", "action": "Please log in to review."}}, "withdrawal": {"approved": {"title": "Withdrawal Approved Notification", "greeting": "Your withdrawal request has been approved.", "withdrawalInfo": "Withdrawal Information:", "amount": "<PERSON><PERSON><PERSON> Amount:", "method": "Withdrawal Method:", "processedAt": "Processed At:", "estimatedArrival": "Estimated Arrival Time:", "workingDays": "business days", "action": "Funds will arrive according to the estimated time, please check for receipt."}, "rejected": {"title": "Withdrawal Request Rejected Notification", "greeting": "We're sorry, your withdrawal request has been rejected.", "withdrawalInfo": "Withdrawal Information:", "amount": "Requested Amount:", "method": "Withdrawal Method:", "rejectedAt": "Rejected At:", "reason": "Rejection Reason:", "refund": "The requested amount has been returned to your account balance.", "action": "If you have any questions, please contact customer service."}}}