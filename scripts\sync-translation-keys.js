const fs = require('fs');
const path = require('path');

// 获取对象的所有键路径
function getAllKeyPaths(obj, prefix = '') {
  const paths = new Set();

  function traverse(current, currentPath) {
    if (
      typeof current === 'object' &&
      current !== null &&
      !Array.isArray(current)
    ) {
      for (const key in current) {
        if (current.hasOwnProperty(key)) {
          const newPath = currentPath ? `${currentPath}.${key}` : key;
          paths.add(newPath);
          traverse(current[key], newPath);
        }
      }
    }
  }

  traverse(obj, prefix);
  return Array.from(paths);
}

// 根据键路径设置值
function setValueByPath(obj, path, value) {
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (
      !(key in current) ||
      typeof current[key] !== 'object' ||
      Array.isArray(current[key])
    ) {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
}

// 根据键路径获取值
function getValueByPath(obj, path) {
  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return undefined;
    }
  }

  return current;
}

// 同步两个文件的键结构
function syncTranslationFiles(enFile, zhFile) {
  try {
    const enContent = JSON.parse(fs.readFileSync(enFile, 'utf8'));
    const zhContent = JSON.parse(fs.readFileSync(zhFile, 'utf8'));

    const enPaths = getAllKeyPaths(enContent);
    const zhPaths = getAllKeyPaths(zhContent);

    const missingInEn = zhPaths.filter(path => !enPaths.includes(path));
    const missingInZh = enPaths.filter(path => !zhPaths.includes(path));

    let enUpdated = false;
    let zhUpdated = false;

    // 为英文文件添加缺失的键
    missingInEn.forEach(path => {
      const zhValue = getValueByPath(zhContent, path);
      if (typeof zhValue === 'string') {
        // 为英文版提供占位符翻译
        const placeholder = `[EN] ${zhValue}`;
        setValueByPath(enContent, path, placeholder);
        enUpdated = true;
      }
    });

    // 为中文文件添加缺失的键
    missingInZh.forEach(path => {
      const enValue = getValueByPath(enContent, path);
      if (typeof enValue === 'string') {
        // 为中文版提供占位符翻译
        const placeholder = `[ZH] ${enValue}`;
        setValueByPath(zhContent, path, placeholder);
        zhUpdated = true;
      }
    });

    // 保存更新的文件
    if (enUpdated) {
      fs.writeFileSync(enFile, JSON.stringify(enContent, null, 2));
    }

    if (zhUpdated) {
      fs.writeFileSync(zhFile, JSON.stringify(zhContent, null, 2));
    }

    return {
      success: true,
      enUpdated,
      zhUpdated,
      missingInEn: missingInEn.length,
      missingInZh: missingInZh.length,
      addedToEn: missingInEn,
      addedToZh: missingInZh,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

// 同步所有不一致的文件
function syncAllInconsistentFiles() {
  const messagesDir = path.join(__dirname, 'messages');
  const enDir = path.join(messagesDir, 'en');
  const zhDir = path.join(messagesDir, 'zh');

  // 从之前的检查结果中获取不一致的文件
  const inconsistentFiles = [
    'my-published-tasks.json',
    'navigation.json',
    'wallet.json',
  ];

  const results = [];

  console.log('🔄 开始同步不一致的翻译文件...\n');

  inconsistentFiles.forEach(filename => {
    const enFile = path.join(enDir, filename);
    const zhFile = path.join(zhDir, filename);

    if (fs.existsSync(enFile) && fs.existsSync(zhFile)) {
      console.log(`处理: ${filename}`);

      // 创建备份
      const enBackup = enFile + '.sync-backup';
      const zhBackup = zhFile + '.sync-backup';
      fs.copyFileSync(enFile, enBackup);
      fs.copyFileSync(zhFile, zhBackup);

      const result = syncTranslationFiles(enFile, zhFile);

      if (result.success) {
        console.log(`  ✅ 同步完成`);
        console.log(
          `  📝 英文文件更新: ${result.enUpdated ? '是' : '否'} (添加 ${result.missingInEn} 个键)`
        );
        console.log(
          `  📝 中文文件更新: ${result.zhUpdated ? '是' : '否'} (添加 ${result.missingInZh} 个键)`
        );

        if (result.addedToEn.length > 0) {
          console.log(
            `  🔤 添加到英文: ${result.addedToEn.slice(0, 3).join(', ')}${result.addedToEn.length > 3 ? '...' : ''}`
          );
        }

        if (result.addedToZh.length > 0) {
          console.log(
            `  🔤 添加到中文: ${result.addedToZh.slice(0, 3).join(', ')}${result.addedToZh.length > 3 ? '...' : ''}`
          );
        }

        console.log(
          `  📁 备份文件: ${path.basename(enBackup)}, ${path.basename(zhBackup)}`
        );
      } else {
        console.log(`  ❌ 错误: ${result.error}`);
      }

      results.push({
        file: filename,
        ...result,
      });

      console.log('');
    } else {
      console.log(`⚠️  跳过 ${filename}: 文件不存在`);
    }
  });

  console.log('📊 同步结果总结:');
  const successCount = results.filter(r => r.success).length;
  console.log(`成功同步: ${successCount}/${results.length}`);

  const totalEnUpdates = results.filter(r => r.enUpdated).length;
  const totalZhUpdates = results.filter(r => r.zhUpdated).length;
  console.log(`英文文件更新: ${totalEnUpdates}`);
  console.log(`中文文件更新: ${totalZhUpdates}`);

  // 保存结果
  fs.writeFileSync(
    path.join(__dirname, 'sync-results.json'),
    JSON.stringify(results, null, 2)
  );

  console.log('\n📄 详细结果已保存到 sync-results.json');
  console.log(
    '\n⚠️  注意: 添加的键使用了占位符翻译 [EN]/[ZH]，请手动更新为正确的翻译'
  );

  return results;
}

// 运行同步
if (require.main === module) {
  syncAllInconsistentFiles();
}

module.exports = { syncTranslationFiles, syncAllInconsistentFiles };
