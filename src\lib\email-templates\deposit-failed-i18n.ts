import {
  emailStyles,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface DepositFailedEmailData {
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  transactionId?: string;
  paymentMethod: string;
  failureReason: string;
  failedAt: string;
  language?: 'zh' | 'en';
}

export const depositFailedTemplateI18n = (
  data: DepositFailedEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.failedAt, language);
  const formattedAmount = formatEmailAmount(
    data.amount,
    data.currency,
    language
  );

  // 脱敏处理交易ID（如果存在）
  const maskedTransactionId =
    data.transactionId && data.transactionId.length > 8
      ? `${data.transactionId.slice(0, 4)}****${data.transactionId.slice(-4)}`
      : data.transactionId || 'N/A';

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
          security: '为了您的账户安全，请勿向任何人透露您的账户信息。',
        },
        buttons: {
          retryDeposit: '重新充值',
          contactSupport: '联系客服',
          viewAccount: '查看账户',
        },
      },
      notifications: {
        depositFailed: {
          title: '充值失败通知',
          greeting: '您的账户充值未能成功完成',
          description:
            '我们很抱歉，您的充值请求遇到了问题。请查看以下详情并尝试解决。',
          transactionDetails: '交易详情',
          amount: '充值金额：',
          currency: '货币类型：',
          paymentMethod: '支付方式：',
          transactionId: '交易编号：',
          failedAt: '失败时间：',
          failureReason: '失败原因：',
          commonReasons: '常见失败原因',
          reasons: {
            insufficientFunds: '银行卡余额不足',
            cardExpired: '银行卡已过期',
            networkError: '网络连接问题',
            bankDeclined: '银行拒绝交易',
            invalidCard: '银行卡信息错误',
            limitExceeded: '超出交易限额',
          },
          solutions: '解决方案',
          solutionSteps: {
            checkCard: '检查银行卡信息是否正确',
            checkBalance: '确认银行卡余额充足',
            contactBank: '联系银行确认卡片状态',
            tryAgain: '稍后重新尝试充值',
            useOtherMethod: '尝试使用其他支付方式',
            contactUs: '联系我们的客服团队',
          },
          nextSteps: '接下来您可以：',
          apology: '我们为给您带来的不便深表歉意，并将竭诚为您提供帮助。',
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
          security:
            'For your account security, please do not share your account information with anyone.',
        },
        buttons: {
          retryDeposit: 'Retry Deposit',
          contactSupport: 'Contact Support',
          viewAccount: 'View Account',
        },
      },
      notifications: {
        depositFailed: {
          title: 'Deposit Failed',
          greeting: 'Your account deposit could not be completed',
          description:
            'We apologize, but your deposit request encountered an issue. Please review the details below and try to resolve it.',
          transactionDetails: 'Transaction Details',
          amount: 'Deposit Amount:',
          currency: 'Currency:',
          paymentMethod: 'Payment Method:',
          transactionId: 'Transaction ID:',
          failedAt: 'Failed At:',
          failureReason: 'Failure Reason:',
          commonReasons: 'Common Failure Reasons',
          reasons: {
            insufficientFunds: 'Insufficient funds in bank account',
            cardExpired: 'Bank card has expired',
            networkError: 'Network connection issue',
            bankDeclined: 'Bank declined the transaction',
            invalidCard: 'Invalid bank card information',
            limitExceeded: 'Transaction limit exceeded',
          },
          solutions: 'Solutions',
          solutionSteps: {
            checkCard: 'Verify your bank card information is correct',
            checkBalance: 'Ensure sufficient funds in your account',
            contactBank: 'Contact your bank to confirm card status',
            tryAgain: 'Try the deposit again later',
            useOtherMethod: 'Try using a different payment method',
            contactUs: 'Contact our customer service team',
          },
          nextSteps: 'What you can do next:',
          apology:
            'We sincerely apologize for any inconvenience and are here to help you resolve this issue.',
        },
      },
    },
  };

  const t = translations[language];

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.depositFailed.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .error-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .amount-highlight {
          font-size: 24px;
          font-weight: bold;
          color: #ef4444;
          text-align: center;
          margin: 16px 0;
        }
        .transaction-card {
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #fecaca;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #7f1d1d;
          font-weight: 500;
        }
        .info-value {
          color: #991b1b;
          font-weight: 600;
        }
        .failure-reason {
          background: #fee2e2;
          border: 1px solid #fca5a5;
          border-radius: 8px;
          padding: 16px;
          margin: 20px 0;
          color: #7f1d1d;
        }
        .solutions-card {
          background: #f0f9ff;
          border: 1px solid #bae6fd;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .steps-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .steps-list li {
          padding: 8px 0;
          padding-left: 24px;
          position: relative;
          color: #1e40af;
        }
        .steps-list li:before {
          content: "→";
          position: absolute;
          left: 0;
          color: #3b82f6;
          font-weight: bold;
        }
        .reasons-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .reasons-list li {
          padding: 6px 0;
          padding-left: 20px;
          position: relative;
          color: #7f1d1d;
          font-size: 14px;
        }
        .reasons-list li:before {
          content: "•";
          position: absolute;
          left: 0;
          color: #ef4444;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Error Icon -->
          <div class="error-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}
          </h2>
          
          <h3 style="text-align: center; color: #ef4444; margin-bottom: 16px;">
            ${t.notifications.depositFailed.greeting}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.depositFailed.description}
          </p>

          <!-- Amount Highlight -->
          <div class="amount-highlight">
            ${formattedAmount}
          </div>

          <!-- Transaction Details -->
          <div class="transaction-card">
            <h4 style="margin-top: 0; color: #7f1d1d;">
              ${t.notifications.depositFailed.transactionDetails}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.depositFailed.amount}</span>
              <span class="info-value">${formattedAmount}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.depositFailed.paymentMethod}</span>
              <span class="info-value">${data.paymentMethod}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.depositFailed.transactionId}</span>
              <span class="info-value" style="font-family: monospace;">${maskedTransactionId}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.depositFailed.failedAt}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
          </div>

          <!-- Failure Reason -->
          <div class="failure-reason">
            <h4 style="margin-top: 0; color: #7f1d1d;">
              ${t.notifications.depositFailed.failureReason}
            </h4>
            <p style="margin-bottom: 0; font-weight: 600;">
              ${data.failureReason}
            </p>
          </div>

          <!-- Common Reasons -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              ${t.notifications.depositFailed.commonReasons}
            </h4>
            <ul class="reasons-list">
              <li>${t.notifications.depositFailed.reasons.insufficientFunds}</li>
              <li>${t.notifications.depositFailed.reasons.cardExpired}</li>
              <li>${t.notifications.depositFailed.reasons.networkError}</li>
              <li>${t.notifications.depositFailed.reasons.bankDeclined}</li>
              <li>${t.notifications.depositFailed.reasons.invalidCard}</li>
              <li>${t.notifications.depositFailed.reasons.limitExceeded}</li>
            </ul>
          </div>

          <!-- Solutions -->
          <div class="solutions-card">
            <h4 style="margin-top: 0; color: #1e40af;">
              ${t.notifications.depositFailed.solutions}
            </h4>
            <ul class="steps-list">
              <li>${t.notifications.depositFailed.solutionSteps.checkCard}</li>
              <li>${t.notifications.depositFailed.solutionSteps.checkBalance}</li>
              <li>${t.notifications.depositFailed.solutionSteps.contactBank}</li>
              <li>${t.notifications.depositFailed.solutionSteps.tryAgain}</li>
              <li>${t.notifications.depositFailed.solutionSteps.useOtherMethod}</li>
              <li>${t.notifications.depositFailed.solutionSteps.contactUs}</li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/dashboard/wallet/deposit" class="email-button email-button-primary">
              ${t.common.buttons.retryDeposit}
            </a>
            <a href="${process.env.DOMAIN}/support" class="email-button email-button-secondary">
              ${t.common.buttons.contactSupport}
            </a>
          </div>

          <!-- Apology -->
          <p style="text-align: center; color: #64748b; margin-top: 32px; font-style: italic;">
            ${t.notifications.depositFailed.apology}
          </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.contact}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.security}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};
