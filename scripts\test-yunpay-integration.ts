#!/usr/bin/env tsx

import { YunPayProvider } from '../src/lib/payment/providers/yunpay';
import { YunPayConfig } from '../src/lib/payment/types';

async function testYunPayIntegration() {
  console.log('🧪 Testing YunPay Integration with Currency Conversion...\n');

  // 模拟YunPay配置
  const config: YunPayConfig = {
    pid: 'test_merchant_id',
    key: 'test_secret_key',
    apiUrl: 'https://pay.example.com',
    paymentMethods: {
      alipay: {
        enabled: true,
        name: 'Alipay',
        description: 'Alipay扫码支付',
        feeRate: 2.5,
        minAmount: 1.0,
      },
      wxpay: {
        enabled: true,
        name: 'WeChat Pay',
        description: 'WeChat Pay扫码支付',
        feeRate: 2.0,
        minAmount: 0.5,
      },
    },
  };

  const yunpayProvider = new YunPayProvider(config);

  try {
    // 测试USD到CNY的支付创建
    console.log('1. Testing USD payment creation (will convert to CNY):');
    const usdPaymentParams = {
      orderNo: `TEST${Date.now()}`,
      amount: 100, // $100 USD
      currency: 'USD',
      paymentMethod: 'alipay',
      description: '测试订单 - USD支付',
      notifyUrl: 'https://example.com/notify',
      returnUrl: 'https://example.com/return',
      userId: 'test_user_123',
    };

    const usdResult = await yunpayProvider.createPayment(usdPaymentParams);
    console.log('   USD Payment Result:');
    console.log(`   - Success: ${usdResult.success}`);
    console.log(
      `   - Payment URL: ${usdResult.paymentUrl ? 'Generated' : 'Not generated'}`
    );
    console.log(`   - Message: ${usdResult.message}`);

    if (usdResult.currencyConversion) {
      const conv = usdResult.currencyConversion;
      console.log(`   - Currency Conversion:`);
      console.log(
        `     * ${conv.originalAmount} ${conv.originalCurrency} -> ${conv.convertedAmount} ${conv.convertedCurrency}`
      );
      console.log(`     * Exchange Rate: ${conv.exchangeRate}`);
      console.log(`     * Source: ${conv.source}`);
    }
    console.log();

    // 测试CNY支付（无需转换）
    console.log('2. Testing CNY payment creation (no conversion needed):');
    const cnyPaymentParams = {
      orderNo: `TEST${Date.now()}`,
      amount: 720, // ¥720 CNY
      currency: 'CNY',
      paymentMethod: 'wxpay',
      description: '测试订单 - CNY支付',
      notifyUrl: 'https://example.com/notify',
      returnUrl: 'https://example.com/return',
      userId: 'test_user_456',
    };

    const cnyResult = await yunpayProvider.createPayment(cnyPaymentParams);
    console.log('   CNY Payment Result:');
    console.log(`   - Success: ${cnyResult.success}`);
    console.log(
      `   - Payment URL: ${cnyResult.paymentUrl ? 'Generated' : 'Not generated'}`
    );
    console.log(`   - Message: ${cnyResult.message}`);
    console.log(
      `   - Currency Conversion: ${cnyResult.currencyConversion ? 'Yes' : 'No (not needed)'}`
    );
    console.log();

    // 测试小数金额
    console.log('3. Testing decimal amount conversion:');
    const decimalPaymentParams = {
      orderNo: `TEST${Date.now()}`,
      amount: 123.45, // $123.45 USD
      currency: 'USD',
      paymentMethod: 'alipay',
      description: '测试订单 - 小数金额',
      notifyUrl: 'https://example.com/notify',
      returnUrl: 'https://example.com/return',
      userId: 'test_user_789',
    };

    const decimalResult =
      await yunpayProvider.createPayment(decimalPaymentParams);
    console.log('   Decimal Amount Result:');
    console.log(`   - Success: ${decimalResult.success}`);

    if (decimalResult.currencyConversion) {
      const conv = decimalResult.currencyConversion;
      console.log(
        `   - Conversion: $${conv.originalAmount} -> ¥${conv.convertedAmount}`
      );
      console.log(`   - Rate: ${conv.exchangeRate}`);
    }
    console.log();

    console.log('✅ All YunPay integration tests completed successfully!');
    console.log('\n📝 Summary:');
    console.log('   - USD payments are automatically converted to CNY');
    console.log('   - CNY payments require no conversion');
    console.log('   - Decimal amounts are handled correctly');
    console.log('   - Exchange rates are fetched from reliable APIs');
    console.log('   - Conversion information is included in payment results');
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// 运行测试
testYunPayIntegration();
