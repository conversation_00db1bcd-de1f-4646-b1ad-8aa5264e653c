import {
  MembershipTier,
  UserMembership,
  UserWallet,
  WalletTransaction,
  TransactionType,
  TransactionStatus,
  ShopWhitelist,
} from '@/lib/types/membership';

// 模拟用户会员信息
export const mockUserMembership: UserMembership = {
  id: 'membership-1',
  userId: 'user-1',
  tier: MembershipTier.PROFESSIONAL,
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-12-31'),
  isActive: true,
  tasksUsedThisMonth: 8,
  whitelistSlotsUsed: 1,
};

// 模拟用户钱包信息
export const mockUserWallet: UserWallet = {
  id: 'wallet-1',
  userId: 'user-1',
  balance: 1250.68,
  frozenAmount: 300.0,
  totalIncome: 3500.0,
  totalExpense: 2249.32,
  updatedAt: new Date(),
};

// 模拟钱包交易记录
export const mockWalletTransactions: WalletTransaction[] = [
  {
    id: 'tx-1',
    userId: 'user-1',
    type: TransactionType.DEPOSIT,
    amount: 500.0,
    status: TransactionStatus.COMPLETED,
    description: 'Alipay充值',
    createdAt: new Date('2024-01-15T10:30:00'),
    completedAt: new Date('2024-01-15T10:31:00'),
  },
  {
    id: 'tx-2',
    userId: 'user-1',
    type: TransactionType.COMMISSION,
    amount: 25.5,
    status: TransactionStatus.COMPLETED,
    description: '委托酬金收入',
    createdAt: new Date('2024-01-14T14:20:00'),
    completedAt: new Date('2024-01-14T14:20:00'),
    reference: 'task-123',
  },
  {
    id: 'tx-3',
    userId: 'user-1',
    type: TransactionType.TASK_FEE,
    amount: -15.0,
    status: TransactionStatus.COMPLETED,
    description: '委托发布费用',
    createdAt: new Date('2024-01-13T09:15:00'),
    completedAt: new Date('2024-01-13T09:15:00'),
    reference: 'task-456',
  },
  {
    id: 'tx-4',
    userId: 'user-1',
    type: TransactionType.WITHDRAW,
    amount: -200.0,
    status: TransactionStatus.COMPLETED,
    description: '提现到Alipay',
    createdAt: new Date('2024-01-12T16:45:00'),
    completedAt: new Date('2024-01-12T18:30:00'),
  },
  {
    id: 'tx-5',
    userId: 'user-1',
    type: TransactionType.MEMBERSHIP,
    amount: -10.0,
    status: TransactionStatus.COMPLETED,
    description: '专业版会员费用',
    createdAt: new Date('2024-01-01T08:00:00'),
    completedAt: new Date('2024-01-01T08:00:00'),
  },
  {
    id: 'tx-6',
    userId: 'user-1',
    type: TransactionType.COMMISSION,
    amount: 18.75,
    status: TransactionStatus.COMPLETED,
    description: '委托酬金收入',
    createdAt: new Date('2024-01-10T11:30:00'),
    completedAt: new Date('2024-01-10T11:30:00'),
    reference: 'task-789',
  },
  {
    id: 'tx-7',
    userId: 'user-1',
    type: TransactionType.DEPOSIT,
    amount: 1000.0,
    status: TransactionStatus.COMPLETED,
    description: '美元账户充值',
    createdAt: new Date('2024-01-08T14:20:00'),
    completedAt: new Date('2024-01-08T14:25:00'),
  },
  {
    id: 'tx-8',
    userId: 'user-1',
    type: TransactionType.WITHDRAW,
    amount: -100.0,
    status: TransactionStatus.PENDING,
    description: '提现到WeChat Pay',
    createdAt: new Date('2024-01-16T10:00:00'),
  },
];

// 模拟商铺白名单数据
export const mockShopWhitelist: ShopWhitelist[] = [
  {
    id: 'whitelist-1',
    userId: 'user-1',
    shopName: '某品牌旗舰店',
    shopUrl: 'https://shop.taobao.com/example1',
    platform: '淘宝',
    reason: '品牌方投诉，要求屏蔽恶意刷单委托',
    createdAt: new Date('2024-01-15'),
    isActive: true,
  },
  {
    id: 'whitelist-2',
    userId: 'user-1',
    shopName: '优质生活馆',
    shopUrl: 'https://mall.jd.com/example2',
    platform: '京东',
    reason: '商家申请保护，避免虚假评价影响',
    createdAt: new Date('2024-01-10'),
    isActive: true,
  },
  {
    id: 'whitelist-3',
    userId: 'user-1',
    shopName: '时尚潮流店',
    shopUrl: 'https://shop.pinduoduo.com/example3',
    platform: '拼多多',
    reason: '平台合作商家，享受白名单保护政策',
    createdAt: new Date('2024-01-05'),
    isActive: false,
  },
];

// 获取用户会员信息
export const getUserMembership = (): UserMembership => {
  return mockUserMembership;
};

// 获取用户钱包信息
export const getUserWallet = (): UserWallet => {
  return mockUserWallet;
};

// 获取钱包交易记录
export const getWalletTransactions = (): WalletTransaction[] => {
  return mockWalletTransactions;
};

// 获取商铺白名单
export const getShopWhitelist = (): ShopWhitelist[] => mockShopWhitelist;
