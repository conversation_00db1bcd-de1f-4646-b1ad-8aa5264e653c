import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { logisticsService } from '@/lib/logistics-service';

// 注册物流单号到17TRACK
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const { id: taskId } = params;

    // 验证委托是否存在且属于当前用户
    const task = await prisma.task.findFirst({
      where: {
        id: taskId,
        OR: [{ publisherId: session.user.id }, { accepterId: session.user.id }],
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: '委托不存在或无权限' },
        { status: 404 },
      );
    }

    if (!task.trackingNumber) {
      return NextResponse.json(
        { error: '委托还未填写物流单号' },
        { status: 400 },
      );
    }

    // 获取请求体参数
    const body = await request.json();
    const { carrierCode } = body;

    // 准备注册数据
    const logisticsData = {
      taskId: task.id,
      trackingNumber: task.trackingNumber,
      carrierCode,
      orderNumber: task.orderNumber || undefined,
      orderTime: task.createdAt.toISOString().split('T')[0], // YYYY-MM-DD 格式
    };

    // 调用物流服务注册
    const result = await logisticsService.registerTracking(logisticsData);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: '物流单号注册成功，将开始自动跟踪',
      });
    } else {
      return NextResponse.json(
        {
          error: result.error || '注册失败',
        },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error('注册物流单号失败:', error);
    return NextResponse.json({ error: '服务异常' }, { status: 500 });
  }
}

// 获取物流跟踪状态
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const { id: taskId } = params;

    // 验证委托是否存在且属于当前用户
    const task = await prisma.task.findFirst({
      where: {
        id: taskId,
        OR: [{ publisherId: session.user.id }, { accepterId: session.user.id }],
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: '委托不存在或无权限' },
        { status: 404 },
      );
    }

    if (!task.trackingNumber) {
      return NextResponse.json(
        { error: '委托还未填写物流单号' },
        { status: 400 },
      );
    }

    try {
      // 获取物流跟踪信息 - 使用自动检测载体码
      const trackingInfo = await logisticsService.getTrackingInfo(
        task.trackingNumber,
        undefined,
      );

      return NextResponse.json({
        success: true,
        data: trackingInfo,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';

      // 如果提示需要先注册，说明审核通过时注册失败了
      if (
        errorMessage.includes('does not register') ||
        errorMessage.includes('please register first')
      ) {
        return NextResponse.json(
          {
            error: '物流单号未注册',
            details: '物流单号未在17TRACK中注册，请联系管理员或重新审核委托',
            suggestion: '这通常发生在审核通过时自动注册失败的情况',
          },
          { status: 400 },
        );
      } else if (
        errorMessage.includes('No tracking information at this time')
      ) {
        // 暂时没有物流信息
        return NextResponse.json(
          {
            error: '暂无物流信息',
            details: '17TRACK系统中暂时没有此物流单号的跟踪信息',
            suggestion:
              '这通常发生在以下情况：1) 物流单号刚刚创建，还未进入物流系统 2) 17TRACK需要时间同步物流信息 3) 请稍后重试或联系发件方确认',
            isTemporary: true,
          },
          { status: 200 },
        ); // 返回200状态码，因为这不是真正的错误
      } else {
        // 其他错误直接返回
        return NextResponse.json(
          {
            error: '获取物流信息失败',
            details: errorMessage,
          },
          { status: 400 },
        );
      }
    }
  } catch (error) {
    console.error('获取物流信息失败:', error);
    return NextResponse.json({ error: '服务异常' }, { status: 500 });
  }
}
