'use client';

import { Play, FileText, ZoomIn } from 'lucide-react';
import Image from 'next/image';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  convertToApiFileUrl,
  getFileType,
  extractFileNameFromUrl,
} from '@/lib/file-utils';

interface EvidenceFileViewerProps {
  files: string[];
  className?: string;
}

// 文件类型和文件名获取功能已移动到 @/lib/file-utils

// 单个文件项组件
function FileItem({ file, index }: { file: string; index: number }) {
  const apiFileUrl = convertToApiFileUrl(file);
  const fileType = getFileType(file);
  const fileName = extractFileNameFromUrl(file);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className='relative group cursor-pointer border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow bg-gray-50'>
          {fileType === 'image' ? (
            <>
              <Image
                src={apiFileUrl}
                alt={`证据文件 ${index + 1}`}
                width={200}
                height={96}
                className='w-full h-24 object-cover'
                onError={e => {
                  // 如果图片加载失败，显示文件图标
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className='hidden w-full h-24 items-center justify-center bg-gray-100'>
                <FileText className='h-8 w-8 text-gray-400' />
              </div>
              <div className='absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center'>
                <ZoomIn className='h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity' />
              </div>
            </>
          ) : fileType === 'video' ? (
            <>
              <video
                src={apiFileUrl}
                className='w-full h-24 object-cover'
                muted
                onError={e => {
                  // 如果视频加载失败，显示文件图标
                  const target = e.target as HTMLVideoElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className='hidden w-full h-24 items-center justify-center bg-gray-100'>
                <FileText className='h-8 w-8 text-gray-400' />
              </div>
              <div className='absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center'>
                <Play className='h-8 w-8 text-white' />
              </div>
              <div className='absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center'>
                <Play className='h-8 w-8 text-white opacity-80' />
              </div>
            </>
          ) : (
            <div className='w-full h-24 flex flex-col items-center justify-center bg-gray-100 text-gray-600'>
              <FileText className='h-8 w-8 mb-1' />
              <span className='text-xs truncate max-w-full px-2'>
                {fileName}
              </span>
            </div>
          )}

          {/* 文件名标签 */}
          <div className='absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-1 truncate'>
            {fileName}
          </div>
        </div>
      </DialogTrigger>

      <DialogContent className='max-w-6xl max-h-[95vh] overflow-hidden p-0'>
        <DialogHeader className='px-6 py-4 border-b'>
          <DialogTitle>
            {fileType === 'image'
              ? '图片预览'
              : fileType === 'video'
                ? '视频预览'
                : '文件下载'}{' '}
            - {fileName}
          </DialogTitle>
        </DialogHeader>
        <div className='flex items-center justify-center p-4 h-[calc(95vh-80px)]'>
          {fileType === 'image' ? (
            <Image
              src={apiFileUrl}
              alt={`证据文件 ${index + 1}`}
              width={800}
              height={600}
              className='max-w-full max-h-full object-contain'
            />
          ) : fileType === 'video' ? (
            <video
              src={apiFileUrl}
              controls
              className='max-w-full max-h-full'
              autoPlay
            />
          ) : (
            <div className='text-center'>
              <FileText className='h-16 w-16 mx-auto mb-4 text-gray-400' />
              <p className='text-lg font-medium mb-2'>{fileName}</p>
              <Button asChild>
                <a
                  href={apiFileUrl}
                  download
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  下载文件
                </a>
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function EvidenceFileViewer({
  files,
  className = '',
}: EvidenceFileViewerProps) {
  if (!files || files.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <FileText className='h-12 w-12 mx-auto text-gray-400 mb-2' />
        <p className='text-sm text-gray-500'>暂无证据文件</p>
      </div>
    );
  }

  // 转换所有文件URL为API格式，确保兼容性
  const processedFiles = files.map(convertToApiFileUrl);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3'>
        {processedFiles.map((file, index) => (
          <FileItem key={index} file={file} index={index} />
        ))}
      </div>
      <p className='text-xs text-gray-500 text-center'>
        点击文件可放大查看 • 共 {files.length} 个文件
      </p>
    </div>
  );
}
