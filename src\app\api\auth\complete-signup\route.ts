import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import prisma from '@/lib/db';

// 完成注册请求
const completeSignUpSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  verificationCode: z.string().length(6, '验证码必须为6位数字'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validation = completeSignUpSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: validation.error.errors[0].message },
        { status: 400 },
      );
    }

    const { email, verificationCode } = validation.data;

    // 查找注册验证码
    const signupIdentifier = `signup:${email}`;
    const verificationRecord = await prisma.verificationToken.findFirst({
      where: {
        identifier: signupIdentifier,
        expires: {
          gt: new Date(), // 确保验证码未过期
        },
      },
    });

    if (!verificationRecord) {
      return NextResponse.json(
        { error: '验证码已过期或不存在' },
        { status: 400 },
      );
    }

    // 解析token中的验证码和注册数据
    const tokenParts = verificationRecord.token.split('|');
    if (tokenParts.length !== 2) {
      return NextResponse.json({ error: '验证码格式错误' }, { status: 400 });
    }

    const [storedCode, encodedData] = tokenParts;

    // 验证验证码
    if (storedCode !== verificationCode) {
      return NextResponse.json({ error: '验证码错误' }, { status: 400 });
    }

    // 解析注册数据
    let registrationData;
    try {
      const decodedData = Buffer.from(encodedData, 'base64').toString();
      registrationData = JSON.parse(decodedData);
    } catch (error) {
      return NextResponse.json({ error: '注册数据解析失败' }, { status: 400 });
    }

    const { name, hashedPassword } = registrationData;

    // 检查用户是否已存在（防止重复注册）
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      // 删除验证码记录
      await prisma.verificationToken.delete({
        where: {
          identifier_token: {
            identifier: signupIdentifier,
            token: verificationRecord.token,
          },
        },
      });

      return NextResponse.json({ error: '该邮箱已被注册' }, { status: 409 });
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: 'USER',
        emailVerified: new Date(), // 标记邮箱已验证
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });

    // 删除使用过的验证码
    await prisma.verificationToken.delete({
      where: {
        identifier_token: {
          identifier: signupIdentifier,
          token: verificationRecord.token,
        },
      },
    });

    return NextResponse.json(
      {
        message: '注册成功，邮箱已验证',
        user,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error('完成注册失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
