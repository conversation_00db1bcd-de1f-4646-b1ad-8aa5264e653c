import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  WithdrawalRequest,
  WithdrawalRequestsResponse,
  WithdrawalRequestsParams,
  UpdateWithdrawalStatusRequest,
  ApiResponse,
} from '@/types/withdrawal';

// 管理员获取提现申请列表
export function useWithdrawalRequests(params?: WithdrawalRequestsParams) {
  return useQuery({
    queryKey: ['admin-withdrawal-requests', params],
    queryFn: async (): Promise<WithdrawalRequestsResponse> => {
      const searchParams = new URLSearchParams();

      if (params?.search) searchParams.append('search', params.search);
      if (params?.withdrawMethod)
        searchParams.append('withdrawMethod', params.withdrawMethod);
      if (params?.status) searchParams.append('status', params.status);
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.pageSize)
        searchParams.append('pageSize', params.pageSize.toString());

      const response = await fetch(`/api/admin/withdrawals?${searchParams}`);
      const data: ApiResponse<WithdrawalRequestsResponse> =
        await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取提现申请列表失败');
      }

      return data.data!;
    },
    enabled: true,
    staleTime: 30 * 1000, // 30秒内不重新请求
    refetchInterval: 60 * 1000, // 60秒自动刷新
  });
}

// 管理员获取单个提现申请详情
export function useWithdrawalRequest(id: string) {
  return useQuery({
    queryKey: ['admin-withdrawal-request', id],
    queryFn: async (): Promise<WithdrawalRequest> => {
      const response = await fetch(`/api/admin/withdrawals/${id}`);
      const data: ApiResponse<WithdrawalRequest> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取提现申请详情失败');
      }

      return data.data!;
    },
    enabled: !!id,
  });
}

// 管理员更新提现申请状态
export function useUpdateWithdrawalStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateWithdrawalStatusRequest;
    }): Promise<WithdrawalRequest> => {
      const response = await fetch(`/api/admin/withdrawals/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<WithdrawalRequest> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新提现申请状态失败');
      }

      return result.data!;
    },
    onSuccess: (data, variables) => {
      // 刷新列表
      queryClient.invalidateQueries({
        queryKey: ['admin-withdrawal-requests'],
      });
      // 刷新详情
      queryClient.invalidateQueries({
        queryKey: ['admin-withdrawal-request', variables.id],
      });

      const statusText = data.status === 'APPROVED' ? '审核通过' : '审核拒绝';
      toast.success(`提现申请${statusText}成功`);
    },
    onError: error => {
      toast.error(error.message || '操作失败');
    },
  });
}

// 管理员批量更新提现申请状态
export function useBatchUpdateWithdrawalStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      ids,
      data,
    }: {
      ids: string[];
      data: UpdateWithdrawalStatusRequest;
    }) => {
      const promises = ids.map(id =>
        fetch(`/api/admin/withdrawals/${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data),
        }),
      );

      const responses = await Promise.all(promises);
      const results = await Promise.all(responses.map(res => res.json()));

      const failed = results.filter(result => !result.success);
      if (failed.length > 0) {
        throw new Error(`${failed.length}个申请操作失败`);
      }

      return results;
    },
    onSuccess: (data, variables) => {
      // 刷新列表
      queryClient.invalidateQueries({
        queryKey: ['admin-withdrawal-requests'],
      });

      const statusText =
        data[0].data.status === 'APPROVED' ? '审核通过' : '审核拒绝';
      toast.success(`批量${statusText}成功`);
    },
    onError: error => {
      toast.error(error.message || '批量操作失败');
    },
  });
}

// 获取提现申请统计信息
export function useWithdrawalStats() {
  return useQuery({
    queryKey: ['withdrawal-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/withdrawals?pageSize=1'); // 只获取统计信息
      const data: ApiResponse<WithdrawalRequestsResponse> =
        await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取统计信息失败');
      }

      return data.data!.stats;
    },
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
    refetchInterval: 5 * 60 * 1000, // 5分钟自动刷新
  });
}

// 用户获取自己的提现申请列表
export function useUserWithdrawalRequests(params?: WithdrawalRequestsParams) {
  return useQuery({
    queryKey: ['user-withdrawal-requests', params],
    queryFn: async (): Promise<WithdrawalRequestsResponse> => {
      const searchParams = new URLSearchParams();

      if (params?.status) searchParams.append('status', params.status);
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.pageSize)
        searchParams.append('pageSize', params.pageSize.toString());

      const response = await fetch(`/api/user/withdrawals?${searchParams}`);
      const data: ApiResponse<WithdrawalRequestsResponse> =
        await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取提现申请列表失败');
      }

      return data.data!;
    },
    enabled: true,
    staleTime: 30 * 1000, // 30秒内不重新请求
  });
}

// 用户创建提现申请
export function useCreateWithdrawalRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any): Promise<WithdrawalRequest> => {
      const response = await fetch('/api/user/withdrawals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result: ApiResponse<WithdrawalRequest> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建提现申请失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      // 刷新用户的提现申请列表
      queryClient.invalidateQueries({ queryKey: ['user-withdrawal-requests'] });
      toast.success('提现申请提交成功，请等待审核');
    },
    onError: error => {
      toast.error(error.message || '提交失败');
    },
  });
}

// 用户取消提现申请
export function useCancelWithdrawalRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<WithdrawalRequest> => {
      const response = await fetch(`/api/user/withdrawals/${id}`, {
        method: 'DELETE',
      });

      const result: ApiResponse<WithdrawalRequest> = await response.json();

      if (!result.success) {
        throw new Error(result.message || '取消提现申请失败');
      }

      return result.data!;
    },
    onSuccess: () => {
      // 刷新用户的提现申请列表
      queryClient.invalidateQueries({ queryKey: ['user-withdrawal-requests'] });
      toast.success('提现申请已取消');
    },
    onError: error => {
      toast.error(error.message || '取消失败');
    },
  });
}
