{"version": "2.0.0", "tasks": [{"label": "npm: dev", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: build", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc"]}, {"label": "npm: test", "type": "shell", "command": "npm", "args": ["run", "test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "npm: test:watch", "type": "shell", "command": "npm", "args": ["run", "test:watch"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "isBackground": true}, {"label": "npm: test:coverage", "type": "shell", "command": "npm", "args": ["run", "test:coverage"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "npm: lint", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$eslint-stylish"]}, {"label": "npm: lint:fix", "type": "shell", "command": "npm", "args": ["run", "lint:fix"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$eslint-stylish"]}, {"label": "npm: format", "type": "shell", "command": "npm", "args": ["run", "format"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "npm: format:check", "type": "shell", "command": "npm", "args": ["run", "format:check"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "TypeScript: Check", "type": "shell", "command": "npx", "args": ["tsc", "--noEmit"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc"]}, {"label": "Database: Seed", "type": "shell", "command": "npm", "args": ["run", "db:seed"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "Database: Reset", "type": "shell", "command": "npm", "args": ["run", "db:reset"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "Clean: All", "type": "shell", "command": "rm", "args": ["-rf", ".next", "node_modules/.cache", "coverage", ".nyc_output"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "windows": {"command": "powershell", "args": ["-Command", "Remove-Item -Recurse -Force -ErrorAction SilentlyContinue .next, node_modules/.cache, coverage, .nyc_output"]}}]}