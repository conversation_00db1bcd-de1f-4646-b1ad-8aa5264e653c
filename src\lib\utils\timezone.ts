// 时区处理工具函数

// 确保Date对象以UTC格式存储和读取
export function ensureUTC(date: Date): Date {
  // 如果Date对象被错误地转换了时区，手动修正
  const timezoneOffset = date.getTimezoneOffset() * 60000;
  return new Date(date.getTime() + timezoneOffset);
}

// 安全地解析时间字符串为UTC Date
export function parseUTCDate(dateString: string): Date {
  const date = new Date(dateString);
  // 确保解析为UTC时间
  return new Date(date.toISOString());
}

// 为PostgreSQL查询准备UTC时间字符串
export function toPGTimestamp(date: Date): string {
  return date.toISOString();
}

// 从PostgreSQL读取的Date对象转换为正确的UTC时间
export function fromPGTimestamp(pgDate: Date): string {
  // 检查服务器时区设置
  const serverTimezoneOffset = new Date().getTimezoneOffset();

  // 对于UTC+8服务器，PostgreSQL返回的时间被错误地加了8小时
  // 我们需要减去8小时来修正
  if (serverTimezoneOffset === -480) {
    // UTC+8
    const correctedDate = new Date(pgDate.getTime() - 8 * 60 * 60 * 1000);
    return correctedDate.toISOString();
  }

  return pgDate.toISOString();
}

// 计算两个时间戳之间的差异（小时）
export function getHoursDifference(
  startDate: Date | string,
  endDate: Date | string,
): number {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

  return (end.getTime() - start.getTime()) / (1000 * 60 * 60);
}
