// CSRF Protection System
import { randomBytes, createHash } from 'crypto';

import { NextRequest } from 'next/server';

import { auth } from '@/lib/auth';

// CSRF 配置
export const CSRF_CONFIG = {
  TOKEN_LENGTH: 32,
  TOKEN_HEADER: 'x-csrf-token',
  TOKEN_COOKIE: 'csrf-token',
  TOKEN_EXPIRY: 60 * 60 * 1000, // 1小时
} as const;

// CSRF Token 存储（生产环境应使用 Redis 或数据库）
const tokenStore = new Map<
  string,
  { token: string; expires: number; userId: string }
>();

// 生成 CSRF Token
export function generateCSRFToken(userId: string): string {
  const token = randomBytes(CSRF_CONFIG.TOKEN_LENGTH).toString('hex');
  const expires = Date.now() + CSRF_CONFIG.TOKEN_EXPIRY;

  // 存储 token
  tokenStore.set(token, { token, expires, userId });

  // 清理过期的 token
  cleanupExpiredTokens();

  return token;
}

// 验证 CSRF Token
export function validateCSRFToken(token: string, userId: string): boolean {
  if (!token || !userId) {
    return false;
  }

  const storedToken = tokenStore.get(token);

  if (!storedToken) {
    return false;
  }

  // 检查是否过期
  if (Date.now() > storedToken.expires) {
    tokenStore.delete(token);
    return false;
  }

  // 检查用户ID是否匹配
  if (storedToken.userId !== userId) {
    return false;
  }

  return true;
}

// 清理过期的 token
function cleanupExpiredTokens(): void {
  const now = Date.now();
  for (const [token, data] of Array.from(tokenStore.entries())) {
    if (now > data.expires) {
      tokenStore.delete(token);
    }
  }
}

// 从请求中提取 CSRF Token
export function extractCSRFToken(request: NextRequest): string | null {
  // 优先从 header 中获取
  const headerToken = request.headers.get(CSRF_CONFIG.TOKEN_HEADER);
  if (headerToken) {
    return headerToken;
  }

  // 从 cookie 中获取
  const cookieToken = request.cookies.get(CSRF_CONFIG.TOKEN_COOKIE)?.value;
  if (cookieToken) {
    return cookieToken;
  }

  // 从表单数据中获取
  const contentType = request.headers.get('content-type');
  if (contentType?.includes('application/x-www-form-urlencoded')) {
    // 这里需要解析表单数据，但在中间件中可能不太合适
    // 建议在具体的 API 路由中处理
  }

  return null;
}

// CSRF 中间件
export async function csrfMiddleware(request: NextRequest): Promise<boolean> {
  // 只对修改数据的请求进行 CSRF 检查
  const safeMethods = ['GET', 'HEAD', 'OPTIONS'];
  if (safeMethods.includes(request.method)) {
    return true;
  }

  // 获取当前用户
  const session = await auth();
  if (!session?.user?.id) {
    // 未登录用户不需要 CSRF 检查
    return true;
  }

  // 提取 CSRF Token
  const token = extractCSRFToken(request);
  if (!token) {
    return false;
  }

  // 验证 token
  return validateCSRFToken(token, session.user.id);
}

// 生成 CSRF Token API
export async function generateCSRFTokenForUser(): Promise<string | null> {
  const session = await auth();
  if (!session?.user?.id) {
    return null;
  }

  return generateCSRFToken(session.user.id);
}

// CSRF 错误类
export class CSRFError extends Error {
  constructor(message: string = 'CSRF token validation failed') {
    super(message);
    this.name = 'CSRFError';
  }
}

// 验证请求的 CSRF Token
export async function validateRequestCSRF(request: NextRequest): Promise<void> {
  const isValid = await csrfMiddleware(request);
  if (!isValid) {
    throw new CSRFError('Invalid or missing CSRF token');
  }
}

// 创建带有 CSRF 保护的 API 处理器
export function withCSRFProtection<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<Response>,
) {
  return async function (request: NextRequest, ...args: T): Promise<Response> {
    try {
      await validateRequestCSRF(request);
      return await handler(request, ...args);
    } catch (error) {
      if (error instanceof CSRFError) {
        return new Response(
          JSON.stringify({
            error: 'CSRF token validation failed',
            message: error.message,
          }),
          {
            status: 403,
            headers: { 'Content-Type': 'application/json' },
          },
        );
      }
      throw error;
    }
  };
}

// 双重提交 Cookie 模式的 CSRF 保护
export function generateDoubleSubmitToken(): string {
  return randomBytes(CSRF_CONFIG.TOKEN_LENGTH).toString('hex');
}

export function validateDoubleSubmitToken(
  cookieToken: string,
  headerToken: string,
): boolean {
  if (!cookieToken || !headerToken) {
    return false;
  }

  // 使用时间安全的比较
  return timingSafeEqual(
    Buffer.from(cookieToken, 'hex'),
    Buffer.from(headerToken, 'hex'),
  );
}

// 时间安全的字符串比较
function timingSafeEqual(a: Buffer, b: Buffer): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a[i] ^ b[i];
  }

  return result === 0;
}

// 生成同步器 Token 模式的 CSRF Token
export function generateSynchronizerToken(sessionId: string): string {
  const timestamp = Date.now().toString();
  const random = randomBytes(16).toString('hex');
  const data = `${sessionId}:${timestamp}:${random}`;

  return createHash('sha256')
    .update(data)
    .update(process.env.NEXTAUTH_SECRET || 'fallback-secret')
    .digest('hex');
}

export function validateSynchronizerToken(
  token: string,
  sessionId: string,
  maxAge: number = CSRF_CONFIG.TOKEN_EXPIRY,
): boolean {
  try {
    // 这里需要从 token 中提取时间戳并验证
    // 简化实现，实际应该包含时间戳验证
    const expectedToken = generateSynchronizerToken(sessionId);
    return timingSafeEqual(
      Buffer.from(token, 'hex'),
      Buffer.from(expectedToken, 'hex'),
    );
  } catch {
    return false;
  }
}

// 导出类型
export type CSRFTokenData = {
  token: string;
  expires: number;
  userId: string;
};
