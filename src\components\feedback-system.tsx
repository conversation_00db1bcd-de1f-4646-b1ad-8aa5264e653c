'use client';

import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  Loader2,
  X,
  Bell,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';

// Toast 通知类型
export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading';

// 通知配置
interface NotificationConfig {
  title: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
}

// Toast 通知系统
export class FeedbackToast {
  static success(message: string, config?: Partial<NotificationConfig>) {
    toast.success(message, {
      description: config?.description,
      duration: config?.duration || 4000,
      action: config?.action
        ? {
            label: config.action.label,
            onClick: config.action.onClick,
          }
        : undefined,
    });
  }

  static error(message: string, config?: Partial<NotificationConfig>) {
    toast.error(message, {
      description: config?.description,
      duration: config?.duration || 6000,
      action: config?.action
        ? {
            label: config.action.label,
            onClick: config.action.onClick,
          }
        : undefined,
    });
  }

  static warning(message: string, config?: Partial<NotificationConfig>) {
    toast.warning(message, {
      description: config?.description,
      duration: config?.duration || 5000,
      action: config?.action
        ? {
            label: config.action.label,
            onClick: config.action.onClick,
          }
        : undefined,
    });
  }

  static info(message: string, config?: Partial<NotificationConfig>) {
    toast.info(message, {
      description: config?.description,
      duration: config?.duration || 4000,
      action: config?.action
        ? {
            label: config.action.label,
            onClick: config.action.onClick,
          }
        : undefined,
    });
  }

  static loading(message: string, config?: Partial<NotificationConfig>) {
    return toast.loading(message, {
      description: config?.description,
    });
  }

  static promise<T>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
  ) {
    return toast.promise(promise, {
      loading,
      success,
      error,
    });
  }
}

// 确认对话框组件
interface ConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive';
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
}

export function ConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText,
  cancelText,
  variant = 'default',
  onConfirm,
  onCancel,
}: ConfirmDialogProps) {
  const t = useTranslations('Messages');
  const [isLoading, setIsLoading] = useState(false);

  // 使用翻译的默认值
  const defaultConfirmText = confirmText || t('confirm.delete.confirm');
  const defaultCancelText = cancelText || t('confirm.delete.cancel');

  const handleConfirm = async () => {
    setIsLoading(true);
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      console.error('Confirm action failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isLoading}>
            {defaultCancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              variant === 'destructive' &&
                'bg-destructive text-destructive-foreground hover:bg-destructive/90',
            )}
          >
            {isLoading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
            {defaultConfirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// 操作反馈组件
interface ActionFeedbackProps {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  description?: string;
  onDismiss?: () => void;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export function ActionFeedback({
  type,
  message,
  description,
  onDismiss,
  action,
  className,
}: ActionFeedbackProps) {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertCircle,
    info: Info,
  };

  const colors = {
    success: 'text-green-600 bg-green-50 border-green-200',
    error: 'text-red-600 bg-red-50 border-red-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    info: 'text-blue-600 bg-blue-50 border-blue-200',
  };

  const Icon = icons[type];

  return (
    <div
      className={cn(
        'flex items-start gap-3 p-4 border rounded-lg',
        colors[type],
        className,
      )}
    >
      <Icon className='h-5 w-5 mt-0.5 flex-shrink-0' />
      <div className='flex-1 min-w-0'>
        <div className='font-medium'>{message}</div>
        {description && (
          <div className='text-sm opacity-90 mt-1'>{description}</div>
        )}
        {action && (
          <Button
            variant='ghost'
            size='sm'
            onClick={action.onClick}
            className='mt-2 h-8 px-2'
          >
            {action.label}
          </Button>
        )}
      </div>
      {onDismiss && (
        <Button
          variant='ghost'
          size='sm'
          onClick={onDismiss}
          className='h-6 w-6 p-0 flex-shrink-0'
        >
          <X className='h-4 w-4' />
        </Button>
      )}
    </div>
  );
}

// 进度反馈组件
interface ProgressFeedbackProps {
  value: number;
  max?: number;
  label?: string;
  description?: string;
  showPercentage?: boolean;
  variant?: 'default' | 'success' | 'warning' | 'error';
  className?: string;
}

export function ProgressFeedback({
  value,
  max = 100,
  label,
  description,
  showPercentage = true,
  variant = 'default',
  className,
}: ProgressFeedbackProps) {
  const percentage = Math.round((value / max) * 100);

  const getVariantClass = () => {
    switch (variant) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-primary';
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {(label || showPercentage) && (
        <div className='flex items-center justify-between text-sm'>
          {label && <span className='font-medium'>{label}</span>}
          {showPercentage && (
            <span className={cn('font-medium', getVariantClass())}>
              {percentage}%
            </span>
          )}
        </div>
      )}
      <Progress value={percentage} className='h-2' />
      {description && (
        <p className='text-xs text-muted-foreground'>{description}</p>
      )}
    </div>
  );
}

// 用户反馈收集组件
interface FeedbackCollectorProps {
  trigger?: React.ReactNode;
  onSubmit?: (feedback: {
    rating: number;
    comment: string;
    type: string;
  }) => void;
}

export function FeedbackCollector({
  trigger,
  onSubmit,
}: FeedbackCollectorProps) {
  const t = useTranslations('Feedback');
  const [open, setOpen] = useState(false);
  const [rating, setRating] = useState<number | null>(null);
  const [comment, setComment] = useState('');
  const [type, setType] = useState<'positive' | 'negative' | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (rating === null || !type) return;

    setIsSubmitting(true);
    try {
      await onSubmit?.({ rating, comment, type });
      FeedbackToast.success(t('success.thankYou'));
      setOpen(false);
      // 重置表单
      setRating(null);
      setComment('');
      setType(null);
    } catch (error) {
      FeedbackToast.error(t('error.submitFailed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant='outline' size='sm'>
            <MessageSquare className='h-4 w-4 mr-2' />
            {t('title')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle>{t('userFeedback')}</DialogTitle>
          <DialogDescription>{t('feedbackImportant')}</DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 快速评价 */}
          <div className='space-y-2'>
            <Label>{t('howWasExperience')}</Label>
            <div className='flex gap-2'>
              <Button
                variant={type === 'positive' ? 'default' : 'outline'}
                size='sm'
                onClick={() => {
                  setType('positive');
                  setRating(5);
                }}
              >
                <ThumbsUp className='h-4 w-4 mr-1' />
                {t('satisfied')}
              </Button>
              <Button
                variant={type === 'negative' ? 'default' : 'outline'}
                size='sm'
                onClick={() => {
                  setType('negative');
                  setRating(2);
                }}
              >
                <ThumbsDown className='h-4 w-4 mr-1' />
                {t('unsatisfied')}
              </Button>
            </div>
          </div>

          {/* 详细评论 */}
          <div className='space-y-2'>
            <Label htmlFor='comment'>{t('detailedDescription')}</Label>
            <Textarea
              id='comment'
              placeholder={t('detailedPlaceholder')}
              value={comment}
              onChange={e => setComment(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={() => setOpen(false)}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={!type || isSubmitting}>
            {isSubmitting && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
            {t('submitFeedback')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 通知中心组件
export function NotificationCenter() {
  const t = useTranslations('Notifications');
  const [notifications, setNotifications] = useState<
    Array<{
      id: string;
      type: 'info' | 'success' | 'warning' | 'error';
      title: string;
      message: string;
      timestamp: Date;
      read: boolean;
    }>
  >([]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n => (n.id === id ? { ...n, read: true } : n)),
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant='ghost' size='sm' className='relative'>
          <Bell className='h-4 w-4' />
          {unreadCount > 0 && (
            <Badge
              variant='destructive'
              className='absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center'
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <div className='flex items-center justify-between'>
            <DialogTitle>{t('notificationCenter')}</DialogTitle>
            {unreadCount > 0 && (
              <Button variant='ghost' size='sm' onClick={markAllAsRead}>
                {t('markAllRead')}
              </Button>
            )}
          </div>
        </DialogHeader>

        <div className='space-y-2 max-h-96 overflow-y-auto'>
          {notifications.length === 0 ? (
            <div className='text-center py-8 text-muted-foreground'>
              {t('noNotifications')}
            </div>
          ) : (
            notifications.map(notification => (
              <div
                key={notification.id}
                className={cn(
                  'p-3 border rounded-lg cursor-pointer transition-colors',
                  !notification.read && 'bg-muted/50',
                )}
                onClick={() => markAsRead(notification.id)}
              >
                <div className='flex items-start gap-2'>
                  <div className='flex-1'>
                    <div className='font-medium text-sm'>
                      {notification.title}
                    </div>
                    <div className='text-xs text-muted-foreground mt-1'>
                      {notification.message}
                    </div>
                    <div className='text-xs text-muted-foreground mt-1'>
                      {notification.timestamp.toLocaleString()}
                    </div>
                  </div>
                  {!notification.read && (
                    <div className='w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-1' />
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default FeedbackToast;
