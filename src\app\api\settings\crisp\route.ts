import { NextResponse } from 'next/server';

import { CrispConfig } from '@/types/crisp';

export async function GET() {
  try {
    // 从环境变量获取 Crisp 配置
    const config: CrispConfig = {
      enabled: process.env.CRISP_ENABLED === 'true',
      websiteId: process.env.CRISP_WEBSITE_ID || '',
    };

    // 如果没有配置 websiteId，则禁用
    if (!config.websiteId) {
      config.enabled = false;
    }

    return NextResponse.json(config);
  } catch (error) {
    console.error('Get Crisp config failed:', error);
    return NextResponse.json(
      { enabled: false, websiteId: '' },
      { status: 500 },
    );
  }
}
