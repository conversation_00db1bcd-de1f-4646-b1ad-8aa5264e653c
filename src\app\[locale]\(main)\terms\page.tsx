'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';

import { Footer } from '@/components/footer';
import { ModernNavbar } from '@/components/modern-navbar';
import SimpleBackground from '@/components/simple-background';

// Professional Legal Document Content Component
function TermsContent() {
  const t = useTranslations('legal.terms');

  const sections = [
    {
      id: 'overview',
      title: t('sections.overview.title'),
      content: t('sections.overview.content'),
    },
    {
      id: 'responsibilities',
      title: t('sections.responsibilities.title'),
      content: t('sections.responsibilities.content'),
    },
    {
      id: 'payment',
      title: t('sections.payment.title'),
      content: t('sections.payment.content'),
    },
    {
      id: 'prohibited',
      title: t('sections.prohibited.title'),
      content: t('sections.prohibited.content'),
    },
    {
      id: 'disputes',
      title: t('sections.disputes.title'),
      content: t('sections.disputes.content'),
    },
    {
      id: 'disclaimer',
      title: t('sections.disclaimer.title'),
      content: t('sections.disclaimer.content'),
    },
    {
      id: 'contact',
      title: t('sections.contact.title'),
      content: t('sections.contact.content'),
    },
  ];

  return (
    <div className='min-h-screen bg-background'>
      <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12'>
        {/* Document Header */}
        <motion.header
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className='text-center mb-12 border-b border-border pb-8'
        >
          <h1 className='text-3xl sm:text-4xl font-bold text-foreground mb-4'>
            {t('title')}
          </h1>
          <p className='text-lg text-muted-foreground mb-4'>
            Please read these terms carefully before using our services
          </p>
          <div className='text-sm text-muted-foreground'>
            <strong>{t('lastUpdated')}:</strong> June 5, 2024
          </div>
        </motion.header>

        {/* Document Content */}
        <motion.main
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className='prose prose-gray dark:prose-invert max-w-none'
        >
          {sections.map((section, index) => (
            <motion.section
              key={section.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className='mb-8'
            >
              <h2 className='text-xl sm:text-2xl font-semibold text-foreground mb-4 border-l-4 border-primary pl-4'>
                {section.title}
              </h2>
              <div
                className='text-muted-foreground leading-relaxed space-y-4'
                dangerouslySetInnerHTML={{ __html: section.content }}
              />
            </motion.section>
          ))}
        </motion.main>

        {/* Help Section */}
        <motion.aside
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className='mt-12 p-6 bg-muted/50 rounded-lg border border-border'
        >
          <h3 className='text-lg font-semibold text-foreground mb-3'>
            {t('needHelp')}
          </h3>
          <p className='text-muted-foreground'>
            {t('helpText')}
          </p>
        </motion.aside>
      </div>
    </div>
  );
}

export default function TermsPage() {
  return (
    <div className='homepage-theme'>
      <div className='homepage-body'>
        <main className='relative'>
          {/* 简洁的背景装饰元素 */}
          <div className='brand-bg' />
          <div className='tech-grid' />

          <SimpleBackground />
          <ModernNavbar showMenuItems={true} />
          <div className='pt-20'>
            <TermsContent />
          </div>
          <Footer />
        </main>
      </div>
    </div>
  );
}
