{"navigation": {"title": "My Wallet", "description": "Manage your wallet balance, deposits, withdrawals and transaction history", "breadcrumb": "Asset Management"}, "balance": {"available": "Available Balance", "frozen": "Frozen Amount", "totalIncome": "Total Income", "totalExpense": "Total Expense", "availableDescription": "Available for task payments and withdrawals", "frozenDescription": "Frozen funds such as task deposits", "incomeDescription": "Cumulative income amount", "expenseDescription": "Cumulative expense amount"}, "deposit": {"title": "Account <PERSON>", "amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "amountPlaceholder": "Enter deposit amount", "minAmount": "Minimum deposit amount", "paymentMethod": "Payment Method", "cryptocurrency": "Cryptocurrency", "comingSoon": "Coming Soon", "processing": "Processing...", "submit": "Deposit Now", "method": "[EN] 支付方式", "crypto": "[EN] 虚拟币", "otherMethods": "[EN] 其他支付方式", "button": "[EN] 立即充值"}, "withdraw": {"title": "Account <PERSON>", "amount": "<PERSON><PERSON><PERSON> Amount", "amountPlaceholder": "Enter withdrawal amount", "minAmount": "Minimum withdrawal amount", "availableBalance": "Available balance", "method": "<PERSON><PERSON><PERSON> Method", "bankAccount": "USD Account", "processing": "Processing...", "submit": "Apply for Withdrawal", "verifyAndSubmit": "Verify Email and Withdraw", "crypto": "[EN] USDT", "button": "[EN] 申请提现", "verifyButton": "[EN] 验证邮箱并提现"}, "transactions": {"title": "Transaction History", "all": "All", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "income": "Income", "expense": "Expense", "empty": "No transaction records", "reference": "Reference", "types": {"DEPOSIT": "<PERSON><PERSON><PERSON><PERSON>", "WITHDRAW": "Withdraw", "TASK_FEE": "Task Fee", "COMMISSION": "Commission", "MEMBERSHIP": "Membership Fee", "REFUND": "Refund", "DEPOSIT_FREEZE": "Deposit Freeze", "DEPOSIT_UNFREEZE": "Deposit Unfreeze", "PENALTY": "Penalty", "BONUS": "Bonus", "FEE": "Fee", "TRANSFER_IN": "Transfer In", "TRANSFER_OUT": "Transfer Out"}, "status": {"PENDING": "Pending", "COMPLETED": "Completed", "FAILED": "Failed", "CANCELLED": "Cancelled"}, "table": {"time": "Time", "type": "Type", "amount": "Amount", "status": "Status", "description": "Description", "reference": "Reference", "actions": "Actions"}, "filters": {"timeRange": "Time Range", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last3Months": "Last 3 Months", "customRange": "Custom Range", "startDate": "Start Date", "endDate": "End Date", "apply": "Apply", "reset": "Reset"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "pages": "pages", "showing": "Showing", "to": "to", "entries": "entries of", "total": "total"}, "export": {"button": "Export Records", "csv": "Export CSV", "excel": "Export Excel", "pdf": "Export PDF"}, "search": {"placeholder": "Search transactions...", "byDescription": "Search by description", "byReference": "Search by reference", "noResults": "No matching transaction records found"}, "dateFormat": {"today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "thisMonth": "This Month", "lastMonth": "Last Month"}}, "messages": {"invalidAmount": "Invalid deposit amount", "invalidAmountDesc": "Please enter a valid deposit amount", "selectPaymentMethod": "Please select a payment method", "amountTooSmall": "Deposit amount too small", "amountTooSmallDesc": "The minimum deposit amount for this payment method is $", "invalidWithdrawAmount": "Please enter a valid withdrawal amount", "insufficientBalance": "Withdrawal amount cannot exceed available balance", "belowMinWithdraw": "Withdrawal amount cannot be below minimum withdrawal amount $", "verificationSuccess": "Email verification successful", "verificationSuccessDesc": "You can now proceed with withdrawal", "loading": "Loading..."}, "modals": {"emailVerification": {"title": "Email Verification", "description": "To ensure withdrawal security, please verify your email address", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Please enter 6-digit verification code", "sendCode": "Send Code", "resendCode": "Resend Code", "sending": "Sending...", "verifying": "Verifying...", "confirm": "Confirm Verification", "cancel": "Cancel", "countdownText": "seconds before resend", "validation": {"codeRequired": "Please enter 6-digit verification code", "codeLength": "Verification code must be 6 digits"}, "messages": {"codeSent": "Verification code sent to your email", "sendFailed": "Failed to send verification code, please try again", "sending": "Sending...", "verifying": "Verifying...", "invalidCode": "Please enter 6-digit verification code", "verifyFailed": "Verification failed, please try again", "resendCountdown": " seconds before resend"}, "verify": "Confirm Verification"}, "bankWithdrawal": {"title": "International USD Account <PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON> Amount", "amountDisplay": "<PERSON><PERSON><PERSON> Amount:", "amountPlaceholder": "Please enter amount on wallet page first", "accountHolder": "Account Holder Name", "accountHolderPlaceholder": "<PERSON> or <PERSON>-<PERSON>", "accountHolderHint": "Please enter the full English name exactly as it appears on your bank account", "bankCard": "Bank Account Number/IBAN", "bankCardPlaceholder": "********************** or **********", "bankCardHint": "Supports IBAN format or traditional account number", "bankName": "Bank Name", "bankNamePlaceholder": "Chase Bank, HSBC, Bank of America", "bankNameHint": "Please enter the full English name of the bank", "bankSwift": "SWIFT Code", "bankSwiftPlaceholder": "CHASUS33, HBUKGB4B", "bankSwiftHint": "8-11 character international bank identifier code", "branchAddress": "Branch Address", "branchAddressPlaceholder": "123 Main Street, New York, NY 10001", "branchAddressHint": "Complete English address of the bank branch", "bankCountry": "Bank Country Code", "bankCountryPlaceholder": "US, GB, CA, AU", "countryCodeHint": "Please enter 2-letter ISO country code, common codes:", "countries": {"US": "United States", "GB": "United Kingdom", "CA": "Canada", "AU": "Australia", "DE": "Germany", "FR": "France", "JP": "Japan", "SG": "Singapore", "HK": "Hong Kong", "CH": "Switzerland"}, "feeInfo": "Fee Information", "processingTime": "Processing Time", "processingTimeDesc": "Usually 3-5 business days", "importantNotice": "Important Notice:", "notices": ["All information must match your bank account information exactly", "Account holder name must match your identity documents", "Please ensure the bank supports receiving international USD transfers", "Processing time is usually 3-5 business days"], "submit": "Confirm <PERSON>", "submitting": "Submitting...", "cancel": "Cancel", "verificationRequired": "Please complete email verification first", "validation": {"amountRequired": "Please enter withdrawal amount", "amountInvalid": "Invalid withdrawal amount", "accountHolderRequired": "Please enter account holder name", "bankCardRequired": "Please enter bank account number", "bankNameRequired": "Please enter bank name", "bankSwiftRequired": "Please enter SWIFT code", "branchAddressRequired": "Please enter branch address", "bankCountryRequired": "Please enter bank country", "accountHolderMin": "Account holder name must be at least 2 characters", "accountHolderMax": "Account holder name cannot exceed 100 characters", "accountHolderFormat": "Account holder name can only contain English letters, spaces, hyphens, apostrophes and periods", "bankCardMin": "Account number must be at least 8 digits", "bankCardMax": "Account number cannot exceed 34 digits", "bankCardFormat": "Account number can only contain uppercase letters and numbers", "bankNameMin": "Bank name must be at least 2 characters", "bankNameMax": "Bank name cannot exceed 100 characters", "bankNameFormat": "Invalid bank name format", "swiftFormat": "Invalid SWIFT code format (e.g.: CHASUS33XXX)", "branchAddressMin": "Bank address must be at least 10 characters", "branchAddressMax": "Bank address cannot exceed 200 characters", "branchAddressFormat": "Invalid bank address format", "countryCodeLength": "Please enter 2-letter country code (e.g.: US, GB, CA)", "countryCodeFormat": "Country code must be 2 uppercase letters", "amountMin": "Minimum withdrawal amount is $", "amountMax": "Single withdrawal amount cannot exceed $50,000", "amountExceedsBalance": "Withdrawal amount cannot exceed available balance", "submitError": "<PERSON><PERSON>wal request failed"}}, "cryptoWithdrawal": {"title": "USDT Withdrawal", "amount": "<PERSON><PERSON><PERSON> Amount", "amountDisplay": "<PERSON><PERSON><PERSON> Amount:", "amountPlaceholder": "Please enter amount on wallet page first", "walletAddress": "Wallet Address", "walletAddressPlaceholder": "Please enter USDT wallet address", "walletAddressHint": "Please ensure the wallet address is correct, incorrect address may result in fund loss", "networkLabels": {"ethereum": "Ethereum", "tron": "TRON"}, "network": "Network Selection", "networkPlaceholder": "Select network", "networkOptions": {"USDT_ERC20": "USDT (ERC20)", "USDT_TRC20": "USDT (TRC20)"}, "networkDescriptions": {"ERC20": "Ethereum network, stable transfer speed", "TRC20": "TRON network, faster transfer speed"}, "networkFee": "Network Fee", "arrivalTime": "Arrival Time", "arrivalTimeDesc": "Usually arrives within 1-2 hours", "importantNotice": "Important Notice", "notices": ["Please ensure the wallet address is accurate", "Cryptocurrency transactions are irreversible, please operate carefully", "A confirmation email will be sent to your registered email after withdrawal", "Please contact customer service if you encounter any issues"], "submit": "Confirm <PERSON>", "submitting": "Submitting...", "cancel": "Cancel", "verificationRequired": "Please complete email verification first", "validation": {"amountRequired": "Please enter valid withdrawal amount", "amountInvalid": "Invalid withdrawal amount", "amountTooLow": "Minimum withdrawal amount is ", "amountTooHigh": "Maximum withdrawal amount per transaction is $10000", "insufficientBalance": "Withdrawal amount cannot exceed available balance", "walletAddressRequired": "Please enter wallet address", "walletAddressInvalid": "Invalid wallet address format", "walletAddressTooLong": "Wallet address too long", "erc20AddressFormat": "ERC20 wallet address must start with 0x", "trc20AddressFormat": "TRC20 wallet address must start with T", "networkRequired": "Please select network", "submitError": "<PERSON><PERSON>wal request failed"}}}, "hooks": {"sendEmail": {"success": {"title": "<PERSON><PERSON> sent successfully!", "description": "Email has been successfully sent to {email}"}, "error": {"title": "Failed to send email", "unknown": "Unknown error", "network": "Network error"}}, "deposit": {"error": "Deposit request failed", "redirecting": "Redirecting to payment page...", "redirectingDesc": "Deposit ${depositAmount}{feeInfo}, actual payment ${totalAmount}", "feeInfo": " (including fee ${amount})", "submitted": "Deposit request submitted", "submittedDesc": "Processing your deposit request, please wait...", "failed": "Depo<PERSON><PERSON> failed"}, "withdraw": {"error": "<PERSON><PERSON>wal request failed", "submitted": "<PERSON><PERSON>wal request submitted", "submittedDesc": "Expected to arrive in 1-3 business days", "failed": "<PERSON><PERSON><PERSON> failed"}}}