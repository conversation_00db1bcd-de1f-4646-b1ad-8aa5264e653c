import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// 筛选参数类型
interface TaskReviewFilters {
  search?: string;
  platform?: string;
  evidenceStatus?: string;
  timeRange?: string;
  page?: number;
  limit?: number;
}

// 待审核委托列表响应类型
interface PendingTasksResponse {
  success: boolean;
  data: {
    tasks: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

// 审核操作数据类型
interface ReviewTaskData {
  action: 'approve' | 'reject';
}

// 获取待审核委托列表
export function usePendingTasks(filters: TaskReviewFilters = {}) {
  return useQuery({
    queryKey: ['admin-pending-tasks', filters],
    queryFn: async (): Promise<PendingTasksResponse> => {
      // 构建查询参数
      const params = new URLSearchParams();

      if (filters.search) {
        params.append('search', filters.search);
      }

      if (filters.platform) {
        params.append('platform', filters.platform);
      }

      if (filters.evidenceStatus) {
        params.append('evidenceStatus', filters.evidenceStatus);
      }

      if (filters.timeRange) {
        params.append('timeRange', filters.timeRange);
      }

      params.append('page', (filters.page || 1).toString());
      params.append('limit', (filters.limit || 20).toString());

      const response = await fetch(
        `/api/admin/tasks/pending?${params.toString()}`,
      );

      if (!response.ok) {
        throw new Error('获取待审核委托失败');
      }

      return response.json();
    },
    staleTime: 30 * 1000, // 30秒内数据保持新鲜
  });
}

// 审核委托
export function useReviewTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      taskId,
      data,
    }: {
      taskId: string;
      data: ReviewTaskData;
    }) => {
      const response = await fetch(`/api/admin/tasks/${taskId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '审核委托失败');
      }

      return response.json();
    },
    onSuccess: data => {
      // 显示成功提示
      toast.success(data.message, {
        duration: 3000,
      });

      // 刷新待审核委托列表
      queryClient.invalidateQueries({ queryKey: ['admin-pending-tasks'] });
      // 刷新委托统计数据
      queryClient.invalidateQueries({ queryKey: ['admin-task-stats'] });
    },
    onError: (error: Error) => {
      // 显示错误提示
      toast.error('审核失败', {
        description: error.message,
        duration: 5000,
      });
    },
  });
}

// 获取委托统计数据
export function useTaskStats() {
  return useQuery({
    queryKey: ['admin-task-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/tasks/stats');

      if (!response.ok) {
        throw new Error('获取委托统计失败');
      }

      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5分钟内数据保持新鲜
  });
}
