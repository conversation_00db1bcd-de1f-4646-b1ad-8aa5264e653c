{
  "recommendations": [
    // ===== AI 开发助手 =====
    "augment.vscode-augment",

    // ===== 核心开发工具 =====
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",

    // ===== React/Next.js 开发 =====
    "dsznajder.es7-react-js-snippets",
    "burkeholland.simple-react-snippets",
    "ms-vscode.vscode-react-native",
    "steoates.autoimport-es6-ts",

    // ===== Git 和版本控制 =====
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.githistory",
    "github.vscode-pull-request-github",

    // ===== 测试工具 =====
    "orta.vscode-jest",
    "ms-playwright.playwright",
    "hbenl.vscode-test-explorer",

    // ===== 代码质量和格式化 =====
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "oderwat.indent-rainbow",
    "aaron-bond.better-comments",
    "gruntfuggly.todo-tree",

    // ===== 主题和图标 =====
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    "github.github-vscode-theme",
    "dracula-theme.theme-dracula",

    // ===== 实用工具 =====
    "ms-vscode.live-server",
    "ritwickdey.liveserver",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.hexeditor",
    "ms-vscode.vscode-markdown",
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",

    // ===== 数据库和API =====
    "ms-vscode.vscode-sqlite",
    "humao.rest-client",
    "rangav.vscode-thunder-client",

    // ===== 容器和部署 =====
    "ms-vscode-remote.remote-containers",
    "ms-azuretools.vscode-docker",

    // ===== 代码片段和自动化 =====
    "ms-vscode.vscode-snippet",
    "alefragnani.bookmarks",
    "ms-vscode.vscode-multi-cursor",

    // ===== 性能和调试 =====
    "ms-vscode.vscode-js-debug",
    "ms-vscode.vscode-js-debug-companion",
    "firefox-devtools.vscode-firefox-debug",

    // ===== 环境变量和配置 =====
    "mikestead.dotenv",
    "ms-vscode.vscode-env-file",

    // ===== 文档和注释 =====
    "mintlify.document",
    "ms-vscode.vscode-jsdoc",

    // ===== 代码导航和搜索 =====
    "ms-vscode.vscode-goto-symbol-stack",
    "ms-vscode.references-view",

    // ===== 项目管理 =====
    "alefragnani.project-manager",
    "ms-vscode.vscode-workspace-trust"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify",
    "ms-vscode.vscode-css",
    "ms-vscode.vscode-html"
  ]
}
