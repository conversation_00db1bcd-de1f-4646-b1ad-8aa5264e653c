#!/usr/bin/env node

/**
 * 测试定时任务端点脚本
 * 使用方法: node scripts/test-cron-endpoints.js
 */

const http = require('http');
const https = require('https');
const readline = require('readline');

// 定时任务端点配置
const ENDPOINTS = [
  {
    name: '自动审核物流',
    path: '/api/cron/auto-approve-reviews',
    description: '检查超过24小时未审核的物流提交并自动通过',
  },
  {
    name: '自动确认收货',
    path: '/api/cron/auto-confirm-delivery',
    description: '检查超过30天未确认的订单并自动确认收货',
  },
  {
    name: '过期任务清理',
    path: '/api/cron/expire-tasks',
    description: '清理已过期的任务',
  },
  {
    name: '会员过期处理',
    path: '/api/cron/expire-membership',
    description: '处理过期的会员资格',
  },
];

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 提示用户输入
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer.trim());
    });
  });
}

// HTTP请求函数
function makeRequest(url, method = 'GET', headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname,
      method: method,
      headers: {
        'User-Agent': 'RefundGo-CronTest/1.0',
        ...headers,
      },
      timeout: 30000, // 30秒超时
    };

    const req = client.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData,
        });
      });
    });

    req.on('error', error => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

// 测试单个端点
async function testEndpoint(baseUrl, endpoint, cronSecret = null) {
  const url = `${baseUrl}${endpoint.path}`;
  const headers = {};

  if (cronSecret) {
    headers['Authorization'] = `Bearer ${cronSecret}`;
  }

  console.log(`\n🔍 测试: ${endpoint.name}`);
  console.log(`   URL: ${url}`);
  console.log(`   描述: ${endpoint.description}`);

  try {
    // 测试 GET 请求（开发环境）
    console.log('   📡 发送 GET 请求...');
    const getResponse = await makeRequest(url, 'GET');

    if (getResponse.statusCode === 200) {
      console.log('   ✅ GET 请求成功');
      try {
        const data = JSON.parse(getResponse.data);
        if (data.message) {
          console.log(`   📝 响应: ${data.message}`);
        }
        if (data.processed !== undefined) {
          console.log(`   📊 处理数量: ${data.processed}`);
        }
      } catch (e) {
        console.log(`   📝 响应: ${getResponse.data.substring(0, 100)}...`);
      }
    } else {
      console.log(`   ⚠️  GET 请求返回状态码: ${getResponse.statusCode}`);
    }

    // 如果有 CRON_SECRET，测试 POST 请求（生产环境）
    if (cronSecret) {
      console.log('   📡 发送 POST 请求（带认证）...');
      const postResponse = await makeRequest(url, 'POST', headers);

      if (postResponse.statusCode === 200) {
        console.log('   ✅ POST 请求成功');
        try {
          const data = JSON.parse(postResponse.data);
          if (data.message) {
            console.log(`   📝 响应: ${data.message}`);
          }
          if (data.processed !== undefined) {
            console.log(`   📊 处理数量: ${data.processed}`);
          }
        } catch (e) {
          console.log(`   📝 响应: ${postResponse.data.substring(0, 100)}...`);
        }
      } else if (postResponse.statusCode === 401) {
        console.log('   ❌ POST 请求认证失败 - 请检查 CRON_SECRET');
      } else {
        console.log(`   ⚠️  POST 请求返回状态码: ${postResponse.statusCode}`);
      }
    }

    return true;
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}`);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🧪 RefundGo 定时任务端点测试工具');
  console.log('=====================================\n');

  try {
    // 获取用户输入
    const baseUrl =
      (await askQuestion('请输入基础URL (默认: http://localhost:3000): ')) ||
      'http://localhost:3000';
    const cronSecret = await askQuestion(
      '请输入 CRON_SECRET (可选，用于测试POST请求): '
    );

    // 验证URL格式
    try {
      new URL(baseUrl);
    } catch (e) {
      console.log('❌ 无效的URL格式');
      process.exit(1);
    }

    console.log('\n🚀 开始测试定时任务端点...');

    let successCount = 0;

    for (const endpoint of ENDPOINTS) {
      const success = await testEndpoint(baseUrl, endpoint, cronSecret);
      if (success) {
        successCount++;
      }

      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n=====================================');
    console.log(
      `✅ 测试完成! ${successCount}/${ENDPOINTS.length} 个端点测试成功`
    );

    if (successCount < ENDPOINTS.length) {
      console.log('\n⚠️  部分端点测试失败，请检查:');
      console.log('   1. 服务器是否正在运行');
      console.log('   2. URL 是否正确');
      console.log('   3. CRON_SECRET 是否正确（如果测试POST请求）');
      console.log('   4. 网络连接是否正常');
    }

    console.log('\n📝 提示:');
    console.log('   - GET 请求用于开发环境手动触发');
    console.log('   - POST 请求用于生产环境定时任务调用');
    console.log('   - 生产环境必须提供正确的 Authorization 头');
  } catch (error) {
    console.log('❌ 测试过程中出现错误:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n测试已取消');
  rl.close();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testEndpoint, ENDPOINTS };
