'use client';

import { <PERSON><PERSON><PERSON><PERSON>gle, Trash2, <PERSON><PERSON><PERSON><PERSON> } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCancelTask } from '@/hooks/use-cancel-task';
import { PublishedTask } from '@/hooks/use-published-tasks';

interface CancelTaskDialogProps {
  children: React.ReactNode;
  task: PublishedTask;
  onCancel?: (taskId: string) => void;
}

export function CancelTaskDialog({
  children,
  task,
  onCancel,
}: CancelTaskDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const cancelTaskMutation = useCancelTask();

  const handleConfirm = async () => {
    try {
      await cancelTaskMutation.mutateAsync(task.id);
      onCancel?.(task.id);
      setIsOpen(false);
    } catch (error) {
      // 错误处理已在hook中完成
    }
  };

  const handleCancel = () => {
    if (!cancelTaskMutation.isPending) {
      setIsOpen(false);
    }
  };

  const t = useTranslations('my-published-tasks');

  const getStatusText = () => {
    switch (task.status) {
      case 'PENDING':
        return t('status.PENDING');
      case 'RECRUITING':
        return t('status.RECRUITING');
      default:
        return task.status;
    }
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return t('time.notSet');
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2 text-red-600'>
            <AlertTriangle className='h-5 w-5' />
            {t('dialog.cancelTask.title')}
          </DialogTitle>
          <DialogDescription className='text-left'>
            {t('dialog.cancelTask.description')}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 委托信息 */}
          <div className='p-4 bg-muted/50 rounded-lg space-y-3'>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-muted-foreground'>
                {t('dialog.cancelTask.taskId')}:
              </span>
              <span className='font-medium'>{task.id}</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-muted-foreground'>
                {t('dialog.cancelTask.currentStatus')}:
              </span>
              <span className='font-medium text-blue-600'>
                {getStatusText()}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-muted-foreground'>
                {t('dialog.cancelTask.deductionAmount')}:
              </span>
              <span className='font-medium text-green-600'>
                ${task.finalTotal.toFixed(2)}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-muted-foreground'>
                {t('dialog.cancelTask.publishTime')}:
              </span>
              <span className='font-medium'>
                {formatDateTime(task.createdAt)}
              </span>
            </div>
          </div>

          {/* 取消说明 */}
          <div className='p-4 bg-orange-50 border border-orange-200 rounded-lg'>
            <div className='flex items-start gap-2'>
              <AlertTriangle className='h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0' />
              <div>
                <h4 className='font-medium text-orange-800 mb-2'>
                  {t('dialog.cancelTask.cancelNoticeTitle')}
                </h4>
                <ul className='text-sm text-orange-700 space-y-1'>
                  {task.status === 'PENDING' ? (
                    <>
                      <li>• {t('dialog.cancelTask.pendingNotice1')}</li>
                      <li>• {t('dialog.cancelTask.pendingNotice2')}</li>
                      <li>• {t('dialog.cancelTask.pendingNotice3')}</li>
                    </>
                  ) : (
                    <>
                      <li>• {t('dialog.cancelTask.recruitingNotice1')}</li>
                      <li>• {t('dialog.cancelTask.recruitingNotice2')}</li>
                      <li>• {t('dialog.cancelTask.recruitingNotice3')}</li>
                      <li>• {t('dialog.cancelTask.recruitingNotice4')}</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className='flex gap-3 pt-2'>
            <Button
              type='button'
              variant='outline'
              onClick={handleCancel}
              disabled={cancelTaskMutation.isPending}
              className='flex-1'
            >
              {t('dialog.cancelTask.cancel')}
            </Button>
            <Button
              type='button'
              variant='outline'
              onClick={handleConfirm}
              disabled={cancelTaskMutation.isPending}
              className='flex-1 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300'
            >
              {cancelTaskMutation.isPending ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2'></div>
                  {t('actions.cancelling')}
                </>
              ) : (
                <>
                  <Trash2 className='h-4 w-4 mr-1' />
                  {t('dialog.cancelTask.confirm')}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
