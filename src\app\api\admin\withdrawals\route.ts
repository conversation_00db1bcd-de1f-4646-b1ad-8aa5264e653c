import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

// GET /api/admin/withdrawals - 获取所有提现申请
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const withdrawMethod = searchParams.get('withdrawMethod');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { id: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (withdrawMethod) {
      where.withdrawMethod = withdrawMethod;
    }

    if (status) {
      where.status = status;
    }

    // 获取提现申请列表
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            username: true,
          },
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 获取总数
    const total = await prisma.withdrawalRequest.count({ where });

    // 获取统计数据
    const stats = await prisma.withdrawalRequest.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    });

    const statsMap = stats.reduce(
      (acc: Record<string, number>, item: any) => {
        acc[item.status] = item._count.status;
        return acc;
      },
      {} as Record<string, number>,
    );

    // 获取不同提现方式的统计
    const methodStats = await prisma.withdrawalRequest.groupBy({
      by: ['withdrawMethod'],
      _count: {
        withdrawMethod: true,
      },
    });

    const methodStatsMap = methodStats.reduce(
      (acc: Record<string, number>, item: any) => {
        acc[item.withdrawMethod] = item._count.withdrawMethod;
        return acc;
      },
      {} as Record<string, number>,
    );

    return NextResponse.json({
      success: true,
      data: {
        withdrawalRequests,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
        stats: {
          total,
          pending: statsMap['PENDING'] || 0,
          approved: statsMap['APPROVED'] || 0,
          rejected: statsMap['REJECTED'] || 0,
          completed: statsMap['COMPLETED'] || 0,
          bankCard:
            (methodStatsMap['BANK_CARD'] || 0) +
            (methodStatsMap['ALIPAY'] || 0) +
            (methodStatsMap['WECHAT'] || 0),
          crypto:
            (methodStatsMap['USDT_ERC20'] || 0) +
            (methodStatsMap['USDT_TRC20'] || 0),
        },
      },
    });
  } catch (error) {
    console.error('获取提现申请列表失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}
