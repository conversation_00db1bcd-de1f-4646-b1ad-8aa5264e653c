export interface EvidenceReviewPublisherEmailData {
  publisherName: string;
  publisherEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  reviewedAt: string;
  approved: boolean;
  rejectReason?: string;
  evidenceFeeRefund?: number;
}

export const evidenceReviewPublisherTemplate = (
  data: EvidenceReviewPublisherEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>证据审核结果通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: ${data.approved ? '#28a745' : '#dc3545'}; margin-bottom: 20px; text-align: center;">
        ${data.approved ? '✅ 证据审核通过' : '❌ 证据审核未通过'}
      </h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.publisherName}！您提交的委托证据审核${data.approved ? '已通过' : '未通过'}。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">审核时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.reviewedAt}</span>
        </div>
      </div>
      
      ${
        data.approved
          ? `
        <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p style="color: #155724; margin: 0; font-size: 14px;">
            ✅ <strong>证据审核通过：</strong><br>
            • 您提交的证据已通过系统审核<br>
            • 证据费用已退还到您的账户余额<br>
            • 退还金额：$${data.evidenceFeeRefund || 0}<br>
            • 您可以继续等待委托完成结果<br>
            • 证据审核通过有助于提高委托完成率
          </p>
        </div>
      `
          : `
        <div style="background: #f8d7da; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p style="color: #721c24; margin: 0; font-size: 14px;">
            ❌ <strong>证据审核未通过：</strong><br>
            • 拒绝原因：${data.rejectReason || '未提供具体原因'}<br>
            • 您可以重新提交更完整的证据<br>
            • 证据费用将保持冻结状态，直到重新审核通过<br>
            • 如有疑问，请联系客服咨询<br>
            • 建议仔细阅读证据提交要求
          </p>
        </div>
      `
      }
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
