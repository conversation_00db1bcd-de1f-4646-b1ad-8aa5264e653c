import { routing } from '@/i18n/routing';

// 从路由配置中导出语言类型
export type Locale = (typeof routing.locales)[number];

// 翻译命名空间类型 - 使用 kebab-case 以匹配文件名
export type TranslationNamespace =
  | 'common'
  | 'navigation'
  | 'homepage'
  | 'dashboard'
  | 'tasks'
  | 'auth'
  | 'wallet'
  | 'transactions'
  | 'admin'
  | 'email'
  | 'tickets'
  | 'publish'
  | 'review'
  | 'membership'
  | 'account-security'
  | 'my-accepted-tasks'
  | 'my-published-tasks'
  | 'shop-whitelist'
  | 'whitelist'
  | 'legal'
  | 'logistics'
  | 'logistics-info'
  | 'messages'
  | 'footer';

// 为了向后兼容，提供 PascalCase 到 kebab-case 的映射
export const NamespaceMapping = {
  Common: 'common',
  Navigation: 'navigation',
  HomePage: 'homepage',
  Dashboard: 'dashboard',
  Tasks: 'tasks',
  Auth: 'auth',
  Wallet: 'wallet',
  transactions: 'transactions',
  Admin: 'admin',
  Email: 'email',
  Tickets: 'tickets',
  Publish: 'publish',
  Review: 'review',
  Membership: 'membership',
  AccountSecurity: 'account-security',
  MyAcceptedTasks: 'my-accepted-tasks',
  MyPublishedTasks: 'my-published-tasks',
  ShopWhitelist: 'shop-whitelist',
  Whitelist: 'whitelist',
  Legal: 'legal',
  Logistics: 'logistics',
  LogisticsInfo: 'logistics-info',
  Messages: 'messages',
  Footer: 'footer',
} as const;

// 翻译键类型（用于类型安全的翻译键）
export type TranslationKey<T extends TranslationNamespace> = string;

// 翻译参数类型
export interface TranslationParams {
  [key: string]: string | number | boolean | Date;
}

// i18n Ally 配置类型
export interface I18nAllyConfig {
  localesPaths: string[];
  pathMatcher: string;
  namespace: boolean;
  keystyle: 'nested' | 'flat';
  enabledParsers: string[];
  sourceLanguage: Locale;
  displayLanguage: Locale;
  enabledFrameworks: string[];
  extract: {
    autoDetect: boolean;
    keygenStyle: string;
    keyMaxLength: number;
    ignoredByFiles: Record<string, string[]>;
  };
  translate: {
    engines: string[];
    fallbackLocale: Locale;
    promptSource: boolean;
  };
  annotations: {
    enabled: boolean;
    inPlace: boolean;
  };
  sortKeys: boolean;
  fileStructure: string;
  ignoredLocales: string[];
  ignoredNamespaces: string[];
  rules: Record<string, string>;
  editor: {
    preferEditor: boolean;
    autoCompletion: boolean;
    hover: boolean;
  };
}

// 扩展 next-intl 的 AppConfig 接口
declare module 'next-intl' {
  interface AppConfig {
    Locale: Locale;
  }
}
