'use client';

import {
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Calculator,
  Percent,
  DollarSign,
  CreditCard,
  // ShoppingCart,
  // Smartphone,
  AlertTriangle,
  Loader2,
  Wallet,
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  usePlatforms,
  useCreatePlatform,
  useUpdatePlatform,
  useDeletePlatform,
  useCategories,
  useCreateCategory,
  useUpdateCategory,
  useDeleteCategory,
  useChargebackTypes,
  useCreateChargebackType,
  useUpdateChargebackType,
  useDeleteChargebackType,
  usePaymentMethods,
  useCreatePaymentMethod,
  useUpdatePaymentMethod,
  useDeletePaymentMethod,
  useSystemRate,
  useUpdateSystemRate,
} from '@/hooks/use-rates-api';
import { Status } from '@/types/rates';

// 导入类型定义
// import type { ChargebackType, PaymentMethod } from '@/types/rates';

export default function RatesSettingsPage() {
  // 使用真实 API 数据
  const { data: platforms = [], isLoading: platformsLoading } = usePlatforms();
  const { data: categories = [], isLoading: categoriesLoading } =
    useCategories();
  const { data: chargebackTypes = [], isLoading: chargebackTypesLoading } =
    useChargebackTypes();
  const { data: paymentMethods = [], isLoading: paymentMethodsLoading } =
    usePaymentMethods();
  const { data: systemRate, isLoading: _systemRateLoading } = useSystemRate();

  // API mutations
  const createPlatformMutation = useCreatePlatform();
  const updatePlatformMutation = useUpdatePlatform();
  const deletePlatformMutation = useDeletePlatform();
  const createCategoryMutation = useCreateCategory();
  const updateCategoryMutation = useUpdateCategory();
  const deleteCategoryMutation = useDeleteCategory();
  const createChargebackTypeMutation = useCreateChargebackType();
  const updateChargebackTypeMutation = useUpdateChargebackType();
  const deleteChargebackTypeMutation = useDeleteChargebackType();
  const createPaymentMethodMutation = useCreatePaymentMethod();
  const updatePaymentMethodMutation = useUpdatePaymentMethod();
  const deletePaymentMethodMutation = useDeletePaymentMethod();
  const updateSystemRateMutation = useUpdateSystemRate();

  // 系统费率的本地状态（用于表单编辑）
  const [localSystemRates, setLocalSystemRates] = useState({
    noEvidenceExtraRate: undefined as number | undefined,
    depositRatio: undefined as number | undefined,
    bankWithdrawalRate: undefined as number | undefined,
    erc20WithdrawalRate: undefined as number | undefined,
    trc20WithdrawalRate: undefined as number | undefined,
    minimumWithdrawalAmount: undefined as number | undefined,
  });

  // 当 systemRate 数据加载完成时，更新本地状态
  useEffect(() => {
    if (systemRate) {
      setLocalSystemRates({
        noEvidenceExtraRate: systemRate.noEvidenceExtraRate,
        depositRatio: systemRate.depositRatio,
        bankWithdrawalRate: (systemRate as any).bankWithdrawalRate,
        erc20WithdrawalRate: (systemRate as any).erc20WithdrawalRate,
        trc20WithdrawalRate: (systemRate as any).trc20WithdrawalRate,
        minimumWithdrawalAmount: (systemRate as any).minimumWithdrawalAmount,
      });
    }
  }, [systemRate]);

  // 对话框状态
  const [isAddPlatformOpen, setIsAddPlatformOpen] = useState(false);
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isAddChargebackOpen, setIsAddChargebackOpen] = useState(false);
  const [isAddPaymentOpen, setIsAddPaymentOpen] = useState(false);

  // 编辑对话框状态
  const [isEditPlatformOpen, setIsEditPlatformOpen] = useState(false);
  const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false);
  const [isEditChargebackOpen, setIsEditChargebackOpen] = useState(false);
  const [isEditPaymentOpen, setIsEditPaymentOpen] = useState(false);

  // 表单状态
  const [newPlatform, setNewPlatform] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [newChargeback, setNewChargeback] = useState({ name: '', rate: 0 });
  const [newPayment, setNewPayment] = useState({ name: '', rate: 0 });

  // 编辑表单状态
  const [editingPlatform, setEditingPlatform] = useState<{
    id: string;
    name: string;
    status: Status;
  } | null>(null);
  const [editingCategory, setEditingCategory] = useState<{
    id: string;
    name: string;
    status: Status;
  } | null>(null);
  const [editingChargeback, setEditingChargeback] = useState<{
    id: string;
    name: string;
    rate: number;
    status: Status;
  } | null>(null);
  const [editingPayment, setEditingPayment] = useState<{
    id: string;
    name: string;
    rate: number;
    status: Status;
  } | null>(null);

  // 添加平台
  const handleAddPlatform = () => {
    if (!newPlatform.trim()) {
      toast.error('请输入平台名称');
      return;
    }

    createPlatformMutation.mutate({
      name: newPlatform,
      status: Status.ACTIVE,
    });
  };

  // 监听平台创建成功，重置表单
  React.useEffect(() => {
    if (createPlatformMutation.isSuccess) {
      setNewPlatform('');
      setIsAddPlatformOpen(false);
      createPlatformMutation.reset();
    }
  }, [createPlatformMutation.isSuccess, createPlatformMutation]);

  // 添加商品分类
  const handleAddCategory = () => {
    if (!newCategory.trim()) {
      toast.error('请输入分类名称');
      return;
    }

    createCategoryMutation.mutate({
      name: newCategory,
      status: Status.ACTIVE,
    });
  };

  // 监听分类创建成功，重置表单
  React.useEffect(() => {
    if (createCategoryMutation.isSuccess) {
      setNewCategory('');
      setIsAddCategoryOpen(false);
      createCategoryMutation.reset();
    }
  }, [createCategoryMutation.isSuccess, createCategoryMutation]);

  // 监听分类更新成功，重置表单
  React.useEffect(() => {
    if (updateCategoryMutation.isSuccess) {
      setEditingCategory(null);
      setIsEditCategoryOpen(false);
      updateCategoryMutation.reset();
    }
  }, [updateCategoryMutation.isSuccess, updateCategoryMutation]);

  // 添加拒付类型
  const handleAddChargeback = () => {
    if (!newChargeback.name.trim()) {
      toast.error('请输入拒付类型名称');
      return;
    }
    if (newChargeback.rate <= 0) {
      toast.error('请输入有效的费率');
      return;
    }

    createChargebackTypeMutation.mutate({
      name: newChargeback.name,
      rate: newChargeback.rate,
      status: Status.ACTIVE,
    });
  };

  // 监听拒付类型创建成功，重置表单
  React.useEffect(() => {
    if (createChargebackTypeMutation.isSuccess) {
      setNewChargeback({ name: '', rate: 0 });
      setIsAddChargebackOpen(false);
      createChargebackTypeMutation.reset();
    }
  }, [createChargebackTypeMutation.isSuccess, createChargebackTypeMutation]);

  // 添加支付方式
  const handleAddPayment = () => {
    if (!newPayment.name.trim()) {
      toast.error('请输入支付方式名称');
      return;
    }
    if (newPayment.rate <= 0) {
      toast.error('请输入有效的费率');
      return;
    }

    createPaymentMethodMutation.mutate({
      name: newPayment.name,
      rate: newPayment.rate,
      status: Status.ACTIVE,
    });
  };

  // 监听支付方式创建成功，重置表单
  React.useEffect(() => {
    if (createPaymentMethodMutation.isSuccess) {
      setNewPayment({ name: '', rate: 0 });
      setIsAddPaymentOpen(false);
      createPaymentMethodMutation.reset();
    }
  }, [createPaymentMethodMutation.isSuccess, createPaymentMethodMutation]);

  // 编辑处理函数
  const handleEditPlatform = (platform: any) => {
    setEditingPlatform({
      id: platform.id,
      name: platform.name,
      status: platform.status,
    });
    setIsEditPlatformOpen(true);
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory({
      id: category.id,
      name: category.name,
      status: category.status,
    });
    setIsEditCategoryOpen(true);
  };

  const handleEditChargeback = (chargeback: any) => {
    setEditingChargeback({
      id: chargeback.id,
      name: chargeback.name,
      rate: chargeback.rate,
      status: chargeback.status,
    });
    setIsEditChargebackOpen(true);
  };

  const handleEditPayment = (payment: any) => {
    setEditingPayment({
      id: payment.id,
      name: payment.name,
      rate: payment.rate,
      status: payment.status,
    });
    setIsEditPaymentOpen(true);
  };

  // 更新处理函数
  const handleUpdatePlatform = () => {
    if (!editingPlatform?.name.trim()) {
      toast.error('请输入平台名称');
      return;
    }

    updatePlatformMutation.mutate({
      id: editingPlatform.id,
      data: {
        name: editingPlatform.name,
        status: editingPlatform.status,
      },
    });
  };

  const handleUpdateCategory = () => {
    if (!editingCategory?.name.trim()) {
      toast.error('请输入分类名称');
      return;
    }

    updateCategoryMutation.mutate({
      id: editingCategory.id,
      data: {
        name: editingCategory.name,
        status: editingCategory.status,
      },
    });
  };

  const handleUpdateChargeback = () => {
    if (!editingChargeback?.name.trim()) {
      toast.error('请输入拒付类型名称');
      return;
    }
    if (editingChargeback.rate <= 0) {
      toast.error('请输入有效的费率');
      return;
    }

    updateChargebackTypeMutation.mutate({
      id: editingChargeback.id,
      data: {
        name: editingChargeback.name,
        rate: editingChargeback.rate,
        status: editingChargeback.status,
      },
    });
  };

  const handleUpdatePayment = () => {
    if (!editingPayment?.name.trim()) {
      toast.error('请输入支付方式名称');
      return;
    }
    if (editingPayment.rate <= 0) {
      toast.error('请输入有效的费率');
      return;
    }

    updatePaymentMethodMutation.mutate({
      id: editingPayment.id,
      data: {
        name: editingPayment.name,
        rate: editingPayment.rate,
        status: editingPayment.status,
      },
    });
  };

  // 监听更新成功，重置表单
  React.useEffect(() => {
    if (updatePlatformMutation.isSuccess) {
      setEditingPlatform(null);
      setIsEditPlatformOpen(false);
      updatePlatformMutation.reset();
    }
  }, [updatePlatformMutation.isSuccess, updatePlatformMutation]);

  React.useEffect(() => {
    if (updateChargebackTypeMutation.isSuccess) {
      setEditingChargeback(null);
      setIsEditChargebackOpen(false);
      updateChargebackTypeMutation.reset();
    }
  }, [updateChargebackTypeMutation.isSuccess, updateChargebackTypeMutation]);

  React.useEffect(() => {
    if (updatePaymentMethodMutation.isSuccess) {
      setEditingPayment(null);
      setIsEditPaymentOpen(false);
      updatePaymentMethodMutation.reset();
    }
  }, [updatePaymentMethodMutation.isSuccess, updatePaymentMethodMutation]);

  // 删除项目
  const handleDelete = (type: string, id: string | number) => {
    switch (type) {
      case 'platform':
        if (typeof id === 'string') {
          deletePlatformMutation.mutate(id);
        }
        break;
      case 'category':
        if (typeof id === 'string') {
          deleteCategoryMutation.mutate(id);
        }
        break;
      case 'chargeback':
        if (typeof id === 'string') {
          deleteChargebackTypeMutation.mutate(id);
        }
        break;
      case 'payment':
        if (typeof id === 'string') {
          deletePaymentMethodMutation.mutate(id);
        }
        break;
    }
  };

  // 更新系统费率
  const handleUpdateSystemRates = () => {
    // 验证所有字段都已填写
    if (
      localSystemRates.noEvidenceExtraRate === undefined ||
      localSystemRates.depositRatio === undefined ||
      localSystemRates.bankWithdrawalRate === undefined ||
      localSystemRates.erc20WithdrawalRate === undefined ||
      localSystemRates.trc20WithdrawalRate === undefined
    ) {
      toast.error('请填写所有费率字段');
      return;
    }

    updateSystemRateMutation.mutate({
      noEvidenceExtraRate: localSystemRates.noEvidenceExtraRate,
      depositRatio: localSystemRates.depositRatio,
      bankWithdrawalRate: localSystemRates.bankWithdrawalRate,
      erc20WithdrawalRate: localSystemRates.erc20WithdrawalRate,
      trc20WithdrawalRate: localSystemRates.trc20WithdrawalRate,
      minimumWithdrawalAmount: localSystemRates.minimumWithdrawalAmount,
    } as any);
  };

  // 状态转换函数
  const getStatusText = (status: Status | '启用' | '禁用') => {
    if (status === Status.ACTIVE || status === '启用') return '启用';
    if (status === Status.INACTIVE || status === '禁用') return '禁用';
    return status;
  };

  // 获取状态颜色
  const getStatusColor = (status: Status | '启用' | '禁用') => {
    const statusText = getStatusText(status);
    return statusText === '启用'
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800';
  };

  // 格式化时间显示
  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
    } catch (_error) {
      return dateString; // 如果格式化失败，返回原始字符串
    }
  };

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='费率设置' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题 */}
        <div className='space-y-1'>
          <h1 className='text-2xl font-bold tracking-tight'>费率设置</h1>
          <p className='text-sm text-muted-foreground'>
            管理平台、商品分类、拒付类型和支付方式的费率配置
          </p>
        </div>

        <Tabs defaultValue='platforms' className='w-full'>
          <TabsList className='grid w-full grid-cols-5'>
            <TabsTrigger value='platforms'>平台管理</TabsTrigger>
            <TabsTrigger value='categories'>商品分类</TabsTrigger>
            <TabsTrigger value='chargebacks'>拒付类型</TabsTrigger>
            <TabsTrigger value='payments'>支付方式</TabsTrigger>
            <TabsTrigger value='system'>系统费率</TabsTrigger>
          </TabsList>

          {/* 平台管理 */}
          <TabsContent value='platforms' className='space-y-4'>
            {/* 编辑平台对话框 */}
            <Dialog
              open={isEditPlatformOpen}
              onOpenChange={setIsEditPlatformOpen}
            >
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>编辑平台</DialogTitle>
                  <DialogDescription>修改平台信息</DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div>
                    <Label htmlFor='edit-platform-name'>平台名称</Label>
                    <Input
                      id='edit-platform-name'
                      placeholder='请输入平台名称'
                      value={editingPlatform?.name || ''}
                      onChange={e =>
                        setEditingPlatform(prev =>
                          prev ? { ...prev, name: e.target.value } : null,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor='edit-platform-status'>状态</Label>
                    <Select
                      value={editingPlatform?.status || Status.ACTIVE}
                      onValueChange={value =>
                        setEditingPlatform(prev =>
                          prev ? { ...prev, status: value as Status } : null,
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='选择状态' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Status.ACTIVE}>启用</SelectItem>
                        <SelectItem value={Status.INACTIVE}>禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant='outline'
                    onClick={() => setIsEditPlatformOpen(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleUpdatePlatform}>保存</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>平台管理</CardTitle>
                <Dialog
                  open={isAddPlatformOpen}
                  onOpenChange={setIsAddPlatformOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加平台
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加平台</DialogTitle>
                      <DialogDescription>添加新的电商平台</DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div>
                        <Label htmlFor='platform-name'>平台名称</Label>
                        <Input
                          id='platform-name'
                          placeholder='请输入平台名称'
                          value={newPlatform}
                          onChange={e => setNewPlatform(e.target.value)}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddPlatformOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddPlatform}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>平台名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {platformsLoading ? (
                      <TableRow>
                        <TableCell colSpan={4} className='text-center py-8'>
                          <Loader2 className='h-6 w-6 animate-spin mx-auto' />
                          <p className='mt-2 text-sm text-muted-foreground'>
                            加载中...
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : platforms.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className='text-center py-8'>
                          <p className='text-sm text-muted-foreground'>
                            暂无数据
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      platforms.map(platform => (
                        <TableRow key={platform.id}>
                          <TableCell className='font-medium'>
                            {platform.name}
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(platform.status)}>
                              {getStatusText(platform.status)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDateTime(platform.createdAt)}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                  onClick={() => handleEditPlatform(platform)}
                                >
                                  <Edit className='mr-2 h-4 w-4' />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className='text-red-600'
                                  onClick={() =>
                                    handleDelete('platform', platform.id)
                                  }
                                >
                                  <Trash2 className='mr-2 h-4 w-4' />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 商品分类 */}
          <TabsContent value='categories' className='space-y-4'>
            {/* 编辑分类对话框 */}
            <Dialog
              open={isEditCategoryOpen}
              onOpenChange={setIsEditCategoryOpen}
            >
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>编辑商品分类</DialogTitle>
                  <DialogDescription>修改商品分类信息</DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div>
                    <Label htmlFor='edit-category-name'>分类名称</Label>
                    <Input
                      id='edit-category-name'
                      placeholder='请输入分类名称'
                      value={editingCategory?.name || ''}
                      onChange={e =>
                        setEditingCategory(prev =>
                          prev ? { ...prev, name: e.target.value } : null,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor='edit-category-status'>状态</Label>
                    <Select
                      value={editingCategory?.status || Status.ACTIVE}
                      onValueChange={value =>
                        setEditingCategory(prev =>
                          prev ? { ...prev, status: value as Status } : null,
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='选择状态' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Status.ACTIVE}>启用</SelectItem>
                        <SelectItem value={Status.INACTIVE}>禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant='outline'
                    onClick={() => setIsEditCategoryOpen(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleUpdateCategory}>保存</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>商品分类</CardTitle>
                <Dialog
                  open={isAddCategoryOpen}
                  onOpenChange={setIsAddCategoryOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加分类
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加商品分类</DialogTitle>
                      <DialogDescription>添加新的商品分类</DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div>
                        <Label htmlFor='category-name'>分类名称</Label>
                        <Input
                          id='category-name'
                          placeholder='请输入分类名称'
                          value={newCategory}
                          onChange={e => setNewCategory(e.target.value)}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddCategoryOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddCategory}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>分类名称</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categoriesLoading ? (
                      <TableRow>
                        <TableCell colSpan={4} className='text-center py-8'>
                          <Loader2 className='h-6 w-6 animate-spin mx-auto' />
                          <p className='mt-2 text-sm text-muted-foreground'>
                            加载中...
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : categories.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className='text-center py-8'>
                          <p className='text-sm text-muted-foreground'>
                            暂无数据
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      categories.map(category => (
                        <TableRow key={category.id}>
                          <TableCell className='font-medium'>
                            {category.name}
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(category.status)}>
                              {getStatusText(category.status)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDateTime(category.createdAt)}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                  onClick={() => handleEditCategory(category)}
                                >
                                  <Edit className='mr-2 h-4 w-4' />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className='text-red-600'
                                  onClick={() =>
                                    handleDelete('category', category.id)
                                  }
                                >
                                  <Trash2 className='mr-2 h-4 w-4' />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 拒付类型 */}
          <TabsContent value='chargebacks' className='space-y-4'>
            {/* 编辑拒付类型对话框 */}
            <Dialog
              open={isEditChargebackOpen}
              onOpenChange={setIsEditChargebackOpen}
            >
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>编辑拒付类型</DialogTitle>
                  <DialogDescription>修改拒付类型信息和费率</DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div>
                    <Label htmlFor='edit-chargeback-name'>拒付名称</Label>
                    <Input
                      id='edit-chargeback-name'
                      placeholder='请输入拒付类型名称'
                      value={editingChargeback?.name || ''}
                      onChange={e =>
                        setEditingChargeback(prev =>
                          prev ? { ...prev, name: e.target.value } : null,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor='edit-chargeback-rate'>费率 (%)</Label>
                    <Input
                      id='edit-chargeback-rate'
                      type='number'
                      step='0.1'
                      placeholder='请输入费率'
                      value={editingChargeback?.rate || 0}
                      onChange={e =>
                        setEditingChargeback(prev =>
                          prev
                            ? { ...prev, rate: parseFloat(e.target.value) || 0 }
                            : null,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor='edit-chargeback-status'>状态</Label>
                    <Select
                      value={editingChargeback?.status || Status.ACTIVE}
                      onValueChange={value =>
                        setEditingChargeback(prev =>
                          prev ? { ...prev, status: value as Status } : null,
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='选择状态' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Status.ACTIVE}>启用</SelectItem>
                        <SelectItem value={Status.INACTIVE}>禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant='outline'
                    onClick={() => setIsEditChargebackOpen(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleUpdateChargeback}>保存</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>拒付类型</CardTitle>
                <Dialog
                  open={isAddChargebackOpen}
                  onOpenChange={setIsAddChargebackOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加拒付类型
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加拒付类型</DialogTitle>
                      <DialogDescription>
                        添加新的拒付类型和对应费率
                      </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div>
                        <Label htmlFor='chargeback-name'>拒付名称</Label>
                        <Input
                          id='chargeback-name'
                          placeholder='请输入拒付类型名称'
                          value={newChargeback.name}
                          onChange={e =>
                            setNewChargeback({
                              ...newChargeback,
                              name: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='chargeback-rate'>费率 (%)</Label>
                        <Input
                          id='chargeback-rate'
                          type='number'
                          step='0.1'
                          placeholder='请输入费率'
                          value={newChargeback.rate}
                          onChange={e =>
                            setNewChargeback({
                              ...newChargeback,
                              rate: parseFloat(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddChargebackOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddChargeback}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>拒付名称</TableHead>
                      <TableHead>费率</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {chargebackTypesLoading ? (
                      <TableRow>
                        <TableCell colSpan={5} className='text-center py-8'>
                          <Loader2 className='h-6 w-6 animate-spin mx-auto' />
                          <p className='mt-2 text-sm text-muted-foreground'>
                            加载中...
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : chargebackTypes.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className='text-center py-8'>
                          <p className='text-sm text-muted-foreground'>
                            暂无数据
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      chargebackTypes.map(chargeback => (
                        <TableRow key={chargeback.id}>
                          <TableCell className='font-medium'>
                            {chargeback.name}
                          </TableCell>
                          <TableCell>
                            <span className='font-medium text-orange-600'>
                              {chargeback.rate}%
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={getStatusColor(chargeback.status)}
                            >
                              {getStatusText(chargeback.status)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDateTime(chargeback.createdAt)}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleEditChargeback(chargeback)
                                  }
                                >
                                  <Edit className='mr-2 h-4 w-4' />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className='text-red-600'
                                  onClick={() =>
                                    handleDelete('chargeback', chargeback.id)
                                  }
                                >
                                  <Trash2 className='mr-2 h-4 w-4' />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 支付方式 */}
          <TabsContent value='payments' className='space-y-4'>
            {/* 编辑支付方式对话框 */}
            <Dialog
              open={isEditPaymentOpen}
              onOpenChange={setIsEditPaymentOpen}
            >
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>编辑支付方式</DialogTitle>
                  <DialogDescription>修改支付方式信息和费率</DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div>
                    <Label htmlFor='edit-payment-name'>支付名称</Label>
                    <Input
                      id='edit-payment-name'
                      placeholder='请输入支付方式名称'
                      value={editingPayment?.name || ''}
                      onChange={e =>
                        setEditingPayment(prev =>
                          prev ? { ...prev, name: e.target.value } : null,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor='edit-payment-rate'>费率 (%)</Label>
                    <Input
                      id='edit-payment-rate'
                      type='number'
                      step='0.1'
                      placeholder='请输入费率'
                      value={editingPayment?.rate || 0}
                      onChange={e =>
                        setEditingPayment(prev =>
                          prev
                            ? { ...prev, rate: parseFloat(e.target.value) || 0 }
                            : null,
                        )
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor='edit-payment-status'>状态</Label>
                    <Select
                      value={editingPayment?.status || Status.ACTIVE}
                      onValueChange={value =>
                        setEditingPayment(prev =>
                          prev ? { ...prev, status: value as Status } : null,
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='选择状态' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Status.ACTIVE}>启用</SelectItem>
                        <SelectItem value={Status.INACTIVE}>禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant='outline'
                    onClick={() => setIsEditPaymentOpen(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleUpdatePayment}>保存</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between'>
                <CardTitle>支付方式</CardTitle>
                <Dialog
                  open={isAddPaymentOpen}
                  onOpenChange={setIsAddPaymentOpen}
                >
                  <DialogTrigger asChild>
                    <Button size='sm'>
                      <Plus className='mr-2 h-4 w-4' />
                      添加支付方式
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加支付方式</DialogTitle>
                      <DialogDescription>
                        添加新的支付方式和对应费率
                      </DialogDescription>
                    </DialogHeader>
                    <div className='space-y-4'>
                      <div>
                        <Label htmlFor='payment-name'>支付名称</Label>
                        <Input
                          id='payment-name'
                          placeholder='请输入支付方式名称'
                          value={newPayment.name}
                          onChange={e =>
                            setNewPayment({
                              ...newPayment,
                              name: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='payment-rate'>费率 (%)</Label>
                        <Input
                          id='payment-rate'
                          type='number'
                          step='0.1'
                          placeholder='请输入费率'
                          value={newPayment.rate}
                          onChange={e =>
                            setNewPayment({
                              ...newPayment,
                              rate: parseFloat(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant='outline'
                        onClick={() => setIsAddPaymentOpen(false)}
                      >
                        取消
                      </Button>
                      <Button onClick={handleAddPayment}>添加</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>支付名称</TableHead>
                      <TableHead>费率</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentMethodsLoading ? (
                      <TableRow>
                        <TableCell colSpan={5} className='text-center py-8'>
                          <Loader2 className='h-6 w-6 animate-spin mx-auto' />
                          <p className='mt-2 text-sm text-muted-foreground'>
                            加载中...
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : paymentMethods.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className='text-center py-8'>
                          <p className='text-sm text-muted-foreground'>
                            暂无数据
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      paymentMethods.map(payment => (
                        <TableRow key={payment.id}>
                          <TableCell className='font-medium'>
                            {payment.name}
                          </TableCell>
                          <TableCell>
                            <span className='font-medium text-blue-600'>
                              {payment.rate}%
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(payment.status)}>
                              {getStatusText(payment.status)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatDateTime(payment.createdAt)}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                  onClick={() => handleEditPayment(payment)}
                                >
                                  <Edit className='mr-2 h-4 w-4' />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className='text-red-600'
                                  onClick={() =>
                                    handleDelete('payment', payment.id)
                                  }
                                >
                                  <Trash2 className='mr-2 h-4 w-4' />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 系统费率 */}
          <TabsContent value='system' className='space-y-4'>
            <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <AlertTriangle className='h-5 w-5 text-orange-500' />
                    无凭证额外费率
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='no-evidence-rate'>费率 (%)</Label>
                    <Input
                      id='no-evidence-rate'
                      type='number'
                      step='0.1'
                      placeholder='请输入'
                      value={localSystemRates.noEvidenceExtraRate ?? ''}
                      onChange={e =>
                        setLocalSystemRates({
                          ...localSystemRates,
                          noEvidenceExtraRate: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    当委托无拒付凭证时，额外收取的费率
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Percent className='h-5 w-5 text-blue-500' />
                    押金比例
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='deposit-ratio'>比例 (%)</Label>
                    <Input
                      id='deposit-ratio'
                      type='number'
                      step='0.1'
                      placeholder='请输入'
                      value={localSystemRates.depositRatio ?? ''}
                      onChange={e =>
                        setLocalSystemRates({
                          ...localSystemRates,
                          depositRatio: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    用户接单时需要缴纳的押金比例
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <CreditCard className='h-5 w-5 text-green-500' />
                    美元提现（支持个人账户与公司账户）费率
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='bank-withdrawal-rate'>费率 (%)</Label>
                    <Input
                      id='bank-withdrawal-rate'
                      type='number'
                      step='0.1'
                      placeholder='请输入'
                      value={localSystemRates.bankWithdrawalRate ?? ''}
                      onChange={e =>
                        setLocalSystemRates({
                          ...localSystemRates,
                          bankWithdrawalRate: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    用户通过美元提现时收取的手续费率
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Wallet className='h-5 w-5 text-purple-500' />
                    ERC20提现费率
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='erc20-withdrawal-rate'>费率 (%)</Label>
                    <Input
                      id='erc20-withdrawal-rate'
                      type='number'
                      step='0.1'
                      placeholder='请输入'
                      value={localSystemRates.erc20WithdrawalRate ?? ''}
                      onChange={e =>
                        setLocalSystemRates({
                          ...localSystemRates,
                          erc20WithdrawalRate: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    用户通过ERC20网络（USDT）提现时收取的手续费率
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Wallet className='h-5 w-5 text-indigo-500' />
                    TRC20提现费率
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='trc20-withdrawal-rate'>费率 (%)</Label>
                    <Input
                      id='trc20-withdrawal-rate'
                      type='number'
                      step='0.1'
                      placeholder='请输入'
                      value={localSystemRates.trc20WithdrawalRate ?? ''}
                      onChange={e =>
                        setLocalSystemRates({
                          ...localSystemRates,
                          trc20WithdrawalRate: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    用户通过TRC20网络（USDT）提现时收取的手续费率
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <DollarSign className='h-5 w-5 text-amber-500' />
                    最小提现额度
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label htmlFor='minimum-withdrawal-amount'>金额 ($)</Label>
                    <Input
                      id='minimum-withdrawal-amount'
                      type='number'
                      step='1'
                      placeholder='请输入'
                      value={localSystemRates.minimumWithdrawalAmount ?? ''}
                      onChange={e =>
                        setLocalSystemRates({
                          ...localSystemRates,
                          minimumWithdrawalAmount: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                    />
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    用户提现时的最小金额限制
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardContent className='pt-6'>
                <div className='flex justify-end'>
                  <Button onClick={handleUpdateSystemRates}>
                    <Calculator className='mr-2 h-4 w-4' />
                    保存系统费率配置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminPageLayout>
  );
}
