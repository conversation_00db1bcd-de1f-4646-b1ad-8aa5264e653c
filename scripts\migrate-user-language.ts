#!/usr/bin/env tsx

/**
 * 用户语言迁移脚本
 * 为现有用户设置默认的注册语言
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始用户语言迁移...');

  try {
    // 统计所有用户数量
    const totalUsers = await prisma.user.count();
    console.log(`数据库中共有 ${totalUsers} 个用户`);

    if (totalUsers === 0) {
      console.log('没有用户，退出');
      return;
    }

    // 显示当前的语言分布
    const currentLanguageStats = await prisma.user.groupBy({
      by: ['registrationLanguage'],
      _count: {
        registrationLanguage: true,
      },
    });

    console.log('\n当前的语言分布：');
    currentLanguageStats.forEach(stat => {
      console.log(
        `  ${stat.registrationLanguage}: ${stat._count.registrationLanguage} 用户`
      );
    });

    // 确保所有用户都有有效的语言设置
    const result = await prisma.user.updateMany({
      where: {
        registrationLanguage: {
          notIn: ['zh', 'en'],
        },
      },
      data: { registrationLanguage: 'en' },
    });

    if (result.count > 0) {
      console.log(`成功更新 ${result.count} 个用户的语言设置为默认值 'en'`);
    } else {
      console.log('所有用户的语言设置都是有效的');
    }

    // 显示迁移后的统计信息
    const finalLanguageStats = await prisma.user.groupBy({
      by: ['registrationLanguage'],
      _count: {
        registrationLanguage: true,
      },
    });

    console.log('\n迁移后的语言分布：');
    finalLanguageStats.forEach(stat => {
      console.log(
        `  ${stat.registrationLanguage}: ${stat._count.registrationLanguage} 用户`
      );
    });

    console.log('✅ 用户语言迁移完成！');
  } catch (error) {
    console.error('用户语言迁移失败:', error);
    process.exit(1);
  }
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
