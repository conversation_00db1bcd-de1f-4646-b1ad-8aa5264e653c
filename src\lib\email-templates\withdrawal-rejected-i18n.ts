import {
  emailStyles,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface WithdrawalRejectedEmailData {
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  withdrawalMethod: string;
  rejectionReason: string;
  processedAt: string;
  transactionId?: string;
  language?: 'zh' | 'en';
}

export const withdrawalRejectedTemplateI18n = (
  data: WithdrawalRejectedEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.processedAt, language);
  const formattedAmount = formatEmailAmount(
    data.amount,
    data.currency,
    language
  );

  // 脱敏处理交易ID
  const maskedTransactionId =
    data.transactionId && data.transactionId.length > 8
      ? `${data.transactionId.slice(0, 4)}****${data.transactionId.slice(-4)}`
      : data.transactionId || 'N/A';

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
          security: '为了您的账户安全，请勿向任何人透露您的账户信息。',
        },
        buttons: {
          retryWithdrawal: '重新申请提现',
          contactSupport: '联系客服',
          viewWallet: '查看钱包',
        },
      },
      notifications: {
        withdrawalRejected: {
          title: '提现申请被拒绝',
          greeting: '很抱歉，您的提现申请未能通过审核',
          description: '我们已仔细审核您的提现申请，但由于以下原因无法处理。',
          transactionDetails: '申请详情',
          amount: '申请金额：',
          withdrawalMethod: '提现方式：',
          transactionId: '申请编号：',
          processedAt: '处理时间：',
          rejectionReason: '拒绝原因',
          balanceRestored: '余额恢复',
          balanceText: '您的账户余额已恢复，可重新申请提现。',
          nextSteps: '接下来您可以：',
          stepsList: {
            review: '根据拒绝原因调整后重新提交申请',
            contact: '联系客服获取详细帮助和指导',
            check: '检查并完善您的账户信息',
          },
          tips: '温馨提示',
          tipsList: {
            balance: '您的账户余额已恢复，可重新申请提现',
            adjust: '请根据拒绝原因调整后重新提交申请',
            help: '如有疑问，请联系客服获取帮助',
          },
          apology: '我们为给您带来的不便深表歉意。',
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
          security:
            'For your account security, please do not share your account information with anyone.',
        },
        buttons: {
          retryWithdrawal: 'Retry Withdrawal',
          contactSupport: 'Contact Support',
          viewWallet: 'View Wallet',
        },
      },
      notifications: {
        withdrawalRejected: {
          title: 'Withdrawal Request Rejected',
          greeting:
            'We regret to inform you that your withdrawal request could not be approved',
          description:
            'We have carefully reviewed your withdrawal request, but we are unable to process it due to the following reason.',
          transactionDetails: 'Request Details',
          amount: 'Requested Amount:',
          withdrawalMethod: 'Withdrawal Method:',
          transactionId: 'Request ID:',
          processedAt: 'Processed At:',
          rejectionReason: 'Rejection Reason',
          balanceRestored: 'Balance Restored',
          balanceText:
            'Your account balance has been restored and you can submit a new withdrawal request.',
          nextSteps: 'What you can do next:',
          stepsList: {
            review:
              'Adjust based on the rejection reason and resubmit your request',
            contact:
              'Contact customer support for detailed assistance and guidance',
            check: 'Review and complete your account information',
          },
          tips: 'Important Notes',
          tipsList: {
            balance:
              'Your account balance has been restored, you can submit a new withdrawal request',
            adjust:
              'Please adjust based on the rejection reason and resubmit your request',
            help: 'If you have any questions, please contact customer support for assistance',
          },
          apology: 'We sincerely apologize for any inconvenience caused.',
        },
      },
    },
  };

  const t = translations[language];

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.withdrawalRejected.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .error-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #ef4444, #dc2626);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .amount-highlight {
          font-size: 24px;
          font-weight: bold;
          color: #ef4444;
          text-align: center;
          margin: 16px 0;
        }
        .transaction-card {
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #fecaca;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #7f1d1d;
          font-weight: 500;
        }
        .info-value {
          color: #991b1b;
          font-weight: 600;
        }
        .rejection-reason {
          background: #fee2e2;
          border: 1px solid #fca5a5;
          border-radius: 8px;
          padding: 16px;
          margin: 20px 0;
          color: #7f1d1d;
        }
        .balance-restored {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 8px;
          padding: 16px;
          margin: 20px 0;
        }
        .steps-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .steps-list li {
          padding: 8px 0;
          padding-left: 24px;
          position: relative;
          color: #1e40af;
        }
        .steps-list li:before {
          content: "→";
          position: absolute;
          left: 0;
          color: #3b82f6;
          font-weight: bold;
        }
        .tips-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .tips-list li {
          padding: 6px 0;
          padding-left: 20px;
          position: relative;
          color: #f59e0b;
          font-size: 14px;
        }
        .tips-list li:before {
          content: "•";
          position: absolute;
          left: 0;
          color: #f59e0b;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Error Icon -->
          <div class="error-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}
          </h2>
          
          <h3 style="text-align: center; color: #ef4444; margin-bottom: 16px;">
            ${t.notifications.withdrawalRejected.greeting}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.withdrawalRejected.description}
          </p>

          <!-- Amount Highlight -->
          <div class="amount-highlight">
            ${formattedAmount}
          </div>

          <!-- Transaction Details -->
          <div class="transaction-card">
            <h4 style="margin-top: 0; color: #7f1d1d;">
              ${t.notifications.withdrawalRejected.transactionDetails}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalRejected.amount}</span>
              <span class="info-value">${formattedAmount}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalRejected.withdrawalMethod}</span>
              <span class="info-value">${data.withdrawalMethod}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalRejected.transactionId}</span>
              <span class="info-value" style="font-family: monospace;">${maskedTransactionId}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalRejected.processedAt}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
          </div>

          <!-- Rejection Reason -->
          <div class="rejection-reason">
            <h4 style="margin-top: 0; color: #7f1d1d;">
              ❌ ${t.notifications.withdrawalRejected.rejectionReason}
            </h4>
            <p style="margin-bottom: 0; font-weight: 600;">
              ${data.rejectionReason}
            </p>
          </div>

          <!-- Balance Restored -->
          <div class="balance-restored">
            <h4 style="margin-top: 0; color: #16a34a;">
              ✅ ${t.notifications.withdrawalRejected.balanceRestored}
            </h4>
            <p style="margin-bottom: 0; color: #16a34a;">
              ${t.notifications.withdrawalRejected.balanceText}
            </p>
          </div>

          <!-- Next Steps -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              ${t.notifications.withdrawalRejected.nextSteps}
            </h4>
            <ul class="steps-list">
              <li>${t.notifications.withdrawalRejected.stepsList.review}</li>
              <li>${t.notifications.withdrawalRejected.stepsList.contact}</li>
              <li>${t.notifications.withdrawalRejected.stepsList.check}</li>
            </ul>
          </div>

          <!-- Tips -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              💡 ${t.notifications.withdrawalRejected.tips}
            </h4>
            <ul class="tips-list">
              <li>${t.notifications.withdrawalRejected.tipsList.balance}</li>
              <li>${t.notifications.withdrawalRejected.tipsList.adjust}</li>
              <li>${t.notifications.withdrawalRejected.tipsList.help}</li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/dashboard/wallet/withdraw" class="email-button email-button-primary">
              ${t.common.buttons.retryWithdrawal}
            </a>
            <a href="${process.env.DOMAIN}/support" class="email-button email-button-secondary">
              ${t.common.buttons.contactSupport}
            </a>
          </div>

          <!-- Apology -->
          <p style="text-align: center; color: #64748b; margin-top: 32px; font-style: italic;">
            ${t.notifications.withdrawalRejected.apology}
          </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.contact}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.security}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `.trim();
};
