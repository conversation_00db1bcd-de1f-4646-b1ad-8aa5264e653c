import { Upload, CheckCircle } from "lucide-react";

export interface VideoTutorialConfig {
  id: string;
  videoId: string;
  titleKey: string;
  descriptionKey: string;
  icon: typeof Upload | typeof CheckCircle;
  iconColor: string;
  category: 'post' | 'complete';
}

/**
 * Video Tutorial Configuration
 *
 * To update video IDs:
 * 1. Replace the videoId with the actual YouTube video ID
 * 2. The video ID is the part after 'v=' in the YouTube URL
 * 3. Example: https://www.youtube.com/watch?v=dQw4w9WgXcQ -> videoId: "dQw4w9WgXcQ"
 */
export const videoTutorials: VideoTutorialConfig[] = [
  {
    id: "main-tutorial",
    videoId: "LXb3EKWsInQ", // Main tutorial video
    titleKey: "tutorial.postTask.title",
    descriptionKey: "tutorial.postTask.description",
    icon: Upload,
    iconColor: "text-blue-600",
    category: 'post',
  },
];



/**
 * Utility function to get video thumbnail URL
 */
export function getVideoThumbnailUrl(videoId: string, quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'maxres'): string {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
}

/**
 * Utility function to get video embed URL
 */
export function getVideoEmbedUrl(videoId: string, options: {
  autoplay?: boolean;
  mute?: boolean;
  controls?: boolean;
  modestbranding?: boolean;
  rel?: boolean;
} = {}): string {
  const params = new URLSearchParams({
    rel: options.rel ? '1' : '0',
    modestbranding: options.modestbranding ? '1' : '0',
    fs: '1',
    cc_load_policy: '1',
    iv_load_policy: '3',
    autohide: '1',
    enablejsapi: '1',
    origin: typeof window !== 'undefined' ? window.location.origin : '',
    ...(options.autoplay && { autoplay: '1' }),
    ...(options.mute && { mute: '1' }),
    ...(options.controls === false && { controls: '0' }),
  });

  return `https://www.youtube-nocookie.com/embed/${videoId}?${params.toString()}`;
}
