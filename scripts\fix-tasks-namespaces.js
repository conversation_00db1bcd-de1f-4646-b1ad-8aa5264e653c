#!/usr/bin/env node

/**
 * 修复 Tasks 相关组件的命名空间问题
 * 
 * 将 Tasks.confirmDialog 和 Tasks.taskDetail 转换为 tasks.confirmDialog 和 tasks.taskDetail
 */

const fs = require('fs');
const path = require('path');

/**
 * 修复单个文件
 */
function fixFile(filePath, oldNamespace, newNamespace) {
  console.log(`修复文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`  ⚠️  文件不存在，跳过`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 替换模式
  const patterns = [
    new RegExp(`useTranslations\\(['"]${oldNamespace.replace('.', '\\.')}['"]\\)`, 'g'),
    new RegExp(`useTranslations\\(["']${oldNamespace.replace('.', '\\.')}["']\\)`, 'g'),
  ];
  
  patterns.forEach(pattern => {
    if (pattern.test(content)) {
      content = content.replace(pattern, `useTranslations('${newNamespace}')`);
      modified = true;
      console.log(`  ✅ 替换: ${oldNamespace} -> ${newNamespace}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  💾 文件已保存`);
    return true;
  } else {
    console.log(`  ⏭️  无需修改`);
    return false;
  }
}

/**
 * 验证翻译文件结构
 */
function validateTranslationFiles() {
  console.log('🔍 验证翻译文件结构...\n');
  
  const locales = ['zh', 'en'];
  const tasksFile = 'tasks.json';
  
  locales.forEach(locale => {
    const filePath = path.join(process.cwd(), 'messages', locale, tasksFile);
    
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${locale}/${tasksFile} 存在`);
      
      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        // 检查必要的键
        const requiredKeys = ['confirmDialog', 'taskDetail'];
        requiredKeys.forEach(key => {
          if (content[key]) {
            console.log(`  ✅ ${key} 部分存在`);
            
            // 检查子键
            if (key === 'confirmDialog') {
              const subKeys = ['title', 'description', 'basicInfo', 'evidenceInfo', 'actions'];
              subKeys.forEach(subKey => {
                if (content[key][subKey]) {
                  console.log(`    ✅ ${key}.${subKey} 存在`);
                } else {
                  console.log(`    ❌ ${key}.${subKey} 缺失`);
                }
              });
            }
            
            if (key === 'taskDetail') {
              const subKeys = ['title', 'basicInfo', 'productInfo', 'evidenceStatus'];
              subKeys.forEach(subKey => {
                if (content[key][subKey]) {
                  console.log(`    ✅ ${key}.${subKey} 存在`);
                } else {
                  console.log(`    ❌ ${key}.${subKey} 缺失`);
                }
              });
            }
          } else {
            console.log(`  ❌ ${key} 部分缺失`);
          }
        });
        
      } catch (error) {
        console.log(`  ❌ JSON 格式错误: ${error.message}`);
      }
    } else {
      console.log(`❌ ${locale}/${tasksFile} 不存在`);
    }
    
    console.log(''); // 空行分隔
  });
}

/**
 * 测试翻译键访问
 */
function testTranslationKeys() {
  console.log('🧪 测试翻译键访问...\n');
  
  const testCases = [
    {
      namespace: 'tasks.confirmDialog',
      keys: [
        'title',
        'description',
        'basicInfo.title',
        'basicInfo.platform',
        'basicInfo.quantity',
        'evidenceInfo.titles.PENDING_SUBMISSION',
        'evidenceInfo.descriptions.PENDING_SUBMISSION',
        'actions.cancel',
        'actions.confirm'
      ]
    },
    {
      namespace: 'tasks.taskDetail',
      keys: [
        'title',
        'basicInfo.title',
        'basicInfo.platform',
        'basicInfo.category',
        'productInfo.title',
        'productInfo.unitPrice',
        'evidenceStatus.title',
        'evidenceStatus.descriptions.PENDING_SUBMISSION',
        'actions.close',
        'actions.acceptTask'
      ]
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`📋 测试命名空间: ${testCase.namespace}`);
    
    testCase.keys.forEach(key => {
      console.log(`  🔑 ${testCase.namespace}.${key}`);
    });
    
    console.log(''); // 空行分隔
  });
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 修复 Tasks 命名空间问题\n');
  
  // 1. 验证翻译文件
  validateTranslationFiles();
  
  // 2. 修复文件
  const filesToFix = [
    {
      path: 'src/components/confirm-accept-task-dialog.tsx',
      old: 'Tasks.confirmDialog',
      new: 'tasks.confirmDialog'
    },
    {
      path: 'src/components/task-detail-sheet.tsx',
      old: 'Tasks.taskDetail',
      new: 'tasks.taskDetail'
    }
  ];
  
  let fixedFiles = 0;
  
  filesToFix.forEach(file => {
    const fullPath = path.join(process.cwd(), file.path);
    if (fixFile(fullPath, file.old, file.new)) {
      fixedFiles++;
    }
  });
  
  console.log(`\n📊 修复结果:`);
  console.log(`  修复文件数: ${fixedFiles}/${filesToFix.length}`);
  
  // 3. 测试翻译键
  testTranslationKeys();
  
  console.log('✅ 修复完成！\n');
  console.log('📝 下一步：');
  console.log('1. 重启 VSCode');
  console.log('2. 打开 confirm-accept-task-dialog.tsx 或 task-detail-sheet.tsx');
  console.log('3. 检查 i18n Ally 是否正确识别翻译');
  console.log('4. 测试自动完成功能');
  console.log('5. 验证组件显示是否正常');
}

main();
