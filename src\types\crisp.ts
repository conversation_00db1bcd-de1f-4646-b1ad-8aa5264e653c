// Crisp 客服聊天系统类型定义
export interface CrispConfig {
  enabled: boolean;
  websiteId: string;
}

// 地理位置信息接口
export interface LocationData {
  ip: string;
  city?: string;
  region?: string;
  country?: string;
  countryCode?: string;
  continent?: string;
  continentCode?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  currency?: string;
  location?: string;
  // 扩展字段
  company?: {
    name?: string;
    domain?: string;
    network?: string;
    type?: string;
  };
  asn?: {
    asn?: string;
    name?: string;
    domain?: string;
    route?: string;
    type?: string;
  };
  threat?: {
    is_tor?: boolean;
    is_vpn?: boolean;
    is_icloud_relay?: boolean;
    is_proxy?: boolean;
    is_datacenter?: boolean;
    is_anonymous?: boolean;
    is_known_attacker?: boolean;
    is_known_abuser?: boolean;
    is_threat?: boolean;
    is_bogon?: boolean;
    scores?: {
      vpn_score?: number;
      proxy_score?: number;
      threat_score?: number;
      trust_score?: number;
    };
  };
  time_zone?: {
    name?: string;
    abbr?: string;
    offset?: string;
    is_dst?: boolean;
    current_time?: string;
  };
  calling_code?: string;
  postal?: string;
  emoji_flag?: string;
}

// 用户行为数据接口
export interface UserBehaviorData {
  visitCount: number;
  lastVisitTime: number;
  totalTimeSpent: number;
  pageViews: number;
  engagementScore: number;
  messagesSent: number;
  lastMessageTime: number;
  userSegment: 'new' | 'returning' | 'engaged' | 'at_risk' | 'dormant';
  // 扩展字段
  avgSessionDuration?: number;
  bounceRate?: number;
  conversionProbability?: number;
  behaviorEvents?: Array<{
    type: string;
    timestamp: string;
    sessionDuration: number;
    pageViews: number;
    engagementScore: number;
  }>;
}

// 消息类型枚举
export enum MessageType {
  WELCOME = 'welcome',
  RE_ENGAGEMENT = 're_engagement',
  FEATURE_HIGHLIGHT = 'feature_highlight',
  SUPPORT_OFFER = 'support_offer',
  MILESTONE_CELEBRATION = 'milestone_celebration',
  SEASONAL_GREETING = 'seasonal_greeting',
}

// 消息策略接口
export interface MessageStrategy {
  messageType: MessageType;
  priority: 'critical' | 'urgent' | 'high' | 'medium' | 'low';
  timing:
    | 'immediate'
    | 'contextual'
    | 'optimal'
    | 'delayed'
    | 'scheduled'
    | 'periodic';
  personalization: 'maximum' | 'high' | 'medium' | 'low';
  messageFormat?: 'mobile_optimized' | 'desktop_standard';
  maxLength?: number;
  communicationStyle?: 'conversational' | 'informational';
  lifecycleStage?: string;
  contextInfo?: ContextInfo;
  user?: any;
  engagementScore?: number;
}

// 上下文信息接口
export interface ContextInfo {
  hour: number;
  isWeekend: boolean;
  isMobile: boolean;
  country: string;
  city: string;
  timezone: string;
}

// AI 消息生成请求接口
export interface AIMessageRequest {
  userBehavior: {
    visitCount: number;
    timeOnPage: number;
    pageDepth: number;
    lastVisit: string;
    messageInteractions: number;
    engagementScore: number;
    lifecycleStage: string;
  };
  context: {
    currentTime: string;
    userLocation: string;
    deviceType: string;
    pageUrl: string;
    referrer: string;
  };
  messageType: MessageType;
  businessInfo: {
    businessName: string;
    industry: string;
    primaryGoal: string;
  };
  language: string;
  tone: string;
}

// AI 消息生成响应接口
export interface AIMessageResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// Crisp 智能消息上下文接口
export interface CrispMessageContext {
  user?: any;
  locationData?: LocationData;
  crispInstance?: any;
}

// 全局 Window 接口扩展
declare global {
  interface Window {
    $crisp: any;
    CRISP_WEBSITE_ID: string;
  }
}

// 分析数据接口
export interface AnalyticsData {
  timestamp: string;
  behavior: UserBehaviorData;
  eventType?: string;
  sessionId: string;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
}
