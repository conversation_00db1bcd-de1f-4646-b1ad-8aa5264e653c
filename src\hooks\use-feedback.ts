'use client';

import { useTranslations } from 'next-intl';
import { useState, useCallback } from 'react';

import { FeedbackToast } from '@/components/feedback-system';

// 反馈操作类型
export type FeedbackAction =
  | 'create'
  | 'update'
  | 'delete'
  | 'save'
  | 'submit'
  | 'upload'
  | 'download'
  | 'login'
  | 'logout'
  | 'custom';

// 反馈配置
interface FeedbackConfig {
  successMessage?: string;
  errorMessage?: string;
  loadingMessage?: string;
  showProgress?: boolean;
  autoClose?: boolean;
  duration?: number;
}

// 默认消息映射
const defaultMessages: Record<
  FeedbackAction,
  {
    loading: string;
    success: string;
    error: string;
  }
> = {
  create: {
    loading: '正在创建...',
    success: '创建成功',
    error: '创建失败',
  },
  update: {
    loading: '正在更新...',
    success: '更新成功',
    error: '更新失败',
  },
  delete: {
    loading: '正在删除...',
    success: '删除成功',
    error: '删除失败',
  },
  save: {
    loading: '正在保存...',
    success: '保存成功',
    error: '保存失败',
  },
  submit: {
    loading: '正在提交...',
    success: '提交成功',
    error: '提交失败',
  },
  upload: {
    loading: '正在上传...',
    success: '上传成功',
    error: '上传失败',
  },
  download: {
    loading: '正在下载...',
    success: '下载成功',
    error: '下载失败',
  },
  login: {
    loading: '正在登录...',
    success: '登录成功',
    error: '登录失败',
  },
  logout: {
    loading: '正在登出...',
    success: '已安全登出',
    error: '登出失败',
  },
  custom: {
    loading: '处理中...',
    success: '操作成功',
    error: '操作失败',
  },
};

// 确认对话框状态
interface ConfirmState {
  open: boolean;
  title: string;
  description: string;
  confirmText: string;
  cancelText: string;
  variant: 'default' | 'destructive';
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
}

export function useFeedback() {
  const t = useTranslations('Messages');
  const [confirmState, setConfirmState] = useState<ConfirmState>({
    open: false,
    title: '',
    description: '',
    confirmText: t('confirm.delete.confirm'),
    cancelText: t('confirm.delete.cancel'),
    variant: 'default',
    onConfirm: () => {},
  });

  // 获取翻译的默认消息
  const getDefaultMessages = useCallback(
    (action: FeedbackAction) => ({
      loading: t(`loading.${action}`),
      success: t(`success.${action}`),
      error: t(`error.${action}`),
    }),
    [t],
  );

  // 显示成功消息
  const showSuccess = useCallback((message: string, description?: string) => {
    FeedbackToast.success(message, { description });
  }, []);

  // 显示错误消息
  const showError = useCallback((message: string, description?: string) => {
    FeedbackToast.error(message, { description });
  }, []);

  // 显示警告消息
  const showWarning = useCallback((message: string, description?: string) => {
    FeedbackToast.warning(message, { description });
  }, []);

  // 显示信息消息
  const showInfo = useCallback((message: string, description?: string) => {
    FeedbackToast.info(message, { description });
  }, []);

  // 显示加载消息
  const showLoading = useCallback((message: string, description?: string) => {
    return FeedbackToast.loading(message, { description });
  }, []);

  // 执行带反馈的异步操作
  const executeWithFeedback = useCallback(
    async <T>(
      action: FeedbackAction,
      operation: () => Promise<T>,
      config?: FeedbackConfig,
    ): Promise<T> => {
      const messages = getDefaultMessages(action);
      const loadingMessage = config?.loadingMessage || messages.loading;
      const successMessage = config?.successMessage || messages.success;
      const errorMessage = config?.errorMessage || messages.error;

      const promise = operation();

      FeedbackToast.promise(promise, {
        loading: loadingMessage,
        success: successMessage,
        error: (error: any) => {
          console.error(`${action} operation failed:`, error);
          return `${errorMessage}: ${error.message || t('error.unknown')}`;
        },
      });

      return promise;
    },
    [getDefaultMessages, t],
  );

  // 显示确认对话框
  const showConfirm = useCallback(
    (
      title: string,
      description: string,
      onConfirm: () => void | Promise<void>,
      options?: {
        confirmText?: string;
        cancelText?: string;
        variant?: 'default' | 'destructive';
        onCancel?: () => void;
      },
    ) => {
      setConfirmState({
        open: true,
        title,
        description,
        confirmText: options?.confirmText || t('confirm.delete.confirm'),
        cancelText: options?.cancelText || t('confirm.delete.cancel'),
        variant: options?.variant || 'default',
        onConfirm,
        onCancel: options?.onCancel,
      });
    },
    [t],
  );

  // 关闭确认对话框
  const closeConfirm = useCallback(() => {
    setConfirmState(prev => ({ ...prev, open: false }));
  }, []);

  // 快捷方法：删除确认
  const confirmDelete = useCallback(
    (itemName: string, onConfirm: () => void | Promise<void>) => {
      showConfirm(
        '确认删除',
        `您确定要删除 "${itemName}" 吗？此操作无法撤销。`,
        onConfirm,
        {
          confirmText: '删除',
          variant: 'destructive',
        },
      );
    },
    [showConfirm],
  );

  // 快捷方法：保存确认
  const confirmSave = useCallback(
    (
      onConfirm: () => void | Promise<void>,
      hasUnsavedChanges: boolean = true,
    ) => {
      if (!hasUnsavedChanges) {
        onConfirm();
        return;
      }

      showConfirm('保存更改', '您有未保存的更改，是否要保存？', onConfirm, {
        confirmText: '保存',
      });
    },
    [showConfirm],
  );

  // 快捷方法：离开确认
  const confirmLeave = useCallback(
    (
      onConfirm: () => void | Promise<void>,
      hasUnsavedChanges: boolean = true,
    ) => {
      if (!hasUnsavedChanges) {
        onConfirm();
        return;
      }

      showConfirm(
        '离开页面',
        '您有未保存的更改，离开页面将丢失这些更改。确定要离开吗？',
        onConfirm,
        {
          confirmText: '离开',
          variant: 'destructive',
        },
      );
    },
    [showConfirm],
  );

  // 批量操作反馈
  const executeBatchWithFeedback = useCallback(
    async <T>(
      items: T[],
      operation: (item: T) => Promise<void>,
      options?: {
        batchSize?: number;
        onProgress?: (completed: number, total: number) => void;
        successMessage?: string;
        errorMessage?: string;
      },
    ) => {
      const batchSize = options?.batchSize || 5;
      const total = items.length;
      let completed = 0;
      const errors: string[] = [];

      const loadingToast = showLoading(`正在处理 ${total} 个项目...`);

      try {
        // 分批处理
        for (let i = 0; i < items.length; i += batchSize) {
          const batch = items.slice(i, i + batchSize);

          await Promise.allSettled(
            batch.map(async item => {
              try {
                await operation(item);
                completed++;
              } catch (error) {
                errors.push(
                  error instanceof Error ? error.message : '未知错误',
                );
              }

              options?.onProgress?.(completed, total);
            }),
          );
        }

        // 显示结果
        if (errors.length === 0) {
          FeedbackToast.success(
            options?.successMessage || `成功处理 ${completed} 个项目`,
          );
        } else if (completed > 0) {
          FeedbackToast.warning(
            `部分成功：${completed} 个成功，${errors.length} 个失败`,
            {
              description: `失败原因：${errors.slice(0, 3).join(', ')}${errors.length > 3 ? '...' : ''}`,
            },
          );
        } else {
          FeedbackToast.error(options?.errorMessage || '批量操作失败', {
            description: `失败原因：${errors.slice(0, 3).join(', ')}${errors.length > 3 ? '...' : ''}`,
          });
        }
      } finally {
        // 关闭加载提示
        if (typeof loadingToast === 'string') {
          // 如果返回的是 toast ID，可以用来关闭
        }
      }

      return { completed, errors };
    },
    [showLoading],
  );

  // 表单验证反馈
  const validateWithFeedback = useCallback(
    (
      validationFn: () => string | null,
      onValid: () => void | Promise<void>,
    ) => {
      const error = validationFn();
      if (error) {
        showError('验证失败', error);
        return false;
      }

      onValid();
      return true;
    },
    [showError],
  );

  return {
    // 基础反馈方法
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,

    // 高级操作方法
    executeWithFeedback,
    executeBatchWithFeedback,
    validateWithFeedback,

    // 确认对话框
    showConfirm,
    closeConfirm,
    confirmDelete,
    confirmSave,
    confirmLeave,
    confirmState,

    // 便捷方法
    toast: FeedbackToast,
  };
}

// 表单反馈 Hook
export function useFormFeedback() {
  const feedback = useFeedback();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const submitWithFeedback = useCallback(
    async (
      submitFn: () => Promise<void>,
      options?: {
        successMessage?: string;
        errorMessage?: string;
        resetForm?: () => void;
      },
    ) => {
      setIsSubmitting(true);
      setErrors({});

      try {
        await submitFn();
        feedback.showSuccess(options?.successMessage || '提交成功');
        options?.resetForm?.();
      } catch (error) {
        const message = error instanceof Error ? error.message : '提交失败';
        feedback.showError(options?.errorMessage || message);

        // 如果是验证错误，设置字段错误
        if (error instanceof Error && error.name === 'ValidationError') {
          // 假设错误对象包含字段错误信息
          const fieldErrors = (error as any).fieldErrors || {};
          setErrors(fieldErrors);
        }
      } finally {
        setIsSubmitting(false);
      }
    },
    [feedback],
  );

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const setFieldError = useCallback((field: string, error: string) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  return {
    ...feedback,
    isSubmitting,
    errors,
    submitWithFeedback,
    clearErrors,
    setFieldError,
  };
}

export default useFeedback;
