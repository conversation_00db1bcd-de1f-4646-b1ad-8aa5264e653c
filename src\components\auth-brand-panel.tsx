'use client';

import { Shield, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { RefundGoLogo } from './refund-go-logo';

interface AuthBrandPanelProps {
  mode: 'signin' | 'signup';
}

export function AuthBrandPanel({ mode }: AuthBrandPanelProps) {
  const t = useTranslations('Auth');

  const isSignUp = mode === 'signup';

  return (
    <div className='relative hidden bg-background md:block border-l border-border'>
      {/* 简洁的背景装饰 */}
      <div className='absolute inset-0 overflow-hidden'>
        <div className='absolute top-1/4 right-1/4 w-32 h-32 bg-primary/5 rounded-full blur-3xl'></div>
        <div className='absolute bottom-1/3 left-1/4 w-24 h-24 bg-primary/3 rounded-full blur-2xl'></div>
      </div>

      <div className='flex h-full items-center justify-center p-8 relative z-10'>
        <div className='text-center max-w-sm'>
          {/* 品牌标识区域 */}
          <div className='mb-8'>
            {/* RefundGo Logo */}
            <div className='mx-auto mb-4 flex justify-center'>
              <RefundGoLogo
                variant="full"
                size="md"
                animated={true}
                showTagline={true}
                className="bg-transparent shadow-none hover:shadow-none"
              />
            </div>

            {/* 主标题 */}
            <h2 className='text-2xl font-bold text-foreground mb-2'>
              {isSignUp
                ? t('rightPanel.signup.title')
                : t('rightPanel.signin.title')}
            </h2>

            {/* 副标题 */}
            <p className='text-sm text-muted-foreground leading-relaxed'>
              {isSignUp
                ? t('rightPanel.signup.subtitle')
                : t('rightPanel.signin.subtitle')}
            </p>
          </div>

          {/* 核心特色展示 */}
          <div className='space-y-4 mb-8'>
            <div className='bg-muted/50 border border-border rounded-xl p-4 hover:bg-muted/70 transition-all duration-200'>
              <div className='flex items-center space-x-3'>
                <div className='w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center'>
                  <Shield className='w-5 h-5 text-primary' />
                </div>
                <div className='text-left'>
                  <div className='text-sm font-semibold text-foreground'>
                    {t('rightPanel.features.privacy.title')}
                  </div>
                  <div className='text-xs text-muted-foreground'>
                    {t('rightPanel.features.privacy.description')}
                  </div>
                </div>
              </div>
            </div>

            <div className='bg-muted/50 border border-border rounded-xl p-4 hover:bg-muted/70 transition-all duration-200'>
              <div className='flex items-center space-x-3'>
                <div className='w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center'>
                  <Zap className='w-5 h-5 text-primary' />
                </div>
                <div className='text-left'>
                  <div className='text-sm font-semibold text-foreground'>
                    {t('rightPanel.features.efficiency.title')}
                  </div>
                  <div className='text-xs text-muted-foreground'>
                    {t('rightPanel.features.efficiency.description')}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 简化的信任指标 */}
          <div className='pt-6 border-t border-border'>
            <div className='grid grid-cols-2 gap-4 text-center'>
              <div>
                <div className='text-lg font-bold text-primary'>20K+</div>
                <div className='text-xs text-muted-foreground'>
                  {t('rightPanel.stats.users')}
                </div>
              </div>
              <div>
                <div className='text-lg font-bold text-primary'>91.3%</div>
                <div className='text-xs text-muted-foreground'>
                  {t('rightPanel.stats.acceptanceRate')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
