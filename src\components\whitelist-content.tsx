'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  Shield,
  Plus,
  ExternalLink,
  Calendar,
  Store,
  AlertCircle,
  CheckCircle,
  Trash2,
  Loader2,
  Clock,
  XCircle,
} from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  useWhitelist,
  useCreateWhitelist,
  useDeleteWhitelist,
  type CreateWhitelistInput,
  type WhitelistStatus,
  type WhitelistItem,
} from '@/hooks/use-whitelist';

// 表单验证schema - moved inside component to access translations

export function WhitelistContent() {
  const t = useTranslations('shop-whitelist');
  const locale = useLocale();

  // 表单验证schema
  const whitelistFormSchema = z.object({
    shopName: z.string().min(1, t('validation.shopNameRequired')),
    shopUrl: z.string().url(t('validation.shopUrlInvalid')),
    platform: z.string().min(1, t('validation.platformRequired')),
  });

  type WhitelistFormData = z.infer<typeof whitelistFormSchema>;

  const [mounted, setMounted] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
    isOpen: boolean;
    item: WhitelistItem | null;
    type: 'withdraw' | 'delete';
  }>({
    isOpen: false,
    item: null,
    type: 'delete',
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  // 使用真实数据hooks
  const { data: whitelistData, isLoading, error } = useWhitelist();
  const createWhitelistMutation = useCreateWhitelist();
  const deleteWhitelistMutation = useDeleteWhitelist();

  const whitelistItems = whitelistData?.whitelistItems || [];
  const membership = whitelistData?.membership;
  const availableSlots = membership?.availableSlots || 0;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<WhitelistFormData>({
    resolver: zodResolver(whitelistFormSchema),
  });

  // 格式化日期
  const formatDate = (date: Date | string) => {
    if (!mounted) return t('messages.timeLoading');
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // 根据当前语言设置格式化日期
    const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US';
    return dateObj.toLocaleDateString(localeCode, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // 获取平台选项
  const platformOptions = [
    { value: 'DHgate', label: t('platforms.dhgate') },
    { value: '独立站', label: t('platforms.other') },
  ];

  // 处理添加白名单
  const onSubmit = (data: WhitelistFormData) => {
    if (availableSlots <= 0) {
      toast.error(t('messages.quotaInsufficient'), {
        description: t('messages.quotaInsufficientDesc'),
      });
      return;
    }

    const createData: CreateWhitelistInput = {
      shopName: data.shopName,
      shopUrl: data.shopUrl,
      platform: data.platform,
    };

    createWhitelistMutation.mutate(createData, {
      onSuccess: () => {
        setIsAddDialogOpen(false);
        reset();
      },
    });
  };

  // 处理删除/撤回申请
  const handleDelete = (id: string) => {
    deleteWhitelistMutation.mutate(id);
  };

  // 打开撤回申请确认对话框
  const handleWithdrawClick = (item: WhitelistItem) => {
    setDeleteConfirmDialog({
      isOpen: true,
      item,
      type: 'withdraw',
    });
  };

  // 打开删除确认对话框
  const handleDeleteClick = (item: WhitelistItem) => {
    setDeleteConfirmDialog({
      isOpen: true,
      item,
      type: 'delete',
    });
  };

  // 确认删除/撤回
  const confirmDelete = () => {
    if (deleteConfirmDialog.item) {
      deleteWhitelistMutation.mutate(deleteConfirmDialog.item.id);
      setDeleteConfirmDialog({
        isOpen: false,
        item: null,
        type: 'delete',
      });
    }
  };

  // 获取审核状态显示
  const getStatusDisplay = (status: WhitelistStatus) => {
    switch (status) {
      case 'PENDING':
        return {
          color:
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-100',
          icon: <Clock className='h-3 w-3' />,
          text: t('status.pending'),
        };
      case 'APPROVED':
        return {
          color:
            'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-100',
          icon: <CheckCircle className='h-3 w-3' />,
          text: t('status.approved'),
        };
      case 'REJECTED':
        return {
          color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-100',
          icon: <XCircle className='h-3 w-3' />,
          text: t('status.rejected'),
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Clock className='h-3 w-3' />,
          text: t('status.unknown'),
        };
    }
  };

  // 如果数据加载中，显示加载状态
  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2'>加载中...</span>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className='text-center py-8'>
        <AlertCircle className='h-12 w-12 mx-auto mb-4 text-red-500' />
        <p className='text-red-600'>加载白名单失败</p>
        <p className='text-sm text-muted-foreground mt-2'>请刷新页面重试</p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            {t('navigation.title')}
          </h1>
          <p className='text-muted-foreground'>{t('navigation.description')}</p>
        </div>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button
              disabled={
                availableSlots <= 0 || createWhitelistMutation.isPending
              }
            >
              {createWhitelistMutation.isPending ? (
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
              ) : (
                <Plus className='h-4 w-4 mr-2' />
              )}
              {t('form.addButton')}
            </Button>
          </DialogTrigger>
          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle>{t('form.dialogTitle')}</DialogTitle>
              <DialogDescription>
                {t('form.dialogDescription')}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='shopName'>{t('form.shopName')}</Label>
                <Input
                  id='shopName'
                  placeholder={t('form.shopNamePlaceholder')}
                  {...register('shopName')}
                />
                {errors.shopName && (
                  <p className='text-sm text-red-600'>
                    {errors.shopName.message}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='platform'>{t('form.platform')}</Label>
                <Select onValueChange={value => setValue('platform', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('form.platformPlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    {platformOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.platform && (
                  <p className='text-sm text-red-600'>
                    {errors.platform.message}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='shopUrl'>{t('form.shopUrl')}</Label>
                <Input
                  id='shopUrl'
                  placeholder={t('form.shopUrlPlaceholder')}
                  {...register('shopUrl')}
                />
                {errors.shopUrl && (
                  <p className='text-sm text-red-600'>
                    {errors.shopUrl.message}
                  </p>
                )}
              </div>

              <div className='flex gap-2 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setIsAddDialogOpen(false)}
                  className='flex-1'
                  disabled={createWhitelistMutation.isPending}
                >
                  {t('form.cancel')}
                </Button>
                <Button
                  type='submit'
                  className='flex-1'
                  disabled={createWhitelistMutation.isPending}
                >
                  {createWhitelistMutation.isPending ? (
                    <>
                      <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                      {t('form.submitting')}
                    </>
                  ) : (
                    t('form.submit')
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* 白名单状态概览 */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('stats.totalShops')}
            </CardTitle>
            <Store className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{whitelistItems.length}</div>
            <p className='text-xs text-muted-foreground'>
              {t('stats.totalShopsDesc')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('stats.availableSlots')}
            </CardTitle>
            <Shield className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-blue-600'>
              {availableSlots}
            </div>
            <p className='text-xs text-muted-foreground'>
              {t('stats.availableSlotsDesc')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {t('stats.totalSlots')}
            </CardTitle>
            <AlertCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-purple-600'>
              {membership?.whitelistSlots || 0}
            </div>
            <p className='text-xs text-muted-foreground'>
              {t('stats.totalSlotsDesc')} ({membership?.tier || 'FREE'})
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 白名单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('list.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          {whitelistItems.length === 0 ? (
            <div className='text-center py-8 text-muted-foreground'>
              <Store className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>{t('list.empty')}</p>
              <p className='text-sm'>{t('list.emptyDesc')}</p>
            </div>
          ) : (
            <div className='space-y-4'>
              {whitelistItems.map(item => (
                <div
                  key={item.id}
                  className='flex items-center justify-between p-4 border rounded-lg'
                >
                  <div className='flex items-start gap-3 flex-1'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-1'>
                        <h3 className='font-medium'>{item.shopName}</h3>
                        <Badge variant='outline'>{item.platform}</Badge>
                        {(() => {
                          const statusDisplay = getStatusDisplay(
                            item.status || 'PENDING',
                          );
                          return (
                            <Badge className={`text-xs ${statusDisplay.color}`}>
                              <span className='mr-1'>{statusDisplay.icon}</span>
                              {statusDisplay.text}
                            </Badge>
                          );
                        })()}
                      </div>

                      <div className='flex items-center gap-2 text-sm text-muted-foreground mb-2'>
                        <ExternalLink className='h-3 w-3' />
                        <a
                          href={item.shopUrl}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='hover:text-primary truncate max-w-xs'
                        >
                          {item.shopUrl}
                        </a>
                      </div>

                      <div className='flex items-center gap-1 text-xs text-muted-foreground'>
                        <Calendar className='h-3 w-3' />
                        {t('list.addedOn')} {formatDate(item.createdAt)}
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center gap-2'>
                    {item.status === 'PENDING' && (
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => handleWithdrawClick(item)}
                        className='text-orange-600 hover:text-orange-700'
                        disabled={deleteWhitelistMutation.isPending}
                      >
                        {deleteWhitelistMutation.isPending ? (
                          <Loader2 className='h-4 w-4 mr-1 animate-spin' />
                        ) : (
                          <XCircle className='h-4 w-4 mr-1' />
                        )}
                        {t('actions.withdraw')}
                      </Button>
                    )}
                    {(item.status === 'APPROVED' ||
                      item.status === 'REJECTED') && (
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => handleDeleteClick(item)}
                        className='text-red-600 hover:text-red-700'
                        disabled={deleteWhitelistMutation.isPending}
                      >
                        {deleteWhitelistMutation.isPending ? (
                          <Loader2 className='h-4 w-4 mr-1 animate-spin' />
                        ) : (
                          <Trash2 className='h-4 w-4 mr-1' />
                        )}
                        {t('actions.delete')}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <AlertCircle className='h-5 w-5' />
            {t('instructions.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-3'>
          <div className='text-sm space-y-2'>
            <p>
              •{' '}
              <strong>
                {t('instructions.whitelistEffect').split(':')[0]}:
              </strong>{' '}
              {t('instructions.whitelistEffect').split(':')[1]}
            </p>
            <p>
              •{' '}
              <strong>{t('instructions.reviewProcess').split(':')[0]}:</strong>{' '}
              {t('instructions.reviewProcess').split(':')[1]}
            </p>
            <p>
              • <strong>{t('instructions.quotaLimit').split(':')[0]}:</strong>{' '}
              {t('instructions.quotaLimit').split(':')[1]}
            </p>
            <p>
              • <strong>{t('instructions.quickReview').split(':')[0]}:</strong>{' '}
              {t('instructions.quickReview').split(':')[1]}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 确认删除/撤回对话框 */}
      <Dialog
        open={deleteConfirmDialog.isOpen}
        onOpenChange={open =>
          !open &&
          setDeleteConfirmDialog({
            isOpen: false,
            item: null,
            type: 'delete',
          })
        }
      >
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>
              {deleteConfirmDialog.type === 'withdraw'
                ? t('confirm.cancel.title')
                : t('confirm.delete.title')}
            </DialogTitle>
            <DialogDescription>
              {deleteConfirmDialog.type === 'withdraw'
                ? '您确定要撤回这个白名单申请吗？撤回后需要重新申请。'
                : t('confirm.delete.message')}
            </DialogDescription>
          </DialogHeader>

          {deleteConfirmDialog.item && (
            <div className='space-y-3'>
              <div className='p-3 bg-gray-50 rounded-lg'>
                <p className='font-medium'>
                  {deleteConfirmDialog.item.shopName}
                </p>
                <p className='text-sm text-muted-foreground'>
                  {deleteConfirmDialog.item.platform}
                </p>
                <p className='text-sm text-muted-foreground truncate'>
                  {deleteConfirmDialog.item.shopUrl}
                </p>
              </div>

              <div className='flex items-center gap-2'>
                <span className='text-sm text-muted-foreground'>
                  {t('list.currentStatus')}：
                </span>
                {(() => {
                  const statusDisplay = getStatusDisplay(
                    deleteConfirmDialog.item.status || 'PENDING',
                  );
                  return (
                    <Badge className={`text-xs ${statusDisplay.color}`}>
                      <span className='mr-1'>{statusDisplay.icon}</span>
                      {statusDisplay.text}
                    </Badge>
                  );
                })()}
              </div>
            </div>
          )}

          <div className='flex gap-2 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={() =>
                setDeleteConfirmDialog({
                  isOpen: false,
                  item: null,
                  type: 'delete',
                })
              }
              className='flex-1'
              disabled={deleteWhitelistMutation.isPending}
            >
              {t('dialogs.cancel')}
            </Button>
            <Button
              type='button'
              variant='destructive'
              onClick={confirmDelete}
              className='flex-1'
              disabled={deleteWhitelistMutation.isPending}
            >
              {deleteWhitelistMutation.isPending ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  {t('actions.processing')}
                </>
              ) : deleteConfirmDialog.type === 'withdraw' ? (
                t('dialogs.confirmWithdraw')
              ) : (
                t('dialogs.confirmDelete')
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
