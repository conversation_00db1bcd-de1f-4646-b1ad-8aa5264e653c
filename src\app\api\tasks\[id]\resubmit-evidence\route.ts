import { EvidenceStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 验证参数
const paramsSchema = z.object({
  id: z.string(),
});

// 验证请求体
const requestBodySchema = z.object({
  evidenceFiles: z.array(z.string()).min(1, '请至少上传一个证据文件'),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户登录
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    // 验证参数
    const resolvedParams = await params;
    const { id } = paramsSchema.parse(resolvedParams);

    // 验证委托存在且属于当前用户
    const task = await prisma.task.findFirst({
      where: {
        id,
        publisherId: session.user.id,
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: '委托不存在或无权限' },
        { status: 404 },
      );
    }

    // 验证证据状态为"未通过"
    if (task.evidenceStatus !== EvidenceStatus.REJECTED) {
      return NextResponse.json(
        { error: '只有证据状态为"未通过"的委托才能重新提交证据' },
        { status: 400 },
      );
    }

    // 验证请求体
    const body = await request.json();
    const { evidenceFiles } = requestBodySchema.parse(body);

    // 更新委托的证据文件和状态
    const updatedTask = await prisma.task.update({
      where: { id },
      data: {
        evidenceFiles,
        evidenceStatus: EvidenceStatus.UNDER_REVIEW, // 重新设置为审核中状态
        evidenceRejectReason: null, // 清除之前的拒绝理由
      },
    });

    return NextResponse.json({
      success: true,
      message: '证据重新提交成功，请等待审核',
      data: {
        taskId: updatedTask.id,
        evidenceStatus: updatedTask.evidenceStatus,
        evidenceFiles: updatedTask.evidenceFiles,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
