import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 支付配置验证Schema
const paymentConfigSchema = z.object({
  provider: z.enum(['yunpay', 'nowpayments']),
  name: z.string().min(1, '请输入显示名称'),
  isEnabled: z.boolean(),
  settings: z.record(z.any()),
});

// 检查字符串是否全部为星号
function isAllStars(value: string): boolean {
  return !!(value && value.length > 0 && /^\*+$/.test(value));
}

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 从数据库获取支付配置
    const configs = await prisma.paymentConfig.findMany({
      orderBy: { provider: 'asc' },
    });

    // 不再创建默认配置，完全依赖数据库中的配置

    // 隐藏敏感信息 - 星号长度与原始密钥一致
    const safeConfigs = configs.map((config: any) => ({
      ...config,
      settings: {
        ...(config.settings as any),
        key: (config.settings as any)?.key
          ? '*'.repeat((config.settings as any).key.length)
          : '',
        apiKey: (config.settings as any)?.apiKey
          ? '*'.repeat((config.settings as any).apiKey.length)
          : '',
        ipnSecret: (config.settings as any)?.ipnSecret
          ? '*'.repeat((config.settings as any).ipnSecret.length)
          : '',
      },
    }));

    return NextResponse.json(safeConfigs);
  } catch (error) {
    return NextResponse.json({ error: '获取配置失败' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const data = paymentConfigSchema.parse(body);

    // 获取当前配置（如果存在）
    const currentConfig = await prisma.paymentConfig.findUnique({
      where: { provider: data.provider },
    });

    // 处理敏感字段：如果提交的值全部为星号则保留原始值，否则更新
    const processedSettings = { ...data.settings };

    if (currentConfig) {
      const currentSettings = currentConfig.settings as any;

      // 处理 key 字段
      if (
        processedSettings.key &&
        isAllStars(processedSettings.key) &&
        currentSettings.key
      ) {
        processedSettings.key = currentSettings.key;
      }

      // 处理 apiKey 字段
      if (
        processedSettings.apiKey &&
        isAllStars(processedSettings.apiKey) &&
        currentSettings.apiKey
      ) {
        processedSettings.apiKey = currentSettings.apiKey;
      }

      // 处理 ipnSecret 字段
      if (
        processedSettings.ipnSecret &&
        isAllStars(processedSettings.ipnSecret) &&
        currentSettings.ipnSecret
      ) {
        processedSettings.ipnSecret = currentSettings.ipnSecret;
      }
    }

    // 使用 upsert 来创建或更新配置
    const config = await prisma.paymentConfig.upsert({
      where: { provider: data.provider },
      update: {
        name: data.name,
        isEnabled: data.isEnabled,
        settings: processedSettings,
        updatedAt: new Date(),
      },
      create: {
        provider: data.provider,
        name: data.name,
        isEnabled: data.isEnabled,
        settings: processedSettings,
      },
    });

    // 返回时隐藏敏感信息 - 星号长度与原始密钥一致
    const safeConfig = {
      ...config,
      settings: {
        ...(config.settings as any),
        key: (config.settings as any)?.key
          ? '*'.repeat((config.settings as any).key.length)
          : '',
        apiKey: (config.settings as any)?.apiKey
          ? '*'.repeat((config.settings as any).apiKey.length)
          : '',
        ipnSecret: (config.settings as any)?.ipnSecret
          ? '*'.repeat((config.settings as any).ipnSecret.length)
          : '',
      },
    };

    return NextResponse.json(safeConfig);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '数据验证失败', details: error.errors },
        { status: 400 },
      );
    }

    return NextResponse.json({ error: '保存配置失败' }, { status: 500 });
  }
}
