'use client';

import { Building2, Ticket, CheckCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useResponsive } from '@/hooks/use-responsive';

// Mock stats for demos
const mockPublishedStats = {
  total: 25,
  available: 8,
  inProgress: 5,
  completed: 10,
  expired: 1,
  cancelled: 1,
};

const mockAcceptedStats = {
  total: 15,
  inProgress: 3,
  pendingLogistics: 2,
  pendingReview: 1,
  pendingDelivery: 4,
  completed: 5,
  expired: 0,
};

// Responsive Published Tasks Tabs with real i18n
function ResponsivePublishedTaskTabsI18n({
  activeTab,
  onTabChange,
  stats,
}: any) {
  const { isMobile, isTablet } = useResponsive();
  const t = useTranslations('MyPublishedTasks');

  const tabItems = [
    {
      value: 'all',
      label: t('tabs.all'),
      shortLabel: 'All',
      count: stats.total,
    },
    {
      value: 'available',
      label: t('tabs.available'),
      shortLabel: 'Open',
      count: stats.available,
    },
    {
      value: 'in_progress',
      label: t('tabs.inProgress'),
      shortLabel: 'Active',
      count: stats.inProgress,
    },
    {
      value: 'completed',
      label: t('tabs.completed'),
      shortLabel: 'Done',
      count: stats.completed,
    },
    {
      value: 'expired',
      label: t('tabs.expired'),
      shortLabel: 'Expired',
      count: stats.expired,
    },
    {
      value: 'cancelled',
      label: t('tabs.cancelled'),
      shortLabel: 'Cancel',
      count: stats.cancelled,
    },
  ];

  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block sm:hidden'>{item.label}</span>
                <span className='hidden sm:block'>
                  {item.label} ({item.count})
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label} ({item.count})
            </button>
          ))}
        </div>
      </Tabs>
    );
  }

  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className='grid w-full grid-cols-6'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label} ({item.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

// Responsive Accepted Tasks Tabs with real i18n
function ResponsiveAcceptedTaskTabsI18n({
  activeTab,
  onTabChange,
  stats,
}: any) {
  const { isMobile, isTablet } = useResponsive();
  const t = useTranslations('MyAcceptedTasks');

  const tabItems = [
    {
      value: 'all',
      label: t('tabs.all'),
      shortLabel: 'All',
      count: stats.total,
    },
    {
      value: 'in_progress',
      label: t('tabs.inProgress'),
      shortLabel: 'Active',
      count: stats.inProgress,
    },
    {
      value: 'pending_logistics',
      label: t('tabs.pendingLogistics'),
      shortLabel: 'Logistics',
      count: stats.pendingLogistics,
    },
    {
      value: 'pending_review',
      label: t('tabs.pendingReview'),
      shortLabel: 'Review',
      count: stats.pendingReview,
    },
    {
      value: 'pending_delivery',
      label: t('tabs.pendingDelivery'),
      shortLabel: 'Delivery',
      count: stats.pendingDelivery,
    },
    {
      value: 'completed',
      label: t('tabs.completed'),
      shortLabel: 'Done',
      count: stats.completed,
    },
    {
      value: 'expired',
      label: t('tabs.expired'),
      shortLabel: 'Expired',
      count: stats.expired,
    },
  ];

  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block sm:hidden'>{item.label}</span>
                <span className='hidden sm:block'>
                  {item.label} ({item.count})
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.slice(0, 6).map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label} ({item.count})
            </button>
          ))}
        </div>
        {tabItems.length > 6 && (
          <div className='grid grid-cols-3 gap-2 mt-2'>
            {tabItems.slice(6).map(item => (
              <button
                key={item.value}
                type='button'
                onClick={() => onTabChange(item.value)}
                className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                  activeTab === item.value
                    ? 'bg-primary text-primary-foreground border-primary'
                    : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
                }`}
              >
                {item.label} ({item.count})
              </button>
            ))}
          </div>
        )}
      </Tabs>
    );
  }

  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className='grid w-full grid-cols-7'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label} ({item.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

// Responsive Tickets Tabs with real i18n
function ResponsiveTicketTabsI18n({ activeTab, onTabChange }: any) {
  const { isMobile, isTablet } = useResponsive();
  const t = useTranslations('Tickets');

  const tabItems = [
    { value: 'all', label: t('filters.all'), shortLabel: 'All' },
    { value: 'PENDING', label: t('status.PENDING'), shortLabel: 'Pending' },
    {
      value: 'IN_PROGRESS',
      label: t('status.IN_PROGRESS'),
      shortLabel: 'Progress',
    },
    { value: 'RESOLVED', label: t('status.RESOLVED'), shortLabel: 'Resolved' },
    { value: 'CLOSED', label: t('status.CLOSED'), shortLabel: 'Closed' },
    { value: 'CANCELLED', label: t('status.CANCELLED'), shortLabel: 'Cancel' },
  ];

  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block'>{item.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label}
            </button>
          ))}
        </div>
      </Tabs>
    );
  }

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className='w-full'>
      <TabsList className='grid w-full grid-cols-6'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

// Main Demo Component with real i18n
export function I18nResponsiveTabsTest() {
  const [publishedTab, setPublishedTab] = useState('all');
  const [acceptedTab, setAcceptedTab] = useState('all');
  const [ticketTab, setTicketTab] = useState('all');
  const { breakpoint, isMobile, isTablet, width } = useResponsive();

  return (
    <div className='space-y-8 p-6'>
      {/* Debug Info */}
      <Card>
        <CardContent className='p-4'>
          <h3 className='font-semibold mb-2'>i18n Responsive Tabs Test</h3>
          <div className='text-sm space-y-1'>
            <p>
              Current breakpoint:{' '}
              <span className='font-mono'>{breakpoint}</span>
            </p>
            <p>
              Screen width: <span className='font-mono'>{width}px</span>
            </p>
            <p>
              Is Mobile:{' '}
              <span className='font-mono'>{isMobile ? 'true' : 'false'}</span>
            </p>
            <p>
              Is Tablet:{' '}
              <span className='font-mono'>{isTablet ? 'true' : 'false'}</span>
            </p>
            <p className='text-green-600 font-medium'>
              ✅ Using real i18n translation keys
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Published Tasks Demo */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Building2 className='h-5 w-5' />
            My Published Tasks - i18n Responsive Tabs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsivePublishedTaskTabsI18n
            activeTab={publishedTab}
            onTabChange={setPublishedTab}
            stats={mockPublishedStats}
          />
          <div className='mt-4 p-3 bg-muted rounded-lg'>
            <p className='text-sm'>
              Active tab: <span className='font-semibold'>{publishedTab}</span>
            </p>
            <p className='text-xs text-muted-foreground'>
              Using MyPublishedTasks namespace
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Accepted Tasks Demo */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <CheckCircle className='h-5 w-5' />
            My Accepted Tasks - i18n Responsive Tabs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveAcceptedTaskTabsI18n
            activeTab={acceptedTab}
            onTabChange={setAcceptedTab}
            stats={mockAcceptedStats}
          />
          <div className='mt-4 p-3 bg-muted rounded-lg'>
            <p className='text-sm'>
              Active tab: <span className='font-semibold'>{acceptedTab}</span>
            </p>
            <p className='text-xs text-muted-foreground'>
              Using MyAcceptedTasks namespace
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Tickets Demo */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Ticket className='h-5 w-5' />
            Tickets - i18n Responsive Tabs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveTicketTabsI18n
            activeTab={ticketTab}
            onTabChange={setTicketTab}
          />
          <div className='mt-4 p-3 bg-muted rounded-lg'>
            <p className='text-sm'>
              Active tab: <span className='font-semibold'>{ticketTab}</span>
            </p>
            <p className='text-xs text-muted-foreground'>
              Using Tickets namespace
            </p>
          </div>
        </CardContent>
      </Card>

      {/* i18n Status */}
      <Card>
        <CardContent className='p-6'>
          <h3 className='font-semibold mb-3 text-green-600'>
            ✅ i18n Functionality Restored
          </h3>
          <div className='space-y-2 text-sm'>
            <div className='flex items-center gap-2'>
              <span className='w-3 h-3 bg-green-500 rounded-full'></span>
              <span>
                <strong>Published Tasks:</strong> Uses MyPublishedTasks.tabs.*
                keys
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='w-3 h-3 bg-green-500 rounded-full'></span>
              <span>
                <strong>Accepted Tasks:</strong> Uses MyAcceptedTasks.tabs.*
                keys
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='w-3 h-3 bg-green-500 rounded-full'></span>
              <span>
                <strong>Tickets:</strong> Uses Tickets.filters.all and
                Tickets.status.* keys
              </span>
            </div>
          </div>

          <div className='mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
            <h4 className='font-medium mb-2'>Translation Keys Verified:</h4>
            <ul className='text-sm space-y-1 list-disc list-inside'>
              <li>All responsive layouts maintain proper i18n support</li>
              <li>Translation keys resolve correctly in all languages</li>
              <li>Responsive design improvements preserved</li>
              <li>No hardcoded English text in components</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
