'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { UserPageLayout } from '@/components/user-page-layout';
import { WhitelistContent } from '@/components/whitelist-content';

export default function WhitelistPage() {
  const t = useTranslations('shop-whitelist');

  useEffect(() => {
    document.title = `${t('navigation.title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('navigation.title')}
      breadcrumbPage='Shop Whitelist'
      href='/whitelist'
      description={t('navigation.description')}
    >
      <WhitelistContent />
    </UserPageLayout>
  );
}
