import { existsSync } from 'fs';
import { readFile } from 'fs/promises';
import { join } from 'path';

import { NextRequest, NextResponse } from 'next/server';

// 支持的文件类型和对应的MIME类型
const MIME_TYPES: Record<string, string> = {
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp',
  '.mp4': 'video/mp4',
  '.avi': 'video/x-msvideo',
  '.mov': 'video/quicktime',
  '.wmv': 'video/x-ms-wmv',
  '.flv': 'video/x-flv',
  '.webm': 'video/webm',
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> },
) {
  try {
    const { path } = await params;

    // 构建文件路径
    const filePath = join(process.cwd(), 'public', 'uploads', ...path);

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return NextResponse.json({ error: '文件不存在' }, { status: 404 });
    }

    // 检查文件是否在允许的上传目录中（安全检查）
    const uploadsDir = join(process.cwd(), 'public', 'uploads');
    if (!filePath.startsWith(uploadsDir)) {
      return NextResponse.json({ error: '无效的文件路径' }, { status: 403 });
    }

    // 读取文件
    const fileBuffer = await readFile(filePath);

    // 获取文件扩展名
    const fileName = path[path.length - 1];
    const ext = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();

    // 确定MIME类型
    const mimeType = MIME_TYPES[ext] || 'application/octet-stream';

    // 设置缓存头
    const headers = new Headers({
      'Content-Type': mimeType,
      'Content-Length': fileBuffer.length.toString(),
      'Cache-Control': 'public, max-age=31536000, immutable', // 1年缓存
      'Content-Disposition': `inline; filename="${fileName}"`,
    });

    return new NextResponse(fileBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('文件服务错误:', error);
    return NextResponse.json({ error: '文件读取失败' }, { status: 500 });
  }
}

export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> },
) {
  try {
    const { path } = await params;

    // 构建文件路径
    const filePath = join(process.cwd(), 'public', 'uploads', ...path);

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return new NextResponse(null, { status: 404 });
    }

    // 检查文件是否在允许的上传目录中（安全检查）
    const uploadsDir = join(process.cwd(), 'public', 'uploads');
    if (!filePath.startsWith(uploadsDir)) {
      return new NextResponse(null, { status: 403 });
    }

    // 获取文件信息
    const stats = await import('fs').then(fs => fs.promises.stat(filePath));

    // 获取文件扩展名
    const fileName = path[path.length - 1];
    const ext = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();

    // 确定MIME类型
    const mimeType = MIME_TYPES[ext] || 'application/octet-stream';

    // 设置响应头
    const headers = new Headers({
      'Content-Type': mimeType,
      'Content-Length': stats.size.toString(),
      'Cache-Control': 'public, max-age=31536000, immutable',
      'Last-Modified': stats.mtime.toUTCString(),
    });

    return new NextResponse(null, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('文件HEAD请求错误:', error);
    return new NextResponse(null, { status: 500 });
  }
}
