# Refundgo Web 2 - Cursor Rules

## Project Overview

Next.js 14+ full-stack application with TypeScript, Tailwind CSS, Prisma ORM, NextAuth v5, and React Three Fiber for 3D components. Built with App Router, Server Components, and modern React patterns.

## Tech Stack

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **UI & Styling**: Tailwind CSS, shadcn/ui, Radix UI, Framer Motion, next-themes
- **3D Graphics**: React Three Fiber + Drei
- **State Management**: TanStack Query (server state), Zustand (client state)
- **Forms**: React Hook Form + Zod validation
- **Backend & Data**: Prisma ORM (PostgreSQL), NextAuth v5 (beta), Server Actions
- **Email**: Resend with multilingual templates
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Vitest, Playwright
- **Dev Tools**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, lint-staged, Augment

## Directory Structure

```
src/
├── app/
│   ├── (admin)/                 # Admin panel routes
│   │   └── admin/
│   ├── [locale]/                # Internationalized routes (en, zh)
│   │   ├── (main)/              # Public routes (homepage, auth, etc.)
│   │   ├── (user)/              # User dashboard routes
│   │   └── payment/             # Payment flow routes
│   ├── api/                     # API routes & webhooks
│   │   ├── admin/
│   │   ├── ai/
│   │   ├── auth/
│   │   ├── cron/
│   │   ├── dashboard/
│   │   ├── email/
│   │   ├── files/
│   │   ├── ip-location/
│   │   ├── payments/
│   │   ├── publisher/
│   │   ├── tasks/
│   │   ├── tickets/
│   │   ├── upload/
│   │   ├── user/
│   │   └── webhook/
│   ├── globals.css              # Global styles
│   └── layout.tsx               # Root layout
├── components/                  # Reusable UI components
│   ├── ui/                      # shadcn/ui components
│   ├── admin/                   # Admin-specific components
│   └── ...
├── hooks/                       # Custom React hooks
├── i18n/                        # next-intl configuration
├── lib/                         # Utilities, config, core logic
│   ├── auth.ts                  # NextAuth config
│   ├── db.ts                    # Prisma client instance
│   ├── email-templates/         # Email templates
│   └── ...
├── styles/                      # Additional stylesheets
└── middleware.ts                # i18n and auth middleware
messages/                        # Translation files for next-intl
├── en/
└── zh/
prisma/                          # Database schema and migrations
public/                          # Static assets
scripts/                         # Development and utility scripts
__tests__/                        # Tests (Jest/Vitest)
├── e2e/
├── integration/
└── unit/
```

## Coding Standards

### TypeScript

- Use strict TypeScript with proper type definitions
- Prefer interfaces over types for object shapes
- Use Zod schemas for runtime validation
- Export types from dedicated files in `/types`

### React/Next.js

- **Server Components First**: Use React Server Components by default for better performance
- **Strategic Client Boundaries**: Add 'use client' only when necessary (state, effects, browser APIs)
- **Composition Patterns**: Follow Server/Client Component composition patterns
- **Data Fetching**: Use fetch() with proper caching in Server Components
- **Async/Await**: Prefer async/await over .then() for better readability
- **Error Handling**: Use proper error boundaries (error.tsx) and not-found.tsx
- **Loading States**: Implement loading.tsx and Suspense for better UX
- **Navigation**: Use Link component for client-side navigation and prefetching
- **Layouts**: Leverage layout.tsx for shared UI and partial rendering

### Styling

- Use Tailwind CSS utility classes
- Follow shadcn/ui component patterns
- Use CSS variables for theming
- Responsive design with mobile-first approach

### Database
## 项目文档 (`docs/`)

`docs/` 目录包含了全面的项目文档。强烈建议在开始工作前查阅这些文档，以便深入了解项目架构、开发流程、功能规格和编码标准。

关键文档包括：
- `project-context.md`: 项目、技术栈和功能的高级概述。
- `system-architecture.md`: 系统架构的详细分解。
- `development-guide.md`: 关于设置开发环境和常用命令的说明。
- `feature-modules.md`: 核心应用模块的深入解释。
- `user-roles.md`: 用户权限和角色的描述。
- `testing-guide.md`: 编写和运行测试的指南。

- Use Prisma schema for all database operations
- Follow proper migration practices
- Use transactions for complex operations
- Implement proper error handling

### API Routes & Server Actions

- **Route Handlers**: Use app/api/route.ts for backend integration
- **Server Actions**: Prefer Server Actions for form submissions and mutations
- **Validation**: Implement request validation with Zod schemas
- **Error Handling**: Handle errors gracefully with proper status codes
- **Authentication**: Use middleware for auth checks
- **Authorization**: Ensure users are authorized for Server Actions
- **Caching**: Use proper fetch caching strategies (force-cache, no-store, revalidate)

## File Naming

- Components: PascalCase (e.g., `UserProfile.tsx`)
- Pages: lowercase with hyphens (e.g., `user-profile/page.tsx`)
- Utilities: camelCase (e.g., `formatDate.ts`)
- Types: PascalCase with descriptive names

## Component Patterns

- **Composition Over Inheritance**: Favor component composition patterns
- **Server Component by Default**: Start with Server Components, add 'use client' when needed
- **Prop Types**: Implement proper TypeScript prop interfaces
- **forwardRef**: Use forwardRef for reusable component libraries
- **Accessibility**: Follow WCAG guidelines and use semantic HTML
- **Context Providers**: Use Client Components for context providers
- **Third-party Integration**: Wrap client-only libraries in Client Components

## State Management

- Use React state for local component state
- Use Zustand for global client state
- Use TanStack Query for server state
- Avoid prop drilling with context when needed

## Testing

- Write unit tests for utilities and hooks
- Use integration tests for components
- Mock external dependencies
- Aim for meaningful test coverage

## Performance

- **Images**: Use Next.js Image component for automatic optimization and modern formats
- **Fonts**: Use next/font for font optimization and reduced CLS
- **Scripts**: Use Next.js Script component for third-party script optimization
- **Caching**: Implement proper data caching with fetch() and unstable_cache
- **Code Splitting**: Use dynamic imports and lazy loading for large components
- **Bundle Analysis**: Monitor bundle size with built-in analyzer
- **Static Assets**: Store in public/ directory for automatic caching
- **Streaming**: Use Suspense for progressive UI rendering
- **Parallel Data Fetching**: Fetch data in parallel to reduce waterfalls

## Security

- Validate all user inputs
- Use CSRF protection
- Implement proper authentication checks
- Sanitize data before database operations

## Modern Next.js 14 Patterns

- **Data Fetching**: Use fetch() with caching options (force-cache, no-store, revalidate)
- **Dynamic APIs**: Use cookies() and headers() functions carefully, wrap in Suspense
- **Server Actions**: Implement form handling with Server Actions instead of API routes
- **Metadata API**: Use generateMetadata for dynamic SEO optimization
- **Route Groups**: Organize routes with (group) folders for better structure
- **Parallel Routes**: Use @folder convention for advanced routing patterns
- **Intercepting Routes**: Use (..) convention for modal-like experiences

## Best Practices

- **Principle of Least Privilege**: Apply minimal permissions and access
- **Environment Variables**: Use .env files for configuration
- **Error Logging**: Implement comprehensive error tracking
- **Code Documentation**: Write self-documenting code with meaningful names
- **Commit Messages**: Use conventional commit format
- **Type Safety**: Leverage TypeScript and Next.js plugin for early error detection
- **SEO**: Use Metadata API for proper page titles and descriptions
- **Accessibility**: Test with screen readers and follow ARIA guidelines

## Code Examples

### Server Component Data Fetching

```tsx
// Prefer this pattern for data fetching
export default async function Page() {
  const data = await fetch('https://api.example.com', {
    next: { revalidate: 3600 } // Cache for 1 hour
  })
  const result = await data.json()

  return <div>{result.title}</div>
}
```

### Client Component Pattern

```tsx
'use client'

import { useState, useEffect } from 'react'

export default function ClientComponent({ initialData }) {
  const [state, setState] = useState(initialData)

  // Client-side logic here
  return <div>Interactive content</div>
}
```

### Server Action Form Handling

```tsx
import { redirect } from 'next/navigation'

async function createUser(formData: FormData) {
  'use server'

  const name = formData.get('name') as string
  // Validate with Zod, save to database
  redirect('/success')
}

export default function Form() {
  return (
    <form action={createUser}>
      <input name="name" required />
      <button type="submit">Submit</button>
    </form>
  )
}
```

### Layout with Metadata

```tsx
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Refundgo',
  description: 'Modern refund management platform'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
```
