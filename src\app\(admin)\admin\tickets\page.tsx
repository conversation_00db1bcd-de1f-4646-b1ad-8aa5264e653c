import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { AdminTicketsContent } from '@/components/admin/admin-tickets-content';
import { constructMetadata } from '@/lib/utils';

export const metadata = constructMetadata({
  title: '工单管理 - 管理后台',
  description: '管理和处理用户提交的工单。',
});

export default function AdminTicketsPage() {
  return (
    <AdminPageLayout
      title='工单管理'
      breadcrumbPage='工单管理'
      href='/admin/tickets'
    >
      <AdminTicketsContent />
    </AdminPageLayout>
  );
}
