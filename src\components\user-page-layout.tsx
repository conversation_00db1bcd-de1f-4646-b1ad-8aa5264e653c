'use client';

import { ArrowLeft, Home } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ReactNode } from 'react';

import { LanguageSwitcher } from '@/components/language-switcher';
import { ModeToggle } from '@/components/mode-toggle';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { useBreadcrumbNavigation } from '@/hooks/use-active-route';
import { Link } from '@/i18n/navigation';
import { cn } from '@/lib/utils';

interface UserPageLayoutProps {
  title: string;
  breadcrumbPage: string;
  href: string;
  children: ReactNode;
  // 新增属性
  headerActions?: ReactNode; // 头部额外操作按钮
  customBreadcrumb?: ReactNode; // 自定义面包屑
  showBackButton?: boolean; // 显示返回按钮
  className?: string; // 自定义内容区域样式
  description?: string; // 页面描述（用于SEO和用户提示）
  loading?: boolean; // 加载状态
}

export function UserPageLayout({
  title,
  breadcrumbPage,
  href,
  children,
  headerActions,
  customBreadcrumb,
  showBackButton = false,
  className,
  description,
  loading = false,
}: UserPageLayoutProps) {
  const { breadcrumbs, hasMultipleLevels } = useBreadcrumbNavigation();
  const t = useTranslations('Navigation');

  // Enhanced breadcrumb component
  const EnhancedBreadcrumb = () => {
    if (customBreadcrumb) {
      return customBreadcrumb;
    }

    return (
      <Breadcrumb>
        <BreadcrumbList>
          {/* Home link */}
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href='/dashboard' className='flex items-center gap-1'>
                <Home className='h-3 w-3' />
                <span className='hidden sm:inline'>{t('dashboard')}</span>
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>

          {/* Current page */}
          {title && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className='font-medium'>
                  {breadcrumbPage || title}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </>
          )}
        </BreadcrumbList>
      </Breadcrumb>
    );
  };

  return (
    <>
      <header className='flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b border-sidebar-border'>
        <div className='flex items-center gap-2 px-4'>
          <SidebarTrigger className='-ml-1' />
          <Separator orientation='vertical' className='mr-2 h-4' />

          {showBackButton && (
            <>
              <Button
                variant='ghost'
                size='sm'
                onClick={() => window.history.back()}
                className='mr-2 h-8 w-8 p-0'
                title={t('backToPrevious')}
              >
                <ArrowLeft className='h-4 w-4' />
              </Button>
              <Separator orientation='vertical' className='mr-2 h-4' />
            </>
          )}

          <EnhancedBreadcrumb />
        </div>

        <div className='ml-auto px-4 flex items-center gap-2'>
          {headerActions}
          <LanguageSwitcher variant='dropdown' size='sm' />
          <ModeToggle />
        </div>
      </header>

      <div className={cn('flex flex-1 flex-col gap-4 p-4 pt-0', className)}>
        {description && (
          <div className='mb-2 p-3 bg-muted/50 rounded-lg border'>
            <p className='text-sm text-muted-foreground leading-relaxed'>
              {description}
            </p>
          </div>
        )}

        {loading ? (
          <div className='flex items-center justify-center py-12'>
            <div className='flex flex-col items-center gap-3'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
              <p className='text-sm text-muted-foreground'>{t('loading')}</p>
            </div>
          </div>
        ) : (
          children
        )}
      </div>
    </>
  );
}
