{"title": "Account Security", "breadcrumb": "Security Settings", "description": "Manage your account security settings and protect your account information", "navigation": {"title": "Account Security", "description": "Manage your account security settings and protect your account information"}, "security": {"emailVerification": "Email Verification", "emailVerified": "Verified", "emailUnverified": "Unverified", "passwordStrength": "Password Strength", "passwordGood": "Good", "securityStatus": "Security Status", "securityNormal": "Normal"}, "profile": {"nickname": "Nickname", "email": "Email Address", "password": "Login Password", "passwordDescription": "Password length 6-32 characters, can only contain letters and numbers", "lastPasswordChange": "Last Password Change"}, "actions": {"modify": "Modify", "change": "Change", "changePassword": "Change Password", "cancel": "Cancel", "save": "Save", "confirm": "Confirm Change", "next": "Next", "back": "Back", "confirmChange": "Confirm Change", "sendCode": "Send Code"}, "nickname": {"dialogTitle": "Change Nickname", "dialogDescription": "Set a new nickname that will be displayed in the system", "newNickname": "New Nickname", "placeholder": "Enter new nickname", "lengthHint": "Nickname length should be between 2-20 characters"}, "email": {"dialogTitle": "Change Email", "step1": "Step 1/2", "step2": "Step 2/2", "step1Description": "Please verify your current email to confirm your identity", "step2Description": "Enter the new email address and verify to complete the change", "verificationCode": "Verification Code", "codePlaceholder": "Enter 6-digit verification code", "newEmail": "New Email Address", "newEmailPlaceholder": "Enter new email address", "codeSentCurrent": "Verification code sent to current email, please check", "codeSentNew": "Verification code sent to new email, please check"}, "password": {"dialogTitle": "Change Password", "dialogDescription": "Enter your current password and new password to complete the change", "currentPassword": "Current Password", "currentPasswordPlaceholder": "Enter current password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "confirmPassword": "Confirm New Password", "confirmPasswordPlaceholder": "Enter new password again", "passwordHint": "Password must be 6-32 characters, can only contain letters and numbers"}, "messages": {"loading": "Loading...", "verificationCodeSent": "Verification code sent to current email", "sendCodeFailed": "Failed to send verification code", "networkError": "Network error, please try again", "passwordChangeSuccess": "Password changed successfully", "passwordChangeFailed": "Failed to change password", "nicknameChangeSuccess": "Nickname changed successfully", "nicknameChangeFailed": "Failed to change nickname", "emailChangeSuccess": "Email changed successfully", "emailChangeFailed": "Failed to change email", "invalidVerificationCode": "Invalid or expired verification code", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "refreshAndRetry": "Please refresh the page and try again", "verificationCodeSentToNewEmail": "Verification code sent to new email", "emailVerificationSuccess": "Email verification successful", "verificationFailed": "Verification failed"}, "validation": {"verificationCodeRequired": "Please enter verification code", "verificationCodeLength": "Verification code should be 6 digits", "emailRequired": "Please enter email address", "nicknameRequired": "Please enter nickname", "nicknameLengthInvalid": "Nickname length should be between 2-20 characters", "currentPasswordRequired": "Please enter current password", "newPasswordRequired": "Please enter new password", "passwordMinLength": "Password must be at least 6 characters", "passwordMaxLength": "Password cannot exceed 32 characters", "passwordFormatInvalid": "Password can only contain letters and numbers", "passwordMismatch": "Passwords do not match"}, "security_alert": {"title": "Security Alert", "description": "To protect your account security, we recommend changing your password regularly, not saving login information on public devices, and contacting customer service immediately if you notice any unusual login activity."}, "twoFactor": {"title": "Two-Factor Authentication", "description": "Add an extra layer of security to your account", "enabled": "Enabled", "disabled": "Disabled", "enable": "Enable 2FA", "disable": "Disable 2FA", "setup": "Setup 2FA", "verify": "Verify", "qrCode": "Scan QR Code", "manualEntry": "Manual Entry", "backupCodes": "Backup Codes", "generateBackupCodes": "Generate Backup Codes"}, "loginLogs": {"title": "Login Logs", "description": "View your account login history", "time": "Login Time", "ip": "IP Address", "device": "Device Info", "location": "Location", "status": "Status", "success": "Success", "failed": "Failed", "current": "Current Session", "unknown": "Unknown", "empty": "No login records", "refresh": "Refresh Records"}, "securityLevel": {"title": "Account Security Level", "low": "Low", "medium": "Medium", "high": "High", "excellent": "Excellent", "recommendations": {"title": "Security Recommendations", "enableTwoFactor": "Enable Two-Factor Authentication", "strongPassword": "Use Strong Password", "regularUpdate": "Update Password Regularly", "verifyEmail": "Verify Em<PERSON> Address"}}, "deviceManagement": {"title": "Device Management", "description": "Manage logged-in devices", "currentDevice": "Current Device", "otherDevices": "Other Devices", "lastActive": "Last Active", "signOut": "Sign Out", "signOutAll": "Sign Out All Devices", "deviceType": {"desktop": "Desktop", "mobile": "Mobile", "tablet": "Tablet", "unknown": "Unknown Device"}}}