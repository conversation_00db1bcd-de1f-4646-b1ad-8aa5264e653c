import prisma from '@/lib/db';

// 生成委托ID
export async function generateTaskId(): Promise<string> {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;

  const prefix = `JF${dateStr}`;

  // 使用数据库事务确保ID唯一性
  const taskId = await prisma.$transaction(async tx => {
    const existingTasksCount = await tx.task.count({
      where: {
        id: {
          startsWith: prefix,
        },
      },
    });

    const sequence = String(existingTasksCount + 1).padStart(3, '0');
    const newTaskId = `${prefix}${sequence}`;

    // 检查ID是否已存在（防止并发问题）
    const existingTask = await tx.task.findUnique({
      where: { id: newTaskId },
    });

    if (existingTask) {
      throw new Error('ID_CONFLICT');
    }

    return newTaskId;
  });

  return taskId;
}

// 重试生成委托ID
export async function generateTaskIdWithRetry(
  maxRetries: number = 5,
): Promise<string> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await generateTaskId();
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === 'ID_CONFLICT' &&
        i < maxRetries - 1
      ) {
        await new Promise(resolve =>
          setTimeout(resolve, 100 + Math.random() * 100),
        );
        continue;
      }
      throw error;
    }
  }

  throw new Error('无法生成唯一的委托ID，请稍后重试');
}
