import prisma from '@/lib/db';

import { NOWPaymentsProvider } from './providers/nowpayments';
import { YunPayProvider } from './providers/yunpay';
import {
  PaymentProvider,
  PaymentConfig,
  YunPayConfig,
  NOWPaymentsConfig,
  CreatePaymentParams,
} from './types';

export class PaymentManager {
  private providers = new Map<string, PaymentProvider>();
  private initialized = false;

  async initialize() {
    if (this.initialized) return;

    const configs = await this.getPaymentConfigs();

    configs.forEach(config => {
      if (!config.isEnabled) return;

      switch (config.provider) {
        case 'yunpay':
          this.providers.set(
            'yunpay',
            new YunPayProvider(config.settings as YunPayConfig),
          );
          break;
        case 'nowpayments':
          this.providers.set(
            'nowpayments',
            new NOWPaymentsProvider(config.settings as NOWPaymentsConfig),
          );
          break;
        default:
        // 忽略未知的支付提供商
      }
    });

    this.initialized = true;
  }

  async createPayment(params: CreatePaymentParams & { provider: string }) {
    if (!this.initialized) {
      await this.initialize();
    }

    const provider = this.providers.get(params.provider);
    if (!provider) {
      throw new Error(`支付提供商 ${params.provider} 不可用`);
    }

    // 如果没有提供回调URL，则使用默认的
    const finalParams = {
      ...params,
      notifyUrl:
        params.notifyUrl ||
        `${process.env.NEXTAUTH_URL}/api/payments/notify/${params.provider}`,
      returnUrl:
        params.returnUrl || `${process.env.NEXTAUTH_URL}/payment/success`,
    };

    return provider.createPayment(finalParams);
  }

  async getProvider(name: string): Promise<PaymentProvider | undefined> {
    if (!this.initialized) {
      await this.initialize();
    }
    return this.providers.get(name);
  }

  async getAvailableProviders(): Promise<string[]> {
    if (!this.initialized) {
      await this.initialize();
    }
    return Array.from(this.providers.keys());
  }

  async refreshProviders() {
    this.providers.clear();
    this.initialized = false;
    await this.initialize();
  }

  private async getPaymentConfigs(): Promise<PaymentConfig[]> {
    try {
      const configs = await prisma.paymentConfig.findMany({
        where: { isEnabled: true },
      });

      return configs.map((config: any) => ({
        id: config.id,
        provider: config.provider,
        name: config.name,
        isEnabled: config.isEnabled,
        settings: config.settings as Record<string, any>,
      }));
    } catch (error) {
      return [];
    }
  }
}

// 全局单例实例
export const paymentManager = new PaymentManager();
