'use client';

import { useQuery } from '@tanstack/react-query';
import {
  CreditCard,
  Smartphone,
  DollarSign,
  Bitcoin,
  Loader2,
  CheckCircle,
  ExternalLink,
  Wallet,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect, useRef, useCallback } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { usePurchaseMembership } from '@/hooks/use-membership-plans';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  provider: string;
  enabled: boolean;
  feeRate?: number;
  minAmount?: number;
}

interface MembershipPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  planId: string;
  planName: string;
  price: number;
  action: 'upgrade' | 'renew';
  onSuccess?: () => void;
}

export function MembershipPaymentDialog({
  open,
  onOpenChange,
  planId,
  planName,
  price,
  action,
  onSuccess,
}: MembershipPaymentDialogProps) {
  const t = useTranslations('membership');
  const [selectedMethod, setSelectedMethod] = useState('');
  const [paymentUrl, setPaymentUrl] = useState('');
  const [showPaymentResult, setShowPaymentResult] = useState(false);
  const [currentOrderNo, setCurrentOrderNo] = useState('');
  const [isCheckingPayment, setIsCheckingPayment] = useState(false);
  const [autoCheckCount, setAutoCheckCount] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const purchaseMutation = usePurchaseMembership();

  // 重置状态
  const handleClose = useCallback(() => {
    // 清除定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setSelectedMethod('');
    setPaymentUrl('');
    setShowPaymentResult(false);
    setCurrentOrderNo('');
    setIsCheckingPayment(false);
    setAutoCheckCount(0);
    onOpenChange(false);
  }, [onOpenChange]);

  // 自动检查支付状态
  useEffect(() => {
    if (showPaymentResult && currentOrderNo && !isCheckingPayment) {
      // 清除之前的定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // 设置新的定时器，每5秒检查一次支付状态
      intervalRef.current = setInterval(async () => {
        try {
          const response = await fetch(
            `/api/payments/status/${currentOrderNo}`,
          );
          const result = await response.json();

          if (response.ok && result.order) {
            const { order } = result;

            if (order.status === 'PAID') {
              // 支付成功，清除定时器并关闭对话框
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
              }
              toast.success(
                action === 'upgrade'
                  ? t('payment.status.upgradeSuccess')
                  : t('payment.status.renewSuccess'),
              );
              onSuccess?.();
              handleClose();
            } else if (
              order.status === 'EXPIRED' ||
              order.status === 'FAILED'
            ) {
              // 支付失败或过期，清除定时器
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
              }
              toast.error(
                order.status === 'EXPIRED'
                  ? t('payment.status.paymentExpired')
                  : t('payment.status.paymentFailed'),
              );
            }
          }
        } catch (error) {
          console.error('Auto check payment status failed:', error);
        }

        // 增加检查次数，最多检查24次（2分钟）
        setAutoCheckCount(prev => prev + 1);
      }, 5000);

      // 2分钟后停止自动检查
      setTimeout(() => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }, 120000);
    }

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [
    showPaymentResult,
    currentOrderNo,
    isCheckingPayment,
    action,
    onSuccess,
    handleClose,
    t,
  ]);

  // 获取可用支付方式
  const { data: paymentMethodsData, isLoading: isLoadingMethods } = useQuery({
    queryKey: ['payment-methods'],
    queryFn: async () => {
      const res = await fetch('/api/payments/methods');
      if (!res.ok) throw new Error(t('payment.status.getPaymentMethodsFailed'));
      const data = await res.json();
      return data.methods as PaymentMethod[];
    },
  });

  // 确保 paymentMethods 始终是一个数组
  const paymentMethods = Array.isArray(paymentMethodsData)
    ? paymentMethodsData
    : [];

  // 图标映射
  const getPaymentIcon = (iconName: string) => {
    switch (iconName) {
      case 'wallet':
        return Wallet;
      case 'smartphone':
        return Smartphone;
      case 'creditCard':
        return CreditCard;
      case 'bitcoin':
        return Bitcoin;
      default:
        return DollarSign;
    }
  };

  // 处理支付方式选择和支付创建
  const handlePayment = async () => {
    if (!selectedMethod) {
      toast.error(t('payment.dialog.selectMethodFirst'));
      return;
    }

    const selectedPaymentMethod = paymentMethods.find(
      m => m.id === selectedMethod,
    );
    if (!selectedPaymentMethod) {
      toast.error(t('payment.dialog.invalidPaymentMethod'));
      return;
    }

    // 计算总费用
    const feeRate = selectedPaymentMethod.feeRate || 0;
    const feeAmount = (price * feeRate) / 100;
    const totalAmount = price + feeAmount;

    try {
      const result = await purchaseMutation.mutateAsync({
        planId,
        planName,
        paymentMethod: selectedMethod,
        action,
      });

      if (result.paymentUrl) {
        setPaymentUrl(result.paymentUrl);
        setCurrentOrderNo(result.orderNo); // 保存订单号
        setShowPaymentResult(true);

        // 对于加密货币支付，直接打开新窗口
        if (selectedMethod === 'crypto') {
          window.open(result.paymentUrl, '_blank');
          toast.success(t('payment.status.redirectedToPayment'));
        }
      }
    } catch (error) {
      console.error('Create payment failed:', error);
    }
  };

  // 检查支付状态
  const checkPaymentStatus = async () => {
    if (!currentOrderNo) {
      toast.error(t('payment.status.orderNotFound'));
      return;
    }

    setIsCheckingPayment(true);

    try {
      const response = await fetch(`/api/payments/status/${currentOrderNo}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || t('payment.status.checkFailed'));
      }

      const { order } = result;

      if (order.status === 'PAID') {
        // 支付成功
        toast.success(
          action === 'upgrade'
            ? t('payment.status.upgradeSuccess')
            : t('payment.status.renewSuccess'),
        );
        onSuccess?.();
        handleClose();
      } else if (order.status === 'EXPIRED') {
        // 支付已过期
        toast.error(t('payment.status.paymentExpired'));
        handleClose();
      } else if (order.status === 'FAILED') {
        // 支付失败
        toast.error(t('payment.status.paymentFailed'));
        handleClose();
      } else {
        // 支付还在进行中
        toast.warning(t('payment.status.paymentPending'), {
          description: t('payment.status.paymentPendingDescription'),
        });
      }
    } catch (error) {
      console.error('Check payment status failed:', error);
      toast.error(t('payment.status.checkFailed'), {
        description:
          error instanceof Error
            ? error.message
            : t('payment.status.unknownError'),
      });
    } finally {
      setIsCheckingPayment(false);
    }
  };

  // 支付成功回调（已废弃，由checkPaymentStatus替代）
  const handlePaymentSuccess = () => {
    checkPaymentStatus();
  };

  if (showPaymentResult) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle className='flex items-center gap-2'>
              <CheckCircle className='h-5 w-5 text-green-600' />
              {t('payment.result.title')}
            </DialogTitle>
            <DialogDescription>
              {action === 'upgrade'
                ? t('payment.result.description.upgrade', { planName })
                : t('payment.result.description.renew', { planName })}
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-4'>
            {paymentUrl && (
              <Card>
                <CardContent className='pt-6'>
                  <div className='text-center space-y-4'>
                    <p className='text-sm text-muted-foreground'>
                      {selectedMethod === 'crypto'
                        ? t('payment.result.cryptoPaymentNote')
                        : t('payment.result.paymentPageNote')}
                    </p>

                    {selectedMethod !== 'crypto' && (
                      <Button
                        onClick={() => window.open(paymentUrl, '_blank')}
                        className='w-full'
                      >
                        <ExternalLink className='h-4 w-4 mr-2' />
                        {t('payment.result.goToPayment')}
                      </Button>
                    )}

                    {/* 自动检查状态提示 */}
                    <div className='flex items-center justify-center gap-2 text-xs text-muted-foreground'>
                      <div className='w-2 h-2 bg-green-500 rounded-full animate-pulse'></div>
                      <span>{t('payment.result.autoCheckingStatus')}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={handleClose}
                className='flex-1'
              >
                {t('payment.dialog.cancel')}
              </Button>
              <Button
                onClick={handlePaymentSuccess}
                disabled={isCheckingPayment}
                className='flex-1'
              >
                {isCheckingPayment ? (
                  <>
                    <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                    {t('payment.result.checkingPaymentStatus')}
                  </>
                ) : (
                  t('payment.result.paymentCompleted')
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>
            {action === 'upgrade'
              ? t('payment.dialog.title.upgrade')
              : t('payment.dialog.title.renew')}
          </DialogTitle>
          <DialogDescription>
            {action === 'upgrade'
              ? t('payment.dialog.description.upgrade', { planName })
              : t('payment.dialog.description.renew', { planName })}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 套餐信息 */}
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-lg'>{planName}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-muted-foreground'>
                  {t('payment.dialog.planPrice')}
                </span>
                <span className='font-medium'>${price.toFixed(2)}</span>
              </div>
            </CardContent>
          </Card>

          {/* 支付方式选择 */}
          <div className='space-y-3'>
            <h4 className='font-medium'>
              {t('payment.dialog.selectPaymentMethod')}
            </h4>

            {isLoadingMethods ? (
              <div className='flex items-center justify-center py-8'>
                <Loader2 className='h-6 w-6 animate-spin' />
              </div>
            ) : (
              <div className='grid gap-2'>
                {paymentMethods.map(method => {
                  const feeRate = method.feeRate || 0;
                  const feeAmount = (price * feeRate) / 100;
                  const totalAmount = price + feeAmount;

                  return (
                    <Card
                      key={method.id}
                      className={`cursor-pointer transition-colors ${
                        selectedMethod === method.id
                          ? 'ring-2 ring-primary bg-primary/5'
                          : 'hover:bg-muted/50'
                      }`}
                      onClick={() => setSelectedMethod(method.id)}
                    >
                      <CardContent className='p-4'>
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center gap-3'>
                            {(() => {
                              const IconComponent = getPaymentIcon(method.icon);
                              return <IconComponent className='h-5 w-5' />;
                            })()}
                            <div>
                              <p className='font-medium'>{method.name}</p>
                              {feeRate > 0 && (
                                <p className='text-xs text-muted-foreground'>
                                  {t('payment.dialog.processingFee', {
                                    rate: feeRate,
                                  })}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className='text-right'>
                            <p className='font-medium'>
                              ${totalAmount.toFixed(2)}
                            </p>
                            {feeRate > 0 && (
                              <p className='text-xs text-muted-foreground'>
                                +${feeAmount.toFixed(2)}
                              </p>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>

          {/* 费用明细 */}
          {selectedMethod && (
            <Card>
              <CardContent className='pt-4'>
                <div className='space-y-2 text-sm'>
                  <div className='flex justify-between'>
                    <span>{t('payment.dialog.planCost')}</span>
                    <span>${price.toFixed(2)}</span>
                  </div>

                  {(() => {
                    const method = paymentMethods.find(
                      m => m.id === selectedMethod,
                    );
                    const feeRate = method?.feeRate || 0;
                    const feeAmount = (price * feeRate) / 100;
                    const totalAmount = price + feeAmount;

                    return (
                      <>
                        {feeRate > 0 && (
                          <div className='flex justify-between text-muted-foreground'>
                            <span>
                              {t('payment.dialog.processingFee', {
                                rate: feeRate,
                              })}
                            </span>
                            <span>+${feeAmount.toFixed(2)}</span>
                          </div>
                        )}
                        <div className='flex justify-between font-medium border-t pt-2'>
                          <span>{t('payment.dialog.total')}</span>
                          <span>${totalAmount.toFixed(2)}</span>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 操作按钮 */}
          <div className='flex gap-2'>
            <Button variant='outline' onClick={handleClose} className='flex-1'>
              {t('payment.dialog.cancel')}
            </Button>
            <Button
              onClick={handlePayment}
              disabled={!selectedMethod || purchaseMutation.isPending}
              className='flex-1'
            >
              {purchaseMutation.isPending ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  {t('payment.dialog.creating')}
                </>
              ) : (
                t('payment.dialog.confirmPayment')
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
