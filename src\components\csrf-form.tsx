'use client';

import { Loader2 } from 'lucide-react';
import React, { FormEvent, ReactNode } from 'react';

import { useCSRFForm } from '@/hooks/use-csrf';

interface CSRFFormProps {
  action: string;
  method?: 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  onSubmit?: (data: any) => void | Promise<void>;
  onError?: (error: Error) => void;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function CSRFForm({
  action,
  method = 'POST',
  onSubmit,
  onError,
  children,
  className,
  disabled = false,
}: CSRFFormProps) {
  const { submitForm, csrfToken, isReady } = useCSRFForm();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!isReady || isSubmitting || disabled) {
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData(event.currentTarget);
      const result = await submitForm(action, formData);

      if (onSubmit) {
        await onSubmit(result);
      }
    } catch (error) {
      const err =
        error instanceof Error ? error : new Error('Form submission failed');
      if (onError) {
        onError(err);
      } else {
        console.error('Form submission error:', err);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isReady) {
    return (
      <div className='flex items-center justify-center p-4'>
        <Loader2 className='h-4 w-4 animate-spin' />
        <span className='ml-2 text-sm text-muted-foreground'>
          正在初始化安全保护...
        </span>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className={className}
      method={method}
      action={action}
    >
      {/* 隐藏的 CSRF Token 字段 */}
      <input type='hidden' name='_csrf' value={csrfToken || ''} />
      <input type='hidden' name='_method' value={method} />

      {/* 表单内容 */}
      <fieldset disabled={isSubmitting || disabled}>{children}</fieldset>

      {/* 提交状态指示器 */}
      {isSubmitting && (
        <div className='absolute inset-0 bg-background/50 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Loader2 className='h-4 w-4 animate-spin' />
            <span className='text-sm'>提交中...</span>
          </div>
        </div>
      )}
    </form>
  );
}

// CSRF 保护的输入组件
interface CSRFInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  name: string;
}

export function CSRFInput({ name, ...props }: CSRFInputProps) {
  return <input name={name} {...props} />;
}

// CSRF 保护的文本域组件
interface CSRFTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  name: string;
}

export function CSRFTextarea({ name, ...props }: CSRFTextareaProps) {
  return <textarea name={name} {...props} />;
}

// CSRF 保护的选择框组件
interface CSRFSelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement> {
  name: string;
  children: ReactNode;
}

export function CSRFSelect({ name, children, ...props }: CSRFSelectProps) {
  return (
    <select name={name} {...props}>
      {children}
    </select>
  );
}

// 高阶组件：为现有表单添加 CSRF 保护
export function withCSRFProtection<P extends object>(
  WrappedComponent: React.ComponentType<P>,
) {
  return function CSRFProtectedComponent(props: P) {
    const { csrfToken, isReady } = useCSRFForm();

    if (!isReady) {
      return (
        <div className='flex items-center justify-center p-4'>
          <Loader2 className='h-4 w-4 animate-spin' />
          <span className='ml-2 text-sm text-muted-foreground'>
            正在加载安全保护...
          </span>
        </div>
      );
    }

    return (
      <>
        <input type='hidden' name='_csrf' value={csrfToken || ''} />
        <WrappedComponent {...props} />
      </>
    );
  };
}

// CSRF Token 显示组件（用于调试）
export function CSRFTokenDisplay() {
  const { csrfToken, isReady } = useCSRFForm();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className='p-2 bg-muted rounded text-xs font-mono'>
      <div>
        CSRF Token: {isReady ? `${csrfToken?.slice(0, 16)}...` : 'Loading...'}
      </div>
      <div>Status: {isReady ? 'Ready' : 'Not Ready'}</div>
    </div>
  );
}

export default CSRFForm;
