// 测试不允许的状态转换消息翻译修复
const fs = require('fs');
const path = require('path');

console.log('开始测试...');

try {
  // 读取相关文件
  const apiPath = path.join(
    __dirname,
    '../../../src/app/api/tickets/[id]/route.ts'
  );
  const serverI18nPath = path.join(
    __dirname,
    '../../../src/lib/server-i18n.ts'
  );
  const zhTransPath = path.join(__dirname, '../../../messages/zh/tickets.json');
  const enTransPath = path.join(__dirname, '../../../messages/en/tickets.json');

  const apiContent = fs.readFileSync(apiPath, 'utf8');
  const serverI18nContent = fs.readFileSync(serverI18nPath, 'utf8');
  const zhTrans = JSON.parse(fs.readFileSync(zhTransPath, 'utf8'));
  const enTrans = JSON.parse(fs.readFileSync(enTransPath, 'utf8'));

  console.log('=== 不允许的状态转换消息翻译修复验证 ===\n');

  // 检查修复的问题
  const fixes = [
    {
      category: 'API端点翻译修复',
      checks: [
        {
          description: '导入服务器端翻译工具',
          test: () =>
            apiContent.includes(
              "import { createServerTranslator } from '@/lib/server-i18n';"
            ),
          expected: true,
        },
        {
          description: '创建翻译器实例',
          test: () =>
            apiContent.includes(
              'const { t } = createServerTranslator(request);'
            ),
          expected: true,
        },
        {
          description: '移除硬编码"不允许的状态转换"',
          test: () => !apiContent.includes("'不允许的状态转换'"),
          expected: true,
        },
        {
          description: '使用翻译函数替换状态转换错误',
          test: () => apiContent.includes("t('invalidTransition')"),
          expected: true,
        },
        {
          description: '移除硬编码"请先登录"',
          test: () => !apiContent.includes("'请先登录'"),
          expected: true,
        },
        {
          description: '使用翻译函数替换登录错误',
          test: () => apiContent.includes("t('loginRequired')"),
          expected: true,
        },
        {
          description: '移除硬编码"工单不存在或无权限访问"',
          test: () => !apiContent.includes("'工单不存在或无权限访问'"),
          expected: true,
        },
        {
          description: '使用翻译函数替换工单不存在错误',
          test: () => apiContent.includes("t('ticketNotFound')"),
          expected: true,
        },
      ],
    },
    {
      category: '服务器端翻译工具',
      checks: [
        {
          description: '服务器端翻译文件存在',
          test: () => fs.existsSync(serverI18nPath),
          expected: true,
        },
        {
          description: '包含语言检测函数',
          test: () => serverI18nContent.includes('detectUserLanguage'),
          expected: true,
        },
        {
          description: '包含翻译消息获取函数',
          test: () => serverI18nContent.includes('getServerMessage'),
          expected: true,
        },
        {
          description: '包含翻译器创建函数',
          test: () => serverI18nContent.includes('createServerTranslator'),
          expected: true,
        },
        {
          description: '包含invalidTransition消息',
          test: () =>
            serverI18nContent.includes('invalidTransition') &&
            serverI18nContent.includes('不允许的状态转换') &&
            serverI18nContent.includes('Invalid status transition'),
          expected: true,
        },
      ],
    },
    {
      category: '前端翻译文件',
      checks: [
        {
          description: '中文翻译包含invalidTransition',
          test: () =>
            zhTrans.messages &&
            zhTrans.messages.invalidTransition === '不允许的状态转换',
          expected: true,
        },
        {
          description: '英文翻译包含invalidTransition',
          test: () =>
            enTrans.messages &&
            enTrans.messages.invalidTransition === 'Invalid status transition',
          expected: true,
        },
      ],
    },
  ];

  let allPassed = true;

  fixes.forEach(category => {
    console.log(`📋 ${category.category}:`);

    category.checks.forEach((check, index) => {
      const result = check.test();
      const status = result === check.expected ? '✅' : '❌';

      if (result !== check.expected) {
        allPassed = false;
      }

      console.log(`   ${index + 1}. ${check.description}: ${status}`);
    });

    console.log('');
  });

  console.log('=== 修复结果总结 ===');
  if (allPassed) {
    console.log('✅ 所有修复项目都已完成！');
    console.log('\n🎯 修复内容：');
    console.log('1. 创建了服务器端翻译工具 (server-i18n.ts)');
    console.log('2. API端点现在使用翻译函数而不是硬编码中文');
    console.log('3. 支持根据请求语言返回对应的错误消息');
    console.log('4. 前端翻译文件已添加相应的错误消息键');
  } else {
    console.log('❌ 仍有问题需要修复');
  }

  console.log('\n=== 翻译对照表 ===');
  console.log('错误类型 | 中文 | 英文');
  console.log('--------|------|------');
  console.log(
    `状态转换错误 | ${zhTrans.messages?.invalidTransition || '缺失'} | ${enTrans.messages?.invalidTransition || '缺失'}`
  );
  console.log('登录错误 | 请先登录 | Please login first');
  console.log(
    '工单不存在 | 工单不存在或无权限访问 | Ticket not found or no permission to access'
  );
  console.log('数据格式错误 | 请求数据格式错误 | Invalid request data format');
  console.log('服务器错误 | 服务器内部错误 | Internal server error');

  console.log('\n=== 工单状态转换规则 ===');
  console.log('当前状态 → 允许的转换');
  console.log('PENDING → CANCELLED (待处理 → 已取消)');
  console.log('IN_PROGRESS → CANCELLED (处理中 → 已取消)');
  console.log('RESOLVED → CLOSED (已解决 → 已关闭)');
  console.log('CLOSED → 无转换 (已关闭 → 不允许任何转换)');
  console.log('CANCELLED → 无转换 (已取消 → 不允许任何转换)');

  console.log('\n=== 测试建议 ===');
  console.log('1. 启动开发服务器: npm run dev');
  console.log('2. 测试中文环境下的状态转换错误：');
  console.log('   - 访问: http://localhost:3002/zh/tickets');
  console.log('   - 尝试进行不允许的状态转换');
  console.log('   - 验证错误消息显示为中文');
  console.log('3. 测试英文环境下的状态转换错误：');
  console.log('   - 访问: http://localhost:3002/en/tickets');
  console.log('   - 尝试进行不允许的状态转换');
  console.log('   - 验证错误消息显示为英文');
  console.log('4. 测试场景：');
  console.log('   - 尝试关闭已关闭的工单');
  console.log('   - 尝试取消已取消的工单');
  console.log('   - 验证API返回正确的错误消息');
} catch (error) {
  console.error('测试脚本执行失败:', error.message);
}
