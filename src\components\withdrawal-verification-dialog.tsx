import { Mail, Clock } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface WithdrawalVerificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onVerificationSuccess: (token: string) => void;
  userEmail?: string;
}

export function WithdrawalVerificationDialog({
  open,
  onOpenChange,
  onVerificationSuccess,
  userEmail,
}: WithdrawalVerificationDialogProps) {
  const t = useTranslations('wallet');
  const [verificationCode, setVerificationCode] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [codeSent, setCodeSent] = useState(false);

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 发送验证码
  const handleSendCode = async () => {
    setIsSending(true);
    try {
      const response = await fetch('/api/user/withdrawal-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send-withdrawal-verification',
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCodeSent(true);
        setCountdown(60);
        toast.success(t('modals.emailVerification.messages.codeSent'));
      } else {
        toast.error(
          result.message || t('modals.emailVerification.messages.sendFailed'),
        );
      }
    } catch (error) {
      toast.error(t('modals.emailVerification.messages.sendFailed'));
    } finally {
      setIsSending(false);
    }
  };

  // 验证验证码
  const handleVerifyCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error(t('modals.emailVerification.messages.invalidCode'));
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch('/api/user/withdrawal-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'verify-withdrawal-code',
          verificationCode,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(t('messages.verificationSuccess'));
        onVerificationSuccess(result.data.withdrawalToken);
        onOpenChange(false);
        // 重置状态
        setVerificationCode('');
        setCodeSent(false);
        setCountdown(0);
      } else {
        toast.error(
          result.message || t('modals.emailVerification.messages.verifyFailed'),
        );
      }
    } catch (error) {
      toast.error(t('modals.emailVerification.messages.verifyFailed'));
    } finally {
      setIsVerifying(false);
    }
  };

  // 重置状态当对话框关闭时
  useEffect(() => {
    if (!open) {
      setVerificationCode('');
      setCodeSent(false);
      setCountdown(0);
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Mail className='h-5 w-5' />
            {t('modals.emailVerification.title')}
          </DialogTitle>
          <DialogDescription>
            {t('modals.emailVerification.description')}
            {userEmail && (
              <span className='block mt-1 font-medium text-foreground'>
                {userEmail}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4 py-4'>
          <div className='space-y-2'>
            <Label htmlFor='verification-code'>
              {t('modals.emailVerification.verificationCode')}
            </Label>
            <Input
              id='verification-code'
              type='text'
              placeholder={t(
                'modals.emailVerification.verificationCodePlaceholder',
              )}
              value={verificationCode}
              onChange={e =>
                setVerificationCode(
                  e.target.value.replace(/\D/g, '').slice(0, 6),
                )
              }
              maxLength={6}
            />
          </div>

          <Button
            type='button'
            variant='outline'
            className='w-full'
            onClick={handleSendCode}
            disabled={isSending || countdown > 0}
          >
            {isSending ? (
              t('modals.emailVerification.messages.sending')
            ) : countdown > 0 ? (
              <span className='flex items-center gap-2'>
                <Clock className='h-4 w-4' />
                {countdown}
                {t('modals.emailVerification.messages.resendCountdown')}
              </span>
            ) : codeSent ? (
              t('modals.emailVerification.resendCode')
            ) : (
              t('modals.emailVerification.sendCode')
            )}
          </Button>
        </div>

        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
          >
            {t('modals.emailVerification.cancel')}
          </Button>
          <Button
            type='button'
            onClick={handleVerifyCode}
            disabled={
              isVerifying || !verificationCode || verificationCode.length !== 6
            }
          >
            {isVerifying
              ? t('modals.emailVerification.messages.verifying')
              : t('modals.emailVerification.verify')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
