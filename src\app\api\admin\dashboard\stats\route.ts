import { TaskStatus, UserRole } from '@prisma/client';
import { NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';

export async function GET() {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    // 获取时间范围（用于计算增长率）
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // 1. 基础统计数据
    const [
      totalUsers,
      newUsersThisMonth,
      newUsersLastMonth,
      totalTasks,
      tasksThisMonth,
      tasksLastMonth,
      totalRevenue,
      revenueThisMonth,
      revenueLastMonth,
    ] = await Promise.all([
      // 总用户数
      prisma.user.count({
        where: { role: UserRole.USER },
      }),
      // 本月新增用户
      prisma.user.count({
        where: {
          role: UserRole.USER,
          createdAt: { gte: thisMonth },
        },
      }),
      // 上月新增用户
      prisma.user.count({
        where: {
          role: UserRole.USER,
          createdAt: {
            gte: lastMonth,
            lt: thisMonth,
          },
        },
      }),
      // 总委托数
      prisma.task.count(),
      // 本月委托数
      prisma.task.count({
        where: {
          createdAt: { gte: thisMonth },
        },
      }),
      // 上月委托数
      prisma.task.count({
        where: {
          createdAt: {
            gte: lastMonth,
            lt: thisMonth,
          },
        },
      }),
      // 总收入（所有用户的总支出）
      prisma.user.aggregate({
        _sum: { totalExpense: true },
      }),
      // 本月收入
      prisma.walletTransaction.aggregate({
        where: {
          type: 'TASK_FEE',
          status: 'COMPLETED',
          completedAt: { gte: thisMonth },
        },
        _sum: { amount: true },
      }),
      // 上月收入
      prisma.walletTransaction.aggregate({
        where: {
          type: 'TASK_FEE',
          status: 'COMPLETED',
          completedAt: {
            gte: lastMonth,
            lt: thisMonth,
          },
        },
        _sum: { amount: true },
      }),
    ]);

    // 计算增长率
    const calculateGrowthRate = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const userGrowth = calculateGrowthRate(
      newUsersThisMonth,
      newUsersLastMonth,
    );
    const taskGrowth = calculateGrowthRate(tasksThisMonth, tasksLastMonth);
    const revenueGrowth = calculateGrowthRate(
      Math.abs(revenueThisMonth._sum.amount || 0),
      Math.abs(revenueLastMonth._sum.amount || 0),
    );

    // 2. 平台分布数据
    const platformStats = await prisma.task.groupBy({
      by: ['platformId'],
      _count: { platformId: true },
      orderBy: { _count: { platformId: 'desc' } },
      take: 10,
    });

    // 获取平台名称
    const platformIds = platformStats.map(stat => stat.platformId);
    const platforms = await prisma.platform.findMany({
      where: { id: { in: platformIds } },
      select: { id: true, name: true },
    });

    const platformMap = platforms.reduce(
      (map, platform) => {
        map[platform.id] = platform.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const totalPlatformTasks = platformStats.reduce(
      (sum, stat) => sum + stat._count.platformId,
      0,
    );
    const platformDistribution = platformStats.map((stat, index) => ({
      platform: platformMap[stat.platformId] || '未知平台',
      count: stat._count.platformId,
      percentage: parseFloat(
        ((stat._count.platformId / totalPlatformTasks) * 100).toFixed(1),
      ),
      color:
        [
          'bg-orange-500',
          'bg-red-500',
          'bg-yellow-500',
          'bg-black',
          'bg-pink-500',
          'bg-blue-500',
          'bg-green-500',
          'bg-purple-500',
          'bg-indigo-500',
          'bg-gray-500',
        ][index] || 'bg-gray-500',
    }));

    // 3. 分类分布数据
    const categoryStats = await prisma.task.groupBy({
      by: ['categoryId'],
      _count: { categoryId: true },
      orderBy: { _count: { categoryId: 'desc' } },
      take: 10,
    });

    // 获取分类名称
    const categoryIds = categoryStats.map(stat => stat.categoryId);
    const categories = await prisma.category.findMany({
      where: { id: { in: categoryIds } },
      select: { id: true, name: true },
    });

    const categoryMap = categories.reduce(
      (map, category) => {
        map[category.id] = category.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const totalCategoryTasks = categoryStats.reduce(
      (sum, stat) => sum + stat._count.categoryId,
      0,
    );
    const categoryDistribution = categoryStats.map(stat => ({
      category: categoryMap[stat.categoryId] || '未知分类',
      count: stat._count.categoryId,
      percentage: parseFloat(
        ((stat._count.categoryId / totalCategoryTasks) * 100).toFixed(1),
      ),
    }));

    // 4. 最近委托（最新5个）
    const recentTasks = await prisma.task.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: {
        platform: {
          select: { name: true },
        },
      },
    });

    const recentTasksData = recentTasks.map(task => ({
      id: task.id,
      platform: task.platform.name,
      amount: task.finalTotal,
      status: getStatusLabel(task.status),
    }));

    // 5. 最近用户（最新4个）
    const recentUsers = await prisma.user.findMany({
      where: { role: UserRole.USER },
      orderBy: { createdAt: 'desc' },
      take: 4,
      select: {
        id: true,
        name: true,
        email: true,
        memberPlan: true,
      },
    });

    const recentUsersData = recentUsers.map(user => ({
      id: user.id,
      name: user.name || '未知用户',
      email: maskEmail(user.email || ''),
      memberType: getMemberPlanLabel(user.memberPlan),
    }));

    return NextResponse.json({
      success: true,
      data: {
        // 基础统计
        stats: {
          totalUsers,
          newUsers: newUsersThisMonth,
          totalTasks,
          totalRevenue: Math.abs(totalRevenue._sum.totalExpense || 0),
          userGrowth: `${userGrowth >= 0 ? '+' : ''}${userGrowth.toFixed(1)}%`,
          taskGrowth: `${taskGrowth >= 0 ? '+' : ''}${taskGrowth.toFixed(1)}%`,
          revenueGrowth: `${revenueGrowth >= 0 ? '+' : ''}${revenueGrowth.toFixed(1)}%`,
        },
        // 分布数据
        platformStats: platformDistribution,
        categoryStats: categoryDistribution,
        // 最近数据
        recentTasks: recentTasksData,
        recentUsers: recentUsersData,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 辅助函数：获取委托状态标签
function getStatusLabel(status: TaskStatus): string {
  switch (status) {
    case TaskStatus.PENDING:
      return '待审核';
    case TaskStatus.RECRUITING:
      return '招募中';
    case TaskStatus.IN_PROGRESS:
      return '进行中';
    case TaskStatus.COMPLETED:
      return '已完成';
    case TaskStatus.REJECTED:
      return '已拒绝';
    case TaskStatus.EXPIRED:
      return '已过期';
    case TaskStatus.CANCELLED:
      return '已取消';
    default:
      return '未知状态';
  }
}

// 辅助函数：获取会员计划标签
function getMemberPlanLabel(memberPlan: string): string {
  switch (memberPlan) {
    case 'FREE':
      return '免费版';
    case 'PRO':
      return '专业版';
    case 'BUSINESS':
      return '商业版';
    default:
      return '免费版';
  }
}

// 辅助函数：邮箱脱敏
function maskEmail(email: string): string {
  if (!email) return '';
  const [local, domain] = email.split('@');
  if (!local || !domain) return email;

  if (local.length <= 3) {
    return `${local.charAt(0)}***@${domain}`;
  }
  return `${local.slice(0, 3)}***@${domain}`;
}
