import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import {
  updateSessionActivity,
  performSecurityCheck,
} from '@/lib/session-management';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: 'No active session' },
        { status: 401 },
      );
    }

    const userId = session.user.id;

    // 执行安全检查
    const isSecure = await performSecurityCheck(userId);
    if (!isSecure) {
      return NextResponse.json(
        { success: false, message: 'Security check failed' },
        { status: 403 },
      );
    }

    // 更新会话活动时间
    await updateSessionActivity(userId);

    return NextResponse.json({
      success: true,
      message: 'Session extended successfully',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
    });
  } catch (error) {
    console.error('Session extension error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to extend session' },
      { status: 500 },
    );
  }
}
