import { track17API } from '@/lib/17track';
import prisma from '@/lib/db';

export interface LogisticsTrackingData {
  taskId: string;
  trackingNumber: string;
  carrierCode?: number;
  orderNumber?: string;
  orderTime?: string;
}

interface LogisticsServiceInterface {
  registerTracking(
    data: LogisticsTrackingData
  ): Promise<{ success: boolean; error?: string }>;
  getTrackingInfo(trackingNumber: string, carrierCode?: number): Promise<any>;
}

export class LogisticsService implements LogisticsServiceInterface {
  // 注册物流单号
  async registerTracking(
    data: LogisticsTrackingData,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { taskId, trackingNumber, carrierCode, orderNumber, orderTime } =
        data;

      // 准备注册请求
      const registerRequest = {
        number: trackingNumber,
        carrier: carrierCode,
        order_no: orderNumber,
        order_time: orderTime,
        tag: taskId, // 使用委托ID作为标签
        auto_detection: true,
        lang: 'zh-hans', // 使用简体中文
      };

      // 调用API注册
      const response = await track17API.registerTracking([registerRequest]);

      if (response.code === 0 && response.data.accepted.length > 0) {
        const accepted = response.data.accepted[0];

        // 记录注册信息到日志
        console.log('物流单号注册成功:', {
          taskId,
          trackingNumber: accepted.number,
          carrierCode: accepted.carrier,
          origin: accepted.origin,
        });

        return { success: true };
      } else {
        const error = response.data.rejected?.[0]?.error?.message || '注册失败';
        console.error('物流单号注册失败:', error);
        return { success: false, error };
      }
    } catch (error) {
      console.error('注册物流单号时发生错误:', error);
      return { success: false, error: '服务异常' };
    }
  }

  // 获取物流跟踪信息
  async getTrackingInfo(
    trackingNumber: string,
    carrierCode?: number,
  ): Promise<any> {
    try {
      const response = await track17API.getTrackingInfo([
        {
          number: trackingNumber,
          carrier: carrierCode,
        },
      ]);

      if (response.code === 0 && response.data.accepted.length > 0) {
        return response.data.accepted[0];
      } else {
        const error = response.data.rejected?.[0]?.error?.message || '查询失败';
        console.error('查询失败:', {
          trackingNumber,
          carrierCode,
          error,
          response: response.data,
        });
        throw new Error(error);
      }
    } catch (error) {
      console.error('查询物流信息时发生错误:', error);
      throw error;
    }
  }

  // 删除物流跟踪
  async deleteTracking(
    trackingNumber: string,
    carrierCode?: number,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await track17API.deleteTracking([
        {
          number: trackingNumber,
          carrier: carrierCode,
        },
      ]);

      if (response.code === 0 && response.data.accepted.length > 0) {
        console.log('物流单号删除成功:', trackingNumber);
        return { success: true };
      } else {
        const error = response.data.rejected?.[0]?.error?.message || '删除失败';
        console.error('物流单号删除失败:', error);
        return { success: false, error };
      }
    } catch (error) {
      console.error('删除物流单号时发生错误:', error);
      return { success: false, error: '服务异常' };
    }
  }

  // 停止物流跟踪
  async stopTracking(
    trackingNumber: string,
    carrierCode?: number,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await track17API.stopTracking([
        {
          number: trackingNumber,
          carrier: carrierCode,
        },
      ]);

      if (response.code === 0 && response.data.accepted.length > 0) {
        console.log('物流跟踪停止成功:', trackingNumber);
        return { success: true };
      } else {
        const error = response.data.rejected?.[0]?.error?.message || '停止失败';
        console.error('物流跟踪停止失败:', error);
        return { success: false, error };
      }
    } catch (error) {
      console.error('停止物流跟踪时发生错误:', error);
      return { success: false, error: '服务异常' };
    }
  }

  // 检查配额
  async checkQuota(): Promise<any> {
    try {
      const response = await track17API.getQuota();

      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error('获取配额失败');
      }
    } catch (error) {
      console.error('检查API配额时发生错误:', error);
      throw error;
    }
  }

  // 格式化物流状态
  formatTrackingStatus(status: string): { label: string; color: string } {
    const statusMap: Record<string, { label: string; color: string }> = {
      NotFound: { label: '查询不到', color: 'gray' },
      InfoReceived: { label: '收到信息', color: 'blue' },
      InTransit: { label: '运输途中', color: 'yellow' },
      Expired: { label: '运输过久', color: 'red' },
      AvailableForPickup: { label: '到达待取', color: 'green' },
      OutForDelivery: { label: '派送途中', color: 'blue' },
      DeliveryFailure: { label: '投递失败', color: 'red' },
      Delivered: { label: '成功签收', color: 'green' },
      Exception: { label: '可能异常', color: 'red' },
    };

    return statusMap[status] || { label: '未知状态', color: 'gray' };
  }

  // 获取运输商列表
  getCarrierList(): Array<{ code: number; name: string; country: string }> {
    return [
      { code: 3011, name: 'China Post', country: 'CN' },
      { code: 100003, name: 'FedEx', country: 'US' },
      { code: 7041, name: 'DHL', country: 'DE' },
      { code: 21051, name: 'USPS', country: 'US' },
      { code: 11031, name: 'Royal Mail', country: 'GB' },
      { code: 1151, name: 'Australia Post', country: 'AU' },
      { code: 190415, name: '优速快递', country: 'CN' },
      { code: 190766, name: '顺丰速运', country: 'CN' },
      { code: 190845, name: '跨越速运', country: 'CN' },
    ];
  }
}

// 创建单例实例
export const logisticsService = new LogisticsService();
