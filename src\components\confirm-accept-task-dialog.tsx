'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Clock,
  AlertTriangle,
  X,
  Info,
  DollarSign,
  FileText,
  Shield,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { useAcceptTask } from '@/hooks/use-accept-task';
import { useSystemRate } from '@/hooks/use-commission-rates';
import { EvidenceStatus } from '@/lib/types/task';

interface ConfirmAcceptTaskDialogProps {
  children: React.ReactNode;
  task: {
    id: string;
    platform: string | { name: string };
    category: string | { name: string };
    quantity: number;
    unitPrice: number;
    totalAmount: number;
    evidenceStatus?: EvidenceStatus | string | null;
    evidenceUploadType?: string;
    commission: number;
    deposit: number;
  };
  isTaskPublisher?: boolean;
  onAccept?: () => void;
}

export function ConfirmAcceptTaskDialog({
  children,
  task,
  isTaskPublisher = false,
  onAccept,
}: ConfirmAcceptTaskDialogProps) {
  const t = useTranslations('Tasks.confirmDialog');
  const tPublish = useTranslations('publish');
  const [isOpen, setIsOpen] = useState(false);
  const acceptTaskMutation = useAcceptTask();
  const { data: systemRate } = useSystemRate();

  // 计算证据费用
  const calculateEvidenceFee = () => {
    if (!task.evidenceUploadType || task.evidenceUploadType === 'NONE') {
      return 0;
    }
    return (task.totalAmount * (systemRate?.noEvidenceExtraRate || 0)) / 100;
  };

  // 获取平台名称
  const getPlatformName = (platform: string | { name: string }) => {
    if (typeof platform === 'object' && platform?.name) {
      // 如果是对象，尝试翻译name字段
      return (
        tPublish(`platformSelection.platformLabels.${platform.name}` as any) ||
        platform.name
      );
    }
    if (typeof platform === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.platformLabels.${platform}` as any) ||
        platform
      );
    }
    return t('basicInfo.unknownPlatform');
  };

  // 获取分类名称
  const getCategoryName = (category: string | { name: string }) => {
    if (typeof category === 'object' && category?.name) {
      // 如果是对象，直接返回name字段，因为API已经返回了正确的显示名称
      return category.name;
    }
    if (typeof category === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.categoryLabels.${category}` as any) ||
        category
      );
    }
    return t('basicInfo.unknownCategory');
  };

  // 获取证据状态信息
  const getEvidenceStatusInfo = () => {
    const evidenceFee = calculateEvidenceFee();
    // 传入的commission已经是基础酬金（不包含证据费用）
    const baseCommission = task.commission;

    if (!task.evidenceUploadType || task.evidenceUploadType === 'NONE') {
      return {
        status: 'NO_EVIDENCE',
        title: t('evidenceInfo.titles.NO_EVIDENCE'),
        description: t('evidenceInfo.descriptions.NO_EVIDENCE'),
        icon: <Shield className='h-4 w-4 text-green-600' />,
        color:
          'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800',
        textColor: 'text-green-800 dark:text-green-300',
        currentCommission: task.commission, // 无证据时使用原始酬金
        finalCommission: task.commission,
        evidenceFee: 0,
        riskLevel: 'low',
      };
    }

    switch (task.evidenceStatus) {
      case 'REVIEWED':
        return {
          status: 'REVIEWED',
          title: t('evidenceInfo.titles.REVIEWED'),
          description: t('evidenceInfo.descriptions.REVIEWED'),
          icon: <CheckCircle className='h-4 w-4 text-green-600' />,
          color:
            'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800',
          textColor: 'text-green-800 dark:text-green-300',
          currentCommission: baseCommission,
          finalCommission: baseCommission,
          evidenceFee,
          riskLevel: 'low',
        };

      case 'UNDER_REVIEW':
        return {
          status: 'UNDER_REVIEW',
          title: t('evidenceInfo.titles.UNDER_REVIEW'),
          description: t('evidenceInfo.descriptions.UNDER_REVIEW'),
          icon: <Clock className='h-4 w-4 text-blue-600' />,
          color:
            'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800',
          textColor: 'text-blue-800 dark:text-blue-300',
          currentCommission: baseCommission,
          finalCommission: baseCommission + evidenceFee,
          evidenceFee,
          riskLevel: 'medium',
        };

      case 'REJECTED':
        return {
          status: 'REJECTED',
          title: t('evidenceInfo.titles.REJECTED'),
          description: t('evidenceInfo.descriptions.REJECTED'),
          icon: <X className='h-4 w-4 text-red-600' />,
          color: 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800',
          textColor: 'text-red-800 dark:text-red-300',
          currentCommission: baseCommission,
          finalCommission: baseCommission + evidenceFee,
          evidenceFee,
          riskLevel: 'low',
        };

      case 'PENDING_SUBMISSION':
      default:
        return {
          status: 'PENDING_SUBMISSION',
          title: t('evidenceInfo.titles.PENDING_SUBMISSION'),
          description: t('evidenceInfo.descriptions.PENDING_SUBMISSION'),
          icon: <AlertTriangle className='h-4 w-4 text-orange-600' />,
          color:
            'bg-orange-50 border-orange-200 dark:bg-orange-950 dark:border-orange-800',
          textColor: 'text-orange-800 dark:text-orange-300',
          currentCommission: baseCommission,
          finalCommission: baseCommission + evidenceFee,
          evidenceFee,
          riskLevel: 'high',
        };
    }
  };

  const handleAccept = async () => {
    try {
      await acceptTaskMutation.mutateAsync(task.id);
      setIsOpen(false);
      onAccept?.();
      toast.success(t('messages.acceptSuccess'), {
        description: t('messages.acceptSuccessDesc'),
      });
    } catch (error) {
      // 错误处理已在 hook 中完成
    }
  };

  const evidenceInfo = getEvidenceStatusInfo();

  if (isTaskPublisher) {
    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>{t('cannotAcceptTitle')}</DialogTitle>
            <DialogDescription>
              {t('cannotAcceptDescription')}
            </DialogDescription>
          </DialogHeader>
          <div className='flex justify-center'>
            <Button variant='outline' onClick={() => setIsOpen(false)}>
              {t('actions.close')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CheckCircle className='h-5 w-5 text-green-600' />
            {t('title')}
          </DialogTitle>
          <DialogDescription>{t('description')}</DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* 基本委托信息 */}
          <div className='bg-muted/50 p-4 rounded-lg'>
            <h3 className='font-semibold mb-3'>{t('basicInfo.title')}</h3>
            <div className='grid grid-cols-2 gap-4 text-sm'>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>
                  {t('basicInfo.platform')}
                </span>
                <span className='font-medium'>
                  {getPlatformName(task.platform)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>
                  {t('basicInfo.quantity')}
                </span>
                <span className='font-medium'>
                  {task.quantity} {t('basicInfo.pieces')}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>
                  {t('basicInfo.unitPrice')}
                </span>
                <span className='font-medium'>
                  ${task.unitPrice.toFixed(2)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-muted-foreground'>
                  {t('basicInfo.deposit')}
                </span>
                <span className='font-medium text-orange-600'>
                  ${task.deposit.toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* 证据费用说明 */}
          <div className={`p-4 rounded-lg border ${evidenceInfo.color}`}>
            <div className='flex items-start gap-3'>
              {evidenceInfo.icon}
              <div className='flex-1'>
                <h4 className={`font-semibold ${evidenceInfo.textColor}`}>
                  {evidenceInfo.title}
                </h4>
                <p className={`text-sm mt-1 ${evidenceInfo.textColor}`}>
                  {evidenceInfo.description}
                </p>

                {evidenceInfo.evidenceFee > 0 && (
                  <div
                    className={`mt-3 space-y-1 text-sm ${evidenceInfo.textColor}`}
                  >
                    <div>
                      • {t('evidenceInfo.evidenceFeeNote')} $
                      {evidenceInfo.evidenceFee.toFixed(2)}
                    </div>
                    {evidenceInfo.status === 'REVIEWED' && (
                      <div>• {t('evidenceInfo.evidenceApproved')}</div>
                    )}
                    {evidenceInfo.status === 'REJECTED' && (
                      <div>• {t('evidenceInfo.evidenceRejected')}</div>
                    )}
                    {(evidenceInfo.status === 'UNDER_REVIEW' ||
                      evidenceInfo.status === 'PENDING_SUBMISSION') && (
                      <div>• {t('evidenceInfo.evidencePending')}</div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 酬金计算说明 */}
          <div className='bg-amber-50 border border-amber-200 dark:bg-amber-950 dark:border-amber-800 p-4 rounded-lg'>
            <div className='flex items-start gap-3'>
              <DollarSign className='h-4 w-4 text-amber-600 mt-0.5' />
              <div className='flex-1'>
                <h4 className='font-semibold text-amber-800 dark:text-amber-300'>
                  {t('rewardCalculation.title')}
                </h4>
                <div className='mt-2 space-y-1 text-sm text-amber-700 dark:text-amber-400'>
                  {evidenceInfo.status === 'REVIEWED' && (
                    <div>
                      {t('rewardCalculation.finalReward')} $
                      {evidenceInfo.finalCommission.toFixed(2)}
                    </div>
                  )}

                  {evidenceInfo.status === 'REJECTED' && (
                    <div>
                      {t('rewardCalculation.finalReward')} $
                      {evidenceInfo.finalCommission.toFixed(2)}{' '}
                      {t('rewardCalculation.withEvidenceFee')}
                    </div>
                  )}

                  {(evidenceInfo.status === 'UNDER_REVIEW' ||
                    evidenceInfo.status === 'PENDING_SUBMISSION') && (
                    <>
                      <div>
                        {t('rewardCalculation.baseReward')} $
                        {evidenceInfo.currentCommission.toFixed(2)}
                      </div>
                      <div>
                        {t('rewardCalculation.ifEvidenceFails')} $
                        {evidenceInfo.evidenceFee.toFixed(2)}
                      </div>
                    </>
                  )}

                  {evidenceInfo.status === 'NO_EVIDENCE' && (
                    <div>
                      {t('rewardCalculation.finalReward')} $
                      {evidenceInfo.finalCommission.toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 风险提示 */}
          {evidenceInfo.riskLevel === 'high' && (
            <Alert className='border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{t('riskWarning')}</AlertDescription>
            </Alert>
          )}

          <Separator />
        </div>

        <div className='flex gap-3 pt-4'>
          <Button
            variant='outline'
            onClick={() => setIsOpen(false)}
            className='flex-1'
          >
            {t('actions.cancel')}
          </Button>
          <Button
            onClick={handleAccept}
            disabled={acceptTaskMutation.isPending}
            className='flex-1 bg-green-600 hover:bg-green-700'
          >
            {acceptTaskMutation.isPending
              ? t('actions.processing')
              : t('actions.confirm')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
