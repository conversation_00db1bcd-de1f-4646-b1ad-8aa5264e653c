import { hasLocale } from 'next-intl';
import { getRequestConfig } from 'next-intl/server';

import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  // 通常对应 [locale] 段
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  // 动态导入分布式翻译文件
  const [
    common,
    navigation,
    homepage,
    dashboard,
    tasks,
    auth,
    wallet,
    transactions,
    admin,
    email,
    tickets,
    publish,
    review,
    membership,
    accountSecurity,
    myAcceptedTasks,
    myPublishedTasks,
    shopWhitelist,
    whitelist,
    legal,
    logistics,
    logisticsInfo,
    messages,
  ] = await Promise.all([
    import(`../../messages/${locale}/common.json`),
    import(`../../messages/${locale}/navigation.json`),
    import(`../../messages/${locale}/homepage.json`),
    import(`../../messages/${locale}/dashboard.json`),
    import(`../../messages/${locale}/tasks.json`),
    import(`../../messages/${locale}/auth.json`),
    import(`../../messages/${locale}/wallet.json`),
    import(`../../messages/${locale}/transactions.json`),
    import(`../../messages/${locale}/admin.json`),
    import(`../../messages/${locale}/email.json`),
    import(`../../messages/${locale}/tickets.json`),
    import(`../../messages/${locale}/publish.json`),
    import(`../../messages/${locale}/review.json`),
    import(`../../messages/${locale}/membership.json`),
    import(`../../messages/${locale}/account-security.json`),
    import(`../../messages/${locale}/my-accepted-tasks.json`),
    import(`../../messages/${locale}/my-published-tasks.json`),
    import(`../../messages/${locale}/shop-whitelist.json`),
    import(`../../messages/${locale}/whitelist.json`),
    import(`../../messages/${locale}/legal.json`),
    import(`../../messages/${locale}/logistics.json`),
    import(`../../messages/${locale}/logistics-info.json`),
    import(`../../messages/${locale}/messages.json`),
  ]);

  return {
    locale,
    messages: {
      'common': common.default,
      'navigation': navigation.default,
      'homepage': homepage.default,
      'dashboard': dashboard.default,
      'tasks': tasks.default,
      'auth': auth.default,
      'wallet': wallet.default,
      'transactions': transactions.default,
      'admin': admin.default,
      'email': email.default,
      'tickets': tickets.default,
      'publish': publish.default,
      'review': review.default,
      'membership': membership.default,
      'account-security': accountSecurity.default,
      'my-accepted-tasks': myAcceptedTasks.default,
      'my-published-tasks': myPublishedTasks.default,
      'shop-whitelist': shopWhitelist.default,
      'whitelist': whitelist.default,
      'legal': legal.default,
      'logistics': logistics.default,
      'logistics-info': logisticsInfo.default,
      'messages': messages.default,
      'footer': homepage.default.footer,
    },
    // 设置时区
    timeZone: 'Asia/Shanghai',
    // 设置当前时间（用于一致的日期格式化）
    now: new Date(),
  };
});
