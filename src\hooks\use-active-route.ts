'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

/**
 * Hook for detecting active routes and navigation states
 */
export function useActiveRoute() {
  const pathname = usePathname();

  // Remove locale prefix for consistent matching
  const normalizedPathname = useMemo(() => {
    // Remove locale prefix (e.g., /en, /zh)
    return pathname.replace(/^\/[a-z]{2}(?=\/|$)/, '') || '/';
  }, [pathname]);

  /**
   * Check if a route is currently active
   */
  const isActive = (href: string): boolean => {
    if (!href || href === '#') return false;

    // Exact match for root
    if (href === '/' && normalizedPathname === '/') {
      return true;
    }

    // For other routes, check if current path starts with the href
    if (href !== '/') {
      return normalizedPathname.startsWith(href);
    }

    return false;
  };

  /**
   * Check if a route is exactly active (no partial matching)
   */
  const isExactActive = (href: string): boolean => {
    if (!href || href === '#') return false;
    return normalizedPathname === href;
  };

  /**
   * Check if any of the provided routes is active
   */
  const isAnyActive = (hrefs: string[]): boolean => {
    return hrefs.some(href => isActive(href));
  };

  /**
   * Get active class name based on route state
   */
  const getActiveClass = (
    href: string,
    activeClass: string = 'active',
    inactiveClass: string = '',
  ): string => {
    return isActive(href) ? activeClass : inactiveClass;
  };

  /**
   * Get navigation item state
   */
  const getNavItemState = (href: string) => {
    const active = isActive(href);
    const exactActive = isExactActive(href);

    return {
      isActive: active,
      isExactActive: exactActive,
      isPartialActive: active && !exactActive,
      className: active ? 'active' : '',
    };
  };

  /**
   * Generate breadcrumb items based on current path
   */
  const getBreadcrumbs = () => {
    const segments = normalizedPathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    // Add home
    breadcrumbs.push({
      label: '首页',
      href: '/',
      isActive: normalizedPathname === '/',
    });

    // Add path segments
    let currentPath = '';
    for (let i = 0; i < segments.length; i++) {
      currentPath += `/${segments[i]}`;
      const isLast = i === segments.length - 1;

      breadcrumbs.push({
        label: getBreadcrumbLabel(segments[i]),
        href: currentPath,
        isActive: isLast,
        isLast,
      });
    }

    return breadcrumbs;
  };

  /**
   * Get user-friendly label for breadcrumb
   */
  const getBreadcrumbLabel = (segment: string): string => {
    const labelMap: Record<string, string> = {
      dashboard: '仪表盘',
      tasks: '委托中心',
      publish: '发布委托',
      'my-accepted-tasks': '我接受的委托',
      'my-published-tasks': '我发布的委托',
      profile: '账号安全',
      wallet: '我的钱包',
      membership: '会员套餐',
      whitelist: '商铺白名单',
      tickets: '工单管理',
      admin: '管理后台',
      users: '用户管理',
      settings: '系统设置',
    };

    return labelMap[segment] || segment;
  };

  /**
   * Check if current route requires authentication
   */
  const requiresAuth = (): boolean => {
    const protectedPaths = [
      '/dashboard',
      '/tasks',
      '/publish',
      '/my-accepted-tasks',
      '/my-published-tasks',
      '/profile',
      '/wallet',
      '/membership',
      '/whitelist',
      '/tickets',
      '/admin',
    ];

    return protectedPaths.some(path => normalizedPathname.startsWith(path));
  };

  /**
   * Check if current route is in admin area
   */
  const isAdminArea = (): boolean => {
    return normalizedPathname.startsWith('/admin');
  };

  /**
   * Check if current route is in user area
   */
  const isUserArea = (): boolean => {
    const userPaths = [
      '/dashboard',
      '/tasks',
      '/publish',
      '/my-accepted-tasks',
      '/my-published-tasks',
      '/profile',
      '/wallet',
      '/membership',
      '/whitelist',
      '/tickets',
    ];

    return userPaths.some(path => normalizedPathname.startsWith(path));
  };

  /**
   * Get navigation context
   */
  const getNavigationContext = () => {
    return {
      currentPath: normalizedPathname,
      originalPath: pathname,
      isHome: normalizedPathname === '/',
      isUserArea: isUserArea(),
      isAdminArea: isAdminArea(),
      requiresAuth: requiresAuth(),
      breadcrumbs: getBreadcrumbs(),
    };
  };

  return {
    pathname: normalizedPathname,
    originalPathname: pathname,
    isActive,
    isExactActive,
    isAnyActive,
    getActiveClass,
    getNavItemState,
    getBreadcrumbs,
    getBreadcrumbLabel,
    requiresAuth,
    isAdminArea,
    isUserArea,
    getNavigationContext,
  };
}

/**
 * Hook for sidebar navigation state
 */
export function useSidebarNavigation() {
  const { isActive, getNavItemState, isUserArea, isAdminArea } =
    useActiveRoute();

  /**
   * Get navigation items with active states
   */
  const getNavItems = (items: any[]) => {
    return items.map(item => ({
      ...item,
      ...getNavItemState(item.url),
      items: item.items?.map((subItem: any) => ({
        ...subItem,
        ...getNavItemState(subItem.url),
      })),
    }));
  };

  /**
   * Check if a navigation group should be expanded
   */
  const shouldExpand = (item: any): boolean => {
    if (!item.items) return false;

    // Expand if any sub-item is active
    return item.items.some((subItem: any) => isActive(subItem.url));
  };

  return {
    isActive,
    getNavItemState,
    getNavItems,
    shouldExpand,
    isUserArea,
    isAdminArea,
  };
}

/**
 * Hook for breadcrumb navigation
 */
export function useBreadcrumbNavigation() {
  const { getBreadcrumbs, getBreadcrumbLabel } = useActiveRoute();

  const breadcrumbs = getBreadcrumbs();

  return {
    breadcrumbs,
    getBreadcrumbLabel,
    hasMultipleLevels: breadcrumbs.length > 1,
    currentLevel: breadcrumbs[breadcrumbs.length - 1],
    parentLevels: breadcrumbs.slice(0, -1),
  };
}

export default useActiveRoute;
