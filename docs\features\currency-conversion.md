# 货币转换功能文档

## 概述

RefundGo
Web项目现在支持自动货币转换功能，主要用于聚合易支付(YunPay)集成。该功能可以自动将USD金额转换为CNY金额，确保发送给聚合易支付的金额是人民币。

## 功能特性

### 🔄 自动货币转换

- **USD到CNY转换**: 自动将美元金额转换为人民币
- **实时汇率**: 使用可靠的免费汇率API获取最新汇率
- **精确计算**: 使用currency.js库避免浮点数精度问题
- **智能缓存**: 汇率缓存1小时，提高性能

### 🛡️ 可靠性保障

- **多API支持**: 主API + Cloudflare镜像 + 降级汇率
- **错误处理**: 完善的错误处理和降级机制
- **日志记录**: 详细的转换日志和审计跟踪

### 📊 数据追踪

- **完整记录**: 保存原始金额、转换金额、汇率等信息
- **审计友好**: 支持财务审计和对账
- **管理界面**: 提供管理员界面查看转换状态

## 技术实现

### 核心组件

#### 1. CurrencyConverter 服务

```typescript
// 位置: src/lib/payment/currency-converter.ts
export class CurrencyConverter {
  async convertUSDToCNY(usdAmount: number): Promise<ConversionResult>;
  async getExchangeRate(from: string, to: string): Promise<ExchangeRate>;
  clearCache(): void;
  getCacheInfo(): { key: string; rate: ExchangeRate }[];
}
```

#### 2. YunPayProvider 集成

```typescript
// 位置: src/lib/payment/providers/yunpay.ts
// 自动检测USD输入并转换为CNY
if (params.currency.toUpperCase() === 'USD') {
  const conversion = await currencyConverter.convertUSDToCNY(params.amount);
  finalAmount = conversion.convertedAmount;
}
```

#### 3. 数据库模型

```sql
-- PaymentOrder表新增字段
originalAmount    DECIMAL(10,2)  -- 原始金额（USD）
originalCurrency  VARCHAR(3)     -- 原始货币
convertedAmount   DECIMAL(10,2)  -- 转换后金额（CNY）
convertedCurrency VARCHAR(3)     -- 转换后货币
exchangeRate      DECIMAL(10,6)  -- 汇率
exchangeRateSource VARCHAR(50)   -- 汇率来源
```

### 汇率API提供商

#### 主要API: fawazahmed0/exchange-api

- **URL**: `https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/usd.json`
- **特点**: 免费、无限制、200+货币支持
- **缓存**: Next.js自动缓存1小时

#### 备用API: Cloudflare镜像

- **URL**: `https://latest.currency-api.pages.dev/v1/currencies/usd.json`
- **用途**: 主API失败时的备用方案

#### 降级汇率

- **USD-CNY**: 7.2 (硬编码降级汇率)
- **用途**: 所有API都失败时的最后保障

## 使用方法

### 1. 支付创建

```typescript
// 创建USD支付，自动转换为CNY
const paymentResult = await yunpayProvider.createPayment({
  orderNo: 'ORDER123',
  amount: 100, // $100 USD
  currency: 'USD', // 输入货币
  paymentMethod: 'alipay',
  // ... 其他参数
});

// 结果包含转换信息
if (paymentResult.currencyConversion) {
  console.log(
    `${paymentResult.currencyConversion.originalAmount} ${paymentResult.currencyConversion.originalCurrency} -> ${paymentResult.currencyConversion.convertedAmount} ${paymentResult.currencyConversion.convertedCurrency}`
  );
}
```

### 2. 手动转换

```typescript
import { currencyConverter } from '@/lib/payment/currency-converter';

const result = await currencyConverter.convertUSDToCNY(100);
console.log(`$${result.originalAmount} = ¥${result.convertedAmount}`);
```

### 3. 管理员功能

- **缓存管理**: `/admin/currency` - 查看和清除汇率缓存
- **测试转换**: 手动测试货币转换功能
- **API端点**:
  - `GET /api/admin/currency/cache` - 获取缓存状态
  - `DELETE /api/admin/currency/cache` - 清除缓存
  - `POST /api/admin/currency/convert` - 测试转换

## 配置说明

### 环境变量

无需额外环境变量，使用免费API。

### 缓存配置

```typescript
// 默认缓存时间: 1小时
private defaultCacheTime = 60 * 60 * 1000;

// 降级汇率配置
private fallbackRates = new Map([
  ['USD-CNY', 7.2],
]);
```

## 测试

### 单元测试

```bash
npm test src/lib/payment/__tests__/currency-converter.test.ts
```

### 集成测试

```bash
npx tsx scripts/test-currency-conversion.ts
npx tsx scripts/test-yunpay-integration.ts
```

## 监控和维护

### 日志监控

- 转换成功/失败日志
- API响应时间监控
- 缓存命中率统计

### 定期检查

- 汇率API可用性
- 降级汇率准确性
- 缓存性能优化

## 注意事项

### 1. 支付提供商兼容性

- **YunPay**: 自动USD→CNY转换
- **NOWPayments**: 保持USD，无转换

### 2. 精度处理

- 使用currency.js避免浮点数问题
- 金额保留2位小数
- 汇率保留6位小数

### 3. 错误处理

- API失败时使用降级汇率
- 记录详细错误日志
- 不影响支付流程

### 4. 性能优化

- 汇率缓存1小时
- 多API并发请求
- 智能降级机制

## 故障排除

### 常见问题

#### 1. 汇率获取失败

```
错误: Unable to get exchange rate for USD to CNY from any provider
解决: 检查网络连接，或使用降级汇率
```

#### 2. 转换金额异常

```
检查: 输入金额是否为正数
检查: 货币代码是否正确（3位字符）
```

#### 3. 缓存问题

```
解决: 访问 /admin/currency 清除缓存
或调用: currencyConverter.clearCache()
```

## 更新日志

### v1.0.0 (2025-01-28)

- ✅ 实现USD到CNY自动转换
- ✅ 集成免费汇率API
- ✅ 添加缓存机制
- ✅ 完善错误处理
- ✅ 数据库字段扩展
- ✅ 管理员界面
- ✅ 测试套件完整
