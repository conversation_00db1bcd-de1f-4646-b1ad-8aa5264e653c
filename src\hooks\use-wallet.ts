import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import {
  UserWallet,
  WalletTransaction,
  TransactionType,
} from '@/lib/types/membership';
import { ApiResponse } from '@/types/rates';

// 获取用户钱包信息
export function useUserWallet() {
  return useQuery({
    queryKey: ['user-wallet'],
    queryFn: async (): Promise<UserWallet> => {
      const response = await fetch('/api/user/wallet');
      const data: ApiResponse<UserWallet> = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get wallet information');
      }

      return data.data!;
    },
  });
}

// 获取用户钱包交易记录
export function useWalletTransactions(filters?: {
  type?: TransactionType;
  page?: number;
  limit?: number;
}) {
  const queryKey = ['wallet-transactions', filters];

  return useQuery({
    queryKey,
    queryFn: async (): Promise<{
      transactions: WalletTransaction[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }> => {
      const params = new URLSearchParams();

      if (filters?.type) params.append('type', filters.type);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/user/wallet/transactions?${params}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get transaction records');
      }

      return {
        transactions: data.data || [],
        pagination: data.pagination,
      };
    },
  });
}

// 充值请求
export function useDeposit() {
  const queryClient = useQueryClient();
  const t = useTranslations('Wallet');

  return useMutation({
    mutationFn: async (data: {
      amount: number;
      method: string;
    }): Promise<any> => {
      const response = await fetch('/api/user/wallet/deposit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || t('hooks.deposit.error'));
      }

      return result.data;
    },
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['user-wallet'] });
      queryClient.invalidateQueries({ queryKey: ['wallet-transactions'] });

      // 如果有支付链接，跳转到支付页面
      if (data.paymentUrl) {
        const feeInfo =
          data.feeAmount > 0
            ? t('hooks.deposit.feeInfo', { amount: data.feeAmount })
            : '';
        toast.success(t('hooks.deposit.redirecting'), {
          description: t('hooks.deposit.redirectingDesc', {
            depositAmount: data.depositAmount,
            feeInfo,
            totalAmount: data.totalPaymentAmount,
          }),
        });

        // 跳转到支付页面
        window.open(data.paymentUrl, '_blank');
      } else {
        toast.success(t('hooks.deposit.submitted'), {
          description: t('hooks.deposit.submittedDesc'),
        });
      }
    },
    onError: (error: Error) => {
      toast.error(t('hooks.deposit.failed'), {
        description: error.message,
      });
    },
  });
}

// 提现请求
export function useWithdraw() {
  const queryClient = useQueryClient();
  const t = useTranslations('Wallet');

  return useMutation({
    mutationFn: async (data: {
      amount: number;
      method: string;
    }): Promise<any> => {
      const response = await fetch('/api/user/wallet/withdraw', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || t('hooks.withdraw.error'));
      }

      return result.data;
    },
    onSuccess: data => {
      queryClient.invalidateQueries({ queryKey: ['user-wallet'] });
      queryClient.invalidateQueries({ queryKey: ['wallet-transactions'] });
      toast.success(t('hooks.withdraw.submitted'), {
        description: t('hooks.withdraw.submittedDesc'),
      });
    },
    onError: (error: Error) => {
      toast.error(t('hooks.withdraw.failed'), {
        description: error.message,
      });
    },
  });
}
