'use client';

import {
  Search,
  MoreHorizontal,
  Eye,
  Filter,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  MessageSquare,
  Loader2,
} from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  usePendingTasks,
  useTaskStats,
  useReviewTask,
} from '@/hooks/use-admin-tasks';
import { useActivePlatforms } from '@/hooks/use-publish-config';
import {
  TASK_STATUS_LABELS,
  TaskStatus,
  EVIDENCE_STATUS_LABELS,
  EvidenceStatus,
} from '@/lib/types/task';

interface TaskReviewFilters {
  search: string;
  platform: string;
  evidenceStatus: string;
  timeRange: string;
}

export default function TaskReviewPage() {
  const [filters, setFilters] = useState<TaskReviewFilters>({
    search: '',
    platform: 'all',
    evidenceStatus: 'all',
    timeRange: 'all',
  });
  const [page, setPage] = useState(1);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(
    null,
  );

  const [detailsSheetOpen, setDetailsSheetOpen] = useState(false);
  const [evidenceSheetOpen, setEvidenceSheetOpen] = useState(false);
  const [viewingTask, setViewingTask] = useState<any>(null);

  // 获取数据
  const {
    data: tasksData,
    isLoading: tasksLoading,
    error: tasksError,
  } = usePendingTasks({
    ...filters,
    platform: filters.platform === 'all' ? undefined : filters.platform,
    evidenceStatus:
      filters.evidenceStatus === 'all' ? undefined : filters.evidenceStatus,
    timeRange: filters.timeRange === 'all' ? undefined : filters.timeRange,
    page,
    limit: 20,
  });

  const { data: statsData } = useTaskStats();
  const { data: platforms = [] } = useActivePlatforms();
  const reviewTaskMutation = useReviewTask();

  const tasks = tasksData?.data?.tasks || [];
  const pagination = tasksData?.data?.pagination;
  const stats = statsData?.data || {
    totalReviewTasks: 0,
    uploadedEvidence: 0,
    pendingUpload: 0,
    noEvidence: 0,
    pendingReview: 0,
    totalValue: 0,
  };

  // 处理筛选变化
  const handleFilterChange = (key: keyof TaskReviewFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // 重置页码
  };

  // 处理搜索
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPage(1);
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({
      search: '',
      platform: 'all',
      evidenceStatus: 'all',
      timeRange: 'all',
    });
    setPage(1);
  };

  // 打开审核对话框
  const openReviewDialog = (task: any, action: 'approve' | 'reject') => {
    setSelectedTask(task);
    setReviewAction(action);
    setReviewDialogOpen(true);
  };

  // 执行审核
  const handleReview = async () => {
    if (!selectedTask || !reviewAction) return;

    await reviewTaskMutation.mutateAsync({
      taskId: selectedTask.id,
      data: {
        action: reviewAction,
      },
    });

    setReviewDialogOpen(false);
    setSelectedTask(null);
    setReviewAction(null);
  };

  // 打开委托详情
  const openTaskDetails = (task: any) => {
    setViewingTask(task);
    setDetailsSheetOpen(true);
  };

  // 打开证据查看
  const openTaskEvidence = (task: any) => {
    setViewingTask(task);
    setEvidenceSheetOpen(true);
  };

  // 获取证据状态显示
  const getEvidenceDisplay = (evidenceStatus: string) => {
    switch (evidenceStatus) {
      case '已上传':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className='h-3 w-3' />,
          variant: 'default' as const,
        };
      case '待上传':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'outline' as const,
        };
      case '无证据':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <FileText className='h-3 w-3' />,
          variant: 'outline' as const,
        };
      default:
        return {
          color: 'bg-blue-100 text-blue-800',
          icon: <AlertTriangle className='h-3 w-3' />,
          variant: 'secondary' as const,
        };
    }
  };

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='委托审核' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题和操作 */}
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
          <div className='space-y-1'>
            <h1 className='text-2xl font-bold tracking-tight'>委托审核</h1>
            <p className='text-sm text-muted-foreground'>
              审核待处理的委托，查看拒付证据并做出审核决定
            </p>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>待审核委托</CardTitle>
              <AlertTriangle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.totalReviewTasks}</div>
              <p className='text-xs text-muted-foreground'>
                需要处理的委托数量
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>已上传证据</CardTitle>
              <FileText className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.uploadedEvidence}</div>
              <p className='text-xs text-muted-foreground'>
                待上传 {stats.pendingUpload} 个委托
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>涉及金额</CardTitle>
              <div className='h-4 w-4 text-muted-foreground'>$</div>
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                ${stats.totalValue.toLocaleString()}
              </div>
              <p className='text-xs text-muted-foreground'>待审核委托总价值</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>证据状态</CardTitle>
              <AlertTriangle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.pendingReview}</div>
              <p className='text-xs text-muted-foreground'>
                待审核 / 无证据 {stats.noEvidence}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Filter className='h-5 w-5' />
              搜索和筛选
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex flex-col lg:flex-row gap-4'>
              <div className='relative flex-1'>
                <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='搜索委托ID或发布者...'
                  className='pl-10'
                  value={filters.search}
                  onChange={e => handleSearchChange(e.target.value)}
                />
              </div>
              <div className='flex flex-col sm:flex-row gap-2'>
                <Select
                  value={filters.platform}
                  onValueChange={value => handleFilterChange('platform', value)}
                >
                  <SelectTrigger className='w-full sm:w-[120px]'>
                    <SelectValue placeholder='平台' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部平台</SelectItem>
                    {platforms.map(platform => (
                      <SelectItem key={platform.id} value={platform.id}>
                        {platform.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={filters.evidenceStatus}
                  onValueChange={value =>
                    handleFilterChange('evidenceStatus', value)
                  }
                >
                  <SelectTrigger className='w-full sm:w-[140px]'>
                    <SelectValue placeholder='证据状态' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部状态</SelectItem>
                    <SelectItem value='已上传'>已上传</SelectItem>
                    <SelectItem value='待上传'>待上传</SelectItem>
                    <SelectItem value='无证据'>无证据</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={filters.timeRange}
                  onValueChange={value =>
                    handleFilterChange('timeRange', value)
                  }
                >
                  <SelectTrigger className='w-full sm:w-[120px]'>
                    <SelectValue placeholder='提交时间' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部时间</SelectItem>
                    <SelectItem value='today'>今天</SelectItem>
                    <SelectItem value='week'>本周</SelectItem>
                    <SelectItem value='month'>本月</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 待审核委托列表 */}
        <Card>
          <CardHeader>
            <CardTitle>待审核委托列表</CardTitle>
          </CardHeader>
          <CardContent>
            {tasksLoading ? (
              <div className='flex items-center justify-center py-8'>
                <Loader2 className='h-6 w-6 animate-spin mr-2' />
                <span>加载中...</span>
              </div>
            ) : tasksError ? (
              <div className='flex items-center justify-center py-8'>
                <span className='text-destructive'>加载失败</span>
              </div>
            ) : tasks.length === 0 ? (
              <div className='flex items-center justify-center py-8'>
                <span className='text-muted-foreground'>暂无待审核委托</span>
              </div>
            ) : (
              <div className='overflow-x-auto'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>委托ID</TableHead>
                      <TableHead>平台</TableHead>
                      <TableHead>拒付类型</TableHead>
                      <TableHead>数量</TableHead>
                      <TableHead>单价</TableHead>
                      <TableHead>发布者</TableHead>
                      <TableHead>证据状态</TableHead>
                      <TableHead>提交时间</TableHead>
                      <TableHead className='text-right'>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tasks.map((task: any) => (
                      <TableRow key={task.id} className='hover:bg-muted/50'>
                        <TableCell>
                          <span className='font-mono text-sm font-medium text-blue-600'>
                            {task.id}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant='outline' className='text-xs'>
                            {task.platform?.name || '未知平台'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant='ghost'
                                className='h-auto p-0 hover:bg-transparent'
                              >
                                <Badge
                                  variant='outline'
                                  className='text-xs bg-orange-100 text-orange-800 cursor-pointer hover:bg-orange-200'
                                >
                                  {task.chargebackTypeNames?.[0] || '未知'}
                                  {task.chargebackTypeNames &&
                                    task.chargebackTypeNames.length > 1 && (
                                      <span className='ml-1 text-orange-600'>
                                        +{task.chargebackTypeNames.length - 1}
                                      </span>
                                    )}
                                </Badge>
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className='w-80'>
                              <div className='space-y-2'>
                                <h4 className='font-medium text-sm'>
                                  拒付类型
                                </h4>
                                <div className='space-y-2'>
                                  {task.chargebackTypeNames?.map(
                                    (type: string, index: number) => (
                                      <div
                                        key={index}
                                        className='flex items-center gap-2'
                                      >
                                        <Badge
                                          variant='outline'
                                          className='text-xs bg-orange-100 text-orange-800'
                                        >
                                          {type}
                                        </Badge>
                                      </div>
                                    ),
                                  )}
                                </div>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </TableCell>
                        <TableCell>
                          <span className='text-sm font-medium'>
                            {task.quantity}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className='text-sm font-medium text-green-600'>
                            ${(task.unitPrice || 0).toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='text-sm font-medium'>
                              {task.publisher?.name || '未知用户'}
                            </p>
                            <p className='text-xs text-muted-foreground'>
                              {task.publisher?.email}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          {(() => {
                            const evidenceDisplay = getEvidenceDisplay(
                              task.evidenceStatus,
                            );
                            return (
                              <Badge
                                variant={evidenceDisplay.variant}
                                className={`text-xs ${evidenceDisplay.color}`}
                              >
                                <span className='mr-1'>
                                  {evidenceDisplay.icon}
                                </span>
                                {EVIDENCE_STATUS_LABELS[
                                  task.evidenceStatus as EvidenceStatus
                                ] || task.evidenceStatus}
                              </Badge>
                            );
                          })()}
                        </TableCell>
                        <TableCell>
                          <span className='text-sm'>
                            {new Date(task.createdAt).toLocaleString('zh-CN')}
                          </span>
                        </TableCell>
                        <TableCell className='text-right'>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <span className='sr-only'>打开菜单</span>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuLabel>审核操作</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => openTaskDetails(task)}
                              >
                                <Eye className='mr-2 h-4 w-4' />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => openTaskEvidence(task)}
                              >
                                <FileText className='mr-2 h-4 w-4' />
                                查看证据
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className='text-green-600'
                                onClick={() =>
                                  openReviewDialog(task, 'approve')
                                }
                              >
                                <CheckCircle className='mr-2 h-4 w-4' />
                                审核通过
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className='text-red-600'
                                onClick={() => openReviewDialog(task, 'reject')}
                              >
                                <XCircle className='mr-2 h-4 w-4' />
                                审核拒绝
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 分页 */}
        {pagination && pagination.totalPages > 1 && (
          <Card>
            <CardContent className='py-4'>
              <div className='flex items-center justify-between'>
                <p className='text-sm text-muted-foreground'>
                  显示第 {(pagination.page - 1) * pagination.limit + 1}-
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total,
                  )}{' '}
                  条，共 {pagination.total} 条记录
                </p>
                <div className='flex items-center gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    disabled={pagination.page <= 1}
                    onClick={() => setPage(page - 1)}
                  >
                    <ChevronLeft className='h-4 w-4' />
                    上一页
                  </Button>
                  <div className='flex items-center gap-1'>
                    {Array.from(
                      { length: Math.min(5, pagination.totalPages) },
                      (_, i) => {
                        const pageNum = i + 1;
                        return (
                          <Button
                            key={pageNum}
                            variant={
                              pageNum === pagination.page
                                ? 'default'
                                : 'outline'
                            }
                            size='sm'
                            className='w-8 h-8 p-0'
                            onClick={() => setPage(pageNum)}
                          >
                            {pageNum}
                          </Button>
                        );
                      },
                    )}
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    disabled={pagination.page >= pagination.totalPages}
                    onClick={() => setPage(page + 1)}
                  >
                    下一页
                    <ChevronRight className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 审核确认对话框 */}
        <AlertDialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {reviewAction === 'approve' ? '确认审核通过' : '确认审核拒绝'}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {reviewAction === 'approve'
                  ? '审核通过后，委托将进入招募状态，用户可以接取这个委托。'
                  : '审核拒绝后，委托费用将退还给发布者，委托状态将变为已拒绝。'}
              </AlertDialogDescription>
            </AlertDialogHeader>

            {selectedTask && (
              <div className='border rounded-lg p-3 bg-muted/50'>
                <div className='text-sm space-y-2'>
                  <p>
                    <strong>委托ID:</strong> {selectedTask.id}
                  </p>
                  <p>
                    <strong>发布者:</strong> {selectedTask.publisher?.name}
                  </p>
                  <p>
                    <strong>平台:</strong> {selectedTask.platform?.name}
                  </p>
                  <p>
                    <strong>金额:</strong> $
                    {(selectedTask.finalTotal || 0).toFixed(2)}
                  </p>
                </div>
              </div>
            )}

            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleReview}
                disabled={reviewTaskMutation.isPending}
                className={
                  reviewAction === 'approve'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-red-600 hover:bg-red-700'
                }
              >
                {reviewTaskMutation.isPending ? (
                  <>
                    <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                    处理中...
                  </>
                ) : reviewAction === 'approve' ? (
                  '确认通过'
                ) : (
                  '确认拒绝'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* 委托详情Sheet */}
        <Sheet open={detailsSheetOpen} onOpenChange={setDetailsSheetOpen}>
          <SheetContent className='w-[600px] sm:w-[800px] overflow-y-auto'>
            <SheetHeader>
              <SheetTitle>委托详情</SheetTitle>
              <SheetDescription>查看委托的完整信息和配置</SheetDescription>
            </SheetHeader>

            {viewingTask && (
              <div className='mt-6 space-y-6'>
                {/* 基本信息 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>基本信息</h3>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        委托ID
                      </label>
                      <p className='font-mono text-sm bg-muted p-2 rounded'>
                        {viewingTask.id}
                      </p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        委托状态
                      </label>
                      <Badge variant='outline' className='w-fit'>
                        {TASK_STATUS_LABELS[viewingTask.status as TaskStatus] ||
                          viewingTask.status}
                      </Badge>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        平台
                      </label>
                      <p>{viewingTask.platform?.name || '未知平台'}</p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        分类
                      </label>
                      <p>{viewingTask.category?.name || '未知分类'}</p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        发布时间
                      </label>
                      <p>
                        {new Date(viewingTask.createdAt).toLocaleString(
                          'zh-CN',
                        )}
                      </p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        证据状态
                      </label>
                      <Badge variant='outline' className='w-fit'>
                        {EVIDENCE_STATUS_LABELS[
                          viewingTask.evidenceStatus as EvidenceStatus
                        ] || viewingTask.evidenceStatus}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 发布者信息 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>发布者信息</h3>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        用户名
                      </label>
                      <p>{viewingTask.publisher?.name || '未知用户'}</p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        邮箱
                      </label>
                      <p>{viewingTask.publisher?.email || '未知邮箱'}</p>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 商品信息 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>商品信息</h3>
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        商品链接
                      </label>
                      <p className='text-sm break-all bg-muted p-2 rounded'>
                        {viewingTask.productUrl}
                      </p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        商品描述
                      </label>
                      <p className='text-sm bg-muted p-2 rounded whitespace-pre-wrap'>
                        {viewingTask.productDescription}
                      </p>
                    </div>
                    <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                      <div className='space-y-2'>
                        <label className='text-sm font-medium text-muted-foreground'>
                          购买数量
                        </label>
                        <p className='text-lg font-medium'>
                          {viewingTask.quantity} 个
                        </p>
                      </div>
                      <div className='space-y-2'>
                        <label className='text-sm font-medium text-muted-foreground'>
                          商品单价
                        </label>
                        <p className='text-lg font-medium text-green-600'>
                          ${(viewingTask.unitPrice || 0).toFixed(2)}
                        </p>
                      </div>
                      <div className='space-y-2'>
                        <label className='text-sm font-medium text-muted-foreground'>
                          商品总价
                        </label>
                        <p className='text-lg font-medium'>
                          $
                          {(
                            (viewingTask.quantity || 0) *
                            (viewingTask.unitPrice || 0)
                          ).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 委托配置 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>委托配置</h3>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        上架时长
                      </label>
                      <p>
                        {viewingTask.listingTime} 小时 (
                        {Math.ceil(parseInt(viewingTask.listingTime, 10) / 24)}{' '}
                        天)
                      </p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        证据上传方式
                      </label>
                      <p>
                        {viewingTask.evidenceUploadType === 'IMMEDIATE'
                          ? '立即上传'
                          : viewingTask.evidenceUploadType === 'DELAYED'
                            ? '延迟上传'
                            : viewingTask.evidenceUploadType === 'NONE'
                              ? '无上传'
                              : '未知'}
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 收货信息 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>收货信息</h3>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        收货人姓名
                      </label>
                      <p>{viewingTask.recipientName}</p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        联系电话
                      </label>
                      <p>{viewingTask.recipientPhone}</p>
                    </div>
                  </div>
                  <div className='space-y-2'>
                    <label className='text-sm font-medium text-muted-foreground'>
                      收货地址
                    </label>
                    <p className='bg-muted p-2 rounded'>
                      {viewingTask.shippingAddress}
                    </p>
                  </div>
                </div>

                <Separator />

                {/* 拒付类型 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>拒付类型</h3>
                  <div className='flex flex-wrap gap-2'>
                    {viewingTask.chargebackTypeNames?.map(
                      (type: string, index: number) => (
                        <Badge
                          key={index}
                          variant='outline'
                          className='bg-orange-100 text-orange-800'
                        >
                          {type}
                        </Badge>
                      ),
                    )}
                  </div>
                </div>

                <Separator />

                {/* 支付方式 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>支付方式</h3>
                  <div className='flex flex-wrap gap-2'>
                    {viewingTask.paymentMethodNames?.map(
                      (method: string, index: number) => (
                        <Badge
                          key={index}
                          variant='outline'
                          className='bg-blue-100 text-blue-800'
                        >
                          {method}
                        </Badge>
                      ),
                    )}
                  </div>
                </div>

                <Separator />

                {/* 费用详情 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>费用详情</h3>
                  <div className='bg-muted/50 p-4 rounded-lg text-center'>
                    <div className='space-y-1'>
                      <p className='text-sm text-muted-foreground'>用户实付</p>
                      <p className='text-2xl font-bold text-primary'>
                        ${(viewingTask.finalTotal || 0).toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 购物车截图 */}
                {viewingTask.cartScreenshots &&
                  viewingTask.cartScreenshots.length > 0 && (
                    <>
                      <Separator />
                      <div className='space-y-4'>
                        <h3 className='text-lg font-semibold'>购物车截图</h3>
                        <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'>
                          {viewingTask.cartScreenshots.map(
                            (screenshot: string, index: number) => (
                              <Dialog key={index}>
                                <DialogTrigger asChild>
                                  <div className='cursor-pointer group'>
                                    <div className='space-y-2'>
                                      <div className='border rounded-lg overflow-hidden group-hover:shadow-md transition-shadow'>
                                        <Image
                                          src={screenshot}
                                          alt={`购物车截图 ${index + 1}`}
                                          width={300}
                                          height={128}
                                          className='w-full h-32 object-cover group-hover:scale-105 transition-transform'
                                          onError={e => {
                                            (e.target as HTMLImageElement).src =
                                              '/placeholder-image.png';
                                          }}
                                        />
                                      </div>
                                      <div className='text-center'>
                                        <span className='text-xs text-muted-foreground'>
                                          截图 {index + 1}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </DialogTrigger>
                                <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
                                  <DialogHeader>
                                    <DialogTitle>
                                      购物车截图 {index + 1}
                                    </DialogTitle>
                                  </DialogHeader>
                                  <div className='flex items-center justify-center p-4 overflow-auto'>
                                    <Image
                                      src={screenshot}
                                      alt={`购物车截图 ${index + 1}`}
                                      width={800}
                                      height={600}
                                      className='max-w-full max-h-full object-contain'
                                      onError={e => {
                                        (e.target as HTMLImageElement).src =
                                          '/placeholder-image.png';
                                      }}
                                    />
                                  </div>
                                </DialogContent>
                              </Dialog>
                            ),
                          )}
                        </div>
                      </div>
                    </>
                  )}

                {/* 委托备注 */}
                {viewingTask.title && (
                  <>
                    <Separator />
                    <div className='space-y-4'>
                      <h3 className='text-lg font-semibold'>委托备注</h3>
                      <div className='bg-muted/50 p-4 rounded-lg'>
                        <p className='whitespace-pre-wrap'>
                          {viewingTask.title}
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}
          </SheetContent>
        </Sheet>

        {/* 证据查看Sheet */}
        <Sheet open={evidenceSheetOpen} onOpenChange={setEvidenceSheetOpen}>
          <SheetContent className='w-[600px] sm:w-[800px] overflow-y-auto'>
            <SheetHeader>
              <SheetTitle>证据文件</SheetTitle>
              <SheetDescription>查看委托相关的证据文件和说明</SheetDescription>
            </SheetHeader>

            {viewingTask && (
              <div className='mt-6 space-y-6'>
                {/* 证据状态 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>证据状态</h3>
                  <div className='flex items-center gap-4'>
                    {(() => {
                      const evidenceDisplay = getEvidenceDisplay(
                        viewingTask.evidenceStatus,
                      );
                      return (
                        <Badge
                          variant={evidenceDisplay.variant}
                          className={`text-sm ${evidenceDisplay.color}`}
                        >
                          <span className='mr-2'>{evidenceDisplay.icon}</span>
                          {EVIDENCE_STATUS_LABELS[
                            viewingTask.evidenceStatus as EvidenceStatus
                          ] || viewingTask.evidenceStatus}
                        </Badge>
                      );
                    })()}
                    <span className='text-sm text-muted-foreground'>
                      上传类型:{' '}
                      {viewingTask.evidenceUploadType === 'IMMEDIATE'
                        ? '立即上传'
                        : viewingTask.evidenceUploadType === 'DELAYED'
                          ? '延迟上传'
                          : '无上传'}
                    </span>
                  </div>
                </div>

                <Separator />

                {/* 证据文件列表 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>证据文件</h3>
                  {viewingTask.evidenceFiles &&
                  viewingTask.evidenceFiles.length > 0 ? (
                    <div className='space-y-3'>
                      {viewingTask.evidenceFiles.map(
                        (file: string, index: number) => {
                          const isImage =
                            file.toLowerCase().includes('.jpg') ||
                            file.toLowerCase().includes('.jpeg') ||
                            file.toLowerCase().includes('.png') ||
                            file.toLowerCase().includes('.gif');

                          return (
                            <div
                              key={index}
                              className='border rounded-lg p-4 space-y-2'
                            >
                              <div className='flex items-center justify-between'>
                                <div className='flex items-center gap-2'>
                                  <FileText className='h-4 w-4 text-muted-foreground' />
                                  <span className='font-medium'>
                                    证据文件 {index + 1}
                                  </span>
                                </div>
                                {isImage ? (
                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button variant='outline' size='sm'>
                                        <Eye className='h-4 w-4 mr-2' />
                                        查看
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
                                      <DialogHeader>
                                        <DialogTitle>
                                          证据文件 {index + 1}
                                        </DialogTitle>
                                      </DialogHeader>
                                      <div className='flex items-center justify-center p-4 overflow-auto'>
                                        <Image
                                          src={file}
                                          alt={`证据文件 ${index + 1}`}
                                          width={800}
                                          height={600}
                                          className='max-w-full max-h-full object-contain'
                                          onError={e => {
                                            (e.target as HTMLImageElement).src =
                                              '/placeholder-image.png';
                                          }}
                                        />
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                ) : (
                                  <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() => window.open(file, '_blank')}
                                  >
                                    <Eye className='h-4 w-4 mr-2' />
                                    查看
                                  </Button>
                                )}
                              </div>
                              <p className='text-sm text-muted-foreground break-all'>
                                {file}
                              </p>
                              {/* 如果是图片文件，显示缩略图预览 */}
                              {isImage && (
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <div className='mt-2 cursor-pointer group'>
                                      <Image
                                        src={file}
                                        alt={`证据文件 ${index + 1}`}
                                        width={400}
                                        height={240}
                                        className='max-w-full max-h-60 object-contain border rounded group-hover:shadow-md transition-shadow'
                                        onError={e => {
                                          (
                                            e.target as HTMLImageElement
                                          ).style.display = 'none';
                                        }}
                                      />
                                    </div>
                                  </DialogTrigger>
                                  <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
                                    <DialogHeader>
                                      <DialogTitle>
                                        证据文件 {index + 1}
                                      </DialogTitle>
                                    </DialogHeader>
                                    <div className='flex items-center justify-center p-4 overflow-auto'>
                                      <Image
                                        src={
                                          file.startsWith('/uploads/')
                                            ? file.replace(
                                                '/uploads/',
                                                '/api/files/',
                                              )
                                            : file
                                        }
                                        alt={`证据文件 ${index + 1}`}
                                        width={800}
                                        height={600}
                                        className='max-w-full max-h-full object-contain'
                                        onError={e => {
                                          (e.target as HTMLImageElement).src =
                                            '/placeholder-image.png';
                                        }}
                                      />
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              )}
                            </div>
                          );
                        },
                      )}
                    </div>
                  ) : (
                    <div className='text-center py-8'>
                      <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                      <p className='text-muted-foreground'>
                        {viewingTask.evidenceUploadType === 'NONE'
                          ? '此委托无证据'
                          : '暂无证据文件'}
                      </p>
                      {viewingTask.evidenceUploadType !== 'NONE' && (
                        <p className='text-sm text-muted-foreground mt-1'>
                          发布者还未上传证据文件
                        </p>
                      )}
                    </div>
                  )}
                </div>

                {/* 证据说明 */}
                <Separator />
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>证据要求说明</h3>
                  <div className='bg-muted/50 p-4 rounded-lg'>
                    <div className='space-y-2 text-sm'>
                      <p>
                        <strong>上传要求:</strong>
                      </p>
                      <ul className='list-disc list-inside space-y-1 text-muted-foreground'>
                        <li>支持图片格式：JPG, PNG, GIF</li>
                        <li>单个文件大小不超过 10MB</li>
                        <li>图片内容清晰，能够证明拒付情况</li>
                        <li>包含订单号、时间等关键信息</li>
                      </ul>
                      {viewingTask.evidenceUploadType === 'DELAYED' && (
                        <p className='mt-2 text-orange-600'>
                          <strong>延迟上传:</strong>{' '}
                          发布者可在委托进行过程中上传证据
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </SheetContent>
        </Sheet>
      </div>
    </AdminPageLayout>
  );
}
