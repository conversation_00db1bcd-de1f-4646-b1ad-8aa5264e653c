import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';
import { sendTaskCompletedAccepterEmail } from '@/lib/email';
import { getTaskCommission } from '@/lib/utils/commission';

// 自动确认收货逻辑
async function processAutoConfirmDelivery() {
  const now = new Date();
  let autoConfirmedCount = 0;

  // 查找超过30天未确认收货的委托（使用原生SQL避免类型问题）
  const pendingDeliveryTasks = await prisma.$queryRaw<
    Array<{
      id: string;
      publisherId: string;
      accepterId: string;
      unitPrice: number;
      quantity: number;
      finalTotal: number;
      evidenceStatus: string | null;
    }>
  >`
    SELECT 
      t.id, 
      t."publisherId", 
      t."accepterId", 
      t."unitPrice", 
      t.quantity, 
      t."finalTotal", 
      t."evidenceStatus",
      p.name as "platformName",
      c.name as "categoryName",
      pub.name as "publisherName",
      pub.email as "publisherEmail",
      acc.name as "accepterName", 
      acc.email as "accepterEmail"
    FROM tasks t
    LEFT JOIN platforms p ON t."platformId" = p.id
    LEFT JOIN categories c ON t."categoryId" = c.id
    LEFT JOIN users pub ON t."publisherId" = pub.id
    LEFT JOIN users acc ON t."accepterId" = acc.id
    WHERE t.status = 'PENDING_DELIVERY' 
      AND t."deliveryDeadline" < ${now}
  `;

  if (pendingDeliveryTasks.length > 0) {
    // 获取系统费率配置和其他费率数据
    const [systemRate, chargebackTypes, paymentMethods] = await Promise.all([
      prisma.systemRate.findFirst(),
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    if (!systemRate) {
      throw new Error('系统配置错误：未找到费率配置');
    }

    const completedTime = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    await prisma.$transaction(async tx => {
      for (const task of pendingDeliveryTasks) {
        if (!task.accepterId) continue;

        // 计算押金和酬金
        const totalPrice = task.unitPrice * task.quantity;
        const depositAmount = totalPrice * (systemRate.depositRatio / 100);

        // 使用真实的酬金计算函数（包含证据状态）
        const taskWithEvidenceStatus = {
          ...task,
          evidenceStatus: task.evidenceStatus?.toString(),
        };
        const commissionAmount = getTaskCommission(
          taskWithEvidenceStatus,
          chargebackTypes,
          paymentMethods,
          systemRate,
        );

        // 1. 更新委托状态为已完成
        await tx.$executeRaw`
          UPDATE tasks 
          SET status = 'COMPLETED', "completedAt" = ${now}
          WHERE id = ${task.id}
        `;

        // 2. 处理证据费的最终归属
        const evidenceFee = (totalPrice * systemRate.noEvidenceExtraRate) / 100;

        if (task.evidenceStatus?.toString() !== 'REVIEWED' && evidenceFee > 0) {
          // 证据未审核通过，从发布者冻结金额中扣除证据费
          await tx.user.update({
            where: { id: task.publisherId },
            data: {
              frozenAmount: {
                decrement: evidenceFee,
              },
            },
          });

          // 创建证据费扣除记录
          await tx.walletTransaction.create({
            data: {
              userId: task.publisherId,
              type: 'TASK_FEE',
              amount: -evidenceFee,
              status: 'COMPLETED',
              description: `委托自动确认收货，证据费转为接单者酬金 - ${task.id}`,
              reference: task.id,
              completedAt: now,
            },
          });
        }

        // 3. 释放押金
        await tx.user.update({
          where: { id: task.accepterId },
          data: {
            frozenAmount: { decrement: depositAmount },
            balance: { increment: depositAmount },
          },
        });

        // 4. 支付酬金
        await tx.user.update({
          where: { id: task.accepterId },
          data: {
            balance: { increment: commissionAmount },
            totalIncome: { increment: commissionAmount },
          },
        });

        // 5. 创建押金释放记录
        await tx.walletTransaction.create({
          data: {
            userId: task.accepterId,
            type: 'DEPOSIT',
            amount: depositAmount,
            status: 'COMPLETED',
            description: `委托确认收货押金释放 - ${task.id}`,
            reference: task.id,
            completedAt: now,
          },
        });

        // 6. 创建酬金支付记录
        await tx.walletTransaction.create({
          data: {
            userId: task.accepterId,
            type: 'COMMISSION',
            amount: commissionAmount,
            status: 'COMPLETED',
            description: `委托确认收货酬金支付 - ${task.id}`,
            reference: task.id,
            completedAt: now,
          },
        });

        // 6. 发送邮件通知（在事务外执行）
        Promise.all([
          // 发送给发布者的邮件
          fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/send-email`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'task-completed-publisher',
              to: (task as any).publisherEmail,
              data: {
                publisherName: (task as any).publisherName,
                publisherEmail: (task as any).publisherEmail,
                taskId: task.id,
                platform: (task as any).platformName,
                category: (task as any).categoryName,
                quantity: task.quantity,
                unitPrice: task.unitPrice,
                totalAmount: totalPrice,
                completedAt: completedTime,
              },
            }),
          }),
          // 发送给接单者的邮件
          fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/send-email`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'task-completed-accepter',
              to: (task as any).accepterEmail,
              data: {
                accepterName: (task as any).accepterName,
                accepterEmail: (task as any).accepterEmail,
                taskId: task.id,
                platform: (task as any).platformName,
                category: (task as any).categoryName,
                quantity: task.quantity,
                unitPrice: task.unitPrice,
                totalAmount: totalPrice,
                completedAt: completedTime,
                commissionEarned: commissionAmount,
                depositReleased: depositAmount,
                totalEarned: commissionAmount,
              },
            }),
          }),
        ])
          .then(async responses => {
            // 记录邮件发送结果
            try {
              const [publisherResponse, accepterResponse] = responses;
              if (!publisherResponse.ok) {
                const publisherError = await publisherResponse.json();
                console.error(
                  '自动确认收货：发送发布者邮件失败:',
                  publisherError,
                );
              } else {
                console.log('自动确认收货：发布者委托完成邮件已发送');
              }

              if (!accepterResponse.ok) {
                const accepterError = await accepterResponse.json();
                console.error(
                  '自动确认收货：发送接单者邮件失败:',
                  accepterError,
                );
              } else {
                console.log('自动确认收货：接单者委托完成邮件已发送');
              }
            } catch (error) {
              console.error('自动确认收货：处理邮件响应时出错:', error);
            }
          })
          .catch(error => {
            console.error('自动确认收货：发送邮件时出错:', error);
          });

        autoConfirmedCount++;
      }
    });
  }

  console.log(
    `自动确认收货委托完成：${autoConfirmedCount} 个委托被自动确认收货`,
  );

  return {
    success: true,
    message: '自动确认收货委托完成',
    data: {
      autoConfirmedCount,
    },
  };
}

// 开发环境的GET请求支持（手动触发）
export async function GET(request: NextRequest) {
  try {
    // 只在开发环境允许GET请求
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: '生产环境不支持GET请求' },
        { status: 405 },
      );
    }

    const result = await processAutoConfirmDelivery();
    return NextResponse.json(result);
  } catch (error) {
    console.error('自动确认收货委托失败:', error);
    return NextResponse.json(
      { error: '自动确认收货委托失败' },
      { status: 500 },
    );
  }
}

// 定时委托：自动确认收货超过30天未操作的委托
export async function POST(request: NextRequest) {
  try {
    // 验证请求来源（安全检查）
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const result = await processAutoConfirmDelivery();
    return NextResponse.json(result);
  } catch (error) {
    console.error('自动确认收货委托失败:', error);
    return NextResponse.json(
      { error: '自动确认收货委托失败' },
      { status: 500 },
    );
  }
}
