import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  ReplyTicketInput,
  UpdateTicketStatusInput,
  AssignTicketInput,
  TicketQueryParams,
  TicketListResponse,
  TicketWithUser,
  TicketStatsResponse,
} from '@/lib/types/ticket';

// 获取管理员工单列表
export function useAdminTickets(
  params: TicketQueryParams & { assigned?: string },
) {
  return useQuery<TicketListResponse>({
    queryKey: ['admin-tickets', params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(
        `/api/admin/tickets?${searchParams.toString()}`,
      );
      if (!response.ok) {
        throw new Error('获取工单列表失败');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 2, // 2分钟
  });
}

// 获取工单统计
export function useTicketStats() {
  return useQuery<TicketStatsResponse>({
    queryKey: ['ticket-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/tickets?stats=true');
      if (!response.ok) {
        throw new Error('获取工单统计失败');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5分钟
  });
}

// 获取管理员工单详情
export function useAdminTicket(ticketId: string) {
  return useQuery<TicketWithUser>({
    queryKey: ['admin-ticket', ticketId],
    queryFn: async () => {
      const response = await fetch(`/api/admin/tickets/${ticketId}`);
      if (!response.ok) {
        throw new Error('获取工单详情失败');
      }
      return response.json();
    },
    enabled: !!ticketId,
  });
}

// 管理员回复工单
export function useAdminReplyTicket(ticketId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ReplyTicketInput) => {
      const response = await fetch(`/api/admin/tickets/${ticketId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '发送回复失败');
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新相关数据
      queryClient.invalidateQueries({ queryKey: ['admin-ticket', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['admin-tickets'] });
      queryClient.invalidateQueries({ queryKey: ['ticket-stats'] });
      toast.success('回复已发送');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 更新工单状态（管理员）
export function useAdminUpdateTicketStatus(ticketId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateTicketStatusInput) => {
      const response = await fetch(`/api/admin/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '更新工单状态失败');
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新相关数据
      queryClient.invalidateQueries({ queryKey: ['admin-ticket', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['admin-tickets'] });
      queryClient.invalidateQueries({ queryKey: ['ticket-stats'] });
      toast.success('工单状态已更新');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 分配工单
export function useAssignTicket(ticketId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: AssignTicketInput & { action: 'assign' }) => {
      const response = await fetch(`/api/admin/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '分配工单失败');
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新相关数据
      queryClient.invalidateQueries({ queryKey: ['admin-ticket', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['admin-tickets'] });
      queryClient.invalidateQueries({ queryKey: ['ticket-stats'] });
      toast.success('工单已分配');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 获取可分配的管理员列表
export function useAdminUsers() {
  return useQuery({
    queryKey: ['admin-users'],
    queryFn: async () => {
      const response = await fetch('/api/admin/users?role=admin');
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '获取管理员列表失败');
      }
      const data = await response.json();
      // 确保返回的是数组
      return Array.isArray(data) ? data : [];
    },
    staleTime: 1000 * 60 * 10, // 10分钟
  });
}
