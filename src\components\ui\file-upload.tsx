'use client';

import {
  Upload,
  X,
  FileText,
  Video,
  Image,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useRef, useCallback } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useFileUpload, UploadedFile } from '@/hooks/use-file-upload';

interface FileUploadProps {
  value?: UploadedFile[];
  onChange?: (files: UploadedFile[]) => void;
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number; // MB
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  description?: string;
}

export function FileUpload({
  value = [],
  onChange,
  accept = 'image/*,video/*',
  multiple = true,
  maxFiles = 10,
  maxSize = 50,
  className = '',
  disabled = false,
  placeholder = '点击上传或拖拽文件到这里',
  description = '支持 PNG, JPG, MP4, MOV 等格式',
}: FileUploadProps) {
  const t = useTranslations('Messages');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadFiles, isUploading, uploadProgress } = useFileUpload();

  const handleFiles = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const fileArray = Array.from(files);

      // 检查文件数量限制
      if (value.length + fileArray.length > maxFiles) {
        toast.error(t('upload.errors.tooManyFiles', { max: maxFiles }));
        return;
      }

      // 检查文件大小
      const oversizedFiles = fileArray.filter(
        file => file.size > maxSize * 1024 * 1024,
      );
      if (oversizedFiles.length > 0) {
        toast.error(
          `文件 ${oversizedFiles[0].name} ${t('upload.errors.fileTooLarge')} (${maxSize}MB)`,
        );
        return;
      }

      try {
        const uploadedFiles = await uploadFiles(fileArray);
        const newFiles = [...value, ...uploadedFiles];
        onChange?.(newFiles);
      } catch (error) {
        // 错误已经在 hook 中处理了
      }
    },
    [value, maxFiles, maxSize, uploadFiles, onChange, t],
  );

  const removeFile = useCallback(
    (index: number) => {
      const newFiles = value.filter((_, i) => i !== index);
      onChange?.(newFiles);
    },
    [value, onChange],
  );

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (disabled) return;

      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
        handleFiles(e.dataTransfer.files);
      }
    },
    [disabled, handleFiles],
  );

  const getFileIcon = (file: UploadedFile) => {
    if (file.fileType === 'videos') {
      return <Video className='h-4 w-4 text-primary' />;
    }
    if (file.fileType === 'images') {
      return <Image className='h-4 w-4 text-primary' aria-label='图片文件' />;
    }
    return <FileText className='h-4 w-4 text-primary' />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 上传区域 */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-primary bg-primary/5'
            : disabled
              ? 'border-muted-foreground/25 bg-muted/50 cursor-not-allowed'
              : 'border-muted-foreground/25 hover:border-muted-foreground/50 cursor-pointer'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => {
          if (!disabled) {
            fileInputRef.current?.click();
          }
        }}
      >
        <input
          ref={fileInputRef}
          type='file'
          multiple={multiple}
          accept={accept}
          onChange={e => {
            handleFiles(e.target.files);
            // 清空 input 的值，这样可以重复选择同一个文件
            e.target.value = '';
          }}
          className='hidden'
          disabled={disabled}
          aria-label={
            multiple ? t('upload.selectFiles') : t('upload.selectFile')
          }
        />
        <div className='space-y-2'>
          <Upload
            className={`mx-auto h-8 w-8 ${disabled ? 'text-muted-foreground/50' : 'text-muted-foreground'}`}
          />
          <div>
            <p
              className={`text-sm font-medium ${disabled ? 'text-muted-foreground/50' : ''}`}
            >
              {placeholder}
            </p>
            <p
              className={`text-xs mt-1 ${disabled ? 'text-muted-foreground/50' : 'text-muted-foreground'}`}
            >
              {description}
            </p>
          </div>
        </div>
      </div>

      {/* 上传进度 */}
      {isUploading && uploadProgress.length > 0 && (
        <div className='space-y-2'>
          <div className='text-sm font-medium'>{t('upload.uploading')}</div>
          {uploadProgress.map((progress, index) => (
            <div key={index} className='space-y-1'>
              <div className='flex items-center justify-between text-xs'>
                <span className='truncate'>{progress.file.name}</span>
                <div className='flex items-center space-x-1'>
                  {progress.status === 'success' && (
                    <CheckCircle className='h-3 w-3 text-green-500' />
                  )}
                  {progress.status === 'error' && (
                    <AlertCircle className='h-3 w-3 text-red-500' />
                  )}
                  <span>{progress.progress}%</span>
                </div>
              </div>
              <Progress value={progress.progress} className='h-1' />
              {progress.error && (
                <p className='text-xs text-red-500'>{progress.error}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 已上传文件列表 */}
      {value.length > 0 && (
        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <div className='text-sm font-medium'>
              {t('upload.uploadedFiles')} ({value.length})
            </div>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => onChange?.([])}
              className='text-xs text-muted-foreground hover:text-destructive'
              disabled={disabled}
            >
              {t('upload.clearAll')}
            </Button>
          </div>
          <div className='space-y-2'>
            {value.map((file, index) => (
              <div
                key={index}
                className='flex items-center justify-between p-3 bg-muted/50 rounded-lg'
              >
                <div className='flex items-center space-x-3'>
                  <div className='w-8 h-8 bg-primary/10 rounded flex items-center justify-center'>
                    {getFileIcon(file)}
                  </div>
                  <div className='min-w-0 flex-1'>
                    <p className='text-sm font-medium truncate'>
                      {file.originalName}
                    </p>
                    <p className='text-xs text-muted-foreground'>
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => removeFile(index)}
                  className='text-muted-foreground hover:text-destructive'
                  disabled={disabled}
                >
                  <X className='h-4 w-4' />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
