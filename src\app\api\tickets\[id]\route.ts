import { TicketStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { createServerTranslator } from '@/lib/server-i18n';
import { updateTicketStatusSchema } from '@/lib/types/ticket';

// 获取工单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 创建服务器端翻译器
    const { t } = createServerTranslator(request);

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t('loginRequired') }, { status: 401 });
    }

    const { id: ticketId } = await params;

    // 查找工单并验证权限
    const ticket = await prisma.ticket.findFirst({
      where: {
        id: ticketId,
        userId: session.user.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        replies: {
          orderBy: {
            createdAt: 'asc',
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            replies: true,
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: t('ticketNotFound') }, { status: 404 });
    }

    return NextResponse.json(ticket);
  } catch (error) {
    console.error('API错误:', error);
    const { t } = createServerTranslator(request);
    return NextResponse.json({ error: t('serverError') }, { status: 500 });
  }
}

// 更新工单状态（用户端）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 创建服务器端翻译器
    const { t } = createServerTranslator(request);

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t('loginRequired') }, { status: 401 });
    }

    const { id: ticketId } = await params;
    const body = await request.json();

    // 验证请求数据
    const validation = updateTicketStatusSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: t('invalidData'),
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { status } = validation.data;

    // 查找工单并验证权限
    const existingTicket = await prisma.ticket.findFirst({
      where: {
        id: ticketId,
        userId: session.user.id,
      },
    });

    if (!existingTicket) {
      return NextResponse.json({ error: t('ticketNotFound') }, { status: 404 });
    }

    // 验证状态转换是否合法（用户只能进行特定的状态转换）
    const allowedTransitions: Record<TicketStatus, TicketStatus[]> = {
      [TicketStatus.PENDING]: [TicketStatus.CANCELLED], // 待处理 -> 已取消
      [TicketStatus.RESOLVED]: [TicketStatus.CLOSED], // 已解决 -> 已关闭
      [TicketStatus.IN_PROGRESS]: [TicketStatus.CANCELLED], // 处理中 -> 已取消
      [TicketStatus.CLOSED]: [], // 已关闭 -> 不允许任何转换
      [TicketStatus.CANCELLED]: [], // 已取消 -> 不允许任何转换
    };

    const currentStatus = existingTicket.status as TicketStatus;
    const allowedStatuses = allowedTransitions[currentStatus];

    if (!allowedStatuses.includes(status as TicketStatus)) {
      return NextResponse.json(
        { error: t('invalidTransition') },
        { status: 400 },
      );
    }

    // 准备更新数据
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    // 设置相关时间戳
    if (status === TicketStatus.CLOSED) {
      updateData.closedAt = new Date();
    }

    // 更新工单状态
    const updatedTicket = await prisma.ticket.update({
      where: { id: ticketId },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: t('ticketStatusUpdated'),
      ticket: updatedTicket,
    });
  } catch (error) {
    console.error('更新工单状态失败:', error);
    const { t } = createServerTranslator(request);
    return NextResponse.json({ error: t('serverError') }, { status: 500 });
  }
}
