"use client";

import { Star, Quote } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";

import { Card, CardContent } from "@/components/ui/card";

export function TestimonialsSection() {
  const t = useTranslations("HomePage");
  const testimonials = [
    {
      id: 1,
      quote: t("testimonials.quotes.0"),
      rating: 5,
      user: {
        name: t("testimonials.users.0.name"),
        role: t("testimonials.users.0.role"),
        company: t("testimonials.users.0.company"),
        avatar: "/placeholder.svg?height=50&width=50",
      },
      platform: "DHgate",
      date: "2024-01-15",
    },
    {
      id: 2,
      quote: t("testimonials.quotes.1"),
      rating: 5,
      user: {
        name: t("testimonials.users.1.name"),
        role: t("testimonials.users.1.role"),
        company: t("testimonials.users.1.company"),
        avatar: "/placeholder.svg?height=50&width=50",
      },
      platform: "PayPal",
      date: "2024-01-20",
    },
    {
      id: 3,
      quote: t("testimonials.quotes.2"),
      rating: 5,
      user: {
        name: t("testimonials.users.2.name"),
        role: t("testimonials.users.2.role"),
        company: t("testimonials.users.2.company"),
        avatar: "/placeholder.svg?height=50&width=50",
      },
      platform: t("taskHall.paymentMethods.creditCard"),
      date: "2024-01-25",
    },
    {
      id: 4,
      quote: t("testimonials.quotes.3"),
      rating: 5,
      user: {
        name: t("testimonials.users.3.name"),
        role: t("testimonials.users.3.role"),
        company: t("testimonials.users.3.company"),
        avatar: "/placeholder.svg?height=50&width=50",
      },
      platform: "DHgate",
      date: "2024-02-01",
    },
  ];

  const statistics = [
    { value: "10,000+", label: t("testimonials.stats.satisfiedUsers") },
    { value: "95%", label: t("testimonials.stats.successRate") },
    { value: "4.9", label: t("testimonials.stats.averageRating") },
    { value: "24/7", label: t("testimonials.stats.customerSupport") },
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{t("testimonials.title")}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("testimonials.subtitle")}
          </p>
        </div>

        {/* Testimonials Scroll */}
        <div className="relative mb-16">
          <div className="flex space-x-6 overflow-x-auto pb-4 scrollbar-hide">
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id} className="flex-shrink-0 w-80 hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <Quote className="w-8 h-8 text-blue-600 mb-4" />

                  <p className="text-gray-700 mb-4 leading-relaxed">&ldquo;{testimonial.quote}&rdquo;</p>

                  {/* Rating */}
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>

                  {/* User Info */}
                  <div className="flex items-center space-x-3">
                    <Image
                      src={testimonial.user.avatar || "/placeholder.svg"}
                      alt={testimonial.user.name}
                      width={48}
                      height={48}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.user.name}</div>
                      <div className="text-sm text-gray-600">
                        {testimonial.user.role} · {testimonial.user.company}
                      </div>
                      <div className="text-xs text-gray-500">
                        {testimonial.platform} · {testimonial.date}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Statistics Bar */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {statistics.map((stat, index) => (
            <div key={index}>
              <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
