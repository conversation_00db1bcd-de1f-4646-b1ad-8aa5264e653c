'use client';

import {
  Search,
  Filter,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  // MessageSquare,
  FileImage,
  FileText,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Copy,
  ExternalLink,
} from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { toast } from 'sonner';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  // SheetTrigger,
} from '@/components/ui/sheet';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import {
  useEvidenceData,
  type EvidenceFilters,
} from '@/hooks/use-evidence-data';
import { useEvidenceReview } from '@/hooks/use-evidence-review';
import { useActivePlatforms } from '@/hooks/use-publish-config';

export default function EvidenceManagePage() {
  // 筛选状态
  const [filters, setFilters] = useState<EvidenceFilters>({
    search: '',
    platform: 'all',
    evidenceStatus: 'all',
    page: 1,
    limit: 20,
  });

  // 查看证据的状态
  const [viewingEvidence, setViewingEvidence] = useState<any>(null);
  const [evidenceSheetOpen, setEvidenceSheetOpen] = useState(false);

  // 拒绝理由对话框状态
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectingEvidence, setRejectingEvidence] = useState<any>(null);
  const [rejectReason, setRejectReason] = useState('');

  // 通过确认对话框状态
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [approvingEvidence, setApprovingEvidence] = useState<any>(null);

  // 获取数据
  const { data: evidenceData, isLoading, error } = useEvidenceData(filters);
  const { data: platforms = [] } = useActivePlatforms();

  // 证据审核Hook
  const evidenceReview = useEvidenceReview();

  const evidenceList = evidenceData?.data?.evidence || [];
  const stats = evidenceData?.data?.stats || {
    totalEvidence: 0,
    pendingReview: 0,
    reviewed: 0,
  };
  const pagination = evidenceData?.data?.pagination;

  // 处理筛选变化
  const handleFilterChange = (key: keyof EvidenceFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value,
      page: 1, // 重置页码
    }));
  };

  // 处理搜索
  const handleSearchChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      search: value || undefined,
      page: 1,
    }));
  };

  // 重置筛选
  const handleResetFilters = () => {
    setFilters({
      search: '',
      platform: 'all',
      evidenceStatus: 'all',
      page: 1,
      limit: 20,
    });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // 打开证据查看
  const openEvidenceSheet = (evidence: any) => {
    setViewingEvidence(evidence);
    setEvidenceSheetOpen(true);
  };

  // 打开通过确认对话框
  const openApproveDialog = (evidence: any) => {
    setApprovingEvidence(evidence);
    setApproveDialogOpen(true);
  };

  // 处理证据通过
  const handleApproveEvidence = async () => {
    if (!approvingEvidence) return;

    try {
      await evidenceReview.mutateAsync({
        taskId: approvingEvidence.taskId,
        action: 'approve',
      });
      toast.success('证据审核通过');
      setApproveDialogOpen(false);
      setApprovingEvidence(null);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : '审核失败');
    }
  };

  // 打开拒绝对话框
  const openRejectDialog = (evidence: any) => {
    setRejectingEvidence(evidence);
    setRejectReason('');
    setRejectDialogOpen(true);
  };

  // 处理证据拒绝
  const handleRejectEvidence = async () => {
    if (!rejectingEvidence || !rejectReason.trim()) {
      toast.error('请填写拒绝理由');
      return;
    }

    try {
      await evidenceReview.mutateAsync({
        taskId: rejectingEvidence.taskId,
        action: 'reject',
        reason: rejectReason.trim(),
      });
      toast.success('证据审核拒绝');
      setRejectDialogOpen(false);
      setRejectingEvidence(null);
      setRejectReason('');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : '审核失败');
    }
  };

  // 获取证据状态显示
  const getEvidenceStatusDisplay = (status: string) => {
    switch (status) {
      case '待审核':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'outline' as const,
        };
      case '已审核':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className='h-3 w-3' />,
          variant: 'default' as const,
        };

      case '未通过':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <XCircle className='h-3 w-3' />,
          variant: 'destructive' as const,
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Clock className='h-3 w-3' />,
          variant: 'outline' as const,
        };
    }
  };

  if (error) {
    return (
      <AdminPageLayout title='管理后台' breadcrumbPage='证据管理' href='/admin'>
        <div className='flex items-center justify-center py-12'>
          <p className='text-sm text-red-600'>
            加载证据数据失败，请刷新页面重试
          </p>
        </div>
      </AdminPageLayout>
    );
  }

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='证据管理' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题和操作 */}
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
          <div className='space-y-1'>
            <h1 className='text-2xl font-bold tracking-tight'>证据管理</h1>
            <p className='text-sm text-muted-foreground'>
              管理和审核委托相关的证据文件，只显示已上传证据的委托
            </p>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>证据总数</CardTitle>
              <FileImage className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.totalEvidence}</div>
              <p className='text-xs text-muted-foreground'>所有已上传的证据</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>待审核</CardTitle>
              <Clock className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.pendingReview}</div>
              <p className='text-xs text-muted-foreground'>等待审核的证据</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>已审核</CardTitle>
              <CheckCircle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.reviewed}</div>
              <p className='text-xs text-muted-foreground'>已完成审核的证据</p>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Filter className='h-5 w-5' />
              搜索和筛选
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex flex-col lg:flex-row gap-4'>
              <div className='relative flex-1'>
                <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='搜索委托ID或昵称...'
                  className='pl-10'
                  value={filters.search || ''}
                  onChange={e => handleSearchChange(e.target.value)}
                />
              </div>
              <div className='flex flex-col sm:flex-row gap-2'>
                <Select
                  value={filters.platform || 'all'}
                  onValueChange={value => handleFilterChange('platform', value)}
                >
                  <SelectTrigger className='w-full sm:w-[120px]'>
                    <SelectValue placeholder='平台' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部平台</SelectItem>
                    {platforms.map(platform => (
                      <SelectItem key={platform.id} value={platform.id}>
                        {platform.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={filters.evidenceStatus || 'all'}
                  onValueChange={value =>
                    handleFilterChange('evidenceStatus', value)
                  }
                >
                  <SelectTrigger className='w-full sm:w-[120px]'>
                    <SelectValue placeholder='证据状态' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>全部状态</SelectItem>
                    <SelectItem value='has_evidence'>已上传</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleResetFilters}
                >
                  重置
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 证据列表 */}
        <Card>
          <CardHeader>
            <CardTitle>证据列表</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className='flex items-center justify-center py-12'>
                <Loader2 className='h-8 w-8 animate-spin' />
                <span className='ml-2 text-sm text-muted-foreground'>
                  加载证据数据...
                </span>
              </div>
            ) : evidenceList.length === 0 ? (
              <div className='text-center py-12'>
                <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                <p className='text-muted-foreground'>暂无证据数据</p>
              </div>
            ) : (
              <>
                <div className='overflow-x-auto'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>委托ID</TableHead>
                        <TableHead>昵称/邮箱</TableHead>
                        <TableHead>平台</TableHead>
                        <TableHead>拒付类型</TableHead>
                        <TableHead>提交日期</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className='text-right'>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {evidenceList.map((evidence, index) => (
                        <TableRow key={index} className='hover:bg-muted/50'>
                          <TableCell>
                            <span className='font-mono text-sm font-medium text-blue-600'>
                              {evidence.taskId}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className='space-y-1'>
                              <p className='text-sm font-medium'>
                                {evidence.uploader.nickname}
                              </p>
                              <p className='text-xs text-muted-foreground'>
                                {evidence.uploader.email}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline' className='text-xs'>
                              {evidence.platform}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className='flex flex-wrap gap-1'>
                              {evidence.chargebackTypes
                                .slice(0, 2)
                                .map((type, i) => (
                                  <Badge
                                    key={i}
                                    variant='secondary'
                                    className='text-xs'
                                  >
                                    {type}
                                  </Badge>
                                ))}
                              {evidence.chargebackTypes.length > 2 && (
                                <Badge variant='secondary' className='text-xs'>
                                  +{evidence.chargebackTypes.length - 2}
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className='text-sm'>
                              {new Date(evidence.submitDate).toLocaleString(
                                'zh-CN',
                              )}
                            </span>
                          </TableCell>
                          <TableCell>
                            {(() => {
                              const statusDisplay = getEvidenceStatusDisplay(
                                evidence.status,
                              );
                              return (
                                <Badge
                                  variant={statusDisplay.variant}
                                  className={`text-xs ${statusDisplay.color}`}
                                >
                                  <span className='mr-1'>
                                    {statusDisplay.icon}
                                  </span>
                                  {evidence.status}
                                </Badge>
                              );
                            })()}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <span className='sr-only'>打开菜单</span>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuLabel>证据操作</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => openEvidenceSheet(evidence)}
                                >
                                  <Eye className='mr-2 h-4 w-4' />
                                  查看证据
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {evidence.status === '待审核' && (
                                  <>
                                    <DropdownMenuItem
                                      className='text-green-600'
                                      onClick={() =>
                                        openApproveDialog(evidence)
                                      }
                                      disabled={evidenceReview.isPending}
                                    >
                                      <CheckCircle className='mr-2 h-4 w-4' />
                                      通过
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      className='text-red-600'
                                      onClick={() => openRejectDialog(evidence)}
                                      disabled={evidenceReview.isPending}
                                    >
                                      <XCircle className='mr-2 h-4 w-4' />
                                      拒绝
                                    </DropdownMenuItem>
                                  </>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* 分页 */}
                {pagination && pagination.totalPages > 1 && (
                  <div className='flex items-center justify-between space-x-2 py-4'>
                    <div className='text-sm text-muted-foreground'>
                      显示 {(pagination.page - 1) * pagination.limit + 1} 到{' '}
                      {Math.min(
                        pagination.page * pagination.limit,
                        pagination.total,
                      )}{' '}
                      条， 共 {pagination.total} 条记录
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page <= 1}
                      >
                        <ChevronLeft className='h-4 w-4' />
                        上一页
                      </Button>
                      <div className='text-sm'>
                        第 {pagination.page} 页，共 {pagination.totalPages} 页
                      </div>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page >= pagination.totalPages}
                      >
                        下一页
                        <ChevronRight className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* 证据查看Sheet */}
        <Sheet open={evidenceSheetOpen} onOpenChange={setEvidenceSheetOpen}>
          <SheetContent className='w-[600px] sm:w-[800px] overflow-y-auto'>
            <SheetHeader>
              <SheetTitle>证据文件</SheetTitle>
              <SheetDescription>查看委托相关的证据文件和说明</SheetDescription>
            </SheetHeader>

            {viewingEvidence && (
              <div className='mt-6 space-y-6'>
                {/* 基本信息 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>基本信息</h3>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        委托ID
                      </label>
                      <p className='font-mono text-sm'>
                        {viewingEvidence.taskId}
                      </p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        平台
                      </label>
                      <p>{viewingEvidence.platform}</p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        发布者
                      </label>
                      <p>{viewingEvidence.uploader.nickname}</p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        提交时间
                      </label>
                      <p>
                        {new Date(viewingEvidence.submitDate).toLocaleString(
                          'zh-CN',
                        )}
                      </p>
                    </div>
                    {/* 在基本信息部分添加产品链接 */}
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        产品链接
                      </label>
                      {viewingEvidence.productUrl ? (
                        <div className='flex items-center gap-2'>
                          <p className='text-sm break-all flex-1'>
                            {viewingEvidence.productUrl}
                          </p>
                          <div className='flex gap-1'>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => {
                                navigator.clipboard.writeText(
                                  viewingEvidence.productUrl,
                                );
                                toast.success('链接已复制到剪贴板');
                              }}
                            >
                              <Copy className='h-3 w-3' />
                            </Button>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() =>
                                window.open(
                                  viewingEvidence.productUrl,
                                  '_blank',
                                )
                              }
                            >
                              <ExternalLink className='h-3 w-3' />
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <p className='text-sm text-muted-foreground'>
                          无产品链接
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 证据状态 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>证据状态</h3>
                  <div className='flex items-center gap-4'>
                    {(() => {
                      const statusDisplay = getEvidenceStatusDisplay(
                        viewingEvidence.status,
                      );
                      return (
                        <Badge
                          variant={statusDisplay.variant}
                          className={`text-sm ${statusDisplay.color}`}
                        >
                          <span className='mr-2'>{statusDisplay.icon}</span>
                          {viewingEvidence.status}
                        </Badge>
                      );
                    })()}
                    <span className='text-sm text-muted-foreground'>
                      上传类型:{' '}
                      {viewingEvidence.evidenceUploadType === 'IMMEDIATE'
                        ? '立即上传'
                        : viewingEvidence.evidenceUploadType === 'LATER'
                          ? '稍后上传'
                          : '无上传'}
                    </span>
                  </div>
                </div>

                <Separator />

                {/* 证据文件列表 */}
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>证据文件</h3>
                  {viewingEvidence.evidenceFiles &&
                  viewingEvidence.evidenceFiles.length > 0 ? (
                    <div className='space-y-3'>
                      {viewingEvidence.evidenceFiles.map(
                        (file: string, index: number) => {
                          const isImage =
                            file.toLowerCase().includes('.jpg') ||
                            file.toLowerCase().includes('.jpeg') ||
                            file.toLowerCase().includes('.png') ||
                            file.toLowerCase().includes('.gif');

                          return (
                            <div
                              key={index}
                              className='border rounded-lg p-4 space-y-2'
                            >
                              <div className='flex items-center justify-between'>
                                <div className='flex items-center gap-2'>
                                  <FileText className='h-4 w-4 text-muted-foreground' />
                                  <span className='font-medium'>
                                    证据文件 {index + 1}
                                  </span>
                                </div>
                                {isImage ? (
                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button variant='outline' size='sm'>
                                        <Eye className='h-4 w-4 mr-2' />
                                        查看
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
                                      <DialogHeader>
                                        <DialogTitle>
                                          证据文件 {index + 1}
                                        </DialogTitle>
                                      </DialogHeader>
                                      <div className='flex items-center justify-center p-4 overflow-auto'>
                                        <Image
                                          src={file}
                                          alt={`证据文件 ${index + 1}`}
                                          width={800}
                                          height={600}
                                          className='max-w-full max-h-full object-contain'
                                          onError={e => {
                                            (e.target as HTMLImageElement).src =
                                              '/placeholder-image.png';
                                          }}
                                        />
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                ) : (
                                  <Button
                                    variant='outline'
                                    size='sm'
                                    onClick={() => window.open(file, '_blank')}
                                  >
                                    <Eye className='h-4 w-4 mr-2' />
                                    查看
                                  </Button>
                                )}
                              </div>
                              <p className='text-sm text-muted-foreground break-all'>
                                {file}
                              </p>
                              {/* 如果是图片文件，显示缩略图预览 */}
                              {isImage && (
                                <Dialog>
                                  <DialogTrigger asChild>
                                    <div className='mt-2 cursor-pointer group'>
                                      <Image
                                        src={file}
                                        alt={`证据文件 ${index + 1}`}
                                        width={400}
                                        height={240}
                                        className='max-w-full max-h-60 object-contain border rounded group-hover:shadow-md transition-shadow'
                                        onError={e => {
                                          (
                                            e.target as HTMLImageElement
                                          ).style.display = 'none';
                                        }}
                                      />
                                    </div>
                                  </DialogTrigger>
                                  <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
                                    <DialogHeader>
                                      <DialogTitle>
                                        证据文件 {index + 1}
                                      </DialogTitle>
                                    </DialogHeader>
                                    <div className='flex items-center justify-center p-4 overflow-auto'>
                                      <Image
                                        src={
                                          file.startsWith('/uploads/')
                                            ? file.replace(
                                                '/uploads/',
                                                '/api/files/',
                                              )
                                            : file
                                        }
                                        alt={`证据文件 ${index + 1}`}
                                        width={800}
                                        height={600}
                                        className='max-w-full max-h-full object-contain'
                                        onError={e => {
                                          (e.target as HTMLImageElement).src =
                                            '/placeholder-image.png';
                                        }}
                                      />
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              )}
                            </div>
                          );
                        },
                      )}
                    </div>
                  ) : (
                    <div className='text-center py-8'>
                      <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                      <p className='text-muted-foreground'>
                        {viewingEvidence.evidenceUploadType === 'NONE'
                          ? '此委托无证据'
                          : '暂无证据文件'}
                      </p>
                      {viewingEvidence.evidenceUploadType !== 'NONE' && (
                        <p className='text-sm text-muted-foreground mt-1'>
                          发布者还未上传证据文件
                        </p>
                      )}
                    </div>
                  )}
                </div>

                {/* 证据说明 */}
                <Separator />
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>证据要求说明</h3>
                  <div className='bg-muted/50 p-4 rounded-lg'>
                    <div className='space-y-2 text-sm'>
                      <p>
                        <strong>上传要求:</strong>
                      </p>
                      <ul className='list-disc list-inside space-y-1 text-muted-foreground'>
                        <li>支持图片格式：JPG, PNG, GIF</li>
                        <li>单个文件大小不超过 10MB</li>
                        <li>图片内容清晰，能够证明拒付情况</li>
                        <li>包含订单号、时间等关键信息</li>
                      </ul>
                      {viewingEvidence.evidenceUploadType === 'LATER' && (
                        <p className='mt-2 text-orange-600'>
                          <strong>延迟上传:</strong>{' '}
                          发布者可在委托进行过程中上传证据
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </SheetContent>
        </Sheet>

        {/* 通过确认对话框 */}
        <Dialog open={approveDialogOpen} onOpenChange={setApproveDialogOpen}>
          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle>确认通过证据</DialogTitle>
            </DialogHeader>
            <div className='space-y-4 py-4'>
              {approvingEvidence && (
                <div className='space-y-2'>
                  <p className='text-sm text-muted-foreground'>
                    委托ID:{' '}
                    <span className='font-mono'>
                      {approvingEvidence.taskId}
                    </span>
                  </p>
                  <p className='text-sm text-muted-foreground'>
                    发布者: {approvingEvidence.uploader.nickname}
                  </p>
                  <p className='text-sm text-muted-foreground'>
                    平台: {approvingEvidence.platform}
                  </p>
                </div>
              )}
              <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
                <div className='flex items-start gap-3'>
                  <CheckCircle className='h-5 w-5 text-green-600 mt-0.5' />
                  <div className='space-y-1'>
                    <p className='text-sm font-medium text-green-800'>
                      确认通过此证据审核？
                    </p>
                    <p className='text-sm text-green-700'>
                      通过后，该委托的证据状态将变更为&ldquo;已审核&rdquo;，用户将收到通知。
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className='flex justify-end gap-2'>
              <Button
                variant='outline'
                onClick={() => setApproveDialogOpen(false)}
                disabled={evidenceReview.isPending}
              >
                取消
              </Button>
              <Button
                onClick={handleApproveEvidence}
                disabled={evidenceReview.isPending}
                className='bg-green-600 hover:bg-green-700'
              >
                {evidenceReview.isPending && (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                )}
                确认通过
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* 拒绝理由对话框 */}
        <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle>拒绝证据</DialogTitle>
            </DialogHeader>
            <div className='space-y-4 py-4'>
              {rejectingEvidence && (
                <div className='space-y-2'>
                  <p className='text-sm text-muted-foreground'>
                    委托ID:{' '}
                    <span className='font-mono'>
                      {rejectingEvidence.taskId}
                    </span>
                  </p>
                  <p className='text-sm text-muted-foreground'>
                    发布者: {rejectingEvidence.uploader.nickname}
                  </p>
                  <p className='text-sm text-muted-foreground'>
                    平台: {rejectingEvidence.platform}
                  </p>
                </div>
              )}
              <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
                <div className='flex items-start gap-3'>
                  <XCircle className='h-5 w-5 text-red-600 mt-0.5' />
                  <div className='space-y-1'>
                    <p className='text-sm font-medium text-red-800'>
                      拒绝证据审核
                    </p>
                    <p className='text-sm text-red-700'>
                      拒绝后，证据文件将被清空，用户需要重新上传证据。
                    </p>
                  </div>
                </div>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='rejectReason'>
                  拒绝理由 <span className='text-red-500'>*</span>
                </Label>
                <Textarea
                  id='rejectReason'
                  placeholder='请详细说明拒绝原因，帮助用户了解问题所在...'
                  value={rejectReason}
                  onChange={e => setRejectReason(e.target.value)}
                  rows={4}
                  className='resize-none'
                />
                <p className='text-xs text-muted-foreground'>
                  拒绝理由将发送给用户，请详细说明问题所在
                </p>
              </div>
            </div>
            <div className='flex justify-end gap-2'>
              <Button
                variant='outline'
                onClick={() => setRejectDialogOpen(false)}
                disabled={evidenceReview.isPending}
              >
                取消
              </Button>
              <Button
                variant='destructive'
                onClick={handleRejectEvidence}
                disabled={evidenceReview.isPending || !rejectReason.trim()}
              >
                {evidenceReview.isPending && (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                )}
                确认拒绝
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AdminPageLayout>
  );
}
