#!/usr/bin/env node

/**
 * 翻译文件迁移脚本
 * 将 react-i18next 格式的翻译文件转换为 next-intl 格式
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const SOURCE_BRANCH = 'feat/i18n-react-i18next-migration';
const SOURCE_DIR = 'src/locales';
const TARGET_DIR = 'messages';
const LOCALES = ['en', 'zh'];

// 翻译文件映射
const FILE_MAPPING = {
  'homepage.json': 'homepage.json',
  'dashboard.json': 'dashboard.json',
  'auth.json': 'auth.json',
  'email.json': 'email.json',
  'tasks.json': 'tasks.json',
  'wallet.json': 'wallet.json',
  'admin.json': 'admin.json',
  'membership.json': 'membership.json',
  'publish.json': 'publish.json',
  'my-accepted-tasks.json': 'my-accepted-tasks.json',
  'my-published-tasks.json': 'my-published-tasks.json',
  'tickets.json': 'tickets.json',
  'legal.json': 'legal.json',
  'review.json': 'review.json',
  'account-security.json': 'account-security.json',
  'shop-whitelist.json': 'shop-whitelist.json',
  'whitelist.json': 'whitelist.json',
  'logistics.json': 'logistics.json',
};

/**
 * 从源分支获取文件内容
 */
function getFileFromBranch(branch, filePath) {
  try {
    const content = execSync(`git show ${branch}:${filePath}`, {
      encoding: 'utf8',
    });
    return JSON.parse(content);
  } catch (error) {
    console.warn(
      `⚠️  无法获取文件 ${filePath} 从分支 ${branch}:`,
      error.message
    );
    return null;
  }
}

/**
 * 确保目录存在
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 创建目录: ${dirPath}`);
  }
}

/**
 * 转换翻译内容格式
 * react-i18next 和 next-intl 的格式基本相同，主要是文件组织方式的差异
 */
function convertTranslationFormat(content) {
  // 对于大多数情况，格式是兼容的
  // 这里可以添加特定的转换逻辑
  return content;
}

/**
 * 写入翻译文件
 */
function writeTranslationFile(filePath, content) {
  const formattedContent = JSON.stringify(content, null, 2);
  fs.writeFileSync(filePath, formattedContent, 'utf8');
  console.log(`✅ 写入文件: ${filePath}`);
}

/**
 * 迁移单个语言的翻译文件
 */
function migrateLocale(locale) {
  console.log(`\n🌐 开始迁移 ${locale} 语言文件...`);

  const targetLocaleDir = path.join(TARGET_DIR, locale);
  ensureDirectoryExists(targetLocaleDir);

  let migratedCount = 0;
  let totalCount = 0;

  for (const [sourceFile, targetFile] of Object.entries(FILE_MAPPING)) {
    totalCount++;
    const sourceFilePath = `${SOURCE_DIR}/${locale}/${sourceFile}`;
    const targetFilePath = path.join(targetLocaleDir, targetFile);

    console.log(`📄 处理文件: ${sourceFile} -> ${targetFile}`);

    // 从源分支获取文件内容
    const content = getFileFromBranch(SOURCE_BRANCH, sourceFilePath);

    if (content) {
      // 转换格式（如果需要）
      const convertedContent = convertTranslationFormat(content);

      // 写入目标文件
      writeTranslationFile(targetFilePath, convertedContent);
      migratedCount++;
    } else {
      console.log(`⚠️  跳过文件: ${sourceFile} (文件不存在或无法读取)`);
    }
  }

  console.log(
    `✅ ${locale} 语言迁移完成: ${migratedCount}/${totalCount} 个文件`
  );
  return { migratedCount, totalCount };
}

/**
 * 更新主语言文件
 */
function updateMainLanguageFiles() {
  console.log('\n📝 更新主语言文件...');

  for (const locale of LOCALES) {
    const localeDir = path.join(TARGET_DIR, locale);
    const mainFilePath = path.join(TARGET_DIR, `${locale}.json`);

    // 收集所有翻译文件的内容
    const combinedContent = {};

    if (fs.existsSync(localeDir)) {
      const files = fs.readdirSync(localeDir);

      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(localeDir, file);
          const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          const namespace = path.basename(file, '.json');
          combinedContent[namespace] = content;
        }
      }
    }

    // 写入主文件
    writeTranslationFile(mainFilePath, combinedContent);
  }
}

/**
 * 验证迁移结果
 */
function validateMigration() {
  console.log('\n🔍 验证迁移结果...');

  let isValid = true;

  for (const locale of LOCALES) {
    const mainFilePath = path.join(TARGET_DIR, `${locale}.json`);
    const localeDir = path.join(TARGET_DIR, locale);

    // 检查主文件是否存在
    if (!fs.existsSync(mainFilePath)) {
      console.error(`❌ 主文件不存在: ${mainFilePath}`);
      isValid = false;
    }

    // 检查语言目录是否存在
    if (!fs.existsSync(localeDir)) {
      console.error(`❌ 语言目录不存在: ${localeDir}`);
      isValid = false;
    } else {
      const files = fs.readdirSync(localeDir);
      console.log(`📊 ${locale} 语言包含 ${files.length} 个翻译文件`);
    }
  }

  return isValid;
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始翻译文件迁移...');
  console.log(`📂 源分支: ${SOURCE_BRANCH}`);
  console.log(`📂 目标目录: ${TARGET_DIR}`);
  console.log(`🌐 支持语言: ${LOCALES.join(', ')}`);

  try {
    // 确保目标目录存在
    ensureDirectoryExists(TARGET_DIR);

    // 迁移每种语言
    let totalMigrated = 0;
    let totalFiles = 0;

    for (const locale of LOCALES) {
      const result = migrateLocale(locale);
      totalMigrated += result.migratedCount;
      totalFiles += result.totalCount;
    }

    // 更新主语言文件
    updateMainLanguageFiles();

    // 验证迁移结果
    const isValid = validateMigration();

    if (isValid) {
      console.log('\n🎉 翻译文件迁移完成！');
      console.log(`📊 总计迁移: ${totalMigrated}/${totalFiles} 个文件`);
      console.log(`🌐 支持语言: ${LOCALES.length} 种`);
    } else {
      console.error('\n❌ 迁移验证失败，请检查错误信息');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 迁移过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  migrateLocale,
  updateMainLanguageFiles,
  validateMigration,
};
