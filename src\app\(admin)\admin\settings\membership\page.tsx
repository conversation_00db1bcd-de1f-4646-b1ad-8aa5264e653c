'use client';

import {
  Crown,
  Users,
  Shield,
  Edit,
  Save,
  Loader2,
  AlertCircle,
} from 'lucide-react';
import { useState } from 'react';
// import { toast } from 'sonner';

import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  useMembershipPlans,
  useUpdateMembershipPlan,
} from '@/hooks/use-membership-api';
import {
  MembershipPlan,
  // UpdateMembershipPlanInput,
  TASK_TYPES,
  SUPPORT_LEVELS,
} from '@/types/membership';

export default function MembershipSettingsPage() {
  // 获取会员套餐数据
  const { data: membershipPlans, isLoading, error } = useMembershipPlans();
  const updateMutation = useUpdateMembershipPlan();

  // 对话框状态
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<MembershipPlan | null>(null);

  // 编辑套餐
  const handleEditPlan = (plan: MembershipPlan) => {
    setEditingPlan({ ...plan });
    setIsEditDialogOpen(true);
  };

  // 保存套餐
  const handleSavePlan = () => {
    if (!editingPlan) return;

    updateMutation.mutate(
      {
        id: editingPlan.id,
        data: {
          name: editingPlan.name,
          price: editingPlan.price,
          period: editingPlan.period,
          maxTasks: editingPlan.maxTasks,
          taskTypes: editingPlan.taskTypes,
          platformRate: editingPlan.platformRate,
          whitelistSlots: editingPlan.whitelistSlots,
          supportLevel: editingPlan.supportLevel,
          features: editingPlan.features,
          isActive: editingPlan.isActive,
        },
      },
      {
        onSuccess: () => {
          setIsEditDialogOpen(false);
          setEditingPlan(null);
        },
      },
    );
  };

  // 切换套餐状态
  const handleTogglePlanStatus = (plan: MembershipPlan) => {
    updateMutation.mutate({
      id: plan.id,
      data: { isActive: !plan.isActive },
    });
  };

  // 更新编辑中的套餐
  const updateEditingPlan = (updates: Partial<MembershipPlan>) => {
    if (!editingPlan) return;
    setEditingPlan({ ...editingPlan, ...updates });
  };

  // 获取套餐图标
  const getPlanIcon = (planName: string) => {
    const name = planName.toLowerCase();
    if (name.includes('免费') || name.includes('free')) {
      return <Users className='h-5 w-5' />;
    }
    if (name.includes('专业') || name.includes('pro')) {
      return <Shield className='h-5 w-5' />;
    }
    if (
      name.includes('商业') ||
      name.includes('business') ||
      name.includes('企业')
    ) {
      return <Crown className='h-5 w-5' />;
    }
    return <Users className='h-5 w-5' />;
  };

  if (isLoading) {
    return (
      <AdminPageLayout title='管理后台' breadcrumbPage='会员套餐' href='/admin'>
        <div className='flex items-center justify-center h-64'>
          <Loader2 className='h-8 w-8 animate-spin' />
        </div>
      </AdminPageLayout>
    );
  }

  if (error) {
    return (
      <AdminPageLayout title='管理后台' breadcrumbPage='会员套餐' href='/admin'>
        <div className='flex items-center justify-center h-64'>
          <div className='text-center'>
            <AlertCircle className='h-8 w-8 text-red-500 mx-auto mb-2' />
            <p className='text-muted-foreground'>加载会员套餐失败</p>
          </div>
        </div>
      </AdminPageLayout>
    );
  }

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='会员套餐' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题 */}
        <div className='space-y-1'>
          <h1 className='text-2xl font-bold tracking-tight'>会员套餐</h1>
          <p className='text-sm text-muted-foreground'>
            管理系统的会员套餐配置和权限设置
          </p>
        </div>

        {/* 套餐配置列表 */}
        <div className='space-y-6'>
          {membershipPlans?.map(plan => (
            <Card key={plan.id}>
              <CardHeader className='pb-4'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-4'>
                    <div className='p-2 rounded-lg bg-muted'>
                      {getPlanIcon(plan.name)}
                    </div>
                    <div>
                      <CardTitle className='text-xl mb-1'>
                        {plan.name}
                      </CardTitle>
                      <p className='text-2xl font-bold text-primary'>
                        ${plan.price}
                        <span className='text-base font-normal text-muted-foreground ml-1'>
                          /{plan.period}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center gap-4'>
                    <div className='flex items-center gap-2'>
                      <Label className='text-sm'>状态</Label>
                      <Switch
                        checked={plan.isActive}
                        onCheckedChange={() => handleTogglePlanStatus(plan)}
                        disabled={updateMutation.isPending}
                      />
                    </div>
                    <Button
                      onClick={() => handleEditPlan(plan)}
                      disabled={updateMutation.isPending}
                    >
                      <Edit className='h-4 w-4 mr-2' />
                      编辑配置
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6'>
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-muted-foreground'>
                      委托限制
                    </Label>
                    <p className='text-lg font-semibold'>
                      {plan.maxTasks ? `${plan.maxTasks}次/月` : '无限制'}
                    </p>
                  </div>
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-muted-foreground'>
                      平台费率
                    </Label>
                    <p className='text-lg font-semibold text-orange-600'>
                      {plan.platformRate}%
                    </p>
                  </div>
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-muted-foreground'>
                      白名单名额
                    </Label>
                    <p className='text-lg font-semibold'>
                      {plan.whitelistSlots}个
                    </p>
                  </div>
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-muted-foreground'>
                      委托类型
                    </Label>
                    <div className='flex flex-wrap gap-1'>
                      {plan.taskTypes.map((type, index) => (
                        <Badge
                          key={index}
                          variant='secondary'
                          className='text-xs'
                        >
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className='space-y-2'>
                    <Label className='text-sm font-medium text-muted-foreground'>
                      客服支持
                    </Label>
                    <p className='text-sm font-medium'>{plan.supportLevel}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 编辑套餐对话框 */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className='sm:max-w-[600px] max-h-[80vh] overflow-y-auto'>
            {editingPlan && (
              <>
                <DialogHeader>
                  <DialogTitle className='flex items-center gap-2'>
                    {getPlanIcon(editingPlan.name)}
                    编辑 {editingPlan.name}
                  </DialogTitle>
                  <DialogDescription>
                    修改会员套餐的配置和权限设置
                  </DialogDescription>
                </DialogHeader>

                <div className='space-y-6'>
                  {/* 基本信息 */}
                  <div className='space-y-4'>
                    <h4 className='font-medium'>基本信息</h4>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label htmlFor='plan-name'>套餐名称</Label>
                        <Input
                          id='plan-name'
                          value={editingPlan.name}
                          onChange={e =>
                            updateEditingPlan({ name: e.target.value })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='plan-price'>价格 (美元)</Label>
                        <Input
                          id='plan-price'
                          type='number'
                          step='0.01'
                          value={editingPlan.price}
                          onChange={e =>
                            updateEditingPlan({
                              price: parseFloat(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* 委托限制 */}
                  <div className='space-y-4'>
                    <h4 className='font-medium'>委托限制</h4>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label htmlFor='max-tasks'>每月最大委托数</Label>
                        <Input
                          id='max-tasks'
                          type='number'
                          placeholder='留空表示无限制'
                          value={editingPlan.maxTasks || ''}
                          onChange={e =>
                            updateEditingPlan({
                              maxTasks: e.target.value
                                ? parseInt(e.target.value, 10)
                                : null,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='platform-rate'>平台费率 (%)</Label>
                        <Input
                          id='platform-rate'
                          type='number'
                          step='0.1'
                          value={editingPlan.platformRate}
                          onChange={e =>
                            updateEditingPlan({
                              platformRate: parseFloat(e.target.value) || 0,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* 权限配置 */}
                  <div className='space-y-4'>
                    <h4 className='font-medium'>权限配置</h4>
                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label htmlFor='whitelist-slots'>白名单店铺名额</Label>
                        <Input
                          id='whitelist-slots'
                          type='number'
                          value={editingPlan.whitelistSlots}
                          onChange={e =>
                            updateEditingPlan({
                              whitelistSlots: parseInt(e.target.value, 10) || 0,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor='support-level'>客服支持级别</Label>
                        <Select
                          value={editingPlan.supportLevel}
                          onValueChange={value =>
                            updateEditingPlan({ supportLevel: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {SUPPORT_LEVELS.map(level => (
                              <SelectItem key={level} value={level}>
                                {level}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* 委托类型 */}
                  <div className='space-y-4'>
                    <h4 className='font-medium'>委托类型</h4>
                    <div className='space-y-2'>
                      {TASK_TYPES.map(type => (
                        <div key={type} className='flex items-center space-x-2'>
                          <input
                            type='checkbox'
                            id={type}
                            aria-label={`Select ${type} task type`}
                            checked={editingPlan.taskTypes.includes(type)}
                            onChange={e => {
                              if (e.target.checked) {
                                updateEditingPlan({
                                  taskTypes: [...editingPlan.taskTypes, type],
                                });
                              } else {
                                updateEditingPlan({
                                  taskTypes: editingPlan.taskTypes.filter(
                                    t => t !== type,
                                  ),
                                });
                              }
                            }}
                            className='rounded border-gray-300'
                          />
                          <Label htmlFor={type} className='text-sm'>
                            {type}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 套餐状态 */}
                  <div className='space-y-4'>
                    <h4 className='font-medium'>套餐状态</h4>
                    <div className='flex items-center space-x-2'>
                      <Switch
                        checked={editingPlan.isActive}
                        onCheckedChange={checked =>
                          updateEditingPlan({ isActive: checked })
                        }
                      />
                      <Label>启用此套餐</Label>
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    variant='outline'
                    onClick={() => setIsEditDialogOpen(false)}
                    disabled={updateMutation.isPending}
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleSavePlan}
                    disabled={updateMutation.isPending}
                  >
                    {updateMutation.isPending && (
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    )}
                    <Save className='mr-2 h-4 w-4' />
                    保存配置
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminPageLayout>
  );
}
