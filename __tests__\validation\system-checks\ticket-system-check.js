// 测试票务管理页面修复效果
const fs = require('fs');
const path = require('path');

// 读取相关文件
const componentPath = path.join(
  __dirname,
  '../../../src/components/tickets-content.tsx'
);
const hooksPath = path.join(__dirname, '../../../src/hooks/use-tickets.ts');
const zhTransPath = path.join(__dirname, '../../../messages/zh/tickets.json');
const enTransPath = path.join(__dirname, '../../../messages/en/tickets.json');

const componentContent = fs.readFileSync(componentPath, 'utf8');
const hooksContent = fs.readFileSync(hooksPath, 'utf8');
const zhTrans = JSON.parse(fs.readFileSync(zhTransPath, 'utf8'));
const enTrans = JSON.parse(fs.readFileSync(enTransPath, 'utf8'));

console.log('=== 票务管理页面修复验证 ===\n');

// 检查修复的问题
const fixes = [
  {
    category: '关闭工单按钮功能',
    checks: [
      {
        description: 'handleCloseTicket函数包含成功回调',
        test: () =>
          hooksContent.includes('onSuccess: () => {') &&
          componentContent.includes('setIsDetailDialogOpen(false)'),
        expected: true,
      },
      {
        description: 'handleCloseTicket函数包含错误处理',
        test: () => componentContent.includes('onError: (error: Error) => {'),
        expected: true,
      },
      {
        description: '关闭工单按钮显示条件正确',
        test: () =>
          componentContent.includes(
            'selectedTicket.status !== TicketStatus.CLOSED'
          ) &&
          componentContent.includes(
            'selectedTicket.status !== TicketStatus.CANCELLED'
          ),
        expected: true,
      },
      {
        description: '按钮禁用状态正确',
        test: () =>
          componentContent.includes(
            'disabled={updateStatusMutation.isPending}'
          ),
        expected: true,
      },
    ],
  },
  {
    category: '消息提示翻译修复',
    checks: [
      {
        description: 'useTickets钩子使用翻译',
        test: () =>
          hooksContent.includes("const t = useTranslations('Tickets')") &&
          hooksContent.includes("t('messages.loadError')"),
        expected: true,
      },
      {
        description: 'useCreateTicket钩子使用翻译',
        test: () =>
          hooksContent.includes("toast.success(t('messages.submitSuccess'))") &&
          hooksContent.includes("t('messages.submitError')"),
        expected: true,
      },
      {
        description: 'useReplyTicket钩子使用翻译',
        test: () =>
          hooksContent.includes("toast.success(t('messages.replySuccess'))") &&
          hooksContent.includes("t('messages.replyError')"),
        expected: true,
      },
      {
        description: 'useUpdateTicketStatus钩子使用翻译',
        test: () =>
          hooksContent.includes(
            "toast.success(t('messages.statusUpdateSuccess'))"
          ) && hooksContent.includes("t('messages.statusUpdateError')"),
        expected: true,
      },
      {
        description: '移除硬编码中文消息',
        test: () =>
          !hooksContent.includes('工单已提交') &&
          !hooksContent.includes('回复已发送') &&
          !hooksContent.includes('工单状态已更新') &&
          !hooksContent.includes('获取工单列表失败'),
        expected: true,
      },
    ],
  },
  {
    category: '翻译文件完整性',
    checks: [
      {
        description: '中文翻译包含所有必要消息',
        test: () => {
          const messages = zhTrans.messages;
          const required = [
            'submitSuccess',
            'replySuccess',
            'statusUpdateSuccess',
            'closeSuccess',
            'loadError',
          ];
          return messages && required.every(key => messages[key]);
        },
        expected: true,
      },
      {
        description: '英文翻译包含所有必要消息',
        test: () => {
          const messages = enTrans.messages;
          const required = [
            'submitSuccess',
            'replySuccess',
            'statusUpdateSuccess',
            'closeSuccess',
            'loadError',
          ];
          return messages && required.every(key => messages[key]);
        },
        expected: true,
      },
      {
        description: '中英文翻译键一致',
        test: () => {
          const zhKeys = Object.keys(zhTrans.messages || {}).sort();
          const enKeys = Object.keys(enTrans.messages || {}).sort();
          return JSON.stringify(zhKeys) === JSON.stringify(enKeys);
        },
        expected: true,
      },
    ],
  },
];

let allPassed = true;

fixes.forEach(category => {
  console.log(`📋 ${category.category}:`);

  category.checks.forEach((check, index) => {
    const result = check.test();
    const status = result === check.expected ? '✅' : '❌';

    if (result !== check.expected) {
      allPassed = false;
    }

    console.log(`   ${index + 1}. ${check.description}: ${status}`);
  });

  console.log('');
});

console.log('=== 修复结果总结 ===');
if (allPassed) {
  console.log('✅ 所有修复项目都已完成！');
  console.log('\n🎯 修复内容：');
  console.log('1. 关闭工单按钮功能已完善，包含成功/错误处理');
  console.log('2. 所有toast消息已替换为翻译函数调用');
  console.log('3. 翻译文件已更新，包含所有必要的消息键');
  console.log('4. 按钮显示逻辑已优化，正确处理不同工单状态');
} else {
  console.log('❌ 仍有问题需要修复');
}

console.log('\n=== 翻译对照表 ===');
console.log('消息类型 | 中文 | 英文');
console.log('--------|------|------');
console.log(
  `提交成功 | ${zhTrans.messages?.submitSuccess || '缺失'} | ${enTrans.messages?.submitSuccess || '缺失'}`
);
console.log(
  `回复成功 | ${zhTrans.messages?.replySuccess || '缺失'} | ${enTrans.messages?.replySuccess || '缺失'}`
);
console.log(
  `状态更新成功 | ${zhTrans.messages?.statusUpdateSuccess || '缺失'} | ${enTrans.messages?.statusUpdateSuccess || '缺失'}`
);
console.log(
  `关闭成功 | ${zhTrans.messages?.closeSuccess || '缺失'} | ${enTrans.messages?.closeSuccess || '缺失'}`
);
console.log(
  `加载错误 | ${zhTrans.messages?.loadError || '缺失'} | ${enTrans.messages?.loadError || '缺失'}`
);

console.log('\n=== 测试建议 ===');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 访问票务管理页面（需要登录）');
console.log('3. 测试关闭工单功能：');
console.log('   - 创建一个测试工单');
console.log('   - 打开工单详情');
console.log('   - 点击"关闭工单"按钮');
console.log('   - 验证成功提示和UI更新');
console.log('4. 测试语言切换：');
console.log('   - 在中英文环境下测试所有操作');
console.log('   - 验证所有提示消息都显示正确语言');
console.log('5. 测试按钮状态：');
console.log('   - 验证已关闭/已取消的工单不显示关闭按钮');
console.log('   - 验证按钮在加载时正确禁用');
