// 测试票务管理页面翻译修复效果
const fs = require('fs');
const path = require('path');

// 读取组件文件
const componentPath = path.join(
  __dirname,
  '../../../src/components/tickets-content.tsx'
);
const componentContent = fs.readFileSync(componentPath, 'utf8');

// 读取翻译文件
const zhTransPath = path.join(__dirname, '../../../messages/zh/tickets.json');
const enTransPath = path.join(__dirname, '../../../messages/en/tickets.json');

const zhTrans = JSON.parse(fs.readFileSync(zhTransPath, 'utf8'));
const enTrans = JSON.parse(fs.readFileSync(enTransPath, 'utf8'));

console.log('=== 票务管理页面翻译修复验证 ===\n');

// 检查修复的问题
const issues = [
  {
    description: '创建工单验证消息',
    pattern: /请填写完整信息/,
    fixed: /t\('messages\.completeInfoRequired'\)/,
    zhKey: 'messages.completeInfoRequired',
    enKey: 'messages.completeInfoRequired',
  },
  {
    description: '标题描述验证消息',
    pattern: /标题和描述不能为空/,
    fixed: /t\('messages\.titleDescriptionRequired'\)/,
    zhKey: 'messages.titleDescriptionRequired',
    enKey: 'messages.titleDescriptionRequired',
  },
  {
    description: '回复内容验证消息',
    pattern: /请输入回复内容/,
    fixed: /t\('messages\.replyContentRequired'\)/,
    zhKey: 'messages.replyContentRequired',
    enKey: 'messages.replyContentRequired',
  },
  {
    description: '工单卡片创建时间标签',
    pattern: /创建：/,
    fixed: /t\('details\.createdLabel'\)/,
    zhKey: 'details.createdLabel',
    enKey: 'details.createdLabel',
  },
  {
    description: '工单卡片更新时间标签',
    pattern: /更新：/,
    fixed: /t\('details\.updatedLabel'\)/,
    zhKey: 'details.updatedLabel',
    enKey: 'details.updatedLabel',
  },
  {
    description: '回复数量标签',
    pattern: /条回复/,
    fixed: /t\('details\.repliesCount'\)/,
    zhKey: 'details.repliesCount',
    enKey: 'details.repliesCount',
  },
  {
    description: '工单详情标题',
    pattern: />\s*工单详情\s*</,
    fixed: /t\('details\.title'\)/,
    zhKey: 'details.title',
    enKey: 'details.title',
  },
  {
    description: '创建时间标签',
    pattern: /创建时间：/,
    fixed: /t\('details\.createdAtLabel'\)/,
    zhKey: 'details.createdAtLabel',
    enKey: 'details.createdAtLabel',
  },
  {
    description: '最后更新标签',
    pattern: /最后更新：/,
    fixed: /t\('details\.lastUpdatedLabel'\)/,
    zhKey: 'details.lastUpdatedLabel',
    enKey: 'details.lastUpdatedLabel',
  },
  {
    description: '问题描述标题',
    pattern: /问题描述/,
    fixed: /t\('details\.description'\)/,
    zhKey: 'details.description',
    enKey: 'details.description',
  },
  {
    description: '回复记录标题',
    pattern: /回复记录/,
    fixed: /t\('details\.replies'\)/,
    zhKey: 'details.replies',
    enKey: 'details.replies',
  },
  {
    description: '客服标签',
    pattern: /客服/,
    fixed: /t\('details\.staff'\)/,
    zhKey: 'details.staff',
    enKey: 'details.staff',
  },
  {
    description: '添加回复标题',
    pattern: /添加回复/,
    fixed: /t\('details\.addReply'\)/,
    zhKey: 'details.addReply',
    enKey: 'details.addReply',
  },
  {
    description: '回复占位符',
    pattern: /请输入您的回复内容\.\.\./,
    fixed: /t\('details\.replyPlaceholder'\)/,
    zhKey: 'details.replyPlaceholder',
    enKey: 'details.replyPlaceholder',
  },
  {
    description: '发送回复按钮',
    pattern: /(?<!\/\/ )发送回复(?!\s*$)/,
    fixed: /t\('details\.sendReply'\)/,
    zhKey: 'details.sendReply',
    enKey: 'details.sendReply',
  },
  {
    description: '关闭工单按钮',
    pattern: /(?<!\/\/ )关闭工单(?!\s*$)/,
    fixed: /t\('details\.closeTicket'\)/,
    zhKey: 'details.closeTicket',
    enKey: 'details.closeTicket',
  },
  {
    description: '加载详情文本',
    pattern: /加载工单详情\.\.\./,
    fixed: /t\('details\.loadingDetails'\)/,
    zhKey: 'details.loadingDetails',
    enKey: 'details.loadingDetails',
  },
];

let allFixed = true;

issues.forEach((issue, index) => {
  console.log(`${index + 1}. ${issue.description}:`);

  // 检查是否还有硬编码的中文
  const hasHardcoded = issue.pattern.test(componentContent);

  // 检查是否使用了翻译函数
  const hasTranslation = issue.fixed.test(componentContent);

  // 检查翻译文件中是否有对应的键
  const zhValue = getNestedValue(zhTrans, issue.zhKey);
  const enValue = getNestedValue(enTrans, issue.enKey);

  if (hasHardcoded) {
    console.log('   ❌ 仍有硬编码中文文本');
    allFixed = false;
  } else if (!hasTranslation) {
    console.log('   ❌ 未使用翻译函数');
    allFixed = false;
  } else if (!zhValue || !enValue) {
    console.log('   ❌ 翻译文件中缺少对应键值');
    console.log(`      中文: ${zhValue || '缺失'}`);
    console.log(`      英文: ${enValue || '缺失'}`);
    allFixed = false;
  } else {
    console.log('   ✅ 已修复');
    console.log(`      中文: ${zhValue}`);
    console.log(`      英文: ${enValue}`);
  }
  console.log('');
});

// 辅助函数：获取嵌套对象的值
function getNestedValue(obj, key) {
  return key.split('.').reduce((o, k) => o && o[k], obj);
}

console.log('=== 修复结果总结 ===');
if (allFixed) {
  console.log('✅ 所有翻译问题已修复！');
  console.log('现在在英文环境下，所有票务管理界面的文本都应该显示为英文。');
} else {
  console.log('❌ 仍有问题需要修复');
}

console.log('\n=== 额外检查 ===');
console.log('1. 日期格式化已更新为根据语言环境动态选择');
console.log('2. 所有用户界面文本已替换为翻译函数调用');
console.log('3. 翻译文件已添加所有必要的键值对');

console.log('\n=== 测试建议 ===');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 访问: http://localhost:3001/en/ticket-management');
console.log('3. 测试创建工单、查看详情、发送回复等功能');
console.log('4. 切换语言验证翻译效果');
console.log('5. 检查所有对话框、按钮、状态标签的文本显示');
