import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { paymentMethodSchema } from '@/types/rates';

// PUT /api/admin/payment-methods/[id] - 更新支付方式
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = paymentMethodSchema.parse(body);

    const existingPaymentMethod = await prisma.paymentMethod.findUnique({
      where: { id },
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { success: false, message: '支付方式不存在' },
        { status: 404 },
      );
    }

    // 检查名称是否与其他记录冲突
    const duplicatePaymentMethod = await prisma.paymentMethod.findFirst({
      where: {
        name: validatedData.name,
        id: { not: id },
      },
    });

    if (duplicatePaymentMethod) {
      return NextResponse.json(
        { success: false, message: '支付方式名称已存在' },
        { status: 400 },
      );
    }

    const updatedPaymentMethod = await prisma.paymentMethod.update({
      where: { id },
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: updatedPaymentMethod,
      message: '支付方式更新成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('更新支付方式失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}

// DELETE /api/admin/payment-methods/[id] - 删除支付方式
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;

    // 检查支付方式是否存在
    const existingPaymentMethod = await prisma.paymentMethod.findUnique({
      where: { id },
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { success: false, message: '支付方式不存在' },
        { status: 404 },
      );
    }

    // 删除支付方式
    await prisma.paymentMethod.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: '支付方式删除成功',
    });
  } catch (error) {
    console.error('删除支付方式失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}
