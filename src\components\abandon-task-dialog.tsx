'use client';

import { Al<PERSON><PERSON>riangle, XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useAbandonTask } from '@/hooks/use-abandon-task';

interface AbandonTaskDialogProps {
  children?: React.ReactNode;
  taskId: string;
  deposit?: number;
  onConfirm?: () => void;
}

export function AbandonTaskDialog({
  children,
  taskId,
  deposit = 0,
  onConfirm,
}: AbandonTaskDialogProps) {
  const t = useTranslations('my-accepted-tasks');
  const [isOpen, setIsOpen] = useState(false);
  const abandonTaskMutation = useAbandonTask();

  const handleConfirm = async () => {
    try {
      await abandonTaskMutation.mutateAsync(taskId);

      // 调用父组件的确认处理函数
      onConfirm?.();

      setIsOpen(false);
    } catch (error) {
      // 错误处理已在hook中完成
    }
  };

  const handleCancel = () => {
    if (!abandonTaskMutation.isPending) {
      setIsOpen(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2 text-red-600'>
            <AlertTriangle className='h-5 w-5' />
            {t('dialog.abandonTask.title')}
          </DialogTitle>
          <DialogDescription className='text-left'>
            {t('dialog.abandonTask.description')}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 警告提示 */}
          <div className='p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950 dark:border-red-800'>
            <div className='flex items-start gap-3'>
              <AlertTriangle className='h-5 w-5 text-red-600 mt-0.5 flex-shrink-0' />
              <div className='space-y-2'>
                <h4 className='font-medium text-red-800 dark:text-red-300'>
                  {t('dialog.abandonTask.warning')}
                </h4>
                <div className='text-sm text-red-700 dark:text-red-400 space-y-1'>
                  <p>• {t('dialog.abandonTask.irreversible')}</p>
                  <p>
                    •{' '}
                    {t('dialog.abandonTask.depositLoss', {
                      amount: (deposit || 0).toFixed(2),
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 确认信息 */}
          <div className='p-4 bg-muted/50 rounded-lg'>
            <div className='text-sm text-muted-foreground'>
              <p className='mb-2'>
                {t('dialog.abandonTask.consequences.title')}
              </p>
              <ul className='list-disc list-inside space-y-1 ml-2'>
                <li>{t('dialog.abandonTask.consequences.admit')}</li>
                <li>{t('dialog.abandonTask.consequences.compensation')}</li>
                <li>{t('dialog.abandonTask.consequences.return')}</li>
              </ul>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className='flex gap-3 pt-2'>
            <Button
              type='button'
              variant='outline'
              onClick={handleCancel}
              disabled={abandonTaskMutation.isPending}
              className='flex-1'
            >
              {t('dialog.abandonTask.cancel')}
            </Button>
            <Button
              type='button'
              variant='outline'
              onClick={handleConfirm}
              disabled={abandonTaskMutation.isPending}
              className='flex-1 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300'
            >
              {abandonTaskMutation.isPending ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2'></div>
                  {t('dialog.abandonTask.processing')}
                </>
              ) : (
                <>
                  <XCircle className='h-4 w-4 mr-1' />
                  {t('dialog.abandonTask.confirm')}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
