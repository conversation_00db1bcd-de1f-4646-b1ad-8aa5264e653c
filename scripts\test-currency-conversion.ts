#!/usr/bin/env tsx

import { currencyConverter } from '../src/lib/payment/currency-converter';

async function testCurrencyConversion() {
  console.log('🧪 Testing Currency Conversion...\n');

  try {
    // 测试 USD 到 CNY 转换
    console.log('1. Testing USD to CNY conversion:');
    const result1 = await currencyConverter.convertUSDToCNY(100);
    console.log(
      `   $${result1.originalAmount} USD = ¥${result1.convertedAmount} CNY`
    );
    console.log(`   Exchange Rate: ${result1.exchangeRate}`);
    console.log(`   Source: ${result1.source}\n`);

    // 测试缓存
    console.log('2. Testing cache (second conversion):');
    const result2 = await currencyConverter.convertUSDToCNY(50);
    console.log(
      `   $${result2.originalAmount} USD = ¥${result2.convertedAmount} CNY`
    );
    console.log(`   Exchange Rate: ${result2.exchangeRate}`);
    console.log(`   Source: ${result2.source}\n`);

    // 测试相同货币
    console.log('3. Testing same currency conversion:');
    const result3 = await currencyConverter.convertCurrency(100, 'USD', 'USD');
    console.log(
      `   $${result3.originalAmount} USD = $${result3.convertedAmount} USD`
    );
    console.log(`   Exchange Rate: ${result3.exchangeRate}`);
    console.log(`   Source: ${result3.source}\n`);

    // 测试小数金额
    console.log('4. Testing decimal amounts:');
    const result4 = await currencyConverter.convertUSDToCNY(123.45);
    console.log(
      `   $${result4.originalAmount} USD = ¥${result4.convertedAmount} CNY`
    );
    console.log(`   Exchange Rate: ${result4.exchangeRate}\n`);

    // 显示缓存信息
    console.log('5. Cache information:');
    const cacheInfo = currencyConverter.getCacheInfo();
    cacheInfo.forEach(item => {
      console.log(
        `   ${item.key}: Rate ${item.rate.rate}, Source: ${item.rate.source}, Expires: ${new Date(item.rate.expiresAt).toLocaleString()}`
      );
    });

    console.log('\n✅ All tests completed successfully!');
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// 运行测试
testCurrencyConversion();
