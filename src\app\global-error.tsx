'use client';

import { AlertTriangle, RefreshCw } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Global error handling component
 * Catches errors in the root layout, including errors in layout.tsx
 * Must define its own <html> and <body> tags
 */
export default function GlobalError({ error, reset }: GlobalErrorProps) {
  React.useEffect(() => {
    // Log critical errors to monitoring service
    console.error('Global application error:', error);

    // Error monitoring service integration can be added here, such as Sentry
    // Sentry.captureException(error);
  }, [error]);

  return (
    <html lang='en'>
      <body>
        <div className='flex min-h-screen items-center justify-center bg-gray-50 p-4 dark:bg-gray-900'>
          <div className='w-full max-w-md text-center'>
            <div className='mb-6'>
              <AlertTriangle className='mx-auto h-20 w-20 text-red-500' />
            </div>

            <h1 className='mb-4 text-3xl font-bold text-gray-900 dark:text-gray-100'>
              System Error
            </h1>

            <p className='mb-6 text-gray-600 dark:text-gray-400'>
              The application encountered a serious error. We have logged this issue, please try again later.
            </p>

            {/* Show error details in development environment */}
            {process.env.NODE_ENV === 'development' && (
              <div className='mb-6 rounded-lg bg-red-50 p-4 text-left dark:bg-red-900/20'>
                <h3 className='mb-2 font-semibold text-red-800 dark:text-red-200'>
                  Global Error Details:
                </h3>
                <pre className='text-xs text-red-700 dark:text-red-300 overflow-auto max-h-32'>
                  {error.message}
                </pre>
                <pre className='mt-2 text-xs text-red-600 dark:text-red-400 overflow-auto max-h-32'>
                  {error.stack}
                </pre>
                {error.digest && (
                  <p className='mt-2 text-xs text-red-600 dark:text-red-400'>
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}

            <div className='flex flex-col gap-3'>
              <Button
                onClick={reset}
                variant='default'
                className='flex items-center justify-center gap-2'
              >
                <RefreshCw className='h-4 w-4' />
                Reload Application
              </Button>

              <Button
                onClick={() => (window.location.href = '/')}
                variant='outline'
                className='flex items-center justify-center gap-2'
              >
                Return to Home
              </Button>
            </div>

            {/* Contact support information */}
            <div className='mt-8 rounded-lg bg-gray-100 p-4 dark:bg-gray-800'>
              <p className='text-sm text-gray-600 dark:text-gray-400'>
                If the problem persists, please contact our technical support team.
              </p>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
