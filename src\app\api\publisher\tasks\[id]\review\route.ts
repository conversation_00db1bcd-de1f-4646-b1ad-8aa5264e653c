import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import {
  sendTaskReviewApprovedAccepterEmail,
  sendTaskReviewRejectedAccepterEmail,
} from '@/lib/email';
import { logisticsService } from '@/lib/logistics-service';
import { getTaskCommission } from '@/lib/utils/commission';

// 审核请求验证Schema
const reviewSchema = z.object({
  approved: z.boolean(),
  rejectReason: z.string().optional(),
});

// 发布者审核API
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;
    const userId = session.user.id;

    // 2. 解析请求数据
    const body = await request.json();
    const validation = reviewSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 },
      );
    }

    const { approved, rejectReason } = validation.data;

    // 3. 查找委托并验证
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 4. 验证用户权限
    if (task.publisherId !== userId) {
      return NextResponse.json(
        { error: '只能审核自己发布的委托' },
        { status: 403 },
      );
    }

    // 5. 检查委托状态
    if (task.status !== ('PENDING_REVIEW' as any)) {
      return NextResponse.json(
        { error: '只能审核等待审核状态的委托' },
        { status: 400 },
      );
    }

    // 6. 验证拒绝原因
    if (!approved && !rejectReason) {
      return NextResponse.json(
        { error: '审核不通过时必须提供拒绝原因' },
        { status: 400 },
      );
    }

    // 7. 更新委托状态和处理物流注册
    const now = new Date();
    if (approved) {
      // 审核通过，进入等待收货状态，设置30天确认收货期限
      const deliveryDeadline = new Date(
        now.getTime() + 30 * 24 * 60 * 60 * 1000,
      ); // 30天后

      // 更新委托状态
      await prisma.$executeRaw`
        UPDATE tasks 
        SET status = 'PENDING_DELIVERY', 
            "reviewedAt" = ${now}, 
            "reviewRejectReason" = NULL,
            "deliveryDeadline" = ${deliveryDeadline}
        WHERE id = ${taskId}
      `;

      // 如果有物流单号，自动注册到17TRACK
      if (task.trackingNumber) {
        try {
          console.log(
            '审核通过，开始注册物流单号到17TRACK:',
            task.trackingNumber,
          );

          const logisticsData = {
            taskId: task.id,
            trackingNumber: task.trackingNumber,
            carrierCode: undefined, // 让17TRACK自动检测
            orderNumber: task.orderNumber || undefined,
            orderTime: task.createdAt.toISOString().split('T')[0], // YYYY-MM-DD 格式
          };

          const registerResult =
            await logisticsService.registerTracking(logisticsData);

          if (registerResult.success) {
            console.log('物流单号注册成功:', task.trackingNumber);
          } else {
            console.error('物流单号注册失败:', registerResult.error);
            // 注册失败不影响审核流程，只记录错误
          }
        } catch (error) {
          console.error('注册物流单号时发生错误:', error);
          // 注册失败不影响审核流程，只记录错误
        }
      }
    } else {
      // 审核不通过，返回进行中状态，清除订单和物流信息
      await prisma.$executeRaw`
        UPDATE tasks 
        SET status = 'IN_PROGRESS', 
            "reviewedAt" = ${now}, 
            "reviewRejectReason" = ${rejectReason},
            "orderNumber" = NULL, 
            "orderScreenshot" = NULL,
            "trackingNumber" = NULL, 
            "logisticsScreenshots" = '{}',
            "logisticsDeadline" = NULL,
            "logisticsReviewDeadline" = NULL
        WHERE id = ${taskId}
      `;
    }

    // 获取更新后的委托信息（使用原生SQL获取审核字段）
    const reviewData = await prisma.$queryRaw<
      Array<{
        id: string;
        status: string;
        reviewedAt: Date | null;
        reviewRejectReason: string | null;
      }>
    >`
      SELECT id, status, "reviewedAt", "reviewRejectReason"
      FROM tasks
      WHERE id = ${taskId}
    `;

    const taskData = reviewData[0];

    // 8. 发送邮件通知接单者
    try {
      const accepterEmail = task.accepter?.email;
      if (accepterEmail && task.accepter) {
        // 获取平台和分类信息
        const [
          platform,
          category,
          systemRate,
          chargebackTypes,
          paymentMethods,
        ] = await Promise.all([
          prisma.platform.findUnique({ where: { id: task.platformId } }),
          prisma.category.findUnique({ where: { id: task.categoryId } }),
          prisma.systemRate.findFirst(),
          prisma.chargebackType.findMany(),
          prisma.paymentMethod.findMany(),
        ]);

        if (approved) {
          // 审核通过邮件
          const taskWithEvidenceStatus = {
            ...task,
            evidenceStatus: task.evidenceStatus?.toString(),
          };

          const estimatedCommission = systemRate
            ? getTaskCommission(
                taskWithEvidenceStatus,
                chargebackTypes,
                paymentMethods,
                systemRate,
              )
            : task.unitPrice * task.quantity * 0.1; // 默认10%酬金

          const deliveryDeadline = new Date(
            now.getTime() + 30 * 24 * 60 * 60 * 1000,
          );

          await sendTaskReviewApprovedAccepterEmail(accepterEmail, {
            accepterName: task.accepter.name || '用户',
            accepterEmail,
            taskId: task.id,
            platform: platform?.name || '未知平台',
            category: category?.name || '未知分类',
            quantity: task.quantity,
            unitPrice: task.unitPrice,
            totalAmount: task.unitPrice * task.quantity,
            reviewedAt: now.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false,
            }),
            orderNumber: task.orderNumber || undefined,
            trackingNumber: task.trackingNumber || undefined,
            deliveryDeadline: deliveryDeadline.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            }),
            estimatedCommission,
          });
        } else {
          // 审核驳回邮件
          const resubmitDeadline = new Date(
            now.getTime() + 24 * 60 * 60 * 1000,
          ); // 24小时后

          await sendTaskReviewRejectedAccepterEmail(accepterEmail, {
            accepterName: task.accepter.name || '用户',
            accepterEmail,
            taskId: task.id,
            platform: platform?.name || '未知平台',
            category: category?.name || '未知分类',
            quantity: task.quantity,
            unitPrice: task.unitPrice,
            totalAmount: task.unitPrice * task.quantity,
            reviewedAt: now.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false,
            }),
            rejectReason: rejectReason || '未提供具体原因',
            orderNumber: task.orderNumber || undefined,
            trackingNumber: task.trackingNumber || undefined,
            resubmitDeadline: resubmitDeadline.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            }),
          });
        }
      }
    } catch (emailError) {
      console.error('发送审核邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    // 9. 返回成功响应
    return NextResponse.json({
      success: true,
      message: approved
        ? '审核通过！委托进入等待确认收货阶段'
        : '审核不通过，接单者需要重新提交订单和物流信息',
      data: {
        task: {
          id: taskData.id,
          status: taskData.status,
          reviewedAt: taskData.reviewedAt?.toISOString(),
          reviewRejectReason: taskData.reviewRejectReason,
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
