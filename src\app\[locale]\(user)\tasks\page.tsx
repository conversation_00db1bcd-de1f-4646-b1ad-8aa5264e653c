'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { LazyTasksPageContent } from '@/components/lazy-components';
import { UserPageLayout } from '@/components/user-page-layout';

export default function TasksPage() {
  const t = useTranslations('tasks');

  useEffect(() => {
    document.title = `${t('title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('title')}
      breadcrumbPage={t('breadcrumb')}
      href='/tasks'
      description={t('description')}
    >
      <LazyTasksPageContent />
    </UserPageLayout>
  );
}
