'use client';

import { XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@/i18n/navigation';

export default function PaymentCancelPage() {
  const t = useTranslations('payment');

  return (
    <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <XCircle className='h-16 w-16 text-red-500 mx-auto mb-4' />
          <CardTitle className='text-2xl text-red-600'>{t('cancel.title')}</CardTitle>
        </CardHeader>
        <CardContent className='text-center space-y-4'>
          <p className='text-muted-foreground'>
            {t('cancel.description')}
          </p>
          <div className='flex gap-4 justify-center'>
            <Button asChild>
              <Link href='/dashboard'>{t('cancel.backToHome')}</Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href='/payment'>{t('cancel.retryPayment')}</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
