'use client';

import { Home, ArrowLeft, Search } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

import { Button } from '@/components/ui/button';

/**
 * 404 页面组件
 * 当用户访问不存在的页面时显示
 */
export default function NotFound() {
  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-lg text-center'>
        {/* 404 图标/插图 */}
        <div className='mb-8'>
          <div className='mx-auto flex h-32 w-32 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800'>
            <span className='text-4xl font-bold text-gray-400 dark:text-gray-600'>
              404
            </span>
          </div>
        </div>

        {/* 标题和描述 */}
        <h1 className='mb-4 text-3xl font-bold text-gray-900 dark:text-gray-100'>
          页面未找到
        </h1>

        <p className='mb-8 text-gray-600 dark:text-gray-400'>
          抱歉，您访问的页面不存在或已被移动。请检查URL是否正确，或使用下面的链接导航到其他页面。
        </p>

        {/* 操作按钮 */}
        <div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
          <Button asChild variant='default' className='flex items-center gap-2'>
            <Link href='/'>
              <Home className='h-4 w-4' />
              返回首页
            </Link>
          </Button>

          <Button
            asChild
            variant='outline'
            className='flex items-center gap-2'
            onClick={() => window.history.back()}
          >
            <button>
              <ArrowLeft className='h-4 w-4' />
              返回上页
            </button>
          </Button>
        </div>

        {/* 搜索建议 */}
        <div className='mt-8 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50'>
          <h3 className='mb-2 font-semibold text-gray-900 dark:text-gray-100'>
            您可能在寻找：
          </h3>
          <div className='flex flex-wrap justify-center gap-2 text-sm'>
            <Link
              href='/dashboard'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              用户仪表盘
            </Link>
            <span className='text-gray-400'>•</span>
            <Link
              href='/tasks'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              委托中心
            </Link>
            <span className='text-gray-400'>•</span>
            <Link
              href='/publish'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              发布委托
            </Link>
            <span className='text-gray-400'>•</span>
            <Link
              href='/profile'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              个人资料
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
