"use client";

import { <PERSON>, Twitter, Facebook, Instagram, Linkedin, Github } from "lucide-react";
import { useTranslations } from "next-intl";

import { FooterModeToggle } from "@/components/footer-mode-toggle";
import { Button } from "@/components/ui/button";

import { RefundGoLogo } from "./refund-go-logo";

export function Footer() {
  const t = useTranslations("HomePage");
  const productLinks = [
    { label: t("footer.product.features"), href: "/#features" },
    { label: t("footer.product.pricing"), href: "/#pricing" },
  ];

  const legalLinks = [
    { label: t("footer.legal.terms"), href: "/terms" },
    { label: t("footer.legal.privacy"), href: "/privacy" },
  ];

  const socialLinks = [
    { icon: Twitter, href: "https://twitter.com/refundgo", label: "Twitter" },
    { icon: Facebook, href: "https://facebook.com/refundgo", label: "Facebook" },
    { icon: Instagram, href: "https://instagram.com/refundgo", label: "Instagram" },
    { icon: Linkedin, href: "https://linkedin.com/company/refundgo", label: "LinkedIn" },
    { icon: Github, href: "https://github.com/refundgo", label: "GitHub" },
  ];

  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <div className="mb-6">
              <RefundGoLogo
                variant="static"
                size="md"
                animated={false}
                showTagline={true}
                forceWhiteText={true}
                className="bg-transparent shadow-none"
              />
            </div>

            {/* Social Media Links and Theme Toggle */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white hover:bg-gray-800 dark:hover:bg-gray-900"
                  asChild
                >
                  <a href={social.href} target="_blank" rel="noopener noreferrer" aria-label={social.label}>
                    <social.icon className="w-5 h-5" />
                  </a>
                </Button>
              ))}

              {/* Theme Toggle */}
              <div className="ml-4 pl-4 border-l border-gray-700 dark:border-gray-600">
                <FooterModeToggle />
              </div>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{t("footer.product.title")}</h4>
            <ul className="space-y-3">
              {productLinks.map((link, index) => (
                <li key={index}>
                  <a href={link.href} className="text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 transition-colors duration-200">
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{t("footer.legal.title")}</h4>
            <ul className="space-y-3">
              {legalLinks.map((link, index) => (
                <li key={index}>
                  <a href={link.href} className="text-gray-300 dark:text-gray-400 hover:text-white dark:hover:text-gray-200 transition-colors duration-200">
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="border-t border-gray-800 dark:border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 dark:text-gray-500 mb-4 md:mb-0">{t("footer.copyright")}</div>
            <div className="flex items-center space-x-4 text-gray-400 dark:text-gray-500">
              <span className="flex items-center">
                {t("footer.madeWith")} <Heart className="w-4 h-4 mx-1 text-red-500" /> {t("footer.by")}
              </span>
              <span className="hidden md:inline">•</span>
              <span>{t("footer.globalService")}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
