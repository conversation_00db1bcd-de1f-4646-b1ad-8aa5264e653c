import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

// 白名单检测结果类型
export interface WhitelistCheckResult {
  isWhitelisted: boolean;
  platform?: string;
  matchedShop?: {
    id: string;
    name: string;
    url: string;
  };
  extractedShop?: {
    shopName: string;
    shopUrl: string;
  };
  message: string;
}

// 白名单检测输入类型
interface WhitelistCheckInput {
  productUrl: string;
}

// 白名单检测hook
export function useWhitelistCheck() {
  return useMutation({
    mutationFn: async (
      data: WhitelistCheckInput,
    ): Promise<WhitelistCheckResult> => {
      const response = await fetch('/api/whitelist/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '白名单检测失败');
      }

      return response.json();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
