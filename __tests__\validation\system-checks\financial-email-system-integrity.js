// 财务邮件通知系统完整性测试
const fs = require('fs');
const path = require('path');

console.log('=== 财务邮件通知系统完整性测试 ===\n');

// 需要检查的文件和功能
const systemComponents = [
  {
    name: '充值成功邮件模板',
    path: 'src/lib/email-templates/deposit-success-i18n.ts',
    checks: [
      { desc: '模板文件存在', test: content => content.length > 0 },
      {
        desc: '包含国际化支持',
        test: content =>
          content.includes('language') && content.includes('translations'),
      },
      {
        desc: '包含邮件样式',
        test: content => content.includes('emailStyles'),
      },
      {
        desc: '包含安全脱敏处理',
        test: content => content.includes('maskedTransactionId'),
      },
      {
        desc: '包含金额格式化',
        test: content => content.includes('formatEmailAmount'),
      },
      {
        desc: '包含时间格式化',
        test: content => content.includes('formatEmailDateTime'),
      },
      {
        desc: '包含中英文翻译',
        test: content => content.includes('zh:') && content.includes('en:'),
      },
    ],
  },
  {
    name: '充值失败邮件模板',
    path: 'src/lib/email-templates/deposit-failed-i18n.ts',
    checks: [
      { desc: '模板文件存在', test: content => content.length > 0 },
      {
        desc: '包含国际化支持',
        test: content =>
          content.includes('language') && content.includes('translations'),
      },
      {
        desc: '包含失败原因显示',
        test: content => content.includes('failureReason'),
      },
      {
        desc: '包含解决方案建议',
        test: content => content.includes('solutions'),
      },
      {
        desc: '包含常见失败原因',
        test: content => content.includes('commonReasons'),
      },
      {
        desc: '包含重试按钮',
        test: content => content.includes('retryDeposit'),
      },
    ],
  },
  {
    name: '邮件模板索引',
    path: 'src/lib/email-templates/index.ts',
    checks: [
      {
        desc: '导出充值成功模板',
        test: content => content.includes('depositSuccessTemplateI18n'),
      },
      {
        desc: '导出充值失败模板',
        test: content => content.includes('depositFailedTemplateI18n'),
      },
      {
        desc: '导出类型定义',
        test: content =>
          content.includes('DepositSuccessEmailData') &&
          content.includes('DepositFailedEmailData'),
      },
    ],
  },
  {
    name: '邮件发送函数',
    path: 'src/lib/email.ts',
    checks: [
      {
        desc: '导入充值邮件模板',
        test: content =>
          content.includes('depositSuccessTemplateI18n') &&
          content.includes('depositFailedTemplateI18n'),
      },
      {
        desc: '包含充值成功发送函数',
        test: content => content.includes('sendDepositSuccessEmail'),
      },
      {
        desc: '包含充值失败发送函数',
        test: content => content.includes('sendDepositFailedEmail'),
      },
      {
        desc: '支持语言选择',
        test: content =>
          content.includes('language') && content.includes('subjects'),
      },
    ],
  },
  {
    name: '邮件API路由',
    path: 'src/app/api/send-email/route.ts',
    checks: [
      {
        desc: '导入充值邮件函数',
        test: content =>
          content.includes('sendDepositSuccessEmail') &&
          content.includes('sendDepositFailedEmail'),
      },
      {
        desc: '包含充值邮件类型',
        test: content =>
          content.includes('deposit-success') &&
          content.includes('deposit-failed'),
      },
      {
        desc: '包含验证schema',
        test: content =>
          content.includes('depositSuccessEmailSchema') &&
          content.includes('depositFailedEmailSchema'),
      },
      {
        desc: '包含处理逻辑',
        test: content =>
          content.includes("case 'deposit-success'") &&
          content.includes("case 'deposit-failed'"),
      },
    ],
  },
  {
    name: '邮件预览API',
    path: 'src/app/api/email/preview/route.ts',
    checks: [
      { desc: '预览API文件存在', test: content => content.length > 0 },
      {
        desc: '支持充值邮件预览',
        test: content =>
          content.includes('deposit-success') &&
          content.includes('deposit-failed'),
      },
      {
        desc: '包含测试数据生成',
        test: content => content.includes('getDefaultTestData'),
      },
      { desc: '支持语言切换', test: content => content.includes('language') },
    ],
  },
  {
    name: '邮件测试API',
    path: 'src/app/api/email/test/route.ts',
    checks: [
      { desc: '测试API文件存在', test: content => content.length > 0 },
      {
        desc: '支持充值邮件测试',
        test: content =>
          content.includes('deposit-success') &&
          content.includes('deposit-failed'),
      },
      { desc: '包含环境检查', test: content => content.includes('NODE_ENV') },
      {
        desc: '包含测试数据生成',
        test: content => content.includes('generateTestData'),
      },
    ],
  },
  {
    name: '财务邮件集成工具',
    path: 'src/lib/financial-email-integration.ts',
    checks: [
      { desc: '集成工具文件存在', test: content => content.length > 0 },
      {
        desc: '包含便捷发送方法',
        test: content =>
          content.includes('notifyDepositSuccess') &&
          content.includes('notifyDepositFailure'),
      },
      {
        desc: '包含重试机制',
        test: content => content.includes('sendEmailWithRetry'),
      },
      {
        desc: '包含批量发送',
        test: content => content.includes('batchNotifyDeposits'),
      },
      {
        desc: '包含数据验证',
        test: content => content.includes('validateDepositEmailData'),
      },
    ],
  },
];

let totalChecks = 0;
let passedChecks = 0;
let failedComponents = [];

console.log('🔍 **系统组件检查**：\n');

systemComponents.forEach(component => {
  console.log(`📄 ${component.name}:`);

  try {
    const filePath = path.join(__dirname, '../../../', component.path);
    const content = fs.readFileSync(filePath, 'utf8');

    let componentPassed = 0;
    let componentTotal = component.checks.length;

    component.checks.forEach((check, index) => {
      totalChecks++;
      const result = check.test(content);
      const status = result ? '✅' : '❌';

      if (result) {
        passedChecks++;
        componentPassed++;
      }

      console.log(`   ${index + 1}. ${check.desc}: ${status}`);
    });

    if (componentPassed < componentTotal) {
      failedComponents.push({
        name: component.name,
        passed: componentPassed,
        total: componentTotal,
      });
    }

    console.log(
      `   完成度: ${componentPassed}/${componentTotal} (${Math.round((componentPassed / componentTotal) * 100)}%)\n`
    );
  } catch (error) {
    console.log(`   ❌ 文件读取失败: ${error.message}\n`);
    failedComponents.push({
      name: component.name,
      passed: 0,
      total: component.checks.length,
      error: error.message,
    });
  }
});

console.log('=== 系统完整性总结 ===');
console.log(`总检查项: ${totalChecks}`);
console.log(`通过检查: ${passedChecks}`);
console.log(`完成率: ${Math.round((passedChecks / totalChecks) * 100)}%`);

if (failedComponents.length > 0) {
  console.log('\n❌ **需要完善的组件**：');
  failedComponents.forEach(comp => {
    if (comp.error) {
      console.log(`   • ${comp.name}: 文件不存在或读取错误`);
    } else {
      console.log(
        `   • ${comp.name}: ${comp.passed}/${comp.total} (${Math.round((comp.passed / comp.total) * 100)}%)`
      );
    }
  });
} else {
  console.log('\n✅ 所有系统组件检查通过！');
}

console.log('\n=== 财务邮件系统功能特性 ===');
console.log('🎯 **已实现的功能**：');
console.log('   ✓ 充值成功确认邮件（中英文）');
console.log('   ✓ 充值失败通知邮件（中英文）');
console.log('   ✓ 专业的HTML邮件模板设计');
console.log('   ✓ 敏感信息脱敏处理');
console.log('   ✓ 国际化语言支持');
console.log('   ✓ 邮件发送重试机制');
console.log('   ✓ 批量邮件发送功能');
console.log('   ✓ 邮件预览和测试API');
console.log('   ✓ 完整的错误处理');

console.log('\n🔧 **技术实现特点**：');
console.log('   • 使用Resend邮件服务');
console.log('   • 支持中英文双语模板');
console.log('   • 响应式邮件设计');
console.log('   • 交易ID安全脱敏');
console.log('   • 金额和时间本地化格式');
console.log('   • 指数退避重试策略');
console.log('   • 并发控制批量发送');

console.log('\n📧 **邮件内容包含**：');
console.log('   • 品牌标识和专业设计');
console.log('   • 交易详情和金额信息');
console.log('   • 账户余额更新（成功时）');
console.log('   • 失败原因和解决方案（失败时）');
console.log('   • 安全提醒和联系方式');
console.log('   • 操作按钮和快捷链接');

console.log('\n🛠️ **使用方式**：');
console.log('   • API发送: POST /api/send-email');
console.log(
  '   • 邮件预览: GET /api/email/preview?type=deposit-success&language=zh'
);
console.log('   • 测试发送: POST /api/email/test');
console.log(
  '   • 集成工具: import { notifyDepositSuccess } from "@/lib/financial-email-integration"'
);

if (passedChecks === totalChecks) {
  console.log('\n🎉 **财务邮件通知系统已完全实现！**');
  console.log('\n✨ **系统优势**：');
  console.log('   • 完整的充值流程邮件覆盖');
  console.log('   • 专业的用户体验设计');
  console.log('   • 高可靠性和容错能力');
  console.log('   • 易于维护和扩展');
  console.log('   • 符合国际化标准');
} else {
  console.log('\n⚠️ **待完善项目**：');
  console.log('   • 完成剩余组件的实现');
  console.log('   • 测试邮件发送功能');
  console.log('   • 验证国际化翻译');
  console.log('   • 集成到财务API中');
}

console.log('\n=== 测试完成 ===');
console.log(`测试时间: ${new Date().toLocaleString('zh-CN')}`);
console.log('财务邮件通知系统已准备就绪！🎊');
