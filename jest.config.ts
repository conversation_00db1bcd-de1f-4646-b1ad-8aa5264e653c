import type { Config } from 'jest';
import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
const config: Config = {
  // Use v8 for better performance and coverage reports
  coverageProvider: 'v8',

  // Use jsdom environment for DOM testing
  testEnvironment: 'jsdom',

  // Setup files to run after Jest is configured
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],

  // Coverage configuration
  collectCoverageFrom: [
    '**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!<rootDir>/out/**',
    '!<rootDir>/.next/**',
    '!<rootDir>/*.config.js',
    '!<rootDir>/*.config.ts',
    '!<rootDir>/coverage/**',
    '!<rootDir>/public/**',
    '!<rootDir>/prisma/**',
    '!<rootDir>/src/middleware.ts',
  ],

  // Test file patterns
  testMatch: [
    '<rootDir>/**/__tests__/unit/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/**/__tests__/integration/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/**/*.(test|spec).{js,jsx,ts,tsx}',
  ],

  // Exclude test utilities from being run as tests
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
    '<rootDir>/out/',
    '<rootDir>/src/__tests__/utils/',
    '<rootDir>/__tests__/fixtures/',
    '<rootDir>/__tests__/validation/',
    '<rootDir>/__tests__/e2e/',
  ],

  // Module name mapping for absolute imports and assets
  moduleNameMapper: {
    // Handle CSS imports (with CSS modules)
    '^.+\\.module\\.(css|sass|scss)$': 'identity-obj-proxy',

    // Handle CSS imports (without CSS modules)
    '^.+\\.(css|sass|scss)$': '<rootDir>/__mocks__/styleMock.js',

    // Handle image imports
    '^.+\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i':
      '<rootDir>/__mocks__/fileMock.js',

    // Handle module aliases (match your tsconfig.json paths)
    '^@/(.*)$': '<rootDir>/src/$1',

    // Handle @next/font
    '@next/font/(.*)': '<rootDir>/__mocks__/nextFontMock.js',
    // Handle next/font
    'next/font/(.*)': '<rootDir>/__mocks__/nextFontMock.js',

    // Disable server-only
    'server-only': '<rootDir>/__mocks__/empty.js',
  },

  // Transform ignore patterns
  transformIgnorePatterns: [
    '/node_modules/',
    '^.+\\.module\\.(css|sass|scss)$',
  ],

  // Test timeout
  testTimeout: 10000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
export default createJestConfig(config);
