import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

export async function POST() {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 2. 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    // 3. 检查是否是免费版用户
    if (user.memberPlan === 'FREE') {
      return NextResponse.json(
        { success: false, message: '免费版用户无需续费' },
        { status: 400 },
      );
    }

    // 4. 获取对应的套餐信息
    const planNameMap = {
      PRO: '专业版',
      BUSINESS: '商业版',
    };

    const planName = planNameMap[user.memberPlan as keyof typeof planNameMap];

    if (!planName) {
      return NextResponse.json(
        { success: false, message: '未知的会员套餐类型' },
        { status: 400 },
      );
    }

    const plan = await prisma.membershipPlan.findFirst({
      where: {
        name: planName,
        isActive: true,
      },
    });

    if (!plan) {
      return NextResponse.json(
        { success: false, message: '对应的套餐配置不存在' },
        { status: 400 },
      );
    }

    // 5. 返回支付信息，引导用户进行支付
    return NextResponse.json({
      success: true,
      message: `请完成支付以续费${plan.name}`,
      data: {
        planId: plan.id,
        planName: plan.name,
        price: plan.price,
        action: 'renew',
        needPayment: true,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
