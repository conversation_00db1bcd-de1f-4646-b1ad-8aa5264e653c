import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 删除白名单条目
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const { id } = await params;

    // 查找白名单条目并验证权限
    const whitelistItem = await prisma.shopWhitelist.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    });

    if (!whitelistItem) {
      return NextResponse.json(
        { error: '白名单条目不存在或无权限操作' },
        { status: 404 },
      );
    }

    // 真删除：完全从数据库中移除
    await prisma.shopWhitelist.delete({
      where: { id },
    });

    return NextResponse.json({
      message: '白名单条目已删除',
    });
  } catch (error) {
    console.error('删除白名单失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
