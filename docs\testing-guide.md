# Testing Guide

This project uses Jest and <PERSON>act Testing Library for unit testing and integration testing.

## Table of Contents

- [Quick Start](#quick-start)
- [Test Structure](#test-structure)
- [Writing Tests](#writing-tests)
- [Best Practices](#best-practices)
- [Common Tools](#common-tools)
- [Mock Strategies](#mock-strategies)
- [CI/CD Integration](#cicd-integration)

## Quick Start

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage

# Run tests in CI mode
npm run test:ci
```

### Test File Naming Conventions

- Unit tests: `*.test.ts` or `*.test.tsx`
- Integration tests: `*.integration.test.ts`
- Test files should be placed in `__tests__` directory or alongside source files

## Test Structure

```
src/
├── __tests__/
│   └── utils/
│       └── test-utils.tsx          # Test utility functions
├── components/
│   ├── ui/
│   │   └── __tests__/
│   │       ├── button.test.tsx     # UI component tests
│   │       └── card.test.tsx
│   └── __tests__/
│       └── task-list.test.tsx      # Business component tests
├── lib/
│   └── __tests__/
│       └── utils.test.ts           # Utility function tests
└── app/
    └── (main)/
        └── __tests__/
            └── page.test.tsx       # Page component tests
```

## Writing Tests

### Basic Test Structure

```tsx
import { render, screen } from '@/src/__tests__/utils/test-utils';
import { ComponentName } from '../component-name';

describe('ComponentName', () => {
  it('should render correctly', () => {
    render(<ComponentName />);

    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Testing UI Components

```tsx
import { render, screen } from '@/src/__tests__/utils/test-utils';
import userEvent from '@testing-library/user-event';
import { Button } from '../button';

describe('Button Component', () => {
  it('handles click events', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();

    render(<Button onClick={handleClick}>Click me</Button>);

    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Testing Business Components

```tsx
import { render, screen } from '@/src/__tests__/utils/test-utils';
import { TaskList } from '../task-list';
import { mockTask } from '@/src/__tests__/utils/test-utils';

describe('TaskList Component', () => {
  it('renders tasks correctly', () => {
    const tasks = [mockTask];
    render(<TaskList tasks={tasks} />);

    expect(screen.getByText(mockTask.platform)).toBeInTheDocument();
  });
});
```

## Best Practices

### 1. Test User Behavior, Not Implementation Details

```tsx
// ❌ Testing implementation details
expect(component.state.isLoading).toBe(true);

// ✅ Testing user-visible behavior
expect(screen.getByText('Loading...')).toBeInTheDocument();
```

### 2. Use Semantic Queries

```tsx
// Priority order
screen.getByRole('button', { name: /submit/i });
screen.getByLabelText(/username/i);
screen.getByPlaceholderText(/enter username/i);
screen.getByText(/welcome/i);
screen.getByTestId('submit-button'); // Last choice
```

### 3. Test Edge Cases

```tsx
describe('TaskList', () => {
  it('handles empty state', () => {
    render(<TaskList tasks={[]} />);
    expect(screen.getByText('No tasks available')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<TaskList tasks={[]} loading={true} />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
});
```

### 4. 使用自定义渲染函数

```tsx
// 使用项目的自定义 render 函数，包含所有 Provider
import { render } from '@/src/__tests__/utils/test-utils';
```

## 常用工具

### 测试工具函数

```tsx
// 等待异步操作完成
import { waitFor } from '@testing-library/react';

await waitFor(() => {
  expect(screen.getByText('Data loaded')).toBeInTheDocument();
});

// 用户交互
import userEvent from '@testing-library/user-event';

const user = userEvent.setup();
await user.click(button);
await user.type(input, 'text');
```

### Mock 数据

```tsx
import { mockTask, mockUser } from '@/src/__tests__/utils/test-utils';

// 使用预定义的 mock 数据
const tasks = [mockTask, { ...mockTask, id: 'task-2' }];
```

## Mock 策略

### 1. Mock 外部依赖

```tsx
// Mock API 调用
jest.mock('@/hooks/use-tasks', () => ({
  useTasks: () => ({
    data: [mockTask],
    isLoading: false,
    error: null,
  }),
}));
```

### 2. Mock Next.js 功能

```tsx
// Mock next/router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/',
    query: {},
  }),
}));
```

### 3. Mock 第三方库

```tsx
// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    button: 'button',
  },
}));
```

## 测试覆盖率

### 覆盖率目标

- 语句覆盖率：> 80%
- 分支覆盖率：> 75%
- 函数覆盖率：> 80%
- 行覆盖率：> 80%

### 查看覆盖率报告

```bash
npm run test:coverage
open coverage/lcov-report/index.html
```

## CI/CD 集成

### GitHub Actions 配置

```yaml
- name: Run tests
  run: npm run test:ci

- name: Upload coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

## 调试测试

### 调试技巧

```tsx
// 查看渲染的 DOM
screen.debug();

// 查看特定元素
screen.debug(screen.getByRole('button'));

// 使用 logRoles 查看可用角色
import { logRoles } from '@testing-library/dom';
logRoles(container);
```

### 常见问题

1. **测试超时**：增加 `testTimeout` 配置
2. **异步操作**：使用 `waitFor` 或 `findBy*` 查询
3. **Mock 不生效**：检查 mock 的导入顺序和位置

## 参考资源

- [Jest 官方文档](https://jestjs.io/)
- [React Testing Library 文档](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Library 最佳实践](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
