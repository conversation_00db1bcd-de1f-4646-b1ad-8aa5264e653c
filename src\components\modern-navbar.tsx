'use client';

import { motion } from 'framer-motion';
import {
  Globe,
  Sparkles,
  Target,
  Play,
  Award,
  Crown,
  MessageCircle,
  Menu,
  X,
  ArrowRight,
  BarChart3,
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import { useState, useEffect } from 'react';

import { LanguageSwitcher } from '@/components/language-switcher';
import { RefundGoLogoHomepage } from '@/components/refund-go-logo';
import { Button } from '@/components/ui/button';
import UserAccountNav from '@/components/UserAccountNav';
import { Link, useRouter } from '@/i18n/navigation';

interface ModernNavbarProps {
  showMenuItems?: boolean
}

export function ModernNavbar({ showMenuItems = true }: ModernNavbarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const { theme } = useTheme();
  const t = useTranslations('Navigation');
  const router = useRouter();
  const { data: session } = useSession();

  // 监听滚动状态
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const menuItems = [
    { key: 'features', href: '#features', label: t('features'), icon: Sparkles },
    { key: 'process', href: '#process', label: t('process'), icon: Target },
    { key: 'tutorial', href: '#tutorial', label: t('tutorial'), icon: Play },
    { key: 'tasks', href: '#tasks', label: t('tasks'), icon: Award },
    { key: 'pricing', href: '#pricing', label: t('pricing'), icon: Crown },
    { key: 'faq', href: '#faq', label: t('faq'), icon: MessageCircle },
  ];

  const handleMenuClick = (href: string) => {
    setIsOpen(false);
    // For legal pages, navigate to homepage with anchor
    if (href.startsWith('#')) {
      router.push(`/${href}`);
    } else {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.8 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled
          ? 'bg-nav-bg-scrolled/95 dark:bg-nav-bg-scrolled/95 backdrop-blur-lg border-b border-nav-border/50 dark:border-nav-border/50 shadow-lg'
          : 'bg-nav-bg/80 dark:bg-nav-bg/80 backdrop-blur-md'
      }`}
    >
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex items-center justify-between h-16'>
          {/* Logo */}
          <motion.div
            className='flex items-center h-full max-h-12 overflow-hidden'
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <Link href='/' className="flex items-center h-full">
              <RefundGoLogoHomepage
                variant="navbar"
                className="bg-transparent shadow-none hover:shadow-xl"
                darkMode={theme === 'dark'}
              />
            </Link>
          </motion.div>

          {/* 桌面端菜单 */}
          {showMenuItems && (
            <div className='hidden lg:flex items-center space-x-1'>
              {menuItems.map((item, index) => (
                <motion.button
                  key={item.key}
                  type='button'
                  onClick={() => handleMenuClick(item.href)}
                  className='flex items-center px-4 py-2 rounded-lg text-nav-text dark:text-nav-text hover:text-nav-text-hover dark:hover:text-nav-text-hover hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg transition-all duration-200 group'
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <item.icon className='h-4 w-4 mr-2 group-hover:text-nav-text-hover dark:group-hover:text-nav-text-hover transition-colors' />
                  {item.label}
                </motion.button>
              ))}
            </div>
          )}

          {/* 桌面端右侧按钮 */}
          <div className='hidden md:flex items-center space-x-3'>
            <LanguageSwitcher
              variant='button'
              size='sm'
              className='hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg transition-colors'
            />
            {session?.user ? (
              <div className='flex items-center space-x-3'>
                <Button
                  variant='outline'
                  size='sm'
                  className='border-nav-border dark:border-nav-border hover:border-nav-text-hover dark:hover:border-nav-text-hover hover:text-nav-text-hover dark:hover:text-nav-text-hover transition-all'
                  onClick={() => router.push('/dashboard')}
                >
                  <BarChart3 className='h-4 w-4 mr-2' />
                  {t('dashboard')}
                </Button>
                <UserAccountNav session={session} />
              </div>
            ) : (
              <div className='flex items-center space-x-3'>
                <Button
                  variant='ghost'
                  size='sm'
                  className='hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg transition-colors'
                  onClick={() => router.push('/sign-in')}
                >
                  {t('signIn')}
                </Button>
                <Button
                  size='sm'
                  className='bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to hover:from-gradient-primary-from/90 hover:to-gradient-primary-to/90 text-white shadow-lg hover:shadow-xl transition-all duration-200'
                  onClick={() => router.push('/sign-up')}
                >
                  {t('signUp')}
                  <ArrowRight className='h-4 w-4 ml-2' />
                </Button>
              </div>
            )}
          </div>

          {/* 移动端菜单按钮 */}
          <div className='md:hidden'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setIsOpen(!isOpen)}
              className='min-h-[44px] min-w-[44px] p-2 hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg transition-colors'
              aria-label={isOpen ? 'Close menu' : 'Open menu'}
            >
              <motion.div
                animate={{ rotate: isOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                {isOpen ? (
                  <X className='h-6 w-6' />
                ) : (
                  <Menu className='h-6 w-6' />
                )}
              </motion.div>
            </Button>
          </div>
        </div>

        {/* 移动端菜单 */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className='md:hidden border-t border-nav-border/50 dark:border-nav-border/50 bg-nav-bg/95 dark:bg-nav-bg/95 backdrop-blur-lg'
          >
            <div className='py-4 space-y-2'>
              {showMenuItems && menuItems.map((item, index) => (
                <motion.button
                  key={item.key}
                  type='button'
                  onClick={() => handleMenuClick(item.href)}
                  className='flex items-center w-full px-4 py-3 text-left rounded-lg hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg transition-colors duration-200 group'
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: index * 0.05 }}
                >
                  <item.icon className='h-5 w-5 mr-3 text-nav-text dark:text-nav-text group-hover:text-nav-text-hover dark:group-hover:text-nav-text-hover transition-colors' />
                  <span className='group-hover:text-nav-text-hover dark:group-hover:text-nav-text-hover transition-colors'>{item.label}</span>
                </motion.button>
              ))}

              <div className='border-t border-nav-border/50 dark:border-nav-border/50 pt-4 mt-4 space-y-3'>
                <LanguageSwitcher
                  variant='button'
                  size='sm'
                  className='w-full justify-start hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg'
                />
                {session?.user ? (
                  <div className='space-y-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      className='w-full justify-start border-nav-border dark:border-nav-border'
                      onClick={() => {
                        setIsOpen(false);
                        router.push('/dashboard');
                      }}
                    >
                      <BarChart3 className='h-4 w-4 mr-2' />
                      {t('dashboard')}
                    </Button>
                  </div>
                ) : (
                  <div className='space-y-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      className='w-full justify-start hover:bg-nav-hover-bg dark:hover:bg-nav-hover-bg'
                      onClick={() => {
                        setIsOpen(false);
                        router.push('/sign-in');
                      }}
                    >
                      {t('signIn')}
                    </Button>
                    <Button
                      size='sm'
                      className='w-full bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white'
                      onClick={() => {
                        setIsOpen(false);
                        router.push('/sign-up');
                      }}
                    >
                      {t('signUp')}
                      <ArrowRight className='h-4 w-4 ml-2' />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.nav>
  );
}
