import { Member<PERSON><PERSON> } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { getUserWhitelistSlots } from '@/lib/utils/membership';

// 创建白名单的验证schema
const createWhitelistSchema = z.object({
  shopName: z.string().min(1, '店铺名称不能为空'),
  shopUrl: z.string().url('请输入有效的店铺链接'),
  platform: z.string().min(1, '请选择平台'),
});

// 获取用户白名单列表
// 翻译消息
const messages = {
  zh: {
    loginRequired: '请先登录',
    serverError: '服务器内部错误',
    invalidData: '请求数据格式错误',
    quotaInsufficient: '白名单名额不足，请升级会员套餐获得更多名额',
    shopExists: '该店铺已在白名单中',
    createSuccess: '白名单创建成功',
  },
  en: {
    loginRequired: 'Please login first',
    serverError: 'Internal server error',
    invalidData: 'Invalid request data format',
    quotaInsufficient:
      'Insufficient whitelist quota, please upgrade membership plan for more quota',
    shopExists: 'This shop is already in the whitelist',
    createSuccess: 'Whitelist created successfully',
  },
};

export async function GET(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t.loginRequired }, { status: 401 });
    }

    // 获取用户的白名单列表（真删除后不需要过滤isActive）
    const whitelistItems = await prisma.shopWhitelist.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // 获取用户信息和会员计划
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        memberPlan: true,
      },
    });

    // 获取用户会员套餐的白名单店铺名额
    const whitelistSlots = await getUserWhitelistSlots(
      user?.memberPlan || 'FREE',
    );
    const whitelistSlotsUsed = whitelistItems.length;
    const availableSlots = whitelistSlots - whitelistSlotsUsed;

    return NextResponse.json({
      whitelistItems,
      membership: {
        tier: user?.memberPlan || 'FREE',
        whitelistSlots,
        whitelistSlotsUsed,
        availableSlots,
      },
    });
  } catch (error) {
    console.error('获取白名单失败:', error);
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}

// 创建白名单条目
export async function POST(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t.loginRequired }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const validation = createWhitelistSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: t.invalidData,
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { shopName, shopUrl, platform } = validation.data;

    // 检查用户的白名单限制
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        memberPlan: true,
      },
    });

    // 获取用户会员套餐的白名单店铺名额
    const whitelistSlots = await getUserWhitelistSlots(
      user?.memberPlan || 'FREE',
    );
    const currentWhitelistCount = await prisma.shopWhitelist.count({
      where: {
        userId: session.user.id,
      },
    });

    if (currentWhitelistCount >= whitelistSlots) {
      return NextResponse.json({ error: t.quotaInsufficient }, { status: 400 });
    }

    // 检查是否已存在相同的店铺
    const existingWhitelist = await prisma.shopWhitelist.findFirst({
      where: {
        userId: session.user.id,
        shopUrl,
      },
    });

    if (existingWhitelist) {
      return NextResponse.json({ error: t.shopExists }, { status: 400 });
    }

    // 创建白名单条目（默认为待审核状态）
    const whitelistItem = await prisma.shopWhitelist.create({
      data: {
        userId: session.user.id,
        shopName,
        shopUrl,
        platform,
        isActive: true, // 记录存在且活跃，但需要审核通过才真正生效
      },
    });

    return NextResponse.json({
      message: t.createSuccess,
      whitelistItem,
    });
  } catch (error) {
    console.error('创建白名单失败:', error);
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}
