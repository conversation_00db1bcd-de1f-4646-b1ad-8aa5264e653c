'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import {
  CrispConfig,
  LocationData,
  UserBehaviorData,
  MessageType,
  MessageStrategy,
  ContextInfo,
  CrispMessageContext,
  AIMessageRequest,
  AIMessageResponse,
} from '@/types/crisp';

export function CrispChat() {
  const [config, setConfig] = useState<CrispConfig | null>(null);
  const { data: session } = useSession();
  const [locationData, setLocationData] = useState<LocationData | null>(null);

  // 获取IP地址和地理位置信息
  useEffect(() => {
    if (!config?.enabled) return;

    const getLocationData = async () => {
      try {
        const response = await fetch('/api/ip-location');
        const data = await response.json();
        console.log('Location data received:', data);

        // 检查响应是否包含错误
        if (data.error) {
          console.warn('IP location API returned error:', data.error);
          return;
        }

        setLocationData(data);
      } catch (error) {
        console.warn('Failed to get location data:', error);
      }
    };

    getLocationData();
  }, [config]);

  // 获取Crisp配置
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/settings/crisp');
        const data = await response.json();
        setConfig(data);
      } catch (error) {
        console.error('Failed to fetch Crisp config:', error);
        setConfig({ enabled: false, websiteId: '' });
      }
    };

    fetchConfig();
  }, []);

  // 加载Crisp脚本
  useEffect(() => {
    if (!config) return;

    if (
      config.enabled &&
      config.websiteId &&
      typeof window !== 'undefined' &&
      !window.$crisp
    ) {
      window.CRISP_WEBSITE_ID = config.websiteId;

      // 动态加载Crisp脚本
      (function () {
        const d = document;
        const s = d.createElement('script');
        s.src = 'https://client.crisp.chat/l.js';
        s.async = true;
        d.getElementsByTagName('head')[0].appendChild(s);
      })();
    }
  }, [config]);

  // 设置访客和用户信息
  useEffect(() => {
    if (!config?.enabled || typeof window === 'undefined') return;

    const checkCrispAndSetInfo = () => {
      if (typeof window.$crisp !== 'undefined') {
        try {
          // 设置登录用户信息
          if (session?.user) {
            if (session.user.email) {
              window.$crisp.push(['set', 'user:email', session.user.email]);
            }

            if (session.user.name) {
              window.$crisp.push(['set', 'user:nickname', session.user.name]);
            }

            if (session.user.image) {
              window.$crisp.push(['set', 'user:avatar', session.user.image]);
            }

            if (session.user.role) {
              window.$crisp.push([
                'set',
                'user:company',
                {
                  name: 'RefundGo',
                  employment: [session.user.role],
                  website: window.location.origin,
                },
              ]);
            }
          }

          // 设置地理位置信息
          if (locationData) {
            setLocationDataInCrisp(locationData);
          }

          // 设置用户业务信息
          if (session?.user) {
            setUserBusinessInfo(session.user);
          }

          // 设置设备和浏览器信息
          setDeviceInfo();

          // 执行智能消息策略
          executeIntelligentMessagingStrategy({
            user: session?.user,
            locationData: locationData || undefined,
            crispInstance: window.$crisp,
          }).catch(error => {
            console.error(
              'Error executing intelligent messaging strategy:',
              error,
            );
          });

          return true;
        } catch (e) {
          console.error('Error setting Crisp info:', e);
          return false;
        }
      }
      return false;
    };

    // 初始尝试设置
    const initialTimeout = setTimeout(() => {
      if (!checkCrispAndSetInfo()) {
        let retryCount = 0;
        const maxRetries = 3;
        const retryInterval = setInterval(() => {
          retryCount++;
          if (checkCrispAndSetInfo() || retryCount >= maxRetries) {
            clearInterval(retryInterval);
          }
        }, 2000);
      }
    }, 1000);

    return () => clearTimeout(initialTimeout);
  }, [config, session, locationData]);

  return null;
}

// 设置地理位置信息到Crisp
function setLocationDataInCrisp(locationData: LocationData) {
  if (!window.$crisp) return;

  try {
    // 基础地理信息
    if (locationData.ip) {
      window.$crisp.push([
        'set',
        'session:data',
        ['IP_Address', locationData.ip],
      ]);
    }

    const fullLocation = [
      locationData.city,
      locationData.region,
      locationData.country,
    ]
      .filter(Boolean)
      .join(', ');

    if (fullLocation) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Full_Address', fullLocation],
      ]);
    }

    if (locationData.country) {
      const countryDisplay = locationData.emoji_flag
        ? `${locationData.emoji_flag} ${locationData.country}`
        : locationData.country;
      window.$crisp.push(['set', 'session:data', ['Country', countryDisplay]]);
    }

    if (locationData.city) {
      window.$crisp.push(['set', 'session:data', ['City', locationData.city]]);
    }

    if (locationData.latitude && locationData.longitude) {
      window.$crisp.push([
        'set',
        'session:data',
        [
          'Coordinates',
          `${locationData.latitude.toFixed(4)}, ${locationData.longitude.toFixed(4)}`,
        ],
      ]);
    }

    if (locationData.postal) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Postal_Code', locationData.postal],
      ]);
    }

    if (locationData.calling_code) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Calling_Code', locationData.calling_code],
      ]);
    }

    // 时区和货币信息
    if (locationData.timezone) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Timezone', locationData.timezone],
      ]);
    }

    if (locationData.time_zone?.current_time) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Local_Time', locationData.time_zone.current_time],
      ]);
    } else if (locationData.timezone) {
      try {
        const localTime = new Date().toLocaleString('zh-CN', {
          timeZone: locationData.timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        });
        window.$crisp.push(['set', 'session:data', ['Local_Time', localTime]]);
      } catch (e) {
        // 时区转换失败，忽略
      }
    }

    if (locationData.currency) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Local_Currency', locationData.currency],
      ]);
    }

    // 网络服务商信息
    if (locationData.company) {
      if (locationData.company.name) {
        window.$crisp.push([
          'set',
          'session:data',
          ['ISP_Name', locationData.company.name],
        ]);
      }

      if (locationData.company.type) {
        window.$crisp.push([
          'set',
          'session:data',
          ['Network_Type', locationData.company.type],
        ]);
      }

      if (locationData.company.domain) {
        window.$crisp.push([
          'set',
          'session:data',
          ['ISP_Domain', locationData.company.domain],
        ]);
      }
    }

    if (locationData.asn) {
      if (locationData.asn.asn && locationData.asn.name) {
        window.$crisp.push([
          'set',
          'session:data',
          ['ASN_Info', `${locationData.asn.asn} - ${locationData.asn.name}`],
        ]);
      }
    }

    // 网络安全信息
    if (locationData.threat) {
      const securityFlags: string[] = [];
      if (locationData.threat.is_vpn) securityFlags.push('VPN');
      if (locationData.threat.is_proxy) securityFlags.push('代理');
      if (locationData.threat.is_tor) securityFlags.push('Tor');
      if (locationData.threat.is_datacenter) securityFlags.push('数据中心');
      if (locationData.threat.is_icloud_relay) securityFlags.push('iCloud中继');

      if (securityFlags.length > 0) {
        window.$crisp.push([
          'set',
          'session:data',
          ['Network_Features', securityFlags.join(', ')],
        ]);
      }

      if (
        locationData.threat.is_threat ||
        locationData.threat.is_known_attacker ||
        locationData.threat.is_known_abuser
      ) {
        const threatFlags: string[] = [];
        if (locationData.threat.is_threat) threatFlags.push('Threat IP');
        if (locationData.threat.is_known_attacker)
          threatFlags.push('Known Attacker');
        if (locationData.threat.is_known_abuser)
          threatFlags.push('Known Abuser');

        window.$crisp.push([
          'set',
          'session:data',
          ['Security_Warning', threatFlags.join(', ')],
        ]);
      }

      if (locationData.threat.scores?.trust_score !== undefined) {
        const trustScore = locationData.threat.scores.trust_score;
        const trustLevel =
          trustScore >= 80 ? 'High' : trustScore >= 60 ? 'Medium' : 'Low';
        window.$crisp.push([
          'set',
          'session:data',
          ['Trust_Score', `${trustScore}/100 (${trustLevel})`],
        ]);
      }
    }
  } catch (err) {
    console.warn('Failed to set location data in Crisp:', err);
  }
}

// 设置用户业务信息
function setUserBusinessInfo(user: any) {
  if (!window.$crisp) return;

  try {
    // 钱包余额
    if (user.balance !== undefined && user.balance !== null) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Wallet_Balance', `￥${user.balance}`],
      ]);
    }

    // 用户角色
    if (user.role) {
      window.$crisp.push(['set', 'session:data', ['User_Role', user.role]]);
    }

    // 注册时间
    if (user.createdAt) {
      const registerDate = new Date(user.createdAt).toLocaleDateString('zh-CN');
      window.$crisp.push([
        'set',
        'session:data',
        ['Registration_Date', registerDate],
      ]);
    }
  } catch (err) {
    console.warn('Failed to set user business info in Crisp:', err);
  }
}

// 设置设备和浏览器信息
function setDeviceInfo() {
  if (!window.$crisp) return;

  try {
    // 浏览器语言
    if (navigator.language) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Browser_Language', navigator.language],
      ]);
    }

    // 屏幕分辨率
    if (screen.width && screen.height) {
      window.$crisp.push([
        'set',
        'session:data',
        ['Screen_Resolution', `${screen.width}x${screen.height}`],
      ]);
    }

    // 用户代理信息
    if (navigator.userAgent) {
      // 检测操作系统
      let os = 'Unknown';
      if (navigator.userAgent.includes('Windows')) os = 'Windows';
      else if (navigator.userAgent.includes('Mac')) os = 'macOS';
      else if (navigator.userAgent.includes('Linux')) os = 'Linux';
      else if (navigator.userAgent.includes('Android')) os = 'Android';
      else if (navigator.userAgent.includes('iOS')) os = 'iOS';

      window.$crisp.push(['set', 'session:data', ['Operating_System', os]]);

      // 检测浏览器
      let browser = 'Unknown';
      if (
        navigator.userAgent.includes('Chrome') &&
        !navigator.userAgent.includes('Edg')
      )
        browser = 'Chrome';
      else if (navigator.userAgent.includes('Firefox')) browser = 'Firefox';
      else if (
        navigator.userAgent.includes('Safari') &&
        !navigator.userAgent.includes('Chrome')
      )
        browser = 'Safari';
      else if (navigator.userAgent.includes('Edg')) browser = 'Edge';

      window.$crisp.push(['set', 'session:data', ['Browser', browser]]);

      // 检测设备类型
      const isMobile =
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent,
        );
      window.$crisp.push([
        'set',
        'session:data',
        ['Device_Type', isMobile ? 'Mobile' : 'Desktop'],
      ]);
    }

    // 时区偏移
    const timezoneOffset = new Date().getTimezoneOffset();
    const offsetHours = Math.abs(timezoneOffset / 60);
    const offsetSign = timezoneOffset <= 0 ? '+' : '-';
    window.$crisp.push([
      'set',
      'session:data',
      ['Timezone_Offset', `UTC${offsetSign}${offsetHours}`],
    ]);

    // 当前会话信息
    window.$crisp.push([
      'set',
      'session:data',
      ['Session_Start', new Date().toLocaleString('zh-CN')],
    ]);

    window.$crisp.push([
      'set',
      'session:data',
      ['Current_Page', window.location.pathname],
    ]);

    if (document.referrer) {
      try {
        const referrerUrl = new URL(document.referrer);
        window.$crisp.push([
          'set',
          'session:data',
          ['Referrer_Page', referrerUrl.hostname + referrerUrl.pathname],
        ]);
      } catch (e) {
        window.$crisp.push([
          'set',
          'session:data',
          ['Referrer_Page', document.referrer],
        ]);
      }
    }
  } catch (deviceError) {
    console.warn('Failed to set device info in Crisp:', deviceError);
  }
}

// 智能消息策略执行函数
async function executeIntelligentMessagingStrategy(
  context: CrispMessageContext,
) {
  const { user, locationData, crispInstance } = context;

  if (!crispInstance) return;

  // 获取或初始化用户行为数据
  const behaviorData = getUserBehaviorData(user?.id || 'anonymous');

  // 执行频率控制检查
  if (!shouldSendMessage(behaviorData)) {
    return;
  }

  // 计算用户参与度分数
  const engagementScore = calculateEngagementScore(behaviorData);

  // 确定用户生命周期阶段
  const lifecycleStage = determineUserLifecycleStage(behaviorData, user);

  // 获取上下文信息
  const contextInfo = getContextualInformation(locationData);

  // 选择最优消息策略
  const messageStrategy = selectOptimalMessageStrategy({
    behaviorData,
    engagementScore,
    lifecycleStage,
    contextInfo,
    user,
  });

  // 生成个性化消息
  const message = await generatePersonalizedMessage(messageStrategy);

  // 计算最优发送时机
  const optimalDelay = calculateOptimalSendTime(
    behaviorData,
    contextInfo,
    messageStrategy,
  );

  // 延迟发送消息
  setTimeout(() => {
    if (typeof window.$crisp !== 'undefined') {
      window.$crisp.push(['do', 'message:show', ['text', message]]);

      // 更新用户行为数据
      updateUserBehaviorData(user?.id || 'anonymous', {
        messagesSent: behaviorData.messagesSent + 1,
        lastMessageTime: Date.now(),
      });
    }
  }, optimalDelay);
}

// 获取用户行为数据
function getUserBehaviorData(userId: string): UserBehaviorData {
  const storageKey = `user_behavior_${userId}`;
  const stored = localStorage.getItem(storageKey);

  if (stored) {
    return JSON.parse(stored);
  }

  // 初始化新用户数据
  const initialData: UserBehaviorData = {
    visitCount: 1,
    lastVisitTime: Date.now(),
    totalTimeSpent: 0,
    pageViews: 1,
    engagementScore: 0,
    messagesSent: 0,
    lastMessageTime: 0,
    userSegment: 'new',
  };

  localStorage.setItem(storageKey, JSON.stringify(initialData));
  return initialData;
}

// 频率控制算法
function shouldSendMessage(behaviorData: UserBehaviorData): boolean {
  const now = Date.now();
  const timeSinceLastMessage = now - behaviorData.lastMessageTime;

  // 基础频率控制：24小时内最多1条消息
  const minInterval = 24 * 60 * 60 * 1000;

  if (timeSinceLastMessage < minInterval) {
    return false;
  }

  // 动态频率控制：基于用户参与度调整
  const engagementMultiplier = Math.max(
    0.5,
    behaviorData.engagementScore / 100,
  );
  const adjustedInterval = minInterval / engagementMultiplier;

  return timeSinceLastMessage >= adjustedInterval;
}

// 计算用户参与度分数
function calculateEngagementScore(behaviorData: UserBehaviorData): number {
  let score = 0;
  const weights = {
    visitFrequency: 0.3,
    sessionQuality: 0.25,
    interactionDepth: 0.25,
    temporalPattern: 0.2,
  };

  // 访问频率分析
  const visitScore = Math.min(30, behaviorData.visitCount * 3);
  score += visitScore * weights.visitFrequency;

  // 会话质量评估
  const avgSessionTime =
    behaviorData.totalTimeSpent / Math.max(1, behaviorData.visitCount);
  const sessionQualityScore =
    Math.min(25, avgSessionTime / (1000 * 60 * 2)) * 25;
  score += sessionQualityScore * weights.sessionQuality;

  // 交互深度评估
  const interactionRatio =
    behaviorData.messagesSent / Math.max(1, behaviorData.visitCount);
  const depthScore = Math.min(
    25,
    behaviorData.pageViews * 2 + interactionRatio * 15,
  );
  score += depthScore * weights.interactionDepth;

  // 时间模式
  const daysSinceFirst = behaviorData.lastVisitTime
    ? Math.max(
        1,
        (Date.now() - behaviorData.lastVisitTime) / (1000 * 60 * 60 * 24),
      )
    : 1;
  const recencyScore = Math.max(0, 20 - daysSinceFirst * 2);
  score += recencyScore * weights.temporalPattern;

  return Math.min(100, Math.round(score));
}

// 确定用户生命周期阶段
function determineUserLifecycleStage(
  behaviorData: UserBehaviorData,
  user?: any,
): string {
  const daysSinceFirstVisit =
    (Date.now() - behaviorData.lastVisitTime) / (24 * 60 * 60 * 1000);

  const metrics = {
    visitFrequency: behaviorData.visitCount,
    engagementLevel: behaviorData.engagementScore,
    recency: daysSinceFirstVisit,
    interactionQuality:
      behaviorData.messagesSent / Math.max(1, behaviorData.visitCount),
    sessionDepth: behaviorData.pageViews / Math.max(1, behaviorData.visitCount),
  };

  if (metrics.visitFrequency === 1) {
    return 'new';
  }

  if (metrics.visitFrequency <= 3 && metrics.recency <= 7) {
    return 'returning';
  }

  if (
    metrics.engagementLevel >= 70 &&
    metrics.visitFrequency >= 5 &&
    metrics.interactionQuality > 0.3
  ) {
    return 'engaged';
  }

  if (
    metrics.recency > 21 ||
    (metrics.recency > 7 && metrics.engagementLevel < 30)
  ) {
    return 'at_risk';
  }

  if (metrics.engagementLevel >= 50 && metrics.visitFrequency >= 3) {
    return 'engaged';
  }

  return 'returning';
}

// 获取上下文信息
function getContextualInformation(locationData?: LocationData): ContextInfo {
  const now = new Date();
  const hour = now.getHours();
  const isWeekend = now.getDay() === 0 || now.getDay() === 6;
  const isMobile =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );

  return {
    hour,
    isWeekend,
    isMobile,
    country: locationData?.country || '',
    city: locationData?.city || '',
    timezone: locationData?.timezone || '',
  };
}

// 选择最优消息策略
function selectOptimalMessageStrategy(params: {
  behaviorData: UserBehaviorData;
  engagementScore: number;
  lifecycleStage: string;
  contextInfo: ContextInfo;
  user?: any;
}): MessageStrategy {
  const { behaviorData, engagementScore, lifecycleStage, contextInfo, user } =
    params;

  // 基础策略矩阵
  const strategyMatrix = {
    new: {
      messageType: MessageType.WELCOME,
      priority: 'high' as const,
      timing: 'immediate' as const,
      personalization: 'high' as const,
    },
    returning: {
      messageType: MessageType.FEATURE_HIGHLIGHT,
      priority: 'medium' as const,
      timing: 'contextual' as const,
      personalization: 'medium' as const,
    },
    engaged: {
      messageType: MessageType.MILESTONE_CELEBRATION,
      priority: 'high' as const,
      timing: 'optimal' as const,
      personalization: 'high' as const,
    },
    at_risk: {
      messageType: MessageType.RE_ENGAGEMENT,
      priority: 'urgent' as const,
      timing: 'immediate' as const,
      personalization: 'maximum' as const,
    },
    dormant: {
      messageType: MessageType.SUPPORT_OFFER,
      priority: 'critical' as const,
      timing: 'immediate' as const,
      personalization: 'maximum' as const,
    },
  };

  // 获取基础策略
  const baseStrategy =
    strategyMatrix[lifecycleStage as keyof typeof strategyMatrix] ||
    strategyMatrix.returning;

  // 应用上下文修改
  const contextualStrategy = applyContextualModifications(
    baseStrategy,
    contextInfo,
    behaviorData,
  );

  return {
    ...contextualStrategy,
    lifecycleStage,
    contextInfo,
    user,
    engagementScore,
  };
}

// 应用上下文修改
function applyContextualModifications(
  baseStrategy: any,
  contextInfo: ContextInfo,
  behaviorData: UserBehaviorData,
) {
  const modifiedStrategy = { ...baseStrategy };

  // 时间基础修改
  const hour = new Date().getHours();
  const isBusinessHours = hour >= 9 && hour <= 17;
  const isWeekend = new Date().getDay() === 0 || new Date().getDay() === 6;

  if (!isBusinessHours || isWeekend) {
    if (modifiedStrategy.timing === 'immediate') {
      modifiedStrategy.timing = 'delayed';
    }
  }

  // 设备基础修改
  if (contextInfo.isMobile) {
    modifiedStrategy.messageFormat = 'mobile_optimized';
    modifiedStrategy.maxLength = 80;
  } else {
    modifiedStrategy.messageFormat = 'desktop_standard';
    modifiedStrategy.maxLength = 150;
  }

  // 行为模式修改
  const interactionRatio =
    behaviorData.messagesSent / Math.max(1, behaviorData.visitCount);
  if (interactionRatio > 0.5) {
    modifiedStrategy.communicationStyle = 'conversational';
  } else {
    modifiedStrategy.communicationStyle = 'informational';
  }

  return modifiedStrategy;
}

// 生成个性化消息
async function generatePersonalizedMessage(
  strategy: MessageStrategy,
): Promise<string> {
  const { messageType, lifecycleStage, contextInfo, user, engagementScore } =
    strategy;

  try {
    // 准备AI API请求数据
    const requestData: AIMessageRequest = {
      userBehavior: {
        visitCount: getVisitCount(),
        timeOnPage: getTimeOnPage(),
        pageDepth: getPageDepth(),
        lastVisit: getLastVisit(),
        messageInteractions: getMessageInteractions(),
        engagementScore: engagementScore || 0,
        lifecycleStage: lifecycleStage || 'new',
      },
      context: {
        currentTime: new Date().toISOString(),
        userLocation:
          `${contextInfo?.city || ''}, ${contextInfo?.country || ''}`.trim(),
        deviceType: contextInfo?.isMobile ? 'mobile' : 'desktop',
        pageUrl: typeof window !== 'undefined' ? window.location.href : '',
        referrer: typeof document !== 'undefined' ? document.referrer : '',
      },
      messageType,
      businessInfo: {
        businessName: 'RefundGo',
        industry: 'Task Platform & Gig Economy',
        primaryGoal: 'user_engagement',
      },
      language: '中文',
      tone: determineTone(lifecycleStage || 'new', engagementScore || 0),
    };

    // 调用AI API生成个性化消息
    const response = await fetch('/api/ai/generate-personalized-message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (response.ok) {
      const result: AIMessageResponse = await response.json();
      if (result.success && result.message) {
        return result.message;
      }
    }
  } catch (error) {
    console.error('Error generating AI personalized message:', error);
  }

  // 回退到模板消息
  return generateFallbackMessage(strategy);
}

// 确定语调
function determineTone(
  lifecycleStage: string,
  engagementScore: number,
): string {
  if (lifecycleStage === 'new') return 'friendly';
  if (lifecycleStage === 'engaged') return 'professional';
  if (lifecycleStage === 'at_risk') return 'helpful';
  if (engagementScore > 70) return 'casual';
  return 'friendly';
}

// 回退消息生成
function generateFallbackMessage(strategy: MessageStrategy): string {
  const { messageType, lifecycleStage, contextInfo, user, engagementScore } =
    strategy;

  // 时间问候语
  let timeGreeting = '';
  if (contextInfo) {
    if (contextInfo.hour >= 5 && contextInfo.hour < 12) {
      timeGreeting = '早上好';
    } else if (contextInfo.hour >= 12 && contextInfo.hour < 14) {
      timeGreeting = '中午好';
    } else if (contextInfo.hour >= 14 && contextInfo.hour < 18) {
      timeGreeting = '下午好';
    } else if (contextInfo.hour >= 18 && contextInfo.hour < 22) {
      timeGreeting = '晚上好';
    } else {
      timeGreeting = '夜深了，您好';
    }
  } else {
    timeGreeting = '您好';
  }

  let message = '';

  switch (messageType) {
    case MessageType.WELCOME:
      if (user?.name) {
        message = `👋 ${timeGreeting}，${user.name}！欢迎来到 RefundGo！\n\n🎯 我是您的专属AI助手，帮助您在委托平台上发布委托或接取委托赚取酬金！`;
      } else {
        message = `👋 ${timeGreeting}！欢迎来到 RefundGo！\n\n🎯 我是您的专属AI助手，帮助您在委托平台上发布委托或接取委托赚取酬金！`;
      }
      break;

    case MessageType.RE_ENGAGEMENT:
      message = `👋 ${timeGreeting}！很高兴再次见到您！\n\n🚀 我们最近新增了更多高酬金委托，委托大厅里有很多优质机会等着您。需要我为您推荐一些吗？`;
      break;

    case MessageType.FEATURE_HIGHLIGHT:
      message = `👋 ${timeGreeting}！\n\n💡 看起来您对我们的平台很感兴趣！我可以为您介绍如何发布高效委托或接取优质委托来赚取酬金。`;
      break;

    case MessageType.MILESTONE_CELEBRATION:
      message = `🎉 ${timeGreeting}！\n\n感谢您一直以来对 RefundGo 的支持！作为我们的忠实用户，现在有更多委托机会和更高酬金等着您！`;
      break;

    case MessageType.SUPPORT_OFFER:
      message = `👋 ${timeGreeting}！\n\n我注意到您有一段时间没有访问了。委托大厅有很多新委托上线，快来看看有没有适合您的机会！`;
      break;

    default:
      message = `👋 ${timeGreeting}！有什么可以帮助您的吗？`;
  }

  // 添加地理位置个性化
  if (contextInfo?.country && contextInfo?.city) {
    if (contextInfo.country === 'China' || contextInfo.country === '中国') {
      message += `\n\n🌍 来自${contextInfo.city}的朋友，平台有适合您当地的委托机会！`;
    } else {
      message += `\n\n🌍 Welcome from ${contextInfo.city}, ${contextInfo.country}! We have tasks suitable for your region!`;
    }
  }

  // 添加设备优化建议
  if (contextInfo?.isMobile) {
    message += '\n\n📱 移动端也能轻松发布和管理委托，随时随地赚取酬金！';
  }

  message += '\n\n💬 有任何关于委托发布或接取的问题都可以随时咨询我！';

  return message;
}

// 计算最优发送时机
function calculateOptimalSendTime(
  behaviorData: UserBehaviorData,
  contextInfo: ContextInfo,
  strategy?: MessageStrategy,
): number {
  const baseDelays = {
    immediate: 1000,
    contextual: 3000,
    optimal: 5000,
    delayed: 8000,
    scheduled: 15000,
    periodic: 30000,
  };

  let delay =
    baseDelays[strategy?.timing as keyof typeof baseDelays] ||
    baseDelays.contextual;

  // 参与度基础时机优化
  const engagementScore = behaviorData.engagementScore;
  const engagementMultiplier = {
    high: 0.5,
    medium: 1.0,
    low: 1.5,
  };

  const engagementLevel =
    engagementScore >= 70 ? 'high' : engagementScore >= 40 ? 'medium' : 'low';
  delay *= engagementMultiplier[engagementLevel];

  // 行为模式调整
  const interactionHistory = behaviorData.messagesSent;
  if (interactionHistory > 3) {
    delay *= 0.7;
  } else if (interactionHistory === 0) {
    delay *= 1.3;
  }

  // 优先级基础调整
  const priorityMultipliers = {
    critical: 0.3,
    urgent: 0.5,
    high: 0.8,
    medium: 1.0,
    low: 1.5,
  };
  delay *=
    priorityMultipliers[
      strategy?.priority as keyof typeof priorityMultipliers
    ] || 1.0;

  // 设备特定优化
  if (contextInfo?.isMobile) {
    delay *= 0.8;
  }

  // 时间考虑
  const hour = new Date().getHours();
  if (hour < 9 || hour > 21) {
    delay *= 2.0;
  }

  return Math.max(500, Math.min(30000, Math.round(delay)));
}

// 更新用户行为数据
function updateUserBehaviorData(
  userId: string,
  updates: Partial<UserBehaviorData>,
  eventType?: string,
) {
  const storageKey = `user_behavior_${userId}`;
  const current = getUserBehaviorData(userId);
  const now = new Date();

  // 计算会话指标
  const sessionDuration = current.lastVisitTime
    ? now.getTime() - current.lastVisitTime
    : 0;

  // 高级行为跟踪
  const updated = {
    ...current,
    ...updates,
    lastVisitTime: now.getTime(),
    totalTimeSpent: (current.totalTimeSpent || 0) + sessionDuration,
  };

  // 跟踪行为模式
  if (eventType) {
    const behaviorEvents = (current as any).behaviorEvents || [];
    behaviorEvents.push({
      type: eventType,
      timestamp: now.toISOString(),
      sessionDuration,
      pageViews: updated.pageViews,
      engagementScore: updated.engagementScore,
    });

    // 只保留最近50个事件
    (updated as any).behaviorEvents = behaviorEvents.slice(-50);
  }

  // 计算高级指标
  (updated as any).avgSessionDuration =
    updated.totalTimeSpent / Math.max(1, updated.visitCount);
  (updated as any).bounceRate = calculateBounceRate(updated);
  (updated as any).conversionProbability =
    calculateConversionProbability(updated);

  // 重新计算用户分段
  updated.userSegment = determineUserLifecycleStage(updated) as any;

  localStorage.setItem(storageKey, JSON.stringify(updated));

  // 存储分析数据
  storeAnalyticsData(updated, eventType);
}

// 计算跳出率
function calculateBounceRate(behavior: UserBehaviorData): number {
  if (behavior.visitCount <= 1) return 0;
  const singlePageSessions = ((behavior as any).behaviorEvents || []).filter(
    (event: any) => event.pageViews <= 1,
  ).length;
  return singlePageSessions / behavior.visitCount;
}

// 计算转化概率
function calculateConversionProbability(behavior: UserBehaviorData): number {
  let probability = 0;

  // 参与度分数影响 (40%)
  const engagementScore = behavior.engagementScore;
  probability += (engagementScore / 100) * 0.4;

  // 访问频率影响 (25%)
  const visitScore = Math.min(1, behavior.visitCount / 10);
  probability += visitScore * 0.25;

  // 交互深度影响 (20%)
  const interactionScore = Math.min(1, behavior.messagesSent / 5);
  probability += interactionScore * 0.2;

  // 时间投入影响 (15%)
  const timeScore = Math.min(
    1,
    (behavior.totalTimeSpent || 0) / (30 * 60 * 1000),
  );
  probability += timeScore * 0.15;

  return Math.min(1, probability);
}

// 存储分析数据
function storeAnalyticsData(behavior: UserBehaviorData, eventType?: string) {
  try {
    const analyticsData = {
      timestamp: new Date().toISOString(),
      behavior,
      eventType,
      sessionId: sessionStorage.getItem('sessionId') || 'unknown',
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    };

    // 存储在会话存储中，用于潜在的批量上传
    const existingAnalytics = JSON.parse(
      sessionStorage.getItem('analyticsQueue') || '[]',
    );
    existingAnalytics.push(analyticsData);

    // 只保留最近100个分析事件
    const trimmedAnalytics = existingAnalytics.slice(-100);
    sessionStorage.setItem('analyticsQueue', JSON.stringify(trimmedAnalytics));
  } catch (error) {
    console.warn('Failed to store analytics data:', error);
  }
}

// 获取用户行为数据的辅助函数
function getVisitCount(): number {
  if (typeof window === 'undefined') return 1;
  const visits = localStorage.getItem('refundgo_visit_count');
  return visits ? parseInt(visits, 10) : 1;
}

function getTimeOnPage(): number {
  if (typeof window === 'undefined') return 0;
  const startTime = sessionStorage.getItem('refundgo_page_start');
  if (!startTime) {
    sessionStorage.setItem('refundgo_page_start', Date.now().toString());
    return 0;
  }
  return Math.floor((Date.now() - parseInt(startTime, 10)) / 1000);
}

function getPageDepth(): number {
  if (typeof window === 'undefined') return 1;
  const depth = sessionStorage.getItem('refundgo_page_depth');
  return depth ? parseInt(depth, 10) : 1;
}

function getLastVisit(): string {
  if (typeof window === 'undefined') return new Date().toISOString();
  const lastVisit = localStorage.getItem('refundgo_last_visit');
  return lastVisit || new Date().toISOString();
}

function getMessageInteractions(): number {
  if (typeof window === 'undefined') return 0;
  const interactions = localStorage.getItem('refundgo_message_interactions');
  return interactions ? parseInt(interactions, 10) : 0;
}

// 打开Crisp聊天窗口的方法
export function openCrispChat() {
  if (typeof window !== 'undefined' && window.$crisp) {
    window.$crisp.push(['do', 'chat:open']);
  } else {
    console.warn('Crisp chat system is not loaded yet');
  }
}
