'use client';

import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
  User,
  Crown,
  Wallet,
  Ticket,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { signOut } from 'next-auth/react';
import { useTranslations } from 'next-intl';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';

export function NavUser({
  user,
}: {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
}) {
  const { isMobile } = useSidebar();
  const t = useTranslations('navigation');
  const pathname = usePathname();

  // 判断是否在管理员区域
  const isAdmin = pathname.startsWith('/admin');

  // 处理登出
  const handleLogout = async () => {
    await signOut({
      callbackUrl: '/sign-in',
      redirect: true,
    });
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size='lg'
              className='group relative overflow-hidden transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-950/50 dark:hover:to-indigo-950/50 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] data-[state=open]:bg-gradient-to-r data-[state=open]:from-blue-50 data-[state=open]:to-indigo-50 dark:data-[state=open]:from-blue-950/50 dark:data-[state=open]:to-indigo-950/50'
            >
              <div className='relative'>
                <Avatar className='h-10 w-10 rounded-xl border-2 border-white dark:border-slate-700 shadow-md'>
                  <AvatarImage
                    src={user.avatar}
                    alt={user.name}
                    className='rounded-xl'
                  />
                  <AvatarFallback className='rounded-xl bg-gradient-to-br from-blue-500 to-indigo-500 text-white font-semibold'>
                    {user.name?.slice(0, 2)?.toUpperCase() || 'CN'}
                  </AvatarFallback>
                </Avatar>
                {/* 在线状态指示器 */}
                <div className='absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-500 border-2 border-white dark:border-slate-800 rounded-full' />
              </div>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold text-slate-900 dark:text-slate-100'>
                  {user.name}
                </span>
                <span className='truncate text-xs text-slate-500 dark:text-slate-400'>
                  {user.email}
                </span>
              </div>
              <ChevronsUpDown className='ml-auto h-4 w-4 text-slate-400 group-hover:text-blue-500 transition-colors duration-300' />
              {/* 悬停效果装饰 */}
              <div className='absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/5 to-indigo-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg' />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl shadow-2xl'
            side={isMobile ? 'bottom' : 'right'}
            align='end'
            sideOffset={4}
          >
            <DropdownMenuLabel className='p-0 font-normal'>
              <div className='flex items-center gap-3 px-3 py-3 text-left text-sm bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 rounded-t-xl'>
                <Avatar className='h-10 w-10 rounded-xl border-2 border-white dark:border-slate-700 shadow-md'>
                  <AvatarImage
                    src={user.avatar}
                    alt={user.name}
                    className='rounded-xl'
                  />
                  <AvatarFallback className='rounded-xl bg-gradient-to-br from-blue-500 to-indigo-500 text-white font-semibold'>
                    {user.name?.slice(0, 2)?.toUpperCase() || 'CN'}
                  </AvatarFallback>
                </Avatar>
                <div className='grid flex-1 text-left text-sm leading-tight'>
                  <span className='truncate font-semibold text-slate-900 dark:text-slate-100'>
                    {user.name}
                  </span>
                  <span className='truncate text-xs text-slate-500 dark:text-slate-400'>
                    {user.email}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator className='bg-gradient-to-r from-transparent via-slate-200 to-transparent dark:via-slate-700' />

            {/* 会员升级 - 仅在用户中心显示 */}
            {!isAdmin && (
              <>
                <DropdownMenuGroup>
                  <DropdownMenuItem asChild>
                    <Link href='/membership' className='w-full'>
                      <Sparkles className='mr-2 h-4 w-4' />
                      {t('userMenu.membershipUpgrade')}
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
              </>
            )}

            <DropdownMenuGroup>
              {/* 账号安全 */}
              <DropdownMenuItem asChild>
                <Link href='/profile' className='w-full'>
                  <User className='mr-2 h-4 w-4' />
                  {t('userMenu.accountSecurity')}
                </Link>
              </DropdownMenuItem>

              {/* 我的钱包 */}
              <DropdownMenuItem asChild>
                <Link href='/wallet' className='w-full'>
                  <Wallet className='mr-2 h-4 w-4' />
                  {t('userMenu.myWallet')}
                </Link>
              </DropdownMenuItem>

              {/* 工单系统 */}
              <DropdownMenuItem asChild>
                <Link href='/tickets' className='w-full'>
                  <Ticket className='mr-2 h-4 w-4' />
                  {t('userMenu.ticketSystem')}
                </Link>
              </DropdownMenuItem>

              {/* 用户中心 */}
              <DropdownMenuItem asChild>
                <Link href='/dashboard' className='w-full'>
                  <BadgeCheck className='mr-2 h-4 w-4' />
                  {t('userMenu.userCenter')}
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            {/* 退出登录 */}
            <DropdownMenuItem
              onClick={handleLogout}
              className='text-red-600 focus:text-red-600'
            >
              <LogOut className='mr-2 h-4 w-4' />
              {t('userMenu.signOut')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
