# RefundGo System Architecture

## Overview

RefundGo is a modern full-stack web application built with Next.js 14+, integrating authentication, payment processing, internationalization, and other complex systems. This document details the technical architecture, technology choices, and implementation specifics.

## Technology Stack

### Frontend Technologies

**Core Framework**:
- **Next.js 14+** - React full-stack framework with App Router support
- **React 18** - UI library with Server Components support
- **TypeScript** - Type-safe JavaScript superset

**UI and Styling**:
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality React component library
- **Radix UI** - Unstyled accessible component primitives
- **Lucide React** - Modern icon library

**State Management and Data Fetching**:
- **TanStack Query (React Query)** - Server state management
- **Zustand** - Lightweight client state management
- **React Hook Form** - High-performance form library

**3D and Animation**:
- **React Three Fiber** - Three.js for React
- **Framer Motion** - Animation and gesture library
- **Three.js** - 3D graphics library

### Backend Technologies

**Server Framework**:
- **Next.js API Routes** - Server-side API endpoints
- **Node.js** - JavaScript runtime environment

**Database and ORM**:
- **PostgreSQL** - Relational database
- **Prisma** - Modern database ORM
- **Prisma Client** - Type-safe database client

**Authentication and Authorization**:
- **NextAuth.js v5 (beta)** - Authentication solution
- **bcryptjs** - Password hashing
- **JWT** - JSON Web Tokens

### Third-Party Service Integrations

**Payment Services**:
- **YunPay** - Alipay and WeChat Pay
- **NOWPayments** - Cryptocurrency payments
- **PayPal** - International payment solution

**Email Service**:
- **Resend** - Modern email delivery service

**File Storage**:
- **Local File System** - File upload and storage
- **Scalable Cloud Storage** - AWS S3, Alibaba Cloud OSS, etc.

### Development Tools and Quality Assurance

**Code Quality**:
- **ESLint** - JavaScript/TypeScript linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking

**Testing Framework**:
- **Jest** - JavaScript testing framework
- **Testing Library** - React component testing
- **Playwright** - End-to-end testing

**Development Tools**:
- **Husky** - Git hooks management
- **lint-staged** - Staged file checking
- **Augment** - AI-assisted development

## System Architecture Design

### Overall Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend App  │    │   API Service   │    │   Database      │
│                 │    │                 │    │                 │
│ - Next.js App   │◄──►│ - API Routes    │◄──►│ - PostgreSQL    │
│ - React UI      │    │ - Business Logic│    │ - Prisma ORM    │
│ - State Mgmt    │    │ - Auth & Auth   │    │ - Data Models   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Third-party     │    │ File Storage    │    │ Email Service   │
│ Services        │    │                 │    │                 │
│ - Payment Gates │    │ - Local Storage │    │ - Resend        │
│ - OAuth Providers│   │ - Cloud Storage │    │ - Email Templates│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Application Layer Structure

**Route Architecture**:

```
src/app/
├── (admin)/                 # Admin panel routes (no i18n)
│   └── admin/               # Admin panel pages
├── [locale]/                # Internationalized routes (en, zh)
│   ├── (main)/              # Public pages (home, login, etc.)
│   ├── (user)/              # User dashboard pages
│   └── payment/             # Payment flow pages
├── api/                     # API routes
│   ├── admin/               # Admin panel API
│   ├── auth/                # Authentication API
│   ├── cron/                # Scheduled tasks API
│   ├── user/                # User API
│   └── ...                  # Other APIs
└── ...
```

### Database Design

**Core Data Models**:

**User System**:
- `User` - Basic user information
- `Account` - OAuth account associations
- `Session` - User session management
- `Wallet` - User wallet information

**Task System**:
- `Task` - Task main information
- `TaskApplication` - Task application records
- `TaskEvidence` - Task evidence management
- `TaskReview` - Task review records

**Category System**:
- `Platform` - E-commerce platform information
- `Category` - Product categories
- `ChargebackType` - Chargeback types
- `PaymentMethod` - Payment methods

**Financial System**:
- `WalletTransaction` - Wallet transaction records
- `PaymentOrder` - Payment orders
- `Withdrawal` - Withdrawal requests
- `SystemRate` - System rate configuration

**Support System**:
- `Ticket` - Ticket information
- `TicketReply` - Ticket replies
- `ShopWhitelist` - Shop whitelist

### Security Architecture

**Authentication Flow**:
```
User Login → NextAuth Verification → Generate JWT → Store Session → Permission Check
```

**Permission Control**:
- **Route Level**: Middleware intercepts unauthorized access
- **API Level**: Each endpoint verifies user permissions
- **Component Level**: Conditional rendering based on user roles
- **Data Level**: Database queries include permission filtering

**Data Security**:
- Password hashing with bcrypt
- Encrypted transmission of sensitive data (HTTPS)
- SQL injection protection (Prisma ORM)
- XSS protection (React auto-escaping)
- CSRF protection (NextAuth built-in)

## Core Business Logic

### Task State Machine

```typescript
enum TaskStatus {
  PENDING = 'PENDING',           // Pending review
  REJECTED = 'REJECTED',         // Review rejected
  RECRUITING = 'RECRUITING',     // Recruiting
  IN_PROGRESS = 'IN_PROGRESS',   // In progress
  PENDING_LOGISTICS = 'PENDING_LOGISTICS',     // Waiting for logistics
  PENDING_REVIEW = 'PENDING_REVIEW',           // Waiting for review
  PENDING_DELIVERY = 'PENDING_DELIVERY',       // Waiting for delivery
  COMPLETED = 'COMPLETED',       // Completed
  EXPIRED = 'EXPIRED',           // Expired
  CANCELLED = 'CANCELLED',       // Cancelled
}
```

**State Transition Rules**:
- Only admins can transition PENDING to RECRUITING or REJECTED
- User acceptance transitions RECRUITING to IN_PROGRESS
- Task execution process has strict time limits
- Certain state transitions require specific conditions

### Payment Processing Flow

**Payment Manager Pattern**:

```typescript
interface PaymentProvider {
  createPayment(params: CreatePaymentParams): Promise<PaymentResult>;
  handleCallback(data: any): Promise<CallbackResult>;
  queryPaymentStatus(orderNo: string): Promise<PaymentStatus>;
}

class PaymentManager {
  private providers: Map<string, PaymentProvider>;

  async createPayment(params: CreatePaymentParams & { provider: string });
  async handleCallback(provider: string, data: any);
}
```

**Payment Flow**:
1. User initiates payment request
2. System creates payment order
3. Call corresponding payment provider API
4. User completes third-party payment
5. Receive payment callback notification
6. Verify payment result
7. Update order status
8. Process business logic

### Commission Calculation System

**Rate Structure**:

```typescript
interface CommissionCalculation {
  chargebackTypeRate: number;    // Chargeback type rate
  paymentMethodRate: number;     // Payment method rate
  evidenceRate: number;          // Evidence rate
  totalRate: number;             // Total rate
  commission: number;            // Commission amount
}
```

**Calculation Logic**:
- Base rate + Category rate + Payment method rate + Evidence rate
- Membership levels enjoy rate discounts
- Special tasks may have additional rate adjustments

## Performance Optimization Strategies

### Frontend Optimization

**Code Splitting**:
- Route-level code splitting
- Component lazy loading
- Dynamic import of third-party libraries

**Caching Strategy**:
- Static asset caching
- API response caching (React Query)
- Browser cache optimization

**Rendering Optimization**:
- Server-side rendering (SSR)
- Static generation (SSG)
- Incremental static regeneration (ISR)

### Backend Optimization

**Database Optimization**:
- Index optimization
- Query optimization
- Connection pool management
- Read-write separation (scalable)

**API Optimization**:
- Response compression
- Request deduplication
- Batch operations
- Paginated queries

**Caching Strategy**:
- Redis caching (scalable)
- Application-level caching
- CDN caching

## Monitoring and Operations

### Error Monitoring

**Error Capture**:
- Global error boundaries
- API error handling
- Uncaught exception logging

**Logging System**:
- Structured logging
- Log level classification
- Sensitive information masking

### Performance Monitoring

**Key Metrics**:
- Page load time
- API response time
- Database query performance
- User behavior analysis

**Monitoring Tools**:
- Application Performance Monitoring (APM)
- Database performance monitoring
- Server resource monitoring

## Deployment and Scaling

### Deployment Architecture

**Production Environment**:
- Containerized deployment (Docker)
- Load balancing
- Database clustering
- CDN acceleration

**CI/CD Pipeline**:
- Code commit triggers build
- Automated testing
- Build Docker images
- Deploy to production environment

### Scalability Design

**Horizontal Scaling**:
- Stateless application design
- Database read-write separation
- Microservices architecture (future)

**Vertical Scaling**:
- Server resource upgrades
- Database performance optimization
- Cache layer expansion

---

**Architecture Version**: 2.0  
**Framework**: Next.js 14+ with App Router  
**Database**: PostgreSQL with Prisma ORM  
**Last Updated**: 2025-01-29
