'use client';

import { Loader2, Clock, Wifi, User, Database, Shield } from 'lucide-react';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';

// 加载状态组件
function LoadingSpinner({
  size = 'default',
}: {
  size?: 'sm' | 'default' | 'lg';
}) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    default: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return <Loader2 className={`animate-spin ${sizeClasses[size]}`} />;
}

// 进度条加载组件
function ProgressLoader({
  steps = ['加载用户信息', '获取权限数据', '初始化界面'],
  duration = 3000,
}: {
  steps?: string[];
  duration?: number;
}) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const stepDuration = duration / steps.length;
    const progressPerStep = 100 / steps.length;

    const timer = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + progressPerStep / (stepDuration / 100);

        // 更新当前步骤
        const newStep = Math.floor(newProgress / progressPerStep);
        if (newStep !== currentStep && newStep < steps.length) {
          setCurrentStep(newStep);
        }

        if (newProgress >= 95) return 95; // 不要到100%，保持加载状态
        return newProgress;
      });
    }, 100);

    return () => clearInterval(timer);
  }, [steps.length, duration, currentStep]);

  return (
    <div className='w-full max-w-md mx-auto space-y-3'>
      <div className='flex items-center gap-2 text-sm text-muted-foreground'>
        <LoadingSpinner size='sm' />
        <span>{steps[currentStep] || '正在加载...'}</span>
      </div>
      <Progress value={progress} className='h-2' />
      <div className='text-xs text-center text-muted-foreground'>
        {Math.round(progress)}% 完成
      </div>
    </div>
  );
}

// 骨架屏组件
export function SkeletonLayout({
  type = 'dashboard',
}: {
  type?: 'dashboard' | 'list' | 'form' | 'profile';
}) {
  const renderDashboardSkeleton = () => (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      {/* Header skeleton */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-3'>
          <Skeleton className='h-8 w-8 rounded-full' />
          <div className='space-y-2'>
            <Skeleton className='h-4 w-32' />
            <Skeleton className='h-3 w-24' />
          </div>
        </div>
        <Skeleton className='h-8 w-24' />
      </div>

      {/* Stats cards skeleton */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className='p-4 border rounded-lg space-y-3'>
            <div className='flex items-center justify-between'>
              <Skeleton className='h-4 w-16' />
              <Skeleton className='h-4 w-4 rounded' />
            </div>
            <Skeleton className='h-8 w-20' />
            <Skeleton className='h-3 w-24' />
          </div>
        ))}
      </div>

      {/* Content skeleton */}
      <div className='grid gap-6'>
        <div className='space-y-4'>
          <Skeleton className='h-6 w-32' />
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className='p-4 border rounded-lg space-y-3'>
                <div className='flex items-center gap-3'>
                  <Skeleton className='h-10 w-10 rounded-full' />
                  <div className='space-y-2'>
                    <Skeleton className='h-4 w-24' />
                    <Skeleton className='h-3 w-16' />
                  </div>
                </div>
                <Skeleton className='h-20 w-full' />
                <div className='flex justify-between'>
                  <Skeleton className='h-3 w-16' />
                  <Skeleton className='h-3 w-12' />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderListSkeleton = () => (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      <div className='flex items-center justify-between'>
        <Skeleton className='h-8 w-48' />
        <Skeleton className='h-8 w-24' />
      </div>
      <div className='space-y-3'>
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className='flex items-center gap-4 p-4 border rounded-lg'
          >
            <Skeleton className='h-12 w-12 rounded-full' />
            <div className='flex-1 space-y-2'>
              <Skeleton className='h-4 w-3/4' />
              <Skeleton className='h-3 w-1/2' />
            </div>
            <Skeleton className='h-8 w-20' />
          </div>
        ))}
      </div>
    </div>
  );

  const renderFormSkeleton = () => (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      <Skeleton className='h-8 w-48' />
      <div className='space-y-6'>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className='space-y-2'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-10 w-full' />
          </div>
        ))}
        <div className='flex gap-3'>
          <Skeleton className='h-10 w-24' />
          <Skeleton className='h-10 w-24' />
        </div>
      </div>
    </div>
  );

  const renderProfileSkeleton = () => (
    <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
      <div className='flex items-center gap-4'>
        <Skeleton className='h-20 w-20 rounded-full' />
        <div className='space-y-2'>
          <Skeleton className='h-6 w-32' />
          <Skeleton className='h-4 w-48' />
          <Skeleton className='h-4 w-24' />
        </div>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className='space-y-4'>
            <Skeleton className='h-5 w-32' />
            <div className='space-y-3'>
              {Array.from({ length: 3 }).map((_, j) => (
                <div key={j} className='space-y-2'>
                  <Skeleton className='h-4 w-20' />
                  <Skeleton className='h-8 w-full' />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  switch (type) {
    case 'list':
      return renderListSkeleton();
    case 'form':
      return renderFormSkeleton();
    case 'profile':
      return renderProfileSkeleton();
    default:
      return renderDashboardSkeleton();
  }
}

// 网络状态检查组件
function NetworkStatus() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOnline) {
    return (
      <div className='flex items-center gap-2 text-sm text-destructive'>
        <Wifi className='h-4 w-4' />
        <span>网络连接已断开</span>
      </div>
    );
  }

  return null;
}

// 加载状态指示器
function LoadingSteps({ currentStep }: { currentStep: number }) {
  const steps = [
    { icon: User, label: '验证身份', description: '检查登录状态' },
    { icon: Shield, label: '加载权限', description: '获取用户权限' },
    { icon: Database, label: '获取数据', description: '加载用户数据' },
  ];

  return (
    <div className='space-y-3'>
      {steps.map((step, index) => {
        const isActive = index === currentStep;
        const isCompleted = index < currentStep;

        return (
          <div key={index} className='flex items-center gap-3'>
            <div
              className={`
              flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors
              ${
                isCompleted
                  ? 'bg-primary border-primary text-primary-foreground'
                  : isActive
                    ? 'border-primary text-primary'
                    : 'border-muted text-muted-foreground'
              }
            `}
            >
              {isCompleted ? (
                <div className='w-2 h-2 bg-primary-foreground rounded-full' />
              ) : (
                <step.icon className='w-4 h-4' />
              )}
            </div>
            <div className='flex-1'>
              <div
                className={`text-sm font-medium ${isActive ? 'text-foreground' : 'text-muted-foreground'}`}
              >
                {step.label}
              </div>
              <div className='text-xs text-muted-foreground'>
                {step.description}
              </div>
            </div>
            {isActive && <LoadingSpinner size='sm' />}
          </div>
        );
      })}
    </div>
  );
}

// 主增强加载组件
export function EnhancedLoading({
  type = 'full',
  showSteps = false,
  customSteps,
  skeletonType = 'dashboard',
}: {
  type?: 'full' | 'skeleton' | 'minimal';
  showSteps?: boolean;
  customSteps?: string[];
  skeletonType?: 'dashboard' | 'list' | 'form' | 'profile';
}) {
  const [loadingTime, setLoadingTime] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [showSkeleton, setShowSkeleton] = useState(false);

  useEffect(() => {
    const startTime = Date.now();

    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      setLoadingTime(Math.floor(elapsed / 1000));

      // 更新步骤
      if (showSteps) {
        const stepInterval = 2000; // 每2秒一个步骤
        const newStep = Math.floor(elapsed / stepInterval);
        if (newStep !== currentStep && newStep < 3) {
          setCurrentStep(newStep);
        }
      }

      // 3秒后显示骨架屏
      if (elapsed > 3000 && type === 'full') {
        setShowSkeleton(true);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [currentStep, showSteps, type]);

  if (type === 'skeleton' || showSkeleton) {
    return <SkeletonLayout type={skeletonType} />;
  }

  if (type === 'minimal') {
    return (
      <div className='flex items-center justify-center p-8'>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center space-y-6'>
        <div className='flex justify-center'>
          <div className='relative'>
            <LoadingSpinner size='lg' />
            <div className='absolute -bottom-8 left-1/2 transform -translate-x-1/2'>
              <div className='flex items-center gap-1 text-xs text-muted-foreground'>
                <Clock className='h-3 w-3' />
                <span>{loadingTime}s</span>
              </div>
            </div>
          </div>
        </div>

        <div className='space-y-2'>
          <h2 className='text-lg font-semibold'>正在加载</h2>
          <p className='text-sm text-muted-foreground'>
            正在为您准备用户界面...
          </p>
        </div>

        {showSteps ? (
          <LoadingSteps currentStep={currentStep} />
        ) : (
          <ProgressLoader steps={customSteps} />
        )}

        <NetworkStatus />

        {loadingTime > 10 && (
          <div className='p-3 bg-muted rounded-lg text-sm text-muted-foreground'>
            <div className='flex items-center gap-2'>
              <Clock className='h-4 w-4' />
              <span>加载时间较长，可能是网络较慢或服务器繁忙</span>
            </div>
          </div>
        )}

        {loadingTime > 5 && (
          <Badge variant='outline' className='text-xs'>
            正在优化加载体验...
          </Badge>
        )}
      </div>
    </div>
  );
}

export default EnhancedLoading;
