export interface TaskReviewRejectedAccepterEmailData {
  accepterName: string;
  accepterEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  reviewedAt: string;
  rejectReason: string;
  orderNumber?: string;
  trackingNumber?: string;
  resubmitDeadline: string;
}

export const taskReviewRejectedAccepterTemplate = (
  data: TaskReviewRejectedAccepterEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>审核驳回通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #dc3545; margin-bottom: 20px; text-align: center;">❌ 审核未通过</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.accepterName}！您提交的订单和物流信息未通过审核。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">审核时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.reviewedAt}</span>
        </div>
      </div>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">驳回原因</h3>
        
        <div style="background: #ffe6e6; padding: 15px; border-radius: 6px; border-left: 4px solid #dc3545;">
          <p style="color: #dc3545; margin: 0; font-size: 14px;">
            <strong>发布者反馈：</strong><br>
            ${data.rejectReason}
          </p>
        </div>
      </div>
      
      ${
        data.orderNumber || data.trackingNumber
          ? `
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">被驳回的信息</h3>
        
        ${
          data.orderNumber
            ? `
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">订单号：</span>
          <span style="color: #dc3545; margin-left: 10px; font-family: monospace; text-decoration: line-through;">${data.orderNumber}</span>
        </div>
        `
            : ''
        }
        
        ${
          data.trackingNumber
            ? `
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">物流单号：</span>
          <span style="color: #dc3545; margin-left: 10px; font-family: monospace; text-decoration: line-through;">${data.trackingNumber}</span>
        </div>
        `
            : ''
        }
      </div>
      `
          : ''
      }
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">下一步操作</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">当前状态：</span>
          <span style="color: #dc3545; font-weight: bold; margin-left: 10px;">需要重新提交</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">重新提交期限：</span>
          <span style="color: #333; margin-left: 10px;">${data.resubmitDeadline}</span>
        </div>
      </div>
      
      <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
          ⚠️ <strong>重要提醒：</strong><br>
          • 请根据发布者的反馈重新提交正确的订单号和物流信息<br>
          • 委托状态已重置为"进行中"，订单和物流信息已清空<br>
          • 请在规定时间内重新提交，否则可能导致委托超时<br>
          • 如有疑问，请及时联系客服
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
