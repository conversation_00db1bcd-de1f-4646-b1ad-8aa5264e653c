import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

// 提交订单请求数据类型
interface SubmitOrderData {
  taskId: string;
  orderNumber: string;
  orderScreenshot?: string;
}

// 提交订单响应类型
interface SubmitOrderResponse {
  success: boolean;
  message: string;
  data: {
    task: {
      id: string;
      status: string;
      completedAt: string;
      platform: string;
      category: string;
    };
    order: {
      orderNumber: string;
      submitTime: string;
    };
    rewards: {
      commission: number;
      depositReleased: number;
      totalEarned: number;
    };
  };
}

export function useSubmitOrder() {
  const t = useTranslations('messages');
  const queryClient = useQueryClient();

  return useMutation<SubmitOrderResponse, Error, SubmitOrderData>({
    mutationFn: async (data: SubmitOrderData) => {
      const response = await fetch(
        `/api/user/accepted-tasks/${data.taskId}/submit-order`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderNumber: data.orderNumber,
            orderScreenshot: data.orderScreenshot,
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '提交订单失败');
      }

      return response.json();
    },
    onSuccess: data => {
      toast.success(t('success.submit'), {
        description: data.message,
        duration: 6000,
      });

      // 刷新已接受委托列表
      queryClient.invalidateQueries({ queryKey: ['accepted-tasks'] });

      // 刷新用户余额等信息
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });

      // 刷新钱包交易记录
      queryClient.invalidateQueries({ queryKey: ['wallet-transactions'] });
    },
    onError: error => {
      toast.error(t('error.submit'), {
        description: error.message || t('error.network'),
        duration: 5000,
      });
    },
  });
}
