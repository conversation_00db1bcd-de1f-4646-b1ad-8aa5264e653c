import { MemberPlan } from '@prisma/client';

import prisma from '@/lib/db';

// 根据用户会员计划获取对应的会员套餐配置
export async function getUserMembershipPlan(memberPlan: MemberPlan) {
  // 根据枚举值映射到套餐名称
  const planNameMap = {
    FREE: '免费版',
    PRO: '专业版',
    BUSINESS: '商业版',
  };

  const planName = planNameMap[memberPlan];

  if (!planName) {
    return null;
  }

  return await prisma.membershipPlan.findFirst({
    where: {
      name: planName,
      isActive: true,
    },
  });
}

// 获取用户会员套餐的委托发布限制
export async function getUserTaskLimit(
  memberPlan: MemberPlan,
): Promise<number | null> {
  const membershipPlan = await getUserMembershipPlan(memberPlan);
  return membershipPlan?.maxTasks || null;
}

// 获取用户会员套餐的白名单店铺名额
export async function getUserWhitelistSlots(
  memberPlan: MemberPlan,
): Promise<number> {
  const membershipPlan = await getUserMembershipPlan(memberPlan);
  return membershipPlan?.whitelistSlots || 0;
}

// 获取用户会员套餐的平台费率
export async function getUserPlatformRate(
  memberPlan: MemberPlan,
): Promise<number> {
  const membershipPlan = await getUserMembershipPlan(memberPlan);
  return membershipPlan?.platformRate || 2.0; // 默认费率2%
}
