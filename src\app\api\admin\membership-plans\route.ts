import { UserRole } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { CreateMembershipPlanSchema } from '@/types/membership';

// 获取所有会员套餐
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const plans = await prisma.membershipPlan.findMany({
      orderBy: { price: 'asc' },
    });

    return NextResponse.json({
      success: true,
      data: plans,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 创建新会员套餐
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const body = await request.json();

    // 验证输入数据
    const validatedData = CreateMembershipPlanSchema.parse(body);

    // 检查套餐名称是否已存在
    const existingPlan = await prisma.membershipPlan.findFirst({
      where: { name: validatedData.name },
    });

    if (existingPlan) {
      return NextResponse.json(
        { success: false, message: '套餐名称已存在' },
        { status: 400 },
      );
    }

    // 创建新会员套餐
    const newPlan = await prisma.membershipPlan.create({
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: newPlan,
      message: '会员套餐创建成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
