# RefundGo 用户角色介绍

## 概述

RefundGo 是一个基于委托的电商拒付服务平台，连接委托发布者和接单者，提供安全、高效的委托处理服务。平台采用分层用户角色系统，每个角色拥有不同的权限和功能访问级别。

## 用户角色体系

### 1. 游客 (Guest)

**角色定义**：未注册或未登录的访问者

**可访问功能**：

- 浏览平台主页和功能介绍
- 查看服务条款和隐私政策
- 访问帮助文档和FAQ
- 查看公开的委托统计信息
- 进行用户注册和登录

**游客使用场景**：

- 初次了解平台服务
- 评估平台可信度和功能
- 决定是否注册成为用户

**限制**：

- 无法查看具体委托详情
- 无法进行任何交易操作
- 无法访问用户中心功能

---

### 2. 普通用户 (USER)

**角色定义**：已完成注册验证的基础用户

#### 核心权限

- `VIEW_DASHBOARD` - 查看个人仪表盘
- `VIEW_PROFILE` - 查看和编辑个人资料
- `PUBLISH_TASK` - 发布委托委托
- `ACCEPT_TASK` - 接受委托委托
- `VIEW_TASKS` - 浏览委托大厅
- `MANAGE_OWN_TASKS` - 管理自己的委托
- `VIEW_WALLET` - 查看钱包余额和交易记录
- `WITHDRAW_FUNDS` - 申请资金提现

#### 主要功能

**委托管理**：

- 发布拒付委托委托
- 浏览和筛选可接受的委托
- 接受符合条件的委托
- 提交委托执行证据
- 跟踪委托进度和状态

**财务管理**：

- 钱包充值（支持多种支付方式）
- 查看余额和冻结金额
- 申请提现到银行卡或USDT
- 查看详细的交易记录

**账户管理**：

- 完善个人资料信息
- 设置账户安全选项
- 管理登录设备和会话
- 查看账户活动日志

**客户支持**：

- 创建和管理工单
- 与客服团队沟通
- 查看帮助文档

#### 会员限制

- 每月委托发布数量有限制
- 无法访问高级功能
- 标准酬金费率
- 基础客服支持级别

#### 普通用户使用场景

- 个人用户进行小规模拒付委托
- 新用户熟悉平台操作流程
- 偶尔性的委托发布和接单需求

---

### 3. 高级用户 (PREMIUM)

**角色定义**：付费订阅用户，享有增强功能和特权

#### 扩展权限

继承普通用户所有权限，额外包含：

- `MANAGE_WHITELIST` - 管理白名单功能
- `ACCESS_PREMIUM` - 访问高级功能
- `BULK_OPERATIONS` - 批量操作功能

#### 高级功能

**白名单管理**：

- 添加和管理店铺白名单
- 批量导入白名单数据
- 白名单状态跟踪和管理

**增强委托功能**：

- 更高的委托发布限额
- 优先委托展示
- 批量委托操作
- 高级筛选和搜索

**财务优势**：

- 降低的平台酬金费率
- 优先提现处理
- 专属客服支持

**数据分析**：

- 详细的委托统计报告
- 收益分析图表
- 历史数据导出

#### 会员套餐类型

**专业版 (PRO)**：

- 月委托限额：100个
- 酬金优惠：标准费率85折
- 白名单槽位：50个
- 客服支持：优先响应

**商业版 (BUSINESS)**：

- 月委托限额：500个
- 酬金优惠：标准费率75折
- 白名单槽位：200个
- 客服支持：专属客服

#### 高级用户使用场景

- 专业的拒付服务提供商
- 大批量委托处理需求
- 需要白名单管理的商户
- 对服务质量要求较高的用户

---

### 4. 管理员 (ADMIN)

**角色定义**：平台运营和管理人员，拥有最高权限

#### 管理权限

拥有所有用户权限，额外包含：

- `ADMIN_DASHBOARD` - 管理员仪表盘
- `MANAGE_USERS` - 用户管理
- `MANAGE_TASKS` - 委托管理
- `MANAGE_PAYMENTS` - 支付管理
- `MANAGE_SETTINGS` - 系统设置
- `VIEW_ANALYTICS` - 数据分析

#### 核心职责

**用户管理**：

- 用户账户审核和验证
- 用户权限调整和角色变更
- 处理用户申诉和纠纷
- 用户行为监控和风控

**委托审核**：

- 新发布委托的审核批准
- 委托证据的审核验证
- 争议委托的仲裁处理
- 委托质量监控

**财务管理**：

- 提现申请审核和处理
- 支付异常处理
- 酬金费率设置和调整
- 财务报表生成和分析

**系统运维**：

- 平台配置参数设置
- 支付渠道管理
- 系统公告发布
- 数据备份和维护

**客服管理**：

- 工单分配和处理
- 客服团队协调
- 用户反馈收集和处理
- 服务质量监控

#### 管理界面功能

**数据仪表盘**：

- 实时平台运营数据
- 用户增长和活跃度统计
- 委托完成率和质量指标
- 财务收支和利润分析

**审核工作台**：

- 待审核委托队列
- 证据审核界面
- 批量审核操作
- 审核历史记录

**用户管理中心**：

- 用户列表和搜索
- 用户详细信息查看
- 账户状态管理
- 权限分配界面

#### 管理员使用场景

- 平台日常运营管理
- 用户服务和支持
- 业务数据分析和决策
- 系统维护和优化

---

## 角色权限矩阵

| 功能模块   | 游客 | 普通用户 | 高级用户 | 管理员 |
| ---------- | ---- | -------- | -------- | ------ |
| 浏览主页   | ✅   | ✅       | ✅       | ✅     |
| 用户注册   | ✅   | ✅       | ✅       | ✅     |
| 委托发布   | ❌   | ✅       | ✅       | ✅     |
| 委托接单   | ❌   | ✅       | ✅       | ✅     |
| 钱包管理   | ❌   | ✅       | ✅       | ✅     |
| 白名单管理 | ❌   | ❌       | ✅       | ✅     |
| 批量操作   | ❌   | ❌       | ✅       | ✅     |
| 用户管理   | ❌   | ❌       | ❌       | ✅     |
| 委托审核   | ❌   | ❌       | ❌       | ✅     |
| 系统设置   | ❌   | ❌       | ❌       | ✅     |

## 角色升级路径

### 普通用户 → 高级用户

- 通过会员中心选择合适的套餐
- 完成在线支付流程
- 系统自动升级权限
- 享受高级功能和优惠

### 权限继承关系

- 高级用户继承普通用户的所有权限
- 管理员拥有所有角色的权限
- 权限检查采用层级验证机制

## 注意事项

1. **权限验证**：所有功能访问都会进行实时权限验证
2. **会员到期**：高级用户会员到期后自动降级为普通用户
3. **安全机制**：管理员操作会记录详细的审计日志
4. **多语言支持**：所有角色界面都支持中英文切换
5. **移动端适配**：所有功能在移动设备上都有良好的用户体验

---
