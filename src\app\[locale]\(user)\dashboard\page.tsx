'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { LazyDashboardContent } from '@/components/lazy-components';
import { UserPageLayout } from '@/components/user-page-layout';

export default function DashboardPage() {
  const t = useTranslations('dashboard');

  useEffect(() => {
    document.title = `${t('title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('title')}
      breadcrumbPage={t('breadcrumb')}
      href='/dashboard'
      description={t('description')}
    >
      <LazyDashboardContent />
    </UserPageLayout>
  );
}
