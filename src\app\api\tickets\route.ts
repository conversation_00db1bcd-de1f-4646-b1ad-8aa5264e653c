import { TicketStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { createServerTranslator } from '@/lib/server-i18n';
import {
  defaultCreateTicketSchema,
  ticketQuerySchema,
  type TicketListResponse,
} from '@/lib/types/ticket';

// 获取用户工单列表
export async function GET(request: NextRequest) {
  try {
    // 创建服务器端翻译器
    const { t } = createServerTranslator(request);

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t('loginRequired') }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);

    // 验证查询参数
    const queryValidation = ticketQuerySchema.safeParse({
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      search: searchParams.get('search'),
      type: searchParams.get('type'),
      status: searchParams.get('status'),
      priority: searchParams.get('priority'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder'),
    });

    if (!queryValidation.success) {
      return NextResponse.json(
        { error: t('invalidData'), details: queryValidation.error.errors },
        { status: 400 },
      );
    }

    const { page, limit, search, type, status, priority, sortBy, sortOrder } =
      queryValidation.data;

    // 构建查询条件
    const where: any = {
      userId: session.user.id,
    };

    // 搜索筛选
    if (search) {
      where.OR = [
        {
          title: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // 类型筛选
    if (type) {
      where.type = type;
    }

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 优先级筛选
    if (priority) {
      where.priority = priority;
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询工单列表和总数
    const [tickets, total] = await Promise.all([
      prisma.ticket.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          replies: {
            take: 3, // 只取最新的3条回复用于预览
            orderBy: {
              createdAt: 'desc',
            },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          _count: {
            select: {
              replies: true,
            },
          },
        },
      }),
      prisma.ticket.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    const response: TicketListResponse = {
      tickets,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API错误:', error);
    const { t } = createServerTranslator(request);
    return NextResponse.json({ error: t('serverError') }, { status: 500 });
  }
}

// 创建新工单
export async function POST(request: NextRequest) {
  try {
    // 创建服务器端翻译器
    const { t } = createServerTranslator(request);

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t('loginRequired') }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validation = defaultCreateTicketSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: t('invalidData'),
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { title, description, type, priority } = validation.data;

    // 创建工单
    const ticket = await prisma.ticket.create({
      data: {
        title,
        description,
        type,
        priority,
        status: TicketStatus.PENDING,
        userId: session.user.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: t('ticketCreated'),
      ticket,
    });
  } catch (error) {
    console.error('创建工单失败:', error);
    const { t } = createServerTranslator(request);
    return NextResponse.json({ error: t('serverError') }, { status: 500 });
  }
}
