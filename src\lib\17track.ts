import crypto from 'crypto';

// API 配置
const TRACK_17_API_KEY = process.env.TRACK_17_API_KEY;
const TRACK_17_API_URL =
  process.env.TRACK_17_API_URL || 'https://api.17track.net/track/v2.2';

// API 响应类型
export interface TrackingProvider {
  key: number;
  name: string;
  alias: string;
  tel: string | null;
  homepage: string;
  country: string;
}

export interface TrackingEvent {
  time_iso: string;
  time_utc: string;
  time_raw: {
    date: string;
    time: string | null;
    timezone: string | null;
  };
  description: string;
  description_translation?: {
    lang: string;
    description: string;
  };
  location: string;
  stage: string;
  sub_status: string;
  address: {
    country: string | null;
    state: string | null;
    city: string | null;
    street: string | null;
    postal_code: string | null;
    coordinates: {
      longitude: string | null;
      latitude: string | null;
    };
  };
}

export interface TrackingInfo {
  number: string;
  carrier: number;
  param: string | null;
  tag: string | null;
  track_info: {
    shipping_info: {
      shipper_address: {
        country: string;
        state: string | null;
        city: string | null;
        street: string | null;
        postal_code: string | null;
        coordinates: {
          longitude: string | null;
          latitude: string | null;
        };
      };
      recipient_address: {
        country: string;
        state: string | null;
        city: string | null;
        street: string | null;
        postal_code: string | null;
        coordinates: {
          longitude: string | null;
          latitude: string | null;
        };
      };
    };
    latest_status: {
      status: string;
      sub_status: string;
      sub_status_descr: string | null;
    };
    latest_event: TrackingEvent | null;
    time_metrics: {
      days_after_order: number;
      days_of_transit: number;
      days_of_transit_done: number;
      days_after_last_update: number;
      estimated_delivery_date: {
        source: string | null;
        from: string | null;
        to: string | null;
      };
    };
    milestone: Array<{
      key_stage: string;
      time_iso: string | null;
      time_utc: string | null;
      time_raw: {
        date: string | null;
        time: string | null;
        timezone: string | null;
      };
    }>;
    misc_info: {
      risk_factor: number;
      service_type: string | null;
      weight_raw: string | null;
      weight_kg: string | null;
      pieces: string | null;
      dimensions: string | null;
      customer_number: string | null;
      reference_number: string | null;
      local_number: string | null;
      local_provider: string | null;
      local_key: number;
    };
    tracking: {
      providers_hash: number;
      providers: Array<{
        provider: TrackingProvider;
        service_type: string | null;
        latest_sync_status: string;
        latest_sync_time: string;
        events_hash: number;
        events: TrackingEvent[];
      }>;
    };
  };
}

export interface RegisterTrackingRequest {
  number: string;
  lang?: string;
  email?: string;
  param?: string;
  order_no?: string;
  order_time?: string;
  carrier?: number;
  final_carrier?: number;
  auto_detection?: boolean;
  tag?: string;
  remark?: string;
}

export interface RegisterTrackingResponse {
  code: number;
  data: {
    accepted: Array<{
      origin: number;
      number: string;
      carrier: number;
      email: string | null;
      tag: string | null;
      lang: string | null;
    }>;
    rejected: Array<{
      number: string;
      tag?: string;
      error: {
        code: number;
        message: string;
      };
    }>;
  };
}

export interface WebhookNotification {
  event: 'TRACKING_UPDATED' | 'TRACKING_STOPPED';
  data: TrackingInfo;
}

// API 客户端类
export class Track17API {
  private apiKey: string;
  private apiUrl: string;

  constructor(
    apiKey: string = TRACK_17_API_KEY || '',
    apiUrl: string = TRACK_17_API_URL,
  ) {
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
  }

  private async makeRequest(endpoint: string, data: any) {
    const response = await fetch(`${this.apiUrl}/${endpoint}`, {
      method: 'POST',
      headers: {
        '17token': this.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(
        `API request failed: ${response.status} ${response.statusText}`,
      );
    }

    return response.json();
  }

  // 注册物流单号
  async registerTracking(
    requests: RegisterTrackingRequest[],
  ): Promise<RegisterTrackingResponse> {
    return this.makeRequest('register', requests);
  }

  // 获取物流详情
  async getTrackingInfo(
    requests: Array<{ number: string; carrier?: number }>,
  ): Promise<{
    code: number;
    data: {
      accepted: TrackingInfo[];
      rejected: Array<{
        number: string;
        error: {
          code: number;
          message: string;
        };
      }>;
    };
  }> {
    return this.makeRequest('gettrackinfo', requests);
  }

  // 删除物流单号
  async deleteTracking(
    requests: Array<{ number: string; carrier?: number }>,
  ): Promise<{
    code: number;
    data: {
      accepted: Array<{
        number: string;
        carrier: number;
      }>;
      rejected: Array<{
        number: string;
        error: {
          code: number;
          message: string;
        };
      }>;
    };
  }> {
    return this.makeRequest('deletetrack', requests);
  }

  // 停止跟踪
  async stopTracking(
    requests: Array<{ number: string; carrier?: number }>,
  ): Promise<{
    code: number;
    data: {
      accepted: Array<{
        number: string;
        carrier: number;
      }>;
      rejected: Array<{
        number: string;
        error: {
          code: number;
          message: string;
        };
      }>;
    };
  }> {
    return this.makeRequest('stoptrack', requests);
  }

  // 重启跟踪
  async restartTracking(
    requests: Array<{ number: string; carrier?: number }>,
  ): Promise<{
    code: number;
    data: {
      accepted: Array<{
        number: string;
        carrier: number;
      }>;
      rejected: Array<{
        number: string;
        error: {
          code: number;
          message: string;
        };
      }>;
    };
  }> {
    return this.makeRequest('retrack', requests);
  }

  // 获取当前剩余单量
  async getQuota(): Promise<{
    code: number;
    data: {
      quota_total: number;
      quota_used: number;
      quota_remain: number;
      today_used: number;
      max_track_daily: number;
      free_email_quota: number;
      free_email_quotaused: number;
    };
  }> {
    return this.makeRequest('getquota', []);
  }

  // 验证WebHook签名
  static verifyWebhookSignature(
    requestText: string,
    signature: string,
    apiKey: string,
  ): boolean {
    const src = `${requestText}/${apiKey}`;
    const hash = crypto.createHash('sha256').update(src, 'utf8').digest('hex');
    return hash === signature;
  }
}

// 物流状态映射
export const TRACKING_STATUS_MAP = {
  NotFound: { label: '查询不到', color: 'gray' },
  InfoReceived: { label: '收到信息', color: 'blue' },
  InTransit: { label: '运输途中', color: 'yellow' },
  Expired: { label: '运输过久', color: 'red' },
  AvailableForPickup: { label: '到达待取', color: 'green' },
  OutForDelivery: { label: '派送途中', color: 'blue' },
  DeliveryFailure: { label: '投递失败', color: 'red' },
  Delivered: { label: '成功签收', color: 'green' },
  Exception: { label: '可能异常', color: 'red' },
};

// 创建默认实例
export const track17API = new Track17API();
