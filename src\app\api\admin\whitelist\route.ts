import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

// 验证管理员权限的辅助函数
async function requireAdmin() {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('请先登录');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (user?.role !== 'ADMIN') {
    throw new Error('权限不足');
  }

  return session;
}

// 获取白名单列表和统计信息
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search');
    const platform = searchParams.get('platform');
    const status = searchParams.get('status');

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { shopName: { contains: search, mode: 'insensitive' } },
        { shopUrl: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (platform && platform !== 'all') {
      where.platform = platform;
    }

    if (status && status !== 'all') {
      where.isActive = status === 'active';
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 获取白名单列表
    const whitelistItems = await prisma.shopWhitelist.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // 获取总数
    const total = await prisma.shopWhitelist.count({ where });

    // 获取统计信息
    const stats = {
      total: await prisma.shopWhitelist.count(),
      active: await prisma.shopWhitelist.count({ where: { isActive: true } }),
      inactive: await prisma.shopWhitelist.count({
        where: { isActive: false },
      }),
      todayAdded: await prisma.shopWhitelist.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
          },
        },
      }),
    };

    return NextResponse.json({
      whitelistItems,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
      stats,
    });
  } catch (error) {
    console.error('获取白名单失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' },
      { status: 500 },
    );
  }
}
