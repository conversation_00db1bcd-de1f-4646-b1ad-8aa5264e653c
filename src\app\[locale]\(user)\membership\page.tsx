'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { MembershipContent } from '@/components/membership-content';
import { UserPageLayout } from '@/components/user-page-layout';

export default function MembershipPage() {
  const t = useTranslations('membership');

  useEffect(() => {
    document.title = `${t('title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('title')}
      breadcrumbPage={t('breadcrumb')}
      href='/membership'
    >
      <MembershipContent />
    </UserPageLayout>
  );
}
