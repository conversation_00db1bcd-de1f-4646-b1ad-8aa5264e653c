# Project Structure

## Root Directory Organization

```
refundgo-web/
├── src/                    # Main source code
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets
├── docs/                   # Project documentation
├── scripts/                # Utility scripts
├── messages/               # Internationalization files
├── __tests__/              # Test files
├── __mocks__/              # Jest mocks
└── .kiro/                  # Kiro AI assistant configuration
```

## Source Code Structure (`src/`)

### App Router (`src/app/`)

- **`[locale]/`** - Internationalized routes (en/zh)
  - **`(main)/`** - Public pages (landing, tasks, auth)
  - **`(user)/`** - User dashboard and profile pages
  - **`payment/`** - Payment flow pages
- **`(admin)/`** - Admin panel routes (separate layout)
- **`api/`** - API routes and endpoints
- **Root files** - Global layouts, error pages, loading states

### Components (`src/components/`)

- **`ui/`** - Reusable UI components (shadcn/ui based)
- **`admin/`** - Admin-specific components
- **`payment/`** - Payment-related components
- **`publish-steps/`** - Task publishing workflow components
- **Root level** - Feature-specific components (dialogs, forms, content)

### Core Libraries (`src/lib/`)

- **`auth.ts`** - NextAuth configuration
- **`db.ts`** - Prisma database client
- **`utils.ts`** - General utility functions
- **`validations/`** - Zod schemas for form validation
- **`email-templates/`** - Email template components
- **`payment/`** - Payment provider integrations
- **`utils/`** - Specialized utility modules

### Custom Hooks (`src/hooks/`)

- Prefixed with `use-` for React hooks
- Feature-specific hooks (tasks, wallet, admin, etc.)
- API interaction hooks using TanStack Query

### Type Definitions (`src/types/`)

- TypeScript type definitions
- Extends Next.js and library types
- Database model types (generated by Prisma)

### Internationalization (`src/i18n/`)

- **`routing.ts`** - Route configuration for locales
- **`navigation.ts`** - Internationalized navigation helpers
- **`request.ts`** - Server-side i18n configuration

## Key Conventions

### File Naming

- **Components**: PascalCase (e.g., `TaskDetailSheet.tsx`)
- **Hooks**: kebab-case with `use-` prefix (e.g., `use-tasks.ts`)
- **Utilities**: kebab-case (e.g., `file-utils.ts`)
- **API routes**: kebab-case directories and `route.ts` files
- **Pages**: kebab-case directories with `page.tsx`

### Import Aliases

- **`@/*`** - Maps to `src/*` for absolute imports
- Always use absolute imports for src/ files
- Relative imports only for same-directory files

### Component Organization

- One component per file
- Co-locate related components in feature directories
- Separate UI components from business logic components
- Use barrel exports (`index.ts`) for component groups

### Database & API

- **Prisma models** - PascalCase with descriptive names
- **API endpoints** - RESTful structure under `/api/`
- **Database operations** - Centralized in `src/lib/db.ts`
- **Validation schemas** - Zod schemas in `src/lib/validations/`

### Testing Structure (`__tests__/`)

- **`unit/`** - Component and utility unit tests
- **`integration/`** - API and database integration tests
- **`e2e/`** - End-to-end tests (Playwright)
- **`fixtures/`** - Test data and mocks
- **`utils/`** - Testing utilities and helpers

### Configuration Files

- **Root level** - Framework and tool configurations
- **Environment** - `.env` for local, `.env.example` for template
- **TypeScript** - Strict mode enabled with path mapping
- **ESLint/Prettier** - Comprehensive code quality rules
