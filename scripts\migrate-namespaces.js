#!/usr/bin/env node

/**
 * 迁移脚本：将 PascalCase 命名空间转换为 kebab-case
 * 
 * 使用方法：
 * node scripts/migrate-namespaces.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 命名空间映射表
const namespaceMapping = {
  'Common': 'common',
  'Navigation': 'navigation',
  'HomePage': 'homepage',
  'Dashboard': 'dashboard',
  'Tasks': 'tasks',
  'Auth': 'auth',
  'Wallet': 'wallet',
  'transactions': 'transactions',
  'Admin': 'admin',
  'Email': 'email',
  'Tickets': 'tickets',
  'Publish': 'publish',
  'Review': 'review',
  'Membership': 'membership',
  'AccountSecurity': 'account-security',
  'MyAcceptedTasks': 'my-accepted-tasks',
  'MyPublishedTasks': 'my-published-tasks',
  'ShopWhitelist': 'shop-whitelist',
  'Whitelist': 'whitelist',
  'Legal': 'legal',
  'Logistics': 'logistics',
  'LogisticsInfo': 'logistics-info',
  'Messages': 'messages',
  'Footer': 'footer',
};

/**
 * 处理单个文件
 */
function processFile(filePath) {
  console.log(`处理文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 替换 useTranslations 调用
  Object.entries(namespaceMapping).forEach(([oldName, newName]) => {
    const patterns = [
      // useTranslations('OldName')
      new RegExp(`useTranslations\\(['"]${oldName}['"]\\)`, 'g'),
      // useTranslations("OldName")
      new RegExp(`useTranslations\\(["']${oldName}["']\\)`, 'g'),
    ];
    
    patterns.forEach(pattern => {
      if (pattern.test(content)) {
        content = content.replace(pattern, `useTranslations('${newName}')`);
        modified = true;
        console.log(`  替换: ${oldName} -> ${newName}`);
      }
    });
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ 文件已更新`);
  } else {
    console.log(`  ⏭️  无需更改`);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始迁移命名空间...\n');
  
  // 查找所有 TypeScript 和 JavaScript 文件
  const patterns = [
    'src/**/*.{ts,tsx,js,jsx}',
    'app/**/*.{ts,tsx,js,jsx}',
    'components/**/*.{ts,tsx,js,jsx}',
    'pages/**/*.{ts,tsx,js,jsx}',
  ];
  
  let totalFiles = 0;
  
  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { 
      ignore: [
        '**/node_modules/**',
        '**/.next/**',
        '**/dist/**',
        '**/build/**',
        '**/*.test.*',
        '**/*.spec.*'
      ]
    });
    
    files.forEach(file => {
      processFile(file);
      totalFiles++;
    });
  });
  
  console.log(`\n✅ 迁移完成！共处理 ${totalFiles} 个文件`);
  console.log('\n📝 下一步：');
  console.log('1. 检查代码是否正常工作');
  console.log('2. 运行测试确保功能正常');
  console.log('3. 重启 VSCode 以刷新 i18n Ally');
  console.log('4. 验证 i18n Ally 功能是否正常');
}

// 检查是否安装了 glob
try {
  require('glob');
} catch (error) {
  console.error('❌ 请先安装 glob 包：npm install glob');
  process.exit(1);
}

main();
