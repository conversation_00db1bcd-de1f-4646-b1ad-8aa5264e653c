import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';
import { hashPassword } from '@/lib/password';
import { signUpSchema } from '@/lib/validations/auth';

/**
 * 从请求头检测用户语言偏好
 */
function detectUserLanguage(request: NextRequest): 'zh' | 'en' {
  // 1. 检查 URL 参数中的语言设置
  const url = new URL(request.url);
  const langParam = url.searchParams.get('lang');
  if (langParam === 'zh' || langParam === 'en') {
    return langParam;
  }

  // 2. 检查 Accept-Language 头
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    // 简单的语言检测逻辑
    if (acceptLanguage.includes('zh')) {
      return 'zh';
    }
  }

  // 3. 默认返回英文
  return 'en';
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 检测用户语言偏好
    const userLanguage = detectUserLanguage(request);

    // 验证请求数据
    const validationResult = signUpSchema.safeParse(body);
    if (!validationResult.success) {
      const errorMessage =
        userLanguage === 'zh' ? '输入数据无效' : 'Invalid input data';
      return NextResponse.json(
        {
          error: errorMessage,
          details: validationResult.error.flatten().fieldErrors,
        },
        { status: 400 },
      );
    }

    const { name, email, password } = validationResult.data;

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      const errorMessage =
        userLanguage === 'zh'
          ? '该邮箱已被注册'
          : 'This email is already registered';
      return NextResponse.json({ error: errorMessage }, { status: 409 });
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户（包含语言设置）
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: 'USER',
        registrationLanguage: userLanguage, // 保存用户注册时的语言偏好
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        registrationLanguage: true,
        createdAt: true,
      },
    });

    const successMessage =
      userLanguage === 'zh' ? '注册成功' : 'Registration successful';
    return NextResponse.json(
      {
        message: successMessage,
        user,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error('Registration error:', error);
    // 这里无法获取用户语言，使用默认错误消息
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
