import { <PERSON>, <PERSON><PERSON>to } from 'next/font/google';
import './globals.css';
import { getLocale } from 'next-intl/server';
import NextTopLoader from 'nextjs-toploader';
import React, { ReactNode } from 'react';

import { ClientOnly } from '@/components/client-only';
import { CrispChat } from '@/components/crisp-chat';
import Providers from '@/components/Providers';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { cn, constructMetadata } from '@/lib/utils';

// 字体配置
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
});

const nunito = Nunito({
  subsets: ['latin'],
  variable: '--font-heading',
});

// 默认metadata
export const metadata = constructMetadata();

interface RootLayoutProps {
  children: ReactNode;
}

export default async function RootLayout({ children }: RootLayoutProps) {
  // 获取当前语言
  const locale = await getLocale();

  return (
    <html lang={locale} className='!scroll-smooth' suppressHydrationWarning>
      <body
        className={cn(
          'min-h-screen font-sans antialiased',
          inter.variable,
          nunito.variable,
        )}
      >
        <ThemeProvider
          attribute='class'
          defaultTheme='system'
          enableSystem
          disableTransitionOnChange
        >
          <NextTopLoader color='black' showSpinner={false} />
          <Providers>
            <Toaster />
            {children}
            <ClientOnly>
              <CrispChat />
            </ClientOnly>
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  );
}
