'use client';

import { Plus } from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState, useMemo } from 'react';

import { TaskFiltersComponent } from '@/components/task-filters';
import { TaskList } from '@/components/task-list';
import { Button } from '@/components/ui/button';
import {
  useActivePlatforms,
  useActiveChargebackTypes,
  useActivePaymentMethods,
} from '@/hooks/use-publish-config';
import { useTasks } from '@/hooks/use-tasks';
import { TaskFilters } from '@/lib/types/task';

export default function TasksPageContent() {
  const t = useTranslations('Tasks');
  const [filters, setFilters] = useState<TaskFilters>({
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  // 获取数据库数据用于显示标签
  const { data: platforms = [] } = useActivePlatforms();
  const { data: chargebackTypes = [] } = useActiveChargebackTypes();
  const { data: paymentMethods = [] } = useActivePaymentMethods();

  // 创建ID到名称的映射
  const platformNameMap = useMemo(() => {
    return platforms.reduce(
      (map, platform) => {
        map[platform.id] = platform.name;
        return map;
      },
      {} as Record<string, string>,
    );
  }, [platforms]);

  const chargebackTypeNameMap = useMemo(() => {
    return Array.isArray(chargebackTypes)
      ? chargebackTypes.reduce(
          (map, type) => {
            map[type.id] = type.name;
            return map;
          },
          {} as Record<string, string>,
        )
      : {};
  }, [chargebackTypes]);

  const paymentMethodNameMap = useMemo(() => {
    return Array.isArray(paymentMethods)
      ? paymentMethods.reduce(
          (map, method) => {
            map[method.id] = method.name;
            return map;
          },
          {} as Record<string, string>,
        )
      : {};
  }, [paymentMethods]);

  // 获取真实委托数据
  const {
    data: tasksResponse,
    isLoading: tasksLoading,
    error: tasksError,
  } = useTasks(filters);
  const tasks = tasksResponse?.data?.tasks || [];

  const handleFiltersChange = (newFilters: TaskFilters) => {
    setFilters(newFilters);
  };

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-start justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>
            {t('navigation.taskHall')}
          </h1>
          <p className='text-muted-foreground'>{t('navigation.browseTasks')}</p>
        </div>
        <Link href='/publish'>
          <Button>
            <Plus className='h-4 w-4 mr-2' />
            {t('actions.publish')}
          </Button>
        </Link>
      </div>

      {/* 筛选器区域 - 现在放在顶部 */}
      <TaskFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* 当前筛选状态显示 */}
      {(filters.search ||
        filters.platform?.length ||
        filters.chargebackType?.length ||
        filters.paymentMethod?.length) && (
        <div className='bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg border'>
          <div className='flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400'>
            <span>{t('filters.title')}:</span>
            <div className='flex flex-wrap gap-1'>
              {filters.search && (
                <span className='bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded text-xs'>
                  {t('search.hint')}: {filters.search}
                </span>
              )}
              {filters.platform?.map(platformId => (
                <span
                  key={platformId}
                  className='bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded text-xs'
                >
                  {platformNameMap[platformId] || platformId}
                </span>
              ))}
              {filters.chargebackType?.map(typeId => (
                <span
                  key={typeId}
                  className='bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded text-xs'
                >
                  {chargebackTypeNameMap[typeId] || typeId}
                </span>
              ))}
              {filters.paymentMethod?.map(methodId => (
                <span
                  key={methodId}
                  className='bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded text-xs'
                >
                  {paymentMethodNameMap[methodId] || methodId}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 委托列表区域 */}
      <div className='space-y-4'>
        {tasksLoading ? (
          <div className='flex items-center justify-center py-8'>
            <div className='text-muted-foreground'>
              {t('emptyState.loadingTasks')}
            </div>
          </div>
        ) : tasksError ? (
          <div className='flex items-center justify-center py-8'>
            <div className='text-destructive'>{t('messages.loadFailed')}</div>
          </div>
        ) : (
          <TaskList tasks={tasks} />
        )}
      </div>
    </div>
  );
}
