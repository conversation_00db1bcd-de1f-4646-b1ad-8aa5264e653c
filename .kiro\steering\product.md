# Product Overview

Refundgo Web is a comprehensive multilingual task publishing and completion platform that connects
task publishers with task accepters. The platform facilitates e-commerce related tasks such as
product purchases, reviews, and logistics tracking with integrated payment processing.

## Core Features

- **Task Management**: Users can publish tasks (typically e-commerce related) and others can accept
  and complete them
- **Payment System**: Integrated wallet system with multiple payment providers and withdrawal
  methods
- **Multilingual Support**: Full internationalization with English and Chinese locales
- **User Authentication**: OAuth integration with Google and GitHub
- **Admin Dashboard**: Comprehensive management interface for users, tasks, and system operations
- **Membership System**: Tiered subscriptions (FREE, PRO, BUSINESS) with different privileges
- **Support System**: Customer support ticketing system
- **Evidence Management**: File upload and review system for task completion proof
- **Logistics Tracking**: Integration with 17track for package monitoring

## User Roles

- **USER**: Regular platform users who can publish and accept tasks
- **ADMIN**: System administrators with full platform management capabilities

## Business Model

The platform operates on a commission-based model where users pay fees for task publishing and
completion, with different rates based on membership tiers and payment methods.
