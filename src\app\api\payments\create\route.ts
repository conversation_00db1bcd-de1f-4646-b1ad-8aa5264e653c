import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { PaymentManager } from '@/lib/payment/payment-manager';

// 创建支付订单的验证Schema
const createPaymentSchema = z.object({
  amount: z.number().positive('金额必须大于0'),
  paymentMethod: z.string().min(1, '请选择支付方式'),
  description: z.string().optional(),
  returnUrl: z.string().url('返回链接格式不正确').optional(),
  notifyUrl: z.string().url('通知链接格式不正确').optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const data = createPaymentSchema.parse(body);

    // 获取用户ID
    const userId = (session.user as any).id;

    // 生成唯一订单号
    const orderNo = `PAY${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // 获取支付配置
    const configs = await prisma.paymentConfig.findMany({
      where: { isEnabled: true },
    });

    if (configs.length === 0) {
      return NextResponse.json(
        { error: '暂无可用的支付方式' },
        { status: 400 },
      );
    }

    // 根据支付方式选择提供商
    let provider: string;
    let providerConfig: any;
    let minAmount: number = 0;

    if (
      data.paymentMethod === 'alipay' ||
      data.paymentMethod === 'wxpay' ||
      data.paymentMethod === 'paypal'
    ) {
      provider = 'yunpay';
      providerConfig = configs.find(c => c.provider === 'yunpay');

      // 获取对应支付方式的最小支付金额
      if (providerConfig?.settings?.paymentMethods) {
        const paymentMethodConfig =
          providerConfig.settings.paymentMethods[data.paymentMethod];
        if (paymentMethodConfig && paymentMethodConfig.enabled) {
          minAmount = paymentMethodConfig.minAmount || 0;
        }
      }
    } else if (data.paymentMethod === 'crypto') {
      provider = 'nowpayments';
      providerConfig = configs.find(c => c.provider === 'nowpayments');

      // 获取NOWPayments的最小支付金额
      if (providerConfig?.settings?.minAmount) {
        minAmount = providerConfig.settings.minAmount;
      }
    } else {
      return NextResponse.json({ error: '不支持的支付方式' }, { status: 400 });
    }

    if (!providerConfig) {
      return NextResponse.json({ error: '支付配置未找到' }, { status: 400 });
    }

    // 验证最小支付金额
    if (minAmount > 0 && data.amount < minAmount) {
      return NextResponse.json(
        {
          error: `支付金额不能少于 $${minAmount.toFixed(2)}`,
        },
        { status: 400 },
      );
    }

    // 创建支付订单记录
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        orderNo,
        provider,
        paymentMethod: data.paymentMethod,
        amount: data.amount,
        currency: 'USD',
        description: data.description || '订单支付',
        returnUrl: data.returnUrl || null,
        notifyUrl: data.notifyUrl || null,
        userId,
        status: 'PENDING',
        expiredAt: new Date(Date.now() + 30 * 60 * 1000), // 30分钟过期
      },
    });

    // 记录支付日志
    await prisma.paymentLog.create({
      data: {
        orderNo,
        action: 'CREATE',
        request: {
          amount: data.amount,
          paymentMethod: data.paymentMethod,
          userId,
        },
        status: 'SUCCESS',
        message: 'Payment order created successfully',
      },
    });

    // 初始化支付管理器
    const paymentManager = new PaymentManager();

    try {
      // 创建支付订单
      const paymentResult = await paymentManager.createPayment({
        orderNo,
        amount: data.amount,
        currency: 'USD',
        paymentMethod: data.paymentMethod,
        description: data.description || '订单支付',
        returnUrl: data.returnUrl || '',
        notifyUrl: data.notifyUrl || '',
        provider,
        userId,
      });

      // 更新订单信息，包括货币转换信息
      const updateData: any = {
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        thirdOrderNo: paymentResult.thirdOrderNo,
      };

      // 如果有货币转换信息，记录到数据库
      if (paymentResult.currencyConversion) {
        const conversion = paymentResult.currencyConversion;
        updateData.originalAmount = conversion.originalAmount;
        updateData.originalCurrency = conversion.originalCurrency;
        updateData.convertedAmount = conversion.convertedAmount;
        updateData.convertedCurrency = conversion.convertedCurrency;
        updateData.exchangeRate = conversion.exchangeRate;
        updateData.exchangeRateSource = conversion.source;
      }

      await prisma.paymentOrder.update({
        where: { orderNo },
        data: updateData,
      });

      // 记录成功日志
      await prisma.paymentLog.create({
        data: {
          orderNo,
          action: 'CREATE',
          request: data,
          response: JSON.parse(JSON.stringify(paymentResult)),
          status: 'SUCCESS',
          message: 'Payment created successfully',
        },
      });

      return NextResponse.json({
        success: true,
        orderNo,
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        amount: data.amount,
        expiredAt: paymentOrder.expiredAt,
      });
    } catch (paymentError: any) {
      // 更新订单状态为失败
      await prisma.paymentOrder.update({
        where: { orderNo },
        data: { status: 'FAILED' },
      });

      // 记录错误日志
      await prisma.paymentLog.create({
        data: {
          orderNo,
          action: 'CREATE',
          request: data,
          response: { error: paymentError.message },
          status: 'FAILED',
          message: paymentError.message,
        },
      });

      throw paymentError;
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: '参数验证失败', details: error.errors },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : '创建支付失败',
      },
      { status: 500 },
    );
  }
}
