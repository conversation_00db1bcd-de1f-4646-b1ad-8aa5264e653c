import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendEmail } from '@/lib/email';
import { getUserEmailLanguage } from '@/lib/email-language-detection';
import {
  verificationCodeTemplateI18n,
  type VerificationCodeData,
} from '@/lib/email-templates/verification-code-i18n';
import { getEmailTranslations } from '@/lib/email-translations';

// 生成6位随机验证码
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 发送验证码Schema
const sendCodeSchema = z.object({
  action: z.literal('send-withdrawal-verification'),
});

// 验证码验证Schema
const verifyCodeSchema = z.object({
  action: z.literal('verify-withdrawal-code'),
  verificationCode: z.string().length(6),
});

// 获取验证码长度错误消息
function getVerificationCodeLengthError(language: 'zh' | 'en'): string {
  return language === 'zh' ? '验证码必须为6位数字' : 'Verification code must be 6 digits';
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      // For unauthorized access, we can't determine user language, so use Accept-Language header
      const language = await getUserEmailLanguage(undefined, request, 'zh');
      return NextResponse.json(
        {
          success: false,
          message: language === 'zh' ? '未授权访问' : 'Unauthorized access'
        },
        { status: 401 },
      );
    }

    const userId = (session.user as any).id;
    const body = await request.json();
    const { action } = body;

    // 发送提现验证码
    if (action === 'send-withdrawal-verification') {
      const validation = sendCodeSchema.safeParse(body);
      if (!validation.success) {
        const language = await getUserEmailLanguage(userId, request, 'zh');
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh' ? '请求参数无效' : 'Invalid request parameters'
          },
          { status: 400 },
        );
      }

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true, name: true },
      });

      if (!user || !user.email) {
        const language = await getUserEmailLanguage(userId, request, 'zh');
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh'
              ? '用户不存在或邮箱地址无效'
              : 'User not found or invalid email address'
          },
          { status: 404 },
        );
      }

      // 生成验证码
      const verificationCode = generateVerificationCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

      // 删除之前的验证码记录
      await prisma.verificationToken.deleteMany({
        where: {
          identifier: `${userId}:WITHDRAWAL_VERIFY`,
        },
      });

      // 创建新的验证码记录
      await prisma.verificationToken.create({
        data: {
          identifier: `${userId}:WITHDRAWAL_VERIFY`,
          token: verificationCode,
          expires: expiresAt,
        },
      });

      // 发送邮件
      try {
        // 获取用户语言偏好
        const language = await getUserEmailLanguage(userId, request, 'zh');

        // 获取翻译
        const translations = getEmailTranslations(language);

        const emailData: VerificationCodeData = {
          userName: user.name || (language === 'zh' ? '用户' : 'User'),
          userEmail: user.email,
          verificationCode,
          expiresIn: 10,
          action: 'withdrawal-verification' as const,
          language,
        };

        await sendEmail({
          to: user.email,
          subject: translations.notifications.verification.withdrawalVerification.subject,
          html: verificationCodeTemplateI18n(emailData),
        });

        return NextResponse.json({
          success: true,
          message: language === 'zh'
            ? '验证码已发送到您的邮箱，请查收'
            : 'Verification code has been sent to your email, please check',
        });
      } catch (emailError) {
        console.error('发送邮件失败:', emailError);
        // Get language for error message (fallback to zh if not available)
        const language = await getUserEmailLanguage(userId, request, 'zh');
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh'
              ? '验证码发送失败，请重试'
              : 'Failed to send verification code, please try again'
          },
          { status: 500 },
        );
      }
    }

    // 验证提现验证码
    if (action === 'verify-withdrawal-code') {
      const validation = verifyCodeSchema.safeParse(body);
      if (!validation.success) {
        const language = await getUserEmailLanguage(userId, request, 'zh');
        // Check if the error is specifically about verification code length
        const hasLengthError = validation.error.issues.some(
          issue => issue.path.includes('verificationCode') && issue.code === 'too_small'
        );

        return NextResponse.json(
          {
            success: false,
            message: language === 'zh' ? '数据验证失败' : 'Data validation failed',
            details: hasLengthError
              ? [getVerificationCodeLengthError(language)]
              : validation.error.issues.map(issue => issue.message),
          },
          { status: 400 },
        );
      }

      const { verificationCode } = validation.data;

      // 验证验证码
      const codeRecord = await prisma.verificationToken.findFirst({
        where: {
          identifier: `${userId}:WITHDRAWAL_VERIFY`,
          token: verificationCode,
          expires: {
            gt: new Date(),
          },
        },
      });

      if (!codeRecord) {
        const language = await getUserEmailLanguage(userId, request, 'zh');
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh'
              ? '验证码无效或已过期'
              : 'Verification code is invalid or expired'
          },
          { status: 400 },
        );
      }

      // 创建一个临时的提现授权token，有效期5分钟
      const withdrawalToken =
        generateVerificationCode() + Date.now().toString();
      const tokenExpiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期

      // 删除验证码记录
      await prisma.verificationToken.delete({
        where: {
          identifier_token: {
            identifier: codeRecord.identifier,
            token: codeRecord.token,
          },
        },
      });

      // 创建提现授权token
      await prisma.verificationToken.create({
        data: {
          identifier: `${userId}:WITHDRAWAL_AUTH`,
          token: withdrawalToken,
          expires: tokenExpiresAt,
        },
      });

      const language = await getUserEmailLanguage(userId, request, 'zh');
      return NextResponse.json({
        success: true,
        message: language === 'zh' ? '邮箱验证成功' : 'Email verification successful',
        data: {
          withdrawalToken,
        },
      });
    }

    const language = await getUserEmailLanguage(userId, request, 'zh');
    return NextResponse.json(
      {
        success: false,
        message: language === 'zh' ? '无效的操作类型' : 'Invalid operation type'
      },
      { status: 400 },
    );
  } catch (error) {
    console.error('提现验证处理失败:', error);
    // For server errors, we might not have userId, so use request-based detection
    const language = await getUserEmailLanguage(undefined, request, 'zh');
    return NextResponse.json(
      {
        success: false,
        message: language === 'zh' ? '服务器内部错误' : 'Internal server error'
      },
      { status: 500 },
    );
  }
}
