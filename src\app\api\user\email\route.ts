import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendEmail } from '@/lib/email';
import { getUserEmailLanguage } from '@/lib/email-language-detection';
import {
  verificationCodeTemplateI18n,
  type VerificationCodeData,
} from '@/lib/email-templates/verification-code-i18n';
import { getEmailTranslations } from '@/lib/email-translations';

// 生成6位随机验证码
function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 发送邮箱验证码
const sendCodeSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址').optional(),
});

// 验证码验证Schema
const verifyCodeSchema = z.object({
  verificationCode: z.string().length(6, '验证码必须为6位数字'),
});

// 更换邮箱验证Schema
const changeEmailSchema = z.object({
  newEmail: z.string().email('请输入有效的邮箱地址'),
  verificationCode: z.string().length(6, '验证码必须为6位数字'),
});

// 发送验证码
export async function POST(request: NextRequest) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const body = await request.json();
    const { action } = body;

    // 发送验证码到当前邮箱
    if (action === 'send-current-email-code') {
      // 获取当前用户信息（包括语言偏好）
      const currentUser = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: {
          email: true,
          name: true,
          registrationLanguage: true,
        },
      });

      if (!currentUser || !currentUser.email) {
        return NextResponse.json(
          { success: false, message: '用户不存在或邮箱地址无效' },
          { status: 404 },
        );
      }

      // 🔍 添加详细调试日志
      console.log('\n🚀 开始邮件验证码发送流程...');
      console.log(`👤 用户ID: ${session.user.id}`);
      console.log(`📧 目标邮箱: ${currentUser.email}`);
      console.log(`🌐 请求URL: ${request.url}`);
      console.log(`📍 Referer头部: ${request.headers.get('referer')}`);
      console.log(`🌍 Accept-Language头部: ${request.headers.get('accept-language')}`);
      console.log(`👤 用户注册语言: ${currentUser.registrationLanguage || '未设置'}`);

      // 使用智能语言检测
      console.log('\n🔍 开始语言检测...');
      const language = await getUserEmailLanguage(
        session.user.id,
        request,
        'zh',
      );

      console.log(`✅ 最终检测语言: ${language}`);

      // 获取对应语言的翻译
      const translations = getEmailTranslations(language);
      console.log(`📝 使用翻译系统: ${language === 'zh' ? '中文' : '英文'}`);

      // 生成验证码
      const verificationCode = generateVerificationCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

      // 删除之前的验证码记录
      await prisma.verificationToken.deleteMany({
        where: {
          identifier: `${session.user.id}:CURRENT_EMAIL_VERIFY`,
        },
      });

      // 创建新的验证码记录
      await prisma.verificationToken.create({
        data: {
          identifier: `${session.user.id}:CURRENT_EMAIL_VERIFY`,
          token: verificationCode,
          expires: expiresAt,
        },
      });

      // 发送邮件
      try {
        const emailData: VerificationCodeData = {
          userName: currentUser.name || (language === 'zh' ? '用户' : 'User'),
          userEmail: currentUser.email,
          verificationCode,
          expiresIn: 10,
          action: 'verify-current-email',
          language,
        };

        // 根据操作类型选择正确的邮件主题
        let emailSubject: string;
        switch (emailData.action) {
          case 'verify-current-email':
            emailSubject = translations.notifications.verification.verifyEmail.subject;
            break;
          case 'change-email':
            emailSubject = translations.notifications.verification.changeEmail.subject;
            break;
          case 'login':
            emailSubject = translations.notifications.verification.login.subject;
            break;
          case 'register':
            emailSubject = translations.notifications.verification.register.subject;
            break;
          case 'reset-password':
            emailSubject = translations.notifications.verification.resetPassword.subject;
            break;
          default:
            emailSubject = translations.notifications.verification.verifyEmail.subject;
        }

        console.log('\n📧 准备发送邮件...');
        console.log(`📋 邮件数据:`, {
          action: emailData.action,
          language: emailData.language,
          userName: emailData.userName,
          userEmail: emailData.userEmail,
          verificationCode: '******', // 隐藏验证码
          expiresIn: emailData.expiresIn,
        });
        console.log(`📬 邮件主题: ${emailSubject}`);
        console.log(`🎯 目标邮箱: ${currentUser.email}`);

        // 生成邮件内容并记录前100个字符用于调试
        const emailHtml = verificationCodeTemplateI18n(emailData);
        console.log(`📄 邮件内容预览: ${emailHtml.substring(0, 200)}...`);

        await sendEmail({
          to: currentUser.email,
          subject: emailSubject,
          html: emailHtml,
        });

        console.log('✅ 邮件发送成功!');

        return NextResponse.json({
          success: true,
          message: language === 'zh'
            ? '验证码已发送到当前邮箱，请查收'
            : 'Verification code has been sent to your current email, please check',
        });
      } catch (emailError) {
        console.error('发送邮件失败:', emailError);
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh'
              ? '验证码发送失败，请重试'
              : 'Failed to send verification code, please try again',
          },
          { status: 500 },
        );
      }
    }

    // 验证当前邮箱验证码
    if (action === 'verify-current-email') {
      const validation = verifyCodeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            success: false,
            message: '数据验证失败',
            details: validation.error.issues.map(issue => issue.message),
          },
          { status: 400 },
        );
      }

      const { verificationCode } = validation.data;

      // 验证验证码
      const codeRecord = await prisma.verificationToken.findFirst({
        where: {
          identifier: `${session.user.id}:CURRENT_EMAIL_VERIFY`,
          token: verificationCode,
          expires: {
            gt: new Date(),
          },
        },
      });

      if (!codeRecord) {
        return NextResponse.json(
          { success: false, message: '验证码无效或已过期' },
          { status: 400 },
        );
      }

      // 删除验证码记录
      await prisma.verificationToken.delete({
        where: {
          identifier_token: {
            identifier: codeRecord.identifier,
            token: codeRecord.token,
          },
        },
      });

      return NextResponse.json({
        success: true,
        message: '当前邮箱验证成功',
      });
    }

    // 发送验证码到新邮箱
    if (action === 'send-new-email-code') {
      // 🔍 添加详细调试日志
      console.log('\n🚀 开始新邮箱验证码发送流程...');
      console.log(`👤 用户ID: ${session.user.id}`);
      console.log(`🌐 请求URL: ${request.url}`);
      console.log(`📍 Referer头部: ${request.headers.get('referer')}`);
      console.log(`🌍 Accept-Language头部: ${request.headers.get('accept-language')}`);

      // 使用智能语言检测
      console.log('\n🔍 开始语言检测...');
      const language = await getUserEmailLanguage(
        session.user.id,
        request,
        'zh',
      );

      console.log(`✅ 最终检测语言: ${language}`);

      // 获取对应语言的翻译
      const translations = getEmailTranslations(language);
      console.log(`📝 使用翻译系统: ${language === 'zh' ? '中文' : '英文'}`);

      const validation = sendCodeSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh' ? '数据验证失败' : 'Data validation failed',
            details: validation.error.issues.map(issue => issue.message),
          },
          { status: 400 },
        );
      }

      const { email } = validation.data;

      if (!email) {
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh' ? '请提供新邮箱地址' : 'Please provide new email address',
          },
          { status: 400 },
        );
      }

      // 检查邮箱是否已被其他用户使用
      const existingUser = await prisma.user.findFirst({
        where: {
          email,
          id: { not: session.user.id },
        },
      });

      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh'
              ? '该邮箱已被其他用户使用'
              : 'This email is already used by another user',
          },
          { status: 400 },
        );
      }

      // 生成验证码
      const verificationCode = generateVerificationCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

      // 删除之前的验证码记录
      await prisma.verificationToken.deleteMany({
        where: {
          identifier: `${session.user.id}:EMAIL_CHANGE:${email}`,
        },
      });

      // 创建新的验证码记录
      await prisma.verificationToken.create({
        data: {
          identifier: `${session.user.id}:EMAIL_CHANGE:${email}`,
          token: verificationCode,
          expires: expiresAt,
        },
      });

      // 发送邮件
      try {
        const emailData: VerificationCodeData = {
          userName: session.user.name || (language === 'zh' ? '用户' : 'User'),
          userEmail: email,
          verificationCode,
          expiresIn: 10,
          action: 'change-email',
          language,
        };

        await sendEmail({
          to: email,
          subject: translations.notifications.verification.changeEmail.subject,
          html: verificationCodeTemplateI18n(emailData),
        });

        return NextResponse.json({
          success: true,
          message: language === 'zh'
            ? '验证码已发送到新邮箱，请查收'
            : 'Verification code has been sent to the new email, please check',
        });
      } catch (emailError) {
        console.error('发送邮件失败:', emailError);
        return NextResponse.json(
          {
            success: false,
            message: language === 'zh'
              ? '验证码发送失败，请重试'
              : 'Failed to send verification code, please try again',
          },
          { status: 500 },
        );
      }
    }

    return NextResponse.json(
      { success: false, message: '未知的操作类型' },
      { status: 400 },
    );
  } catch (error) {
    console.error('邮箱操作失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 更换邮箱
export async function PATCH(request: NextRequest) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const body = await request.json();
    const validation = changeEmailSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: '数据验证失败',
          details: validation.error.issues.map(issue => issue.message),
        },
        { status: 400 },
      );
    }

    const { newEmail, verificationCode } = validation.data;

    // 验证验证码
    const codeRecord = await prisma.verificationToken.findFirst({
      where: {
        identifier: `${session.user.id}:EMAIL_CHANGE:${newEmail}`,
        token: verificationCode,
        expires: {
          gt: new Date(),
        },
      },
    });

    if (!codeRecord) {
      return NextResponse.json(
        { success: false, message: '验证码无效或已过期' },
        { status: 400 },
      );
    }

    // 再次检查邮箱是否已被其他用户使用
    const existingUser = await prisma.user.findFirst({
      where: {
        email: newEmail,
        id: { not: session.user.id },
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { success: false, message: '该邮箱已被其他用户使用' },
        { status: 400 },
      );
    }

    // 使用事务更新邮箱和删除验证码
    await prisma.$transaction([
      prisma.user.update({
        where: { id: session.user.id },
        data: {
          email: newEmail,
          emailVerified: new Date(), // 标记新邮箱为已验证
          updatedAt: new Date(),
        },
      }),
      prisma.verificationToken.delete({
        where: {
          identifier_token: {
            identifier: codeRecord.identifier,
            token: codeRecord.token,
          },
        },
      }),
    ]);

    return NextResponse.json({
      success: true,
      message: '邮箱更换成功',
    });
  } catch (error) {
    console.error('更换邮箱失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
