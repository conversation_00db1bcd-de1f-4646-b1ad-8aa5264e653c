import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/hooks/useEmailTranslation';
import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendTaskCancelledPublisherEmail } from '@/lib/email';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const resolvedParams = await params;
    const taskId = resolvedParams.id;

    // 查找委托并验证所有权
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            name: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 验证委托所有权
    if (task.publisher.id !== session.user.id) {
      return NextResponse.json({ error: '无权操作此委托' }, { status: 403 });
    }

    // 检查委托状态，只有审核中和招募中的委托可以取消
    if (
      task.status !== TaskStatus.RECRUITING &&
      task.status !== TaskStatus.PENDING
    ) {
      return NextResponse.json(
        { error: '只能取消审核中或招募中的委托' },
        { status: 400 },
      );
    }

    // 使用事务处理取消操作和退款
    const userId = session.user.id!;
    const result = await prisma.$transaction(async tx => {
      // 获取系统费率配置
      const systemRate = await tx.systemRate.findFirst();
      const evidenceRate = systemRate?.noEvidenceExtraRate || 2.0;

      // 更新委托状态为已取消
      const updatedTask = await tx.task.update({
        where: { id: taskId },
        data: {
          status: TaskStatus.CANCELLED,
          updatedAt: new Date(),
        },
      });

      // 计算证据费和非证据费
      const evidenceFee = (task.totalAmount * evidenceRate) / 100;
      const nonEvidenceFee = task.finalTotal - evidenceFee;

      // 退还非证据费到可用余额
      if (nonEvidenceFee > 0) {
        await tx.user.update({
          where: { id: userId },
          data: {
            balance: {
              increment: nonEvidenceFee,
            },
          },
        });

        // 创建非证据费退款记录
        await tx.walletTransaction.create({
          data: {
            userId,
            type: 'REFUND',
            amount: nonEvidenceFee,
            status: 'COMPLETED',
            description: `委托 ${taskId} 取消退款（发布费用）`,
            completedAt: new Date(),
          },
        });
      }

      // 退还证据费：从冻结金额释放到可用余额
      if (evidenceFee > 0) {
        await tx.user.update({
          where: { id: userId },
          data: {
            frozenAmount: {
              decrement: evidenceFee,
            },
            balance: {
              increment: evidenceFee,
            },
          },
        });

        // 创建证据费退款记录
        await tx.walletTransaction.create({
          data: {
            userId,
            type: 'REFUND',
            amount: evidenceFee,
            status: 'COMPLETED',
            description: `委托 ${taskId} 取消退款（证据费）`,
            completedAt: new Date(),
          },
        });
      }

      return {
        updatedTask,
        evidenceFee,
        nonEvidenceFee,
      };
    });

    // 发送邮件通知发布者
    try {
      const publisherEmail = task.publisher.email;
      if (publisherEmail) {
        // 获取用户的语言偏好
        const userLanguage = await getUserEmailLanguage(publisherEmail);

        await sendTaskCancelledPublisherEmail(publisherEmail, {
          userName: task.publisher.name || '用户',
          userEmail: publisherEmail,
          taskId: task.id,
          taskTitle:
            task.title ||
            `${task.platform?.name || '未知平台'} - ${task.category?.name || '未知分类'}`,
          cancelledAt: new Date().toISOString(),
          refundAmount: task.finalTotal,
          currency: 'USD',
          cancellationReason: '发布者主动取消',
          language: userLanguage,
        });
      }
    } catch (emailError) {
      console.error('发送委托取消邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '委托已成功取消',
      data: {
        taskId,
        status: 'CANCELLED',
        cancelledAt: result.updatedTask.updatedAt.toISOString(),
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
