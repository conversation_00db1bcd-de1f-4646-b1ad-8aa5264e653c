import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { replyTicketSchema } from '@/lib/types/ticket';

// 验证管理员权限的辅助函数
async function requireAdmin() {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('请先登录');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (user?.role !== 'ADMIN') {
    throw new Error('权限不足');
  }

  return session;
}

// 管理员回复工单
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    const adminSession = await requireAdmin();

    const { id: ticketId } = await params;
    const body = await request.json();

    // 验证请求数据
    const validation = replyTicketSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: '请求数据无效', details: validation.error.errors },
        { status: 400 },
      );
    }

    const { content } = validation.data;

    // 验证工单是否存在
    const ticket = await prisma.ticket.findUnique({
      where: { id: ticketId },
    });

    if (!ticket) {
      return NextResponse.json({ error: '工单不存在' }, { status: 404 });
    }

    // 检查工单状态（已取消的工单不能回复）
    if (ticket.status === 'CANCELLED') {
      return NextResponse.json(
        { error: '此工单已取消，无法添加回复' },
        { status: 400 },
      );
    }

    // 创建管理员回复
    const reply = await prisma.ticketReply.create({
      data: {
        ticketId,
        content,
        authorId: adminSession.user!.id!,
        authorName:
          adminSession.user!.name || adminSession.user!.email || '管理员',
        isStaff: true,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // 如果工单状态是待处理，自动更新为处理中
    if (ticket.status === 'PENDING') {
      await prisma.ticket.update({
        where: { id: ticketId },
        data: {
          status: 'IN_PROGRESS',
          updatedAt: new Date(),
        },
      });
    } else {
      // 更新工单的最后更新时间
      await prisma.ticket.update({
        where: { id: ticketId },
        data: { updatedAt: new Date() },
      });
    }

    return NextResponse.json(reply, { status: 201 });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
