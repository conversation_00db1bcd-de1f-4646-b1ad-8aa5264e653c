import prisma from '@/lib/db';

/**
 * 获取用户的注册语言
 * @param userEmail 用户邮箱
 * @returns 用户的注册语言，默认为 'en'
 */
export async function getUserRegistrationLanguage(
  userEmail: string,
): Promise<'zh' | 'en'> {
  try {
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: { registrationLanguage: true },
    });

    if (!user || !user.registrationLanguage) {
      return 'en'; // 默认英文
    }

    return user.registrationLanguage as 'zh' | 'en';
  } catch (error) {
    console.error('获取用户注册语言失败:', error);
    return 'en'; // 出错时默认英文
  }
}

/**
 * 通过用户ID获取用户的注册语言
 * @param userId 用户ID
 * @returns 用户的注册语言，默认为 'en'
 */
export async function getUserRegistrationLanguageById(
  userId: string,
): Promise<'zh' | 'en'> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { registrationLanguage: true },
    });

    if (!user || !user.registrationLanguage) {
      return 'en'; // 默认英文
    }

    return user.registrationLanguage as 'zh' | 'en';
  } catch (error) {
    console.error('获取用户注册语言失败:', error);
    return 'en'; // 出错时默认英文
  }
}

/**
 * 更新用户的注册语言（仅在必要时使用）
 * @param userEmail 用户邮箱
 * @param language 新的语言设置
 */
export async function updateUserRegistrationLanguage(
  userEmail: string,
  language: 'zh' | 'en',
): Promise<boolean> {
  try {
    await prisma.user.update({
      where: { email: userEmail },
      data: { registrationLanguage: language },
    });
    return true;
  } catch (error) {
    console.error('更新用户注册语言失败:', error);
    return false;
  }
}

/**
 * 批量更新现有用户的注册语言为默认值
 * 用于数据迁移
 */
export async function migrateExistingUsersLanguage(): Promise<void> {
  try {
    // 简化版本：只更新空字符串的情况
    await prisma.user.updateMany({
      where: {
        registrationLanguage: '',
      },
      data: { registrationLanguage: 'en' },
    });
    console.log('现有用户语言迁移完成');
  } catch (error) {
    console.error('用户语言迁移失败:', error);
  }
}
