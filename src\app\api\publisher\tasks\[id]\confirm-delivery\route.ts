import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { getTaskCommission } from '@/lib/utils/commission';

// 发布者确认收货API
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;
    const userId = session.user.id;

    // 2. 查找委托并验证
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 3. 验证用户权限
    if (task.publisherId !== userId) {
      return NextResponse.json(
        { error: '只能确认自己发布的委托' },
        { status: 403 },
      );
    }

    // 4. 检查委托状态
    if (task.status !== ('PENDING_DELIVERY' as any)) {
      return NextResponse.json(
        { error: '只能确认等待收货状态的委托' },
        { status: 400 },
      );
    }

    // 5. 检查接单者
    if (!task.accepterId) {
      return NextResponse.json({ error: '委托没有接单者' }, { status: 400 });
    }

    // 6. 获取系统费率配置和其他费率数据
    const [systemRate, chargebackTypes, paymentMethods] = await Promise.all([
      prisma.systemRate.findFirst(),
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    if (!systemRate) {
      return NextResponse.json({ error: '系统配置错误' }, { status: 500 });
    }

    // 7. 计算相关金额
    const totalPrice = task.unitPrice * task.quantity;
    const depositAmount = totalPrice * (systemRate.depositRatio / 100);

    // 使用真实的酬金计算函数（包含证据状态）
    const taskWithEvidenceStatus = {
      ...task,
      evidenceStatus: task.evidenceStatus?.toString(),
    };
    const commissionAmount = getTaskCommission(
      taskWithEvidenceStatus,
      chargebackTypes,
      paymentMethods,
      systemRate,
    );

    // 8. 使用事务处理确认收货的所有操作
    const result = await prisma.$transaction(async tx => {
      // 8.1 更新委托状态为已完成
      await tx.task.update({
        where: { id: taskId },
        data: {
          status: 'COMPLETED' as any,
          completedAt: new Date(),
        },
      });

      // 8.2 处理证据费的最终归属
      const evidenceFee = (totalPrice * systemRate.noEvidenceExtraRate) / 100;

      if (task.evidenceStatus?.toString() !== 'REVIEWED' && evidenceFee > 0) {
        // 证据未审核通过，从发布者冻结金额中扣除证据费
        await tx.user.update({
          where: { id: task.publisherId },
          data: {
            frozenAmount: {
              decrement: evidenceFee,
            },
          },
        });

        // 创建证据费扣除记录
        await tx.walletTransaction.create({
          data: {
            userId: task.publisherId,
            type: 'TASK_FEE',
            amount: -evidenceFee,
            status: 'COMPLETED',
            description: `委托完成，证据费转为接单者酬金 - ${taskId}`,
            reference: taskId,
            completedAt: new Date(),
          },
        });
      }

      // 8.3 释放接受者的冻结押金并返还到可用余额
      await tx.user.update({
        where: { id: task.accepterId! },
        data: {
          frozenAmount: {
            decrement: depositAmount,
          },
          balance: {
            increment: depositAmount,
          },
        },
      });

      // 8.4 向接受者支付酬金
      await tx.user.update({
        where: { id: task.accepterId! },
        data: {
          balance: {
            increment: commissionAmount,
          },
          totalIncome: {
            increment: commissionAmount,
          },
          completedTasks: {
            increment: 1,
          },
        },
      });

      // 8.5 创建押金释放记录
      await tx.walletTransaction.create({
        data: {
          userId: task.accepterId!,
          type: 'DEPOSIT',
          amount: depositAmount,
          status: 'COMPLETED',
          description: `委托完成押金释放 - ${taskId}`,
          reference: taskId,
          completedAt: new Date(),
        },
      });

      // 8.6 创建酬金收入记录
      await tx.walletTransaction.create({
        data: {
          userId: task.accepterId!,
          type: 'COMMISSION',
          amount: commissionAmount,
          status: 'COMPLETED',
          description: `委托完成酬金 - ${taskId}`,
          reference: taskId,
          completedAt: new Date(),
        },
      });

      return {
        depositAmount,
        commissionAmount,
        evidenceFee:
          task.evidenceStatus?.toString() !== 'REVIEWED' ? evidenceFee : 0,
      };
    });

    // 9. 发送邮件通知
    const completedTime = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    try {
      // 发送给发布者的邮件
      const publisherEmailResponse = await fetch(
        `${process.env.NEXT_PUBLIC_APP_URL}/api/send-email`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'task-completed-publisher',
            to: task.publisher.email,
            data: {
              publisherName: task.publisher.name || '发布者',
              publisherEmail: task.publisher.email,
              taskId: task.id,
              platform: task.platform?.name || '平台',
              category: task.category?.name || '分类',
              quantity: task.quantity,
              unitPrice: task.unitPrice,
              totalAmount: totalPrice,
              completedAt: completedTime,
            },
          }),
        },
      );

      // 发送给接单者的邮件
      const accepterEmailResponse = await fetch(
        `${process.env.NEXT_PUBLIC_APP_URL}/api/send-email`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'task-completed-accepter',
            to: task.accepter!.email,
            data: {
              accepterName: task.accepter?.name || '接单者',
              accepterEmail: task.accepter!.email,
              taskId: task.id,
              platform: task.platform?.name || '平台',
              category: task.category?.name || '分类',
              quantity: task.quantity,
              unitPrice: task.unitPrice,
              totalAmount: totalPrice,
              completedAt: completedTime,
              commissionEarned: commissionAmount,
              depositReleased: depositAmount,
              totalEarned: commissionAmount,
            },
          }),
        },
      );

      // 记录邮件发送结果
      if (!publisherEmailResponse.ok) {
        const publisherError = await publisherEmailResponse.json();
        console.error('发送发布者邮件失败:', publisherError);
      } else {
        console.log('发布者委托完成邮件已发送');
      }

      if (!accepterEmailResponse.ok) {
        const accepterError = await accepterEmailResponse.json();
        console.error('发送接单者邮件失败:', accepterError);
      } else {
        console.log('接单者委托完成邮件已发送');
      }
    } catch (emailError) {
      console.error('发送邮件时出错:', emailError);
    }

    // 10. 返回成功响应
    return NextResponse.json({
      success: true,
      message: `确认收货成功！接单者已获得酬金 $${commissionAmount.toFixed(2)}，押金 $${depositAmount.toFixed(2)} 已释放`,
      data: {
        task: {
          id: taskId,
          status: 'COMPLETED',
          completedAt: new Date().toISOString(),
        },
        rewards: {
          commission: result.commissionAmount,
          depositReleased: result.depositAmount,
          totalEarned: result.commissionAmount,
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
