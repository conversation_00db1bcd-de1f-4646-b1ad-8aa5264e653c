import { useState, useEffect } from 'react';

import { LocationData } from '@/types/crisp';

interface IPLanguageDetectionResult {
  detectedLanguage: string | null;
  isLoading: boolean;
  error: string | null;
  locationData: LocationData | null;
}

export function useIPLanguageDetection(): IPLanguageDetectionResult {
  const [detectedLanguage, setDetectedLanguage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [locationData, setLocationData] = useState<LocationData | null>(null);

  useEffect(() => {
    const detectLanguageFromIP = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/ip-location');
        if (!response.ok) {
          throw new Error('Failed to fetch location data');
        }

        const data: LocationData = await response.json();
        setLocationData(data);

        // 根据国家代码判断语言
        const language = getLanguageFromCountryCode(data.countryCode);
        setDetectedLanguage(language);
      } catch (err) {
        console.error('IP language detection failed:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        // 如果检测失败，默认使用中文
        setDetectedLanguage('zh');
      } finally {
        setIsLoading(false);
      }
    };

    detectLanguageFromIP();
  }, []);

  return {
    detectedLanguage,
    isLoading,
    error,
    locationData,
  };
}

// 根据国家代码判断语言
function getLanguageFromCountryCode(countryCode?: string): string {
  if (!countryCode) {
    return 'zh'; // 默认中文
  }

  // 中文地区：中国大陆、台湾、香港、澳门
  const chineseRegions = ['CN', 'TW', 'HK', 'MO'];

  if (chineseRegions.includes(countryCode.toUpperCase())) {
    return 'zh';
  }

  // 其他地区默认英文
  return 'en';
}
