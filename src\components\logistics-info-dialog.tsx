'use client';

import {
  Truck,
  Copy,
  Package,
  Loader2,
  AlertCircle,
  ExternalLink,
  Eye,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ReactNode, useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ViewOrderDialog } from '@/components/view-order-dialog';

interface LogisticsInfoDialogProps {
  children: ReactNode;
  taskId: string;
  trackingNumber: string;
  taskStatus?: string;
  orderNumber?: string;
  orderScreenshot?: string;
  logisticsScreenshots?: string[];
  orderReviewDeadline?: string;
  logisticsReviewDeadline?: string;
}

interface TrackingData {
  success: boolean;
  data?: any;
  error?: string;
  isTemporary?: boolean; // 新增属性，用于判断是否是临时无物流信息
}

export function LogisticsInfoDialog({
  children,
  taskId,
  trackingNumber,
  taskStatus,
  orderNumber,
  orderScreenshot,
  logisticsScreenshots,
  orderReviewDeadline,
  logisticsReviewDeadline,
}: LogisticsInfoDialogProps) {
  const t = useTranslations('logistics-info');
  const [isOpen, setIsOpen] = useState(false);
  const [trackingData, setTrackingData] = useState<TrackingData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const copyTrackingNumber = () => {
    if (trackingNumber) {
      navigator.clipboard.writeText(trackingNumber);
      toast.success(t('trackingNumberCopied'));
    }
  };

  const registerTracking = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/tasks/${taskId}/logistics/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          carrierCode: undefined, // 让17TRACK自动检测
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        // 重新获取跟踪信息
        await fetchTrackingData();
      } else {
        toast.error(t('registerFailed'), {
          description: result.error,
        });
      }
    } catch (error) {
      toast.error(t('registerFailed'), {
        description: t('networkError'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTrackingData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/tasks/${taskId}/logistics/register`);
      const result = await response.json();

      if (response.ok) {
        if (result.success) {
          // 成功获取到物流数据
          setTrackingData(result);
        } else if (result.isTemporary) {
          // 临时无物流信息（如"暂无物流信息"）
          setTrackingData({
            success: false,
            error: result.error || t('noLogisticsInfo'),
            isTemporary: true,
          });
        } else {
          // 其他错误
          setTrackingData({
            success: false,
            error: result.error || t('fetchLogisticsFailed'),
          });
        }
      } else {
        setTrackingData({
          success: false,
          error: result.error || t('fetchLogisticsFailed'),
        });
      }
    } catch (error) {
      setTrackingData({
        success: false,
        error: t('networkError'),
      });
    } finally {
      setIsLoading(false);
    }
  }, [taskId, t]);

  // 当对话框打开时获取物流数据
  useEffect(() => {
    if (isOpen && trackingNumber) {
      fetchTrackingData();
    }
  }, [isOpen, trackingNumber, fetchTrackingData]);

  const formatTrackingStatus = (status: string) => {
    const statusMap: Record<string, { label: string; color: string }> = {
      NotFound: { label: t('status.notFound'), color: 'text-gray-600' },
      InfoReceived: { label: t('status.infoReceived'), color: 'text-blue-600' },
      InTransit: { label: t('status.inTransit'), color: 'text-yellow-600' },
      Expired: { label: t('status.expired'), color: 'text-red-600' },
      AvailableForPickup: {
        label: t('status.availableForPickup'),
        color: 'text-green-600',
      },
      OutForDelivery: {
        label: t('status.outForDelivery'),
        color: 'text-blue-600',
      },
      DeliveryFailure: {
        label: t('status.deliveryFailure'),
        color: 'text-red-600',
      },
      Delivered: { label: t('status.delivered'), color: 'text-green-600' },
      Exception: { label: t('status.exception'), color: 'text-red-600' },
    };

    return (
      statusMap[status] || {
        label: status || t('status.unknown'),
        color: 'text-gray-600',
      }
    );
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch {
      return dateString;
    }
  };

  const trackInfo = trackingData?.data?.track_info;
  const error = trackingData?.error;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='w-full max-w-lg mx-4 sm:mx-auto max-h-[85vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Truck className='h-5 w-5' />
            {t('title')}
          </DialogTitle>
          <DialogDescription>
            {t('taskId')}: {taskId}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 订单号 */}
          {orderNumber && (
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                  <Package className='h-4 w-4' />
                  <span>{t('orderNumber')}</span>
                </div>
                <Badge
                  variant='secondary'
                  className='bg-green-100 text-green-800 text-xs'
                >
                  {t('submitted')}
                </Badge>
              </div>

              <div className='flex items-center gap-2'>
                <div className='flex-1 p-2 bg-muted rounded text-sm font-mono'>
                  {orderNumber}
                </div>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() =>
                    navigator.clipboard
                      .writeText(orderNumber)
                      .then(() => toast.success(t('orderNumberCopied')))
                  }
                >
                  <Copy className='h-4 w-4' />
                </Button>
                <ViewOrderDialog
                  taskId={taskId}
                  orderNumber={orderNumber}
                  orderScreenshot={orderScreenshot}
                  trackingNumber={trackingNumber}
                  logisticsScreenshots={logisticsScreenshots}
                  orderReviewDeadline={orderReviewDeadline}
                  logisticsReviewDeadline={logisticsReviewDeadline}
                  taskStatus={taskStatus}
                >
                  <Button size='sm' variant='outline'>
                    <Eye className='h-4 w-4' />
                  </Button>
                </ViewOrderDialog>
              </div>
            </div>
          )}

          {/* 物流单号 */}
          <div className='space-y-2'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                <Truck className='h-4 w-4' />
                <span>{t('trackingNumber')}</span>
              </div>
              {taskStatus === 'PENDING_DELIVERY' ? (
                <Badge
                  variant='secondary'
                  className='bg-green-100 text-green-800 text-xs'
                >
                  {t('approved')}
                </Badge>
              ) : (
                <Badge
                  variant='secondary'
                  className='bg-blue-100 text-blue-800 text-xs'
                >
                  {t('reviewing')}
                </Badge>
              )}
            </div>

            <div className='flex items-center gap-2'>
              <div className='flex-1 p-2 bg-muted rounded text-sm font-mono'>
                {trackingNumber}
              </div>
              <Button size='sm' variant='outline' onClick={copyTrackingNumber}>
                <Copy className='h-4 w-4' />
              </Button>
            </div>
          </div>

          {/* 加载状态 */}
          {isLoading && (
            <div className='flex items-center justify-center py-4'>
              <Loader2 className='h-6 w-6 animate-spin' />
              <span className='ml-2'>{t('loading')}</span>
            </div>
          )}

          {/* 错误状态 */}
          {!isLoading && error && (
            <Alert className='border-orange-200 bg-orange-50 dark:bg-orange-900/20'>
              <AlertCircle className='h-4 w-4 text-orange-600' />
              <AlertDescription className='text-orange-800 dark:text-orange-200'>
                <div className='space-y-2'>
                  <div className='font-medium'>{error}</div>
                  {error.includes(t('noLogisticsInfo')) && (
                    <div className='text-sm'>
                      <p className='mb-2'>{t('errorReasons.title')}</p>
                      <ul className='list-disc list-inside space-y-1'>
                        <li>{t('errorReasons.reason1')}</li>
                        <li>{t('errorReasons.reason2')}</li>
                        <li>{t('errorReasons.reason3')}</li>
                      </ul>
                      <p className='mt-2 text-xs text-orange-600 dark:text-orange-400'>
                        {t('errorReasons.suggestion')}
                      </p>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* 物流信息 */}
          {!isLoading && trackingData?.success && trackInfo && (
            <>
              {/* 物流基本信息 */}
              <div className='space-y-3'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('carrier')}
                    </span>
                    <div className='font-medium'>
                      {trackInfo.tracking?.providers?.[0]?.provider?.name ||
                        t('unknown')}
                    </div>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('currentStatus')}
                    </span>
                    <div
                      className={`font-medium ${formatTrackingStatus(trackInfo.latest_status?.status).color}`}
                    >
                      {
                        formatTrackingStatus(trackInfo.latest_status?.status)
                          .label
                      }
                    </div>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('estimatedDelivery')}
                    </span>
                    <div className='font-medium'>
                      {trackInfo.time_metrics?.estimated_delivery_date?.from
                        ? formatDate(
                            trackInfo.time_metrics.estimated_delivery_date.from,
                          ).split(' ')[0]
                        : t('noInfo')}
                    </div>
                  </div>
                  <div>
                    <span className='text-gray-600 dark:text-gray-400'>
                      {t('lastUpdate')}
                    </span>
                    <div className='font-medium'>
                      {trackInfo.latest_event?.time_utc
                        ? formatDate(trackInfo.latest_event.time_utc).split(
                            ' ',
                          )[0]
                        : t('noInfo')}
                    </div>
                  </div>
                </div>
              </div>

              {/* 物流轨迹 */}
              {trackInfo.tracking?.providers?.[0]?.events &&
                trackInfo.tracking.providers[0].events.length > 0 && (
                  <div className='space-y-3'>
                    <div className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                      {t('trackingHistory')}
                    </div>
                    <div className='space-y-2'>
                      {trackInfo.tracking.providers[0].events.map(
                        (event: any, index: number) => (
                          <div
                            key={index}
                            className='flex gap-3 p-2 rounded border bg-muted/30'
                          >
                            <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                            <div className='flex-1 min-w-0'>
                              <div className='text-xs text-gray-500 dark:text-gray-400'>
                                {formatDate(event.time_iso || event.time_utc)}
                              </div>
                              <div className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                                {event.description}
                              </div>
                              {event.location && (
                                <div className='text-xs text-gray-500 dark:text-gray-400'>
                                  {event.location}
                                </div>
                              )}
                            </div>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                )}

              {/* 时效信息 */}
              {trackInfo.time_metrics && (
                <div className='p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
                  <div className='text-sm font-medium text-blue-700 dark:text-blue-300 mb-2'>
                    {t('timeMetrics.title')}
                  </div>
                  <div className='grid grid-cols-2 gap-2 text-xs text-blue-600 dark:text-blue-400'>
                    <div>
                      {t('timeMetrics.transitDays')}:{' '}
                      {trackInfo.time_metrics.days_of_transit || 0}
                      {t('timeMetrics.days')}
                    </div>
                    <div>
                      {t('timeMetrics.afterOrder')}:{' '}
                      {trackInfo.time_metrics.days_after_order || 0}
                      {t('timeMetrics.days')}
                    </div>
                    <div>
                      {t('timeMetrics.noUpdate')}:{' '}
                      {trackInfo.time_metrics.days_after_last_update || 0}
                      {t('timeMetrics.days')}
                    </div>
                    <div>
                      {t('timeMetrics.serviceType')}:{' '}
                      {trackInfo.misc_info?.service_type ||
                        t('timeMetrics.standard')}
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* 没有数据时的提示 */}
          {!isLoading && !trackingData && (
            <div className='text-center py-4 text-muted-foreground'>
              <Package className='h-8 w-8 mx-auto mb-2 opacity-50' />
              <p>{t('clickToView')}</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
