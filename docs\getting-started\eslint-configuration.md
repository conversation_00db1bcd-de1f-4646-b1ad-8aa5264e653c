# ESLint Configuration Guide

This project uses a comprehensive ESLint configuration based on Context7 best practices for
Next.js + TypeScript projects.

## Configuration Files

We provide two ESLint configuration formats:

### 1. Legacy Configuration (`.eslintrc.json`)

- **Recommended for most projects**
- Compatible with existing tooling
- Easier to understand and modify

### 2. Flat Configuration (`eslint.config.mjs`)

- **Modern ESLint format** (ESLint 9+)
- More flexible and performant
- Future-proof configuration

## Key Features

### 🔧 **Code Quality Rules**

- **No unused variables**: Prevents dead code with TypeScript-aware detection
- **No explicit `any`**: Warns about `any` usage while allowing rest args
- **Console warnings**: Allows console in development, warns in production
- **Debugger detection**: Prevents debugger statements in production

### 📦 **Import/Export Management**

- **Automatic import sorting**: Alphabetical with proper grouping
- **No duplicate imports**: Prevents redundant import statements
- **TypeScript import resolution**: Proper module resolution

### ⚛️ **React/Next.js Optimizations**

- **Next.js Core Web Vitals**: Performance-focused rules
- **React Hooks**: Proper hooks usage enforcement
- **JSX best practices**: Key props, no duplicate props, proper JSX usage
- **Next.js specific rules**: Image optimization, Link usage, etc.

### ♿ **Accessibility (A11Y)**

- **WCAG compliance**: Comprehensive accessibility rule set
- **Image alt text**: Enforces proper alt text for images
- **ARIA attributes**: Proper ARIA usage validation
- **Keyboard navigation**: Ensures keyboard accessibility

### 🎨 **Code Style**

- **Consistent formatting**: Unified code style across the project
- **Modern JavaScript**: Enforces ES6+ features
- **Semicolons**: Required for consistency
- **Spacing and indentation**: Consistent code formatting

### 🧪 **Testing Support**

- **Jest globals**: Proper test environment setup
- **Relaxed rules**: Less strict rules for test files
- **Test-specific patterns**: Optimized for testing scenarios

## Rule Categories

### Error Level Rules (🚨)

These rules will cause ESLint to exit with an error:

- `no-debugger`: No debugger statements
- `@typescript-eslint/no-unused-vars`: No unused variables
- `react/jsx-key`: React list items must have keys
- `jsx-a11y/aria-props`: Valid ARIA properties

### Warning Level Rules (⚠️)

These rules will show warnings but won't break the build:

- `no-console`: Console statements (allowed in development)
- `@typescript-eslint/no-explicit-any`: Usage of `any` type
- `react/display-name`: Component display names
- Most accessibility rules

### Style Rules (💅)

These rules enforce consistent code style:

- `semi`: Semicolon usage
- `comma-dangle`: Trailing commas
- `object-curly-spacing`: Object spacing
- `arrow-spacing`: Arrow function spacing

## File-Specific Overrides

### Test Files (`**/*.test.{js,jsx,ts,tsx}`)

- Console statements allowed
- `any` type usage allowed
- Jest globals available

### Configuration Files (`**/*.config.{js,ts}`)

- Console statements allowed
- `require()` statements allowed
- Less strict import rules

## Usage

### Running ESLint

```bash
# Check for linting errors
npm run lint

# Fix auto-fixable issues
npm run lint:fix

# Check without output (for CI)
npm run lint:check

# Fix both linting and formatting
npm run fix
```

### IDE Integration

Most modern IDEs will automatically detect and use the ESLint configuration:

- **VS Code**: Install the ESLint extension
- **WebStorm**: ESLint is built-in
- **Vim/Neovim**: Use appropriate ESLint plugins

### Pre-commit Hooks

The project uses `lint-staged` to run ESLint on staged files:

```json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]
  }
}
```

## Customization

### Disabling Rules

To disable a rule for a specific line:

```javascript
// eslint-disable-next-line rule-name
const problematicCode = something();
```

To disable a rule for an entire file:

```javascript
/* eslint-disable rule-name */
```

### Adding Custom Rules

Edit `.eslintrc.json` to add or modify rules:

```json
{
  "rules": {
    "your-custom-rule": "error",
    "existing-rule": "off"
  }
}
```

## Migration Guide

### From Basic Next.js ESLint

If you're upgrading from a basic Next.js ESLint setup:

1. **Backup your current `.eslintrc.json`**
2. **Install new dependencies**:

   ```bash
   npm install --save-dev @eslint/js @eslint/eslintrc typescript-eslint eslint-config-prettier eslint-plugin-import eslint-import-resolver-typescript
   ```

3. **Replace your `.eslintrc.json`** with the new configuration
4. **Run ESLint** to see what needs to be fixed:

   ```bash
   npm run lint
   ```

5. **Fix issues gradually** using `npm run lint:fix`

### To Flat Configuration

To use the modern flat configuration:

1. **Rename** `.eslintrc.json` to `.eslintrc.json.backup`
2. **Use** `eslint.config.mjs` as your primary configuration
3. **Update your IDE** to use the new configuration format
4. **Test thoroughly** to ensure all rules work as expected

## Troubleshooting

### Common Issues

1. **"Parsing error"**: Make sure TypeScript is properly configured
2. **"Rule not found"**: Check if all required plugins are installed
3. **"Import resolution"**: Verify `tsconfig.json` paths are correct

### Performance

If ESLint is slow:

- Use `.eslintignore` to exclude unnecessary files
- Consider using the flat configuration for better performance
- Run ESLint only on changed files in CI

## Best Practices

1. **Fix errors before warnings**: Address errors first, then warnings
2. **Use auto-fix**: Let ESLint fix what it can automatically
3. **Understand rules**: Don't just disable rules, understand why they exist
4. **Team consistency**: Ensure all team members use the same configuration
5. **Regular updates**: Keep ESLint and plugins updated

## Resources

- [ESLint Official Documentation](https://eslint.org/docs/)
- [Next.js ESLint Documentation](https://nextjs.org/docs/basic-features/eslint)
- [TypeScript ESLint Documentation](https://typescript-eslint.io/)
- [ESLint Plugin React Documentation](https://github.com/jsx-eslint/eslint-plugin-react)
- [ESLint Plugin JSX A11y Documentation](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y)
