'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { TicketsContent } from '@/components/tickets-content';
import { UserPageLayout } from '@/components/user-page-layout';

export default function TicketsPage() {
  const t = useTranslations('Tickets');

  useEffect(() => {
    document.title = `${t('title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('title')}
      breadcrumbPage={t('breadcrumb')}
      href='/tickets'
    >
      <TicketsContent />
    </UserPageLayout>
  );
}
