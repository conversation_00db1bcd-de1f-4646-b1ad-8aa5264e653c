import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';
import { sendTaskReviewPublisherEmail } from '@/lib/email';

// 审核委托请求验证Schema
const reviewTaskSchema = z.object({
  action: z.enum(['approve', 'reject']),
});

// 审核委托
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;

    // 解析请求数据
    const body = await request.json();
    const validation = reviewTaskSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 },
      );
    }

    const { action } = validation.data;

    // 查找委托
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 检查委托状态
    if (task.status !== TaskStatus.PENDING) {
      return NextResponse.json(
        { error: '只能审核待审核状态的委托' },
        { status: 400 },
      );
    }

    // 执行审核操作
    let updateData: any = {};
    let newStatus: TaskStatus;

    if (action === 'approve') {
      newStatus = TaskStatus.RECRUITING;
      updateData = {
        status: newStatus,
        approvedAt: new Date(),
        publishedAt: new Date(),
        // 设置委托过期时间（根据上架时长）
        expiresAt: new Date(
          Date.now() + parseInt(task.listingTime, 10) * 60 * 60 * 1000,
        ),
      };
    } else {
      newStatus = TaskStatus.REJECTED;
      updateData = {
        status: newStatus,
        rejectedAt: new Date(),
      };
    }

    // 更新委托状态
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: updateData,
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 如果是拒绝委托，需要退款给发布者
    if (action === 'reject') {
      await prisma.$transaction(async tx => {
        // 获取系统费率配置来计算证据费
        const systemRate = await tx.systemRate.findFirst();
        const evidenceRate = systemRate?.noEvidenceExtraRate || 2.0;
        const evidenceFee = (task.totalAmount * evidenceRate) / 100;
        const nonEvidenceFee = task.finalTotal - evidenceFee;

        // 退还可用余额部分（非证据费）
        if (nonEvidenceFee > 0) {
          await tx.user.update({
            where: { id: task.publisherId },
            data: {
              balance: {
                increment: nonEvidenceFee,
              },
            },
          });

          // 创建非证据费退款记录
          await tx.walletTransaction.create({
            data: {
              userId: task.publisherId,
              type: 'REFUND',
              amount: nonEvidenceFee,
              status: 'COMPLETED',
              description: `委托 ${taskId} 审核拒绝退款（发布费用）`,
              completedAt: new Date(),
            },
          });
        }

        // 退还冻结金额部分（证据费）
        if (evidenceFee > 0) {
          await tx.user.update({
            where: { id: task.publisherId },
            data: {
              frozenAmount: {
                decrement: evidenceFee,
              },
              balance: {
                increment: evidenceFee,
              },
            },
          });

          // 创建证据费退款记录
          await tx.walletTransaction.create({
            data: {
              userId: task.publisherId,
              type: 'REFUND',
              amount: evidenceFee,
              status: 'COMPLETED',
              description: `委托 ${taskId} 审核拒绝退款（证据费）`,
              completedAt: new Date(),
            },
          });
        }
      });
    }

    // 发送邮件通知发布者
    try {
      const publisherEmail = updatedTask.publisher.email;
      if (publisherEmail) {
        await sendTaskReviewPublisherEmail(publisherEmail, {
          publisherName: updatedTask.publisher.name || '用户',
          publisherEmail,
          taskId: updatedTask.id,
          platform: updatedTask.platform?.name || '未知平台',
          category: updatedTask.category?.name || '未知分类',
          quantity: updatedTask.quantity,
          unitPrice: updatedTask.unitPrice,
          totalAmount: updatedTask.unitPrice * updatedTask.quantity,
          reviewedAt: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
          }),
          approved: action === 'approve',
          rejectReason:
            action === 'reject'
              ? '委托审核未通过，请修改后重新提交'
              : undefined,
          listingTime:
            action === 'approve' ? updatedTask.listingTime : undefined,
          expiresAt:
            action === 'approve'
              ? updatedTask.expiresAt?.toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false,
                })
              : undefined,
        });
      }
    } catch (emailError) {
      console.error('发送委托审核邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: action === 'approve' ? '委托审核通过' : '委托审核拒绝',
      data: {
        task: updatedTask,
        action,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
