import { NextRequest, NextResponse } from 'next/server';

import { generateCSRFTokenForUser, CSRF_CONFIG } from '@/lib/csrf';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const token = await generateCSRFTokenForUser();

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'User not authenticated' },
        { status: 401 },
      );
    }

    const response = NextResponse.json({
      token,
      expires: Date.now() + CSRF_CONFIG.TOKEN_EXPIRY,
    });

    // 设置 CSRF token cookie
    response.cookies.set(CSRF_CONFIG.TOKEN_COOKIE, token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: CSRF_CONFIG.TOKEN_EXPIRY / 1000, // 转换为秒
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to generate CSRF token',
      },
      { status: 500 },
    );
  }
}
