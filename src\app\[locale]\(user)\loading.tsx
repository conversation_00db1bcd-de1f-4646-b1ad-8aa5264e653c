import { useTranslations } from 'next-intl';
import React from 'react';

/**
 * 用户区域加载组件
 * 在用户页面加载时显示
 */
export default function UserLoading() {
  const t = useTranslations('common.userLoading');
  return (
    <div className='flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800'>
      <div className='flex flex-col items-center space-y-6 p-8'>
        {/* 优化的加载动画 */}
        <div className='relative'>
          <div className='h-16 w-16 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600 shadow-lg'></div>
          <div className='absolute inset-0 flex items-center justify-center'>
            <div className='h-8 w-8 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 animate-pulse'></div>
          </div>
          {/* 外圈装饰 */}
          <div className='absolute -inset-2 rounded-full border border-blue-100 animate-ping opacity-20'></div>
        </div>

        {/* 优化的加载文本 */}
        <div className='text-center max-w-md'>
          <h2 className='text-xl font-bold text-gray-900 dark:text-gray-100 mb-2 animate-fade-in'>
            {t('title')}
          </h2>
          <p className='text-sm text-gray-600 dark:text-gray-300 leading-relaxed animate-fade-in-delay'>
            {t('description')}
          </p>
        </div>

        {/* 优化的功能加载提示 */}
        <div className='flex flex-wrap justify-center gap-3 text-sm'>
          <div className='flex items-center space-x-2 px-3 py-1 bg-white/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm'>
            <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
            <span className='text-gray-700 dark:text-gray-300 animate-pulse'>
              {t('features.dashboard')}
            </span>
          </div>
          <div className='flex items-center space-x-2 px-3 py-1 bg-white/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm'>
            <div className='w-2 h-2 bg-green-500 rounded-full animate-pulse delay-100'></div>
            <span className='text-gray-700 dark:text-gray-300 animate-pulse delay-100'>
              {t('features.tasks')}
            </span>
          </div>
          <div className='flex items-center space-x-2 px-3 py-1 bg-white/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm'>
            <div className='w-2 h-2 bg-yellow-500 rounded-full animate-pulse delay-200'></div>
            <span className='text-gray-700 dark:text-gray-300 animate-pulse delay-200'>
              {t('features.wallet')}
            </span>
          </div>
          <div className='flex items-center space-x-2 px-3 py-1 bg-white/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm'>
            <div className='w-2 h-2 bg-purple-500 rounded-full animate-pulse delay-300'></div>
            <span className='text-gray-700 dark:text-gray-300 animate-pulse delay-300'>
              {t('features.profile')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
