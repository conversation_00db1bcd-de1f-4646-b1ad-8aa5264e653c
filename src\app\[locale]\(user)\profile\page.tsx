'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { AccountSecurityContent } from '@/components/account-security-content';
import { UserPageLayout } from '@/components/user-page-layout';

export default function ProfilePage() {
  const t = useTranslations('AccountSecurity');

  useEffect(() => {
    document.title = `${t('title')} - RefundGo`;
  }, [t]);

  return (
    <UserPageLayout
      title={t('title')}
      breadcrumbPage={t('breadcrumb')}
      href='/profile'
      description={t('description')}
    >
      <AccountSecurityContent />
    </UserPageLayout>
  );
}
