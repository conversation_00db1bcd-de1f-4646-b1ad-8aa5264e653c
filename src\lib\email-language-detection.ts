/**
 * Email Language Detection System
 * Implements intelligent language detection for email notifications
 * Based on 4-tier hierarchy: user preference > Accept-Language > URL locale > fallback
 */

import { NextRequest } from 'next/server';

import prisma from '@/lib/db';
import type { SupportedLanguage } from '@/lib/email-translations';

/**
 * Language detection hierarchy:
 * 1. Primary: User's registrationLanguage field from database
 * 2. Secondary: URL locale parameter (/zh/ or /en/ routes) - PRIORITIZED for active user choice
 * 3. Tertiary: Accept-Language header from HTTP request - browser default preference
 * 4. Default: Chinese (zh) if no language can be determined
 */
export async function getUserEmailLanguage(
  userId?: string,
  request?: NextRequest,
  fallbackLanguage: SupportedLanguage = 'zh',
): Promise<SupportedLanguage> {
  // 1. Check user's registration language (highest priority)
  if (userId) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { registrationLanguage: true },
      });

      if (user?.registrationLanguage && isValidLanguage(user.registrationLanguage)) {
        console.log(`📧 Language detection: Using user preference "${user.registrationLanguage}" for user ${userId}`);
        return user.registrationLanguage as SupportedLanguage;
      }
    } catch (error) {
      console.warn('Failed to fetch user language preference:', error);
    }
  }

  // 2. Check URL locale (from Referer header - prioritized for active user choice)
  if (request) {
    const refererUrl = request.headers.get('referer');
    if (refererUrl) {
      const urlLanguage = extractLanguageFromUrl(refererUrl);
      if (urlLanguage) {
        console.log(`📧 Language detection: Using URL locale "${urlLanguage}" from referer: ${refererUrl} (user's active choice)`);
        return urlLanguage;
      }
    }

    // Fallback: also check the request URL itself (for direct API calls)
    const urlLanguage = extractLanguageFromUrl(request.url);
    if (urlLanguage) {
      console.log(`📧 Language detection: Using URL locale "${urlLanguage}" from request URL`);
      return urlLanguage;
    }
  }

  // 3. Check Accept-Language header (browser default preference)
  if (request) {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      const detectedLanguage = parseAcceptLanguageHeader(acceptLanguage);
      if (detectedLanguage) {
        console.log(`📧 Language detection: Using Accept-Language header "${detectedLanguage}" (browser default)`);
        return detectedLanguage;
      }
    }
  }

  // 4. Return fallback language
  console.log(`📧 Language detection: Using fallback language "${fallbackLanguage}"`);
  return fallbackLanguage;
}

/**
 * Parse Accept-Language header to extract preferred language
 */
function parseAcceptLanguageHeader(acceptLanguage: string): SupportedLanguage | null {
  try {
    // Parse Accept-Language header format: "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7"
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [code, qValue] = lang.trim().split(';q=');
        const quality = qValue ? parseFloat(qValue) : 1.0;
        return { code: code.toLowerCase(), quality };
      })
      .sort((a, b) => b.quality - a.quality); // Sort by quality (preference)

    // Find the first supported language
    for (const { code } of languages) {
      if (code.startsWith('en')) {
        return 'en';
      }
      if (code.startsWith('zh')) {
        return 'zh';
      }
    }

    return null;
  } catch (error) {
    console.warn('Failed to parse Accept-Language header:', error);
    return null;
  }
}

/**
 * Extract language from URL path
 */
function extractLanguageFromUrl(url: string): SupportedLanguage | null {
  try {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);

    console.log(`🔍 URL Analysis: ${url} -> pathname: ${urlObj.pathname} -> segments: [${pathSegments.join(', ')}]`);

    // Check if the first path segment is a language code
    if (pathSegments.length > 0) {
      const firstSegment = pathSegments[0].toLowerCase();
      console.log(`🔍 First segment: "${firstSegment}"`);

      if (firstSegment === 'en') {
        console.log(`✅ Detected English locale from URL`);
        return 'en';
      }
      if (firstSegment === 'zh') {
        console.log(`✅ Detected Chinese locale from URL`);
        return 'zh';
      }
    }

    console.log(`❌ No locale detected from URL`);
    return null;
  } catch (error) {
    console.warn('Failed to extract language from URL:', error);
    return null;
  }
}

/**
 * Validate if a language code is supported
 */
function isValidLanguage(language: string): boolean {
  return ['zh', 'en'].includes(language);
}

/**
 * Get user's email language with caching for performance
 * This version includes basic caching to avoid repeated database queries
 */
const languageCache = new Map<string, { language: SupportedLanguage; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function getUserEmailLanguageCached(
  userId?: string,
  request?: NextRequest,
  fallbackLanguage: SupportedLanguage = 'zh',
): Promise<SupportedLanguage> {
  // Check cache first if userId is provided
  if (userId) {
    const cached = languageCache.get(userId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log(`📧 Language detection: Using cached language "${cached.language}" for user ${userId}`);
      return cached.language;
    }
  }

  // Get language using standard detection
  const language = await getUserEmailLanguage(userId, request, fallbackLanguage);

  // Cache the result if userId is provided
  if (userId) {
    languageCache.set(userId, {
      language,
      timestamp: Date.now(),
    });
  }

  return language;
}

/**
 * Clear language cache for a specific user (useful when user updates language preference)
 */
export function clearUserLanguageCache(userId: string): void {
  languageCache.delete(userId);
  console.log(`📧 Language cache cleared for user ${userId}`);
}

/**
 * Clear all language cache (useful for maintenance)
 */
export function clearAllLanguageCache(): void {
  languageCache.clear();
  console.log('📧 All language cache cleared');
}

/**
 * Get language detection statistics for monitoring
 */
export function getLanguageDetectionStats(): {
  cacheSize: number;
  cacheEntries: Array<{ userId: string; language: SupportedLanguage; age: number }>;
} {
  const now = Date.now();
  const entries = Array.from(languageCache.entries()).map(([userId, data]) => ({
    userId,
    language: data.language,
    age: now - data.timestamp,
  }));

  return {
    cacheSize: languageCache.size,
    cacheEntries: entries,
  };
}

/**
 * Detect language from user object (for cases where we have user data but no request)
 */
export function detectLanguageFromUser(user: {
  registrationLanguage?: string | null;
}): SupportedLanguage {
  if (user.registrationLanguage && isValidLanguage(user.registrationLanguage)) {
    return user.registrationLanguage as SupportedLanguage;
  }
  return 'zh'; // Default fallback
}

/**
 * Batch language detection for multiple users (useful for bulk email operations)
 */
export async function batchGetUserEmailLanguages(
  userIds: string[],
  fallbackLanguage: SupportedLanguage = 'zh',
): Promise<Record<string, SupportedLanguage>> {
  try {
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
      },
      select: {
        id: true,
        registrationLanguage: true,
      },
    });

    const result: Record<string, SupportedLanguage> = {};

    // Create a map for quick lookup
    const userMap = new Map(users.map(user => [user.id, user]));

    // Process each user ID
    for (const userId of userIds) {
      const user = userMap.get(userId);
      if (user && user.registrationLanguage && isValidLanguage(user.registrationLanguage)) {
        result[userId] = user.registrationLanguage as SupportedLanguage;
      } else {
        result[userId] = fallbackLanguage;
      }
    }

    console.log(`📧 Batch language detection completed for ${userIds.length} users`);
    return result;
  } catch (error) {
    console.error('Failed to batch detect user languages:', error);

    // Return fallback for all users
    const result: Record<string, SupportedLanguage> = {};
    for (const userId of userIds) {
      result[userId] = fallbackLanguage;
    }
    return result;
  }
}

/**
 * Language detection middleware helper
 * Extracts language from various sources in a Next.js API route context
 */
export async function detectEmailLanguageFromContext(
  context: {
    userId?: string;
    request?: NextRequest;
    userAgent?: string;
    ip?: string;
  },
  fallbackLanguage: SupportedLanguage = 'zh',
): Promise<{
  language: SupportedLanguage;
  source: 'user_preference' | 'url_locale' | 'accept_language' | 'fallback';
  confidence: number;
}> {
  // Try user preference first
  if (context.userId) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: context.userId },
        select: { registrationLanguage: true },
      });

      if (user?.registrationLanguage && isValidLanguage(user.registrationLanguage)) {
        return {
          language: user.registrationLanguage as SupportedLanguage,
          source: 'user_preference',
          confidence: 1.0,
        };
      }
    } catch (error) {
      console.warn('Failed to fetch user language preference:', error);
    }
  }

  // Try URL locale (prioritized for active user choice)
  if (context.request) {
    const refererUrl = context.request.headers.get('referer');
    if (refererUrl) {
      const urlLanguage = extractLanguageFromUrl(refererUrl);
      if (urlLanguage) {
        return {
          language: urlLanguage,
          source: 'url_locale',
          confidence: 0.9,
        };
      }
    }

    // Fallback: also check the request URL itself
    const urlLanguage = extractLanguageFromUrl(context.request.url);
    if (urlLanguage) {
      return {
        language: urlLanguage,
        source: 'url_locale',
        confidence: 0.8,
      };
    }
  }

  // Try Accept-Language header (browser default preference)
  if (context.request) {
    const acceptLanguage = context.request.headers.get('accept-language');
    if (acceptLanguage) {
      const detectedLanguage = parseAcceptLanguageHeader(acceptLanguage);
      if (detectedLanguage) {
        return {
          language: detectedLanguage,
          source: 'accept_language',
          confidence: 0.6,
        };
      }
    }
  }

  // Return fallback
  return {
    language: fallbackLanguage,
    source: 'fallback',
    confidence: 0.0,
  };
}
