#!/usr/bin/env node

/**
 * 自动配置 cron-job.org 定时任务脚本
 * 使用方法: node scripts/setup-cron-jobs.js
 */

const https = require('https');
const readline = require('readline');

// 配置
const API_KEY = 'wqHdLbP8bd632DeO1o+PhYsbuyic3cUKaeSJYhOQb6E=';
const API_BASE_URL = 'api.cron-job.org';

// 定时任务配置
const CRON_JOBS = [
  {
    title: 'Auto Approve Reviews',
    path: '/api/cron/auto-approve-reviews',
    schedule: {
      timezone: 'Asia/Shanghai',
      hours: [-1], // 每小时
      minutes: [0], // 第0分钟
      mdays: [-1], // 每天
      months: [-1], // 每月
      wdays: [-1], // 每周
    },
    description: '自动审核物流单号提交，超过24小时未审核的自动通过',
  },
  {
    title: 'Auto Confirm Delivery',
    path: '/api/cron/auto-confirm-delivery',
    schedule: {
      timezone: 'Asia/Shanghai',
      hours: [0, 6, 12, 18], // 每6小时
      minutes: [0],
      mdays: [-1],
      months: [-1],
      wdays: [-1],
    },
    description: '自动确认收货，超过30天未确认的自动确认',
  },
  {
    title: 'Expire Tasks',
    path: '/api/cron/expire-tasks',
    schedule: {
      timezone: 'Asia/Shanghai',
      hours: [0], // 每天凌晨
      minutes: [0],
      mdays: [-1],
      months: [-1],
      wdays: [-1],
    },
    description: '清理过期任务',
  },
  {
    title: 'Expire Membership',
    path: '/api/cron/expire-membership',
    schedule: {
      timezone: 'Asia/Shanghai',
      hours: [2], // 每天凌晨2点
      minutes: [0],
      mdays: [-1],
      months: [-1],
      wdays: [-1],
    },
    description: '处理过期会员',
  },
];

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 提示用户输入
function askQuestion(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer.trim());
    });
  });
}

// HTTP请求函数
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: API_BASE_URL,
      port: 443,
      path: path,
      method: method,
      headers: {
        Authorization: `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
      },
    };

    const req = https.request(options, res => {
      let responseData = '';

      res.on('data', chunk => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ statusCode: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ statusCode: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', error => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 创建定时任务
async function createCronJob(domain, cronSecret, jobConfig) {
  const jobData = {
    job: {
      url: `${domain}${jobConfig.path}`,
      enabled: true,
      schedule: {
        ...jobConfig.schedule,
        expiresAt: 0,
      },
      requestMethod: 2, // POST
      extendedData: {
        headers: {
          Authorization: `Bearer ${cronSecret}`,
        },
      },
      title: jobConfig.title,
    },
  };

  try {
    const response = await makeRequest('POST', '/jobs', jobData);

    if (response.statusCode === 200 || response.statusCode === 201) {
      console.log(`✅ 成功创建定时任务: ${jobConfig.title}`);
      console.log(`   描述: ${jobConfig.description}`);
      console.log(`   URL: ${domain}${jobConfig.path}`);
      return true;
    } else {
      console.log(`❌ 创建定时任务失败: ${jobConfig.title}`);
      console.log(`   状态码: ${response.statusCode}`);
      console.log(`   响应: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 创建定时任务出错: ${jobConfig.title}`);
    console.log(`   错误: ${error.message}`);
    return false;
  }
}

// 获取现有任务
async function getExistingJobs() {
  try {
    const response = await makeRequest('GET', '/jobs');

    if (response.statusCode === 200) {
      return response.data.jobs || [];
    } else {
      console.log('⚠️  无法获取现有任务列表');
      return [];
    }
  } catch (error) {
    console.log('⚠️  获取现有任务时出错:', error.message);
    return [];
  }
}

// 主函数
async function main() {
  console.log('🚀 RefundGo 定时任务配置工具');
  console.log('================================\n');

  try {
    // 获取用户输入
    const domain = await askQuestion(
      '请输入您的域名 (例如: https://your-domain.com): '
    );
    const cronSecret = await askQuestion('请输入您的 CRON_SECRET: ');

    if (!domain || !cronSecret) {
      console.log('❌ 域名和 CRON_SECRET 都是必需的');
      process.exit(1);
    }

    // 验证域名格式
    if (!domain.startsWith('http://') && !domain.startsWith('https://')) {
      console.log('❌ 域名必须以 http:// 或 https:// 开头');
      process.exit(1);
    }

    console.log('\n📋 检查现有任务...');
    const existingJobs = await getExistingJobs();

    if (existingJobs.length > 0) {
      console.log(`找到 ${existingJobs.length} 个现有任务:`);
      existingJobs.forEach(job => {
        console.log(`  - ${job.title} (${job.enabled ? '启用' : '禁用'})`);
      });

      const shouldContinue = await askQuestion('\n是否继续创建新任务? (y/n): ');
      if (shouldContinue.toLowerCase() !== 'y') {
        console.log('操作已取消');
        process.exit(0);
      }
    }

    console.log('\n🔧 开始创建定时任务...');

    let successCount = 0;

    for (const jobConfig of CRON_JOBS) {
      console.log(`\n正在创建: ${jobConfig.title}`);
      const success = await createCronJob(domain, cronSecret, jobConfig);
      if (success) {
        successCount++;
      }

      // 添加延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n================================');
    console.log(
      `✅ 配置完成! 成功创建 ${successCount}/${CRON_JOBS.length} 个定时任务`
    );

    if (successCount < CRON_JOBS.length) {
      console.log('\n⚠️  部分任务创建失败，请检查:');
      console.log('   1. API 密钥是否正确');
      console.log('   2. 是否已达到账户限制');
      console.log('   3. 网络连接是否正常');
    }

    console.log('\n📝 下一步:');
    console.log('   1. 在生产环境设置 CRON_SECRET 环境变量');
    console.log('   2. 确保您的域名可以正常访问');
    console.log('   3. 监控定时任务的执行情况');
    console.log('\n🔗 管理您的定时任务: https://cron-job.org/en/members/jobs/');
  } catch (error) {
    console.log('❌ 配置过程中出现错误:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n操作已取消');
  rl.close();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createCronJob, CRON_JOBS };
