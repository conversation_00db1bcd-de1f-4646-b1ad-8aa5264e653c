import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 验证管理员权限的辅助函数
async function requireAdmin() {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('请先登录');
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (user?.role !== 'ADMIN') {
    throw new Error('权限不足');
  }

  return session;
}

// 更新白名单状态的验证schema
const updateWhitelistSchema = z.object({
  isActive: z.boolean(),
});

// 获取白名单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    await requireAdmin();

    const { id } = await params;

    // 获取白名单详情
    const whitelistItem = await prisma.shopWhitelist.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!whitelistItem) {
      return NextResponse.json({ error: '白名单条目不存在' }, { status: 404 });
    }

    return NextResponse.json(whitelistItem);
  } catch (error) {
    console.error('获取白名单详情失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' },
      { status: 500 },
    );
  }
}

// 更新白名单状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    await requireAdmin();

    const { id } = await params;
    const body = await request.json();

    // 验证请求数据
    const validation = updateWhitelistSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: '请求数据格式错误',
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { isActive } = validation.data;

    // 检查白名单条目是否存在
    const existingWhitelist = await prisma.shopWhitelist.findUnique({
      where: { id },
    });

    if (!existingWhitelist) {
      return NextResponse.json({ error: '白名单条目不存在' }, { status: 404 });
    }

    // 更新白名单状态
    const updatedWhitelist = await prisma.shopWhitelist.update({
      where: { id },
      data: {
        isActive,
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: `白名单条目已${isActive ? '启用' : '禁用'}`,
      whitelistItem: updatedWhitelist,
    });
  } catch (error) {
    console.error('更新白名单状态失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' },
      { status: 500 },
    );
  }
}

// 删除白名单条目
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证管理员权限
    await requireAdmin();

    const { id } = await params;

    // 检查白名单条目是否存在
    const existingWhitelist = await prisma.shopWhitelist.findUnique({
      where: { id },
    });

    if (!existingWhitelist) {
      return NextResponse.json({ error: '白名单条目不存在' }, { status: 404 });
    }

    // 硬删除白名单条目
    await prisma.shopWhitelist.delete({
      where: { id },
    });

    return NextResponse.json({
      message: '白名单条目已删除',
    });
  } catch (error) {
    console.error('删除白名单失败:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '服务器内部错误' },
      { status: 500 },
    );
  }
}
