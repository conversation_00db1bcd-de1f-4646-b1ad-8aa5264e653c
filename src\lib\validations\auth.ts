import { z } from 'zod';

// 创建验证模式的工厂函数，接受翻译函数
export const createSignInSchema = (t: (key: string) => string) =>
  z.object({
    email: z
      .string()
      .min(1, t('validation.required'))
      .email(t('validation.email')),
    password: z
      .string()
      .min(1, t('validation.required'))
      .min(6, t('validation.password')),
  });

// 向后兼容的默认模式
export const signInSchema = z.object({
  email: z.string().min(1, '请输入邮箱地址').email('请输入有效的邮箱地址'),
  password: z.string().min(1, '请输入密码').min(6, '密码长度至少6位'),
});

// 创建注册验证模式的工厂函数
export const createSignUpSchema = (t: (key: string) => string) =>
  z
    .object({
      name: z
        .string()
        .min(1, t('validation.required'))
        .min(2, t('validation.minLength').replace('{min}', '2'))
        .max(50, t('validation.maxLength').replace('{max}', '50')),
      email: z
        .string()
        .min(1, t('validation.required'))
        .email(t('validation.email')),
      password: z
        .string()
        .min(6, t('validation.password'))
        .max(32, t('validation.maxLength').replace('{max}', '32'))
        .regex(/^[a-zA-Z0-9]+$/, '密码只能包含字母和数字'),
      confirmPassword: z.string().min(1, t('validation.required')),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation.confirmPassword'),
      path: ['confirmPassword'],
    });

// 向后兼容的默认模式
export const signUpSchema = z
  .object({
    name: z
      .string()
      .min(1, '请输入姓名')
      .min(2, '姓名至少2个字符')
      .max(50, '姓名不能超过50个字符'),
    email: z.string().min(1, '请输入邮箱地址').email('请输入有效的邮箱地址'),
    password: z
      .string()
      .min(6, '密码长度至少6位')
      .max(32, '密码长度不能超过32位')
      .regex(/^[a-zA-Z0-9]+$/, '密码只能包含字母和数字'),
    confirmPassword: z.string().min(1, '请确认密码'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  });

// 类型定义
export type SignInInput = z.infer<typeof signInSchema>;
export type SignUpInput = z.infer<typeof signUpSchema>;
