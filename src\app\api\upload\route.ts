import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';

// 允许的文件类型
const ALLOWED_TYPES = {
  image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  video: [
    'video/mp4',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/flv',
    'video/webm',
  ],
};

// 文件大小限制 (50MB)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, message: '没有选择文件' },
        { status: 400 },
      );
    }

    const uploadResults = [];

    for (const file of files) {
      // 验证文件大小
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { success: false, message: `文件 ${file.name} 超过大小限制 (50MB)` },
          { status: 400 },
        );
      }

      // 验证文件类型
      const allAllowedTypes = [...ALLOWED_TYPES.image, ...ALLOWED_TYPES.video];
      if (!allAllowedTypes.includes(file.type)) {
        return NextResponse.json(
          { success: false, message: `不支持的文件类型: ${file.type}` },
          { status: 400 },
        );
      }

      // 生成唯一文件名
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const extension = file.name.split('.').pop();
      const fileName = `${timestamp}_${randomString}.${extension}`;

      // 确定文件类型目录
      const fileType = ALLOWED_TYPES.image.includes(file.type)
        ? 'images'
        : 'videos';

      // 创建上传目录
      const uploadDir = join(process.cwd(), 'public', 'uploads', fileType);
      try {
        await mkdir(uploadDir, { recursive: true });
      } catch (error) {
        // 目录可能已存在，忽略错误
      }

      // 保存文件
      const filePath = join(uploadDir, fileName);

      // 使用stream的方式读取文件，避免使用arrayBuffer
      const stream = file.stream();
      const reader = stream.getReader();
      const chunks: Uint8Array[] = [];

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      // 合并所有chunks
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const buffer = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of chunks) {
        buffer.set(chunk, offset);
        offset += chunk.length;
      }

      await writeFile(filePath, Buffer.from(buffer));

      // 生成访问 URL - 使用专门的文件服务API路由
      const fileUrl = `/api/files/${fileType}/${fileName}`;

      uploadResults.push({
        originalName: file.name,
        fileName,
        fileUrl,
        fileType,
        size: file.size,
        mimeType: file.type,
      });
    }

    return NextResponse.json({
      success: true,
      message: '文件上传成功',
      data: uploadResults,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
