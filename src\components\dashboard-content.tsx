'use client';

import { useQuery } from '@tanstack/react-query';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Eye,
  Calendar,
  Users,
  Target,
  Award,
  ArrowUpRight,
  ArrowDownRight,
  Star,
  MapPin,
  Timer,
  Building2,
  Tag,
  CreditCard,
  FileText,
  Minus,
  XCircle,
  Smartphone,
  Building,
} from 'lucide-react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useEffect } from 'react';

import { TaskCard } from '@/components/task-list';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import { useResponsive } from '@/hooks/use-responsive';
import { mockTasks } from '@/lib/mock-data/tasks';
import {
  TransactionType,
  TransactionStatus,
  WalletTransaction,
} from '@/lib/types/membership';
import {
  Platform,
  ChargebackType,
  PaymentMethod,
  TaskStatus,
  EvidenceStatus,
  Task,
} from '@/lib/types/task';
import {
  getTaskCommission,
  getTaskBaseCommission,
} from '@/lib/utils/commission';

// 随机选择推荐委托
function getRandomRecommendedTasks(tasks: Task[], count: number = 3): Task[] {
  // 过滤可用委托
  const availableTasks = tasks.filter(
    task =>
      task.status === TaskStatus.PENDING &&
      task.deadline &&
      new Date(task.deadline) > new Date(), // 未过期且有截止时间
  );

  // 委托不足时返回所有
  if (availableTasks.length <= count) {
    return availableTasks;
  }

  // 随机选择
  const shuffled = [...availableTasks].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// 获取最近交易记录
function getRecentTransactions(
  transactions: WalletTransaction[],
  count: number = 3,
): WalletTransaction[] {
  return transactions
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    )
    .slice(0, count);
}

// 倒计时组件
interface CountdownProps {
  deadline: Date;
  t: any;
}

function Countdown({ deadline, t }: CountdownProps) {
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    isExpired: boolean;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0, isExpired: false });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const deadlineTime = new Date(deadline).getTime();
      const difference = deadlineTime - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
        );
        const minutes = Math.floor(
          (difference % (1000 * 60 * 60)) / (1000 * 60),
        );
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds, isExpired: false });
      } else {
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true,
        });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [deadline]);

  if (timeLeft.isExpired) {
    return (
      <div className='flex items-center gap-1 text-red-500'>
        <Clock className='h-4 w-4' />
        <span className='text-sm font-medium'>{t('countdown.expired')}</span>
      </div>
    );
  }

  return (
    <div className='flex items-center gap-1 text-orange-500'>
      <Clock className='h-4 w-4' />
      <span className='text-sm font-medium'>
        {timeLeft.days > 0 && `${timeLeft.days}${t('countdown.days')} `}
        {String(timeLeft.hours).padStart(2, '0')}:
        {String(timeLeft.minutes).padStart(2, '0')}:
        {String(timeLeft.seconds).padStart(2, '0')}
      </span>
    </div>
  );
}

// 交易记录相关函数
function getTransactionStatusColor(status: TransactionStatus) {
  switch (status) {
    case TransactionStatus.COMPLETED:
      return 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200';
    case TransactionStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200';
    case TransactionStatus.FAILED:
      return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200';
    case TransactionStatus.CANCELLED:
      return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200';
  }
}

function getTransactionStatusIcon(status: TransactionStatus) {
  switch (status) {
    case TransactionStatus.COMPLETED:
      return <CheckCircle className='h-4 w-4' />;
    case TransactionStatus.PENDING:
      return <Clock className='h-4 w-4' />;
    case TransactionStatus.FAILED:
      return <XCircle className='h-4 w-4' />;
    case TransactionStatus.CANCELLED:
      return <AlertCircle className='h-4 w-4' />;
    default:
      return <Clock className='h-4 w-4' />;
  }
}

function getTransactionTypeIcon(type: TransactionType) {
  switch (type) {
    case TransactionType.DEPOSIT:
      return <Plus className='h-4 w-4 text-green-600' />;
    case TransactionType.WITHDRAW:
      return <Minus className='h-4 w-4 text-red-600' />;
    case TransactionType.COMMISSION:
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    case TransactionType.TASK_FEE:
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    case TransactionType.MEMBERSHIP:
      return <TrendingDown className='h-4 w-4 text-orange-600' />;
    case TransactionType.REFUND:
      return <Plus className='h-4 w-4 text-blue-600' />;
    default:
      return <DollarSign className='h-4 w-4' />;
  }
}

function StatCard({
  title,
  value,
  change,
  icon: Icon,
  prefix = '',
  suffix = '',
  t,
}: {
  title: string;
  value: string | number;
  change: number;
  icon: any;
  prefix?: string;
  suffix?: string;
  t: any;
}) {
  const isPositive = change >= 0;

  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-gray-600 dark:text-gray-300'>
          {title}
        </CardTitle>
        <Icon className='h-4 w-4 text-gray-600 dark:text-gray-300' />
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>
          {prefix}
          {typeof value === 'number' ? value.toLocaleString() : value}
          {suffix}
        </div>
        <div className='flex items-center text-xs text-gray-600 dark:text-gray-300'>
          {isPositive ? (
            <ArrowUpRight className='mr-1 h-3 w-3 text-green-500' />
          ) : (
            <ArrowDownRight className='mr-1 h-3 w-3 text-red-500' />
          )}
          <span className={isPositive ? 'text-green-500' : 'text-red-500'}>
            {Math.abs(change)}%
          </span>
          <span className='ml-1'>{t('stats.changeFromLastMonth')}</span>
        </div>
      </CardContent>
    </Card>
  );
}

export function DashboardContent() {
  const t = useTranslations('Dashboard');
  const tTransactions = useTranslations('transactions');
  const locale = useLocale();
  const { isMobile, isTablet } = useResponsive();
  const [mounted, setMounted] = useState(false);

  // 获取统计数据
  const { data: statsData, isLoading: isStatsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/stats');
      if (!response.ok) {
        throw new Error(t('errors.fetchStatsError'));
      }
      return response.json();
    },
  });

  // 获取交易记录
  const { data: transactionsData, isLoading: isTransactionsLoading } = useQuery(
    {
      queryKey: ['wallet-transactions', { limit: 3 }],
      queryFn: async () => {
        const response = await fetch('/api/user/wallet/transactions?limit=3');
        if (!response.ok) {
          throw new Error(t('errors.fetchTransactionsError'));
        }
        return response.json();
      },
    },
  );

  // 获取推荐委托
  const { data: recommendedTasksData, isLoading: isRecommendedTasksLoading } =
    useQuery({
      queryKey: ['recommended-tasks'],
      queryFn: async () => {
        const response = await fetch(
          '/api/dashboard/recommended-tasks?limit=3',
        );
        if (!response.ok) {
          throw new Error(t('errors.fetchRecommendedTasksError'));
        }
        return response.json();
      },
    });

  useEffect(() => {
    setMounted(true);
  }, []);

  // 格式化日期时间
  const formatDateTime = (date: Date) => {
    if (!mounted) return t('common.loading');
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 通用词汇翻译函数
  const translateCommonTerms = (text: string): string => {
    if (!text) return text;

    let translatedText = text;

    // 定义通用词汇翻译映射
    const termTranslations: Record<string, string> = {
      // 费用相关
      手续费: 'Fee',
      证据费: 'Evidence Fee',
      发布费用: 'Publishing Fee',
      押金: 'Deposit',
      酬金: 'Commission',
      违约金: 'Penalty',
      奖励金: 'Bonus',

      // 平台相关
      独立站: 'Independent Site',
      DHgate: 'DHgate',

      // 支付方式相关
      美元账户: 'USD Account',
      银行卡: 'Bank Card',
      银行账户: 'Bank Account',
      支付宝: 'Alipay',
      微信支付: 'WeChat Pay',

      // 状态相关
      冻结中: 'Frozen',
      已完成: 'Completed',
      处理中: 'Processing',
      已取消: 'Cancelled',
      已拒绝: 'Rejected',
      已通过: 'Approved',
      失败: 'Failed',
      成功: 'Success',
      冻结: 'Frozen',

      // 错误信息相关
      银行卡信息错误: 'Bank card information error',
      银行信息错误: 'Bank information error',
      收款信息错误: 'Payment information error',
      账户信息不匹配: 'Account information mismatch',
      网络超时: 'Network timeout',
      系统维护中: 'System maintenance',
      未提供原因: 'No reason provided',

      // 操作相关
      充值: 'Deposit',
      提现: 'Withdrawal',
      退款: 'Refund',
      转账: 'Transfer',
      扣除: 'Deduction',
      释放: 'Release',
      解冻: 'Unfreeze',

      // 委托相关
      委托完成: 'Task Completed',
      委托: 'Task',
      接单: 'Accept',
      发布: 'Publish',
      完成: 'Complete',
      取消: 'Cancel',
      过期: 'Expired',
      审核: 'Review',
      证据: 'Evidence',
      确认收货: 'Confirm Delivery',
      自动确认: 'Auto Confirm',

      // 会员相关
      会员: 'Membership',
      升级: 'Upgrade',
      续费: 'Renewal',
      套餐: 'Plan',

      // 其他常用词
      申请: 'Application',
      被拒绝: 'Rejected',
      余额已返还: 'Balance Refunded',
      转为: 'Converted to',
      获得: 'Earned',
      补偿: 'Compensation',
      违约: 'Violation',
      放弃: 'Abandon',
      超时: 'Timeout',
      到账: 'Received',
    };

    // 按词汇长度排序，优先匹配长词汇
    const sortedTerms = Object.keys(termTranslations).sort(
      (a, b) => b.length - a.length,
    );

    // 逐个替换词汇，保持词汇间的空格
    for (const chineseTerm of sortedTerms) {
      const englishTerm = termTranslations[chineseTerm];
      // 使用全局替换，但保持原有的分隔符
      translatedText = translatedText.replace(
        new RegExp(chineseTerm, 'g'),
        englishTerm,
      );
    }

    // 后处理：修复可能的词汇连接问题
    translatedText = translatedText
      // 在中英文之间添加空格
      .replace(/([a-zA-Z])([一-龯])/g, '$1 $2')
      .replace(/([一-龯])([a-zA-Z])/g, '$1 $2')
      // 修复常见的词汇连接问题
      .replace(/TaskDeposit/g, 'Task Deposit')
      .replace(/TaskEvidence/g, 'Task Evidence')
      .replace(/TaskPublishing/g, 'Task Publishing')
      .replace(/TaskComplete/g, 'Task Complete')
      .replace(/TaskAcceptance/g, 'Task Acceptance')
      .replace(/TaskExpired/g, 'Task Expired')
      .replace(/TaskCancellation/g, 'Task Cancellation')
      .replace(/TaskReview/g, 'Task Review')
      .replace(/TaskAuto/g, 'Task Auto')
      .replace(/TaskDelivery/g, 'Task Delivery')
      .replace(/TaskTimeout/g, 'Task Timeout')
      .replace(/TaskAbandonment/g, 'Task Abandonment')
      .replace(/BankCard/g, 'Bank Card')
      .replace(/BankAccount/g, 'Bank Account')
      .replace(/WeChatPay/g, 'WeChat Pay')
      .replace(/USDAccount/g, 'USD Account')
      .replace(/EvidenceFee/g, 'Evidence Fee')
      .replace(/PublishingFee/g, 'Publishing Fee')
      .replace(/MembershipPlan/g, 'Membership Plan')
      .replace(/MembershipUpgrade/g, 'Membership Upgrade')
      .replace(/MembershipRenewal/g, 'Membership Renewal')
      .replace(/BonusReward/g, 'Bonus Reward')
      .replace(/DepositFreeze/g, 'Deposit Frozen')
      .replace(/DepositRelease/g, 'Deposit Release')
      .replace(/DepositDeduction/g, 'Deposit Deduction')
      .replace(/DepositRefund/g, 'Deposit Refund')
      .replace(/CommissionPaid/g, 'Commission Paid')
      .replace(/CommissionEarned/g, 'Commission Earned')
      .replace(/BalanceRefunded/g, 'Balance Refunded')
      .replace(/PaymentInformation/g, 'Payment Information')
      .replace(/AccountInformation/g, 'Account Information')
      .replace(/NetworkTimeout/g, 'Network Timeout')
      .replace(/SystemMaintenance/g, 'System Maintenance')
      .replace(/ConfirmDelivery/g, 'Confirm Delivery')
      .replace(/AutoConfirm/g, 'Auto Confirm')
      .replace(/IndependentSite/g, 'Independent Site')
      // 清理多余的空格
      .replace(/\s+/g, ' ')
      .trim();

    return translatedText;
  };

  // 翻译交易描述
  const translateTransactionDescription = (
    description: string,
    type: string,
  ) => {
    if (!description) return '';

    // 组件未挂载时返回原始描述
    if (!mounted) {
      return description;
    }

    // 在中文环境下，直接返回原始描述（确保显示完整信息）
    if (locale === 'zh') {
      return description;
    }

    // 只在英文环境下进行翻译
    try {
      // 1. 充值相关
      if (description.includes('钱包充值 - ')) {
        const match = description.match(
          /钱包充值 - (.+?)(?:\s*（含手续费([0-9.]+)）)?$/,
        );
        if (match) {
          const amount = match[1];
          const fee = match[2];

          if (fee) {
            return `Wallet Deposit - ${amount} (Including Fee $${fee})`;
          } else {
            return `Wallet Deposit - ${amount}`;
          }
        }
      }

      // 充值相关（标准格式）
      if (description.includes('充值 - ')) {
        const match = description.match(/充值 - (.+?)（手续费([0-9.]+)）$/);
        if (match) {
          const method = match[1];
          const fee = match[2];

          let translatedMethod = method;
          if (method === 'Alipay') {
            translatedMethod = 'Alipay';
          } else if (method === 'WeChat Pay') {
            translatedMethod = 'WeChat Pay';
          } else if (method === 'PayPal') {
            translatedMethod = 'PayPal';
          } else if (method === 'NOWPayments (Cryptocurrency)') {
            translatedMethod = 'NOWPayments (Cryptocurrency)';
          }

          return `Deposit - ${translatedMethod} (Fee $${fee})`;
        } else {
          const simpleMatch = description.match(/充值 - (.+)$/);
          if (simpleMatch) {
            const method = simpleMatch[1];

            let translatedMethod = method;
            if (method === 'Alipay') {
              translatedMethod = 'Alipay';
            } else if (method === 'WeChat Pay') {
              translatedMethod = 'WeChat Pay';
            } else if (method === 'PayPal') {
              translatedMethod = 'PayPal';
            } else if (method === 'NOWPayments (Cryptocurrency)') {
              translatedMethod = 'NOWPayments (Cryptocurrency)';
            }

            return `Deposit - ${translatedMethod}`;
          }
        }
      }

      // 2. 提现相关
      if (description.includes('提现申请 - ')) {
        const match = description.match(/提现申请 - (.+?)（手续费([0-9.]+)）$/);
        if (match) {
          const method = match[1];
          const fee = match[2];

          let translatedMethod = method;
          if (method === '美元账户') {
            translatedMethod = 'USD Account';
          } else if (method === 'USDT ERC20') {
            translatedMethod = 'USDT ERC20';
          } else if (method === 'USDT TRC20') {
            translatedMethod = 'USDT TRC20';
          }

          return `Withdrawal Application - ${translatedMethod} (Fee $${fee})`;
        } else {
          const simpleMatch = description.match(/提现申请 - (.+)$/);
          if (simpleMatch) {
            const method = simpleMatch[1];

            let translatedMethod = method;
            if (method === '美元账户') {
              translatedMethod = 'USD Account';
            } else if (method === 'USDT ERC20') {
              translatedMethod = 'USDT ERC20';
            } else if (method === 'USDT TRC20') {
              translatedMethod = 'USDT TRC20';
            }

            return `Withdrawal Application - ${translatedMethod}`;
          }
        }
      }

      // 提现申请拒绝退款
      if (description.includes('提现申请被拒绝，余额已返还')) {
        const match = description.match(/提现申请被拒绝，余额已返还 - (.+)$/);
        if (match) {
          const reason = match[1];
          return `Withdrawal Rejected, Balance Refunded - ${reason}`;
        } else {
          return 'Withdrawal Rejected, Balance Refunded';
        }
      }

      // 提现申请拒绝退款（简化格式）
      if (description.includes('提现申请拒绝退款')) {
        const match = description.match(/提现申请拒绝退款 - (.+)$/);
        if (match) {
          const reason = match[1];
          // 处理"未提供原因"的情况
          if (reason === '未提供原因') {
            return 'Withdrawal Request Rejected Refund - No reason provided';
          }
          return `Withdrawal Request Rejected Refund - ${reason}`;
        } else {
          return 'Withdrawal Request Rejected Refund';
        }
      }

      // 提现失败相关
      if (description.includes('提现失败')) {
        const match = description.match(/提现失败 - (.+)$/);
        if (match) {
          const reason = match[1];
          // 处理常见的失败原因
          if (reason === '收款信息错误') {
            return 'Withdrawal Failed - Incorrect payment information';
          } else if (reason === '银行信息错误') {
            return 'Withdrawal Failed - Incorrect bank information';
          } else if (reason === '网络超时') {
            return 'Withdrawal Failed - Network timeout';
          }
          return `Withdrawal Failed - ${reason}`;
        } else {
          return 'Withdrawal Failed';
        }
      }

      // 3. 委托相关费用
      // 发布委托费用
      if (description.includes('发布委托费用')) {
        if (description.includes('发布委托费用 - ')) {
          const title = description.replace('发布委托费用 - ', '');
          return `Task Publishing Fee - ${title}`;
        } else {
          return 'Task Publishing Fee';
        }
      }

      // 发布委托证据费（冻结中）
      if (description.includes('发布委托证据费（冻结中）')) {
        const match = description.match(/发布委托证据费（冻结中）- (.+)$/);
        if (match) {
          const title = match[1];
          return `Task Evidence Fee (Frozen) - ${title}`;
        } else {
          return 'Task Evidence Fee (Frozen)';
        }
      }

      // 接受委托押金
      if (description.includes('接受委托押金')) {
        const match = description.match(/接受委托押金 - (.+)$/);
        if (match) {
          const taskId = match[1];
          return `Task Acceptance Deposit - ${taskId}`;
        } else if (description.includes('接受委托押金冻结')) {
          const match2 = description.match(/接受委托押金冻结 - (.+)$/);
          if (match2) {
            const title = match2[1];
            return `Task Acceptance Deposit Freeze - ${title}`;
          }
        }
        return 'Task Acceptance Deposit';
      }

      // 委托押金相关（通用处理）
      if (description.includes('委托押金')) {
        // 委托押金退还
        if (description.includes('委托押金退还')) {
          const match = description.match(/委托押金退还 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Deposit Refund - ${taskId}`;
          } else {
            return 'Task Deposit Refund';
          }
        }
        // 委托押金
        else {
          const match = description.match(/委托押金 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Deposit - ${taskId}`;
          } else {
            return 'Task Deposit';
          }
        }
      }

      // 委托完成相关
      if (description.includes('委托完成')) {
        // 委托完成，证据费转为接单者酬金
        if (description.includes('委托完成，证据费转为接单者酬金')) {
          const match = description.match(
            /委托完成，证据费转为接单者酬金 - (.+)$/,
          );
          if (match) {
            const taskId = match[1];
            return `Task Completed, Evidence Fee Converted to Commission - ${taskId}`;
          }
        }
        // 委托完成押金释放
        else if (description.includes('委托完成押金释放')) {
          const match = description.match(/委托完成押金释放 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Completed, Deposit Released - ${taskId}`;
          }
        }
        // 委托完成酬金
        else if (description.includes('委托完成酬金')) {
          const match = description.match(/委托完成酬金 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Completion Commission - ${taskId}`;
          }
        }
      }

      // 完成委托获得酬金
      if (description.includes('完成委托获得酬金')) {
        if (description.includes('完成委托获得酬金 - ')) {
          const title = description.replace('完成委托获得酬金 - ', '');
          return `Commission from Completed Task - ${title}`;
        } else {
          return 'Commission from Completed Task';
        }
      }

      // 委托过期相关
      if (description.includes('委托过期')) {
        // 委托过期退款（发布费用）
        if (description.includes('委托过期退款（发布费用）')) {
          const match = description.match(/委托过期退款（发布费用）- (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Refund (Publishing Fee) - ${taskId}`;
          }
        }
        // 委托过期退款（证据费）
        else if (description.includes('委托过期退款（证据费）')) {
          const match = description.match(/委托过期退款（证据费）- (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Refund (Evidence Fee) - ${taskId}`;
          }
        }
        // 委托过期押金扣除
        else if (description.includes('委托过期押金扣除')) {
          const match = description.match(/委托过期押金扣除 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Deposit Deduction - ${taskId}`;
          }
        }
        // 委托过期退款（简单格式）
        else if (description.includes('委托过期退款')) {
          const match = description.match(/委托过期退款 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Expired Refund - ${taskId}`;
          }
        }
      }

      // 委托取消相关
      if (description.includes('委托') && description.includes('取消退款')) {
        // 委托 {taskId} 取消退款（发布费用）
        if (description.includes('取消退款（发布费用）')) {
          const match = description.match(/委托 (.+?) 取消退款（发布费用）$/);
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Cancellation Refund (Publishing Fee)`;
          }
        }
        // 委托 {taskId} 取消退款（证据费）
        else if (description.includes('取消退款（证据费）')) {
          const match = description.match(/委托 (.+?) 取消退款（证据费）$/);
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Cancellation Refund (Evidence Fee)`;
          }
        }
      }

      // 4. 会员相关
      if (description.includes('会员')) {
        // 会员升级/续费 - Premium（含手续费2.00）
        if (
          description.includes('会员升级') ||
          description.includes('会员续费')
        ) {
          const upgradeMatch = description.match(
            /会员升级 - (.+?)（含手续费([0-9.]+)）$/,
          );
          const renewMatch = description.match(
            /会员续费 - (.+?)（含手续费([0-9.]+)）$/,
          );

          if (upgradeMatch) {
            const plan = upgradeMatch[1];
            const fee = upgradeMatch[2];
            return `Membership Upgrade - ${plan} (Including Fee $${fee})`;
          } else if (renewMatch) {
            const plan = renewMatch[1];
            const fee = renewMatch[2];
            return `Membership Renewal - ${plan} (Including Fee $${fee})`;
          }

          // 不含手续费的格式
          const simpleUpgradeMatch = description.match(/会员升级 - (.+)$/);
          const simpleRenewMatch = description.match(/会员续费 - (.+)$/);

          if (simpleUpgradeMatch) {
            const plan = simpleUpgradeMatch[1];
            return `Membership Upgrade - ${plan}`;
          } else if (simpleRenewMatch) {
            const plan = simpleRenewMatch[1];
            return `Membership Renewal - ${plan}`;
          }
        }

        // 升级会员套餐 - Premium
        if (description.includes('升级会员套餐')) {
          const match = description.match(/升级会员套餐 - (.+)$/);
          if (match) {
            const plan = match[1];
            return `Membership Plan Upgrade - ${plan}`;
          }
        }
      }

      // 5. 退款相关
      if (description.includes('退款')) {
        // 退款到钱包
        if (description.includes('退款到钱包')) {
          const match = description.match(/退款到钱包 - (.+)$/);
          if (match) {
            const reason = match[1];
            return `Refund to Wallet - ${reason}`;
          } else {
            return 'Refund to Wallet';
          }
        }

        // 证据费退还
        if (description.includes('证据费退还')) {
          const match = description.match(/证据费退还，退还委托用 - (.+)$/);
          if (match) {
            const title = match[1];
            return `Evidence Fee Refund for Task - ${title}`;
          } else {
            return 'Evidence Fee Refund';
          }
        }
      }

      // 6. 违约金和奖励金
      if (description.includes('违约金')) {
        // 违约金扣除
        if (description.includes('违约金扣除')) {
          const match = description.match(/违约金扣除 - (.+)$/);
          if (match) {
            const reason = match[1];
            return `Penalty Deduction - ${reason}`;
          }
        }

        // 放弃委托违约金
        if (description.includes('放弃委托违约金')) {
          const match = description.match(/放弃委托违约金 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Abandonment Penalty - ${taskId}`;
          }
        }
      }

      // 接单者违约补偿（单独处理，因为不包含"违约金"字样）
      if (description.includes('接单者违约补偿')) {
        const match = description.match(/接单者违约补偿 - (.+)$/);
        if (match) {
          const taskId = match[1];
          return `Accepter Violation Compensation - ${taskId}`;
        }
      }

      if (description.includes('奖励金')) {
        const match = description.match(/奖励金 - (.+)$/);
        if (match) {
          const reason = match[1];
          return `Bonus Reward - ${reason}`;
        } else {
          return 'Bonus Reward';
        }
      }

      // 7. 委托审核相关
      if (description.includes('审核拒绝退款')) {
        // 委托 {taskId} 审核拒绝退款（发布费用）
        if (description.includes('审核拒绝退款（发布费用）')) {
          const match = description.match(
            /委托 (.+?) 审核拒绝退款（发布费用）$/,
          );
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Review Rejected Refund (Publishing Fee)`;
          }
        }
        // 委托 {taskId} 审核拒绝退款（证据费）
        else if (description.includes('审核拒绝退款（证据费）')) {
          const match = description.match(/委托 (.+?) 审核拒绝退款（证据费）$/);
          if (match) {
            const taskId = match[1];
            return `Task ${taskId} Review Rejected Refund (Evidence Fee)`;
          }
        }
      }

      // 8. 证据审核相关
      if (description.includes('证据审核通过')) {
        const match = description.match(
          /证据审核通过，退还证据费用 - 委托 (.+)$/,
        );
        if (match) {
          const taskId = match[1];
          return `Evidence Review Approved, Evidence Fee Refunded - Task ${taskId}`;
        } else {
          return 'Evidence Review Approved, Evidence Fee Refunded';
        }
      }

      // 9. 自动确认收货相关
      if (description.includes('自动确认收货')) {
        // 委托自动确认收货，证据费转为接单者酬金
        if (description.includes('证据费转为接单者酬金')) {
          const match = description.match(
            /委托自动确认收货，证据费转为接单者酬金 - (.+)$/,
          );
          if (match) {
            const taskId = match[1];
            return `Task Auto-Confirmed Delivery, Evidence Fee Converted to Commission - ${taskId}`;
          }
        }
      }

      // 10. 押金释放相关
      if (description.includes('押金释放')) {
        // 委托确认收货押金释放
        if (description.includes('确认收货押金释放')) {
          const match = description.match(/委托确认收货押金释放 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Delivery Confirmed, Deposit Released - ${taskId}`;
          }
        }
        // 委托超时押金释放
        else if (description.includes('超时押金释放')) {
          const match = description.match(/委托超时押金释放 - (.+)$/);
          if (match) {
            const taskId = match[1];
            return `Task Timeout, Deposit Released - ${taskId}`;
          }
        }
      }

      // 11. 酬金支付相关
      if (description.includes('酬金支付')) {
        const match = description.match(/委托确认收货酬金支付 - (.+)$/);
        if (match) {
          const taskId = match[1];
          return `Task Delivery Confirmed, Commission Paid - ${taskId}`;
        }
      }

      // 如果没有找到特定模式，进行通用词汇翻译
      return translateCommonTerms(description);
    } catch (error) {
      console.warn('Translation error for transaction description:', error);
      return description;
    }
  };

  // 处理数据
  const recentTransactions = transactionsData?.data || [];

  const currentStatsData = statsData?.data || {
    totalEarnings: 0,
    earningsChange: 0,
    completedTasks: 0,
    tasksChange: 0,
    activeTasksCount: 0,
    activeTasksChange: 0,
  };

  const recommendedTasks = recommendedTasksData?.data || [];

  // 快捷操作数据
  const quickActions = [
    {
      title: t('quickActions.publishTask.title'),
      description: t('quickActions.publishTask.description'),
      href: '/publish',
      icon: Plus,
      color: 'bg-blue-500',
    },
    {
      title: t('quickActions.browseTasks.title'),
      description: t('quickActions.browseTasks.description'),
      href: '/tasks',
      icon: Eye,
      color: 'bg-green-500',
    },
    {
      title: t('quickActions.wallet.title'),
      description: t('quickActions.wallet.description'),
      href: '/wallet',
      icon: DollarSign,
      color: 'bg-purple-500',
    },
    {
      title: t('quickActions.tickets.title'),
      description: t('quickActions.tickets.description'),
      href: '/tickets',
      icon: AlertCircle,
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className='space-y-6'>
      {/* 欢迎信息 */}
      <div className='flex flex-col gap-2 md:flex-row md:items-center md:justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight md:text-3xl'>
            {t('welcome.title')}
          </h1>
          <p className='text-gray-600 dark:text-gray-300'>
            {t('welcome.description')}
          </p>
        </div>
        <div className='flex items-center gap-2'>
          <Button asChild>
            <Link href='/publish'>
              <Plus className='mr-2 h-4 w-4' />
              {t('actions.publish')}
            </Link>
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className='grid gap-4 md:grid-cols-3'>
        <StatCard
          title={t('stats.totalEarnings')}
          value={currentStatsData.totalEarnings}
          change={currentStatsData.earningsChange}
          icon={DollarSign}
          prefix='$'
          t={t}
        />
        <StatCard
          title={t('stats.completedTasks')}
          value={currentStatsData.completedTasks}
          change={currentStatsData.tasksChange}
          icon={CheckCircle}
          t={t}
        />
        <StatCard
          title={t('stats.activeTasks')}
          value={currentStatsData.activeTasksCount}
          change={currentStatsData.activeTasksChange}
          icon={Clock}
          t={t}
        />
      </div>

      <div className='grid gap-6 md:grid-cols-1 lg:grid-cols-7'>
        {/* 最近账单 */}
        <Card className='lg:col-span-4'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div>
                <CardTitle>{t('recentBills.title')}</CardTitle>
                <CardDescription>
                  {t('recentBills.description')}
                </CardDescription>
              </div>
              <Button variant='outline' size='sm' asChild>
                <Link href='/wallet'>
                  {t('recentBills.viewAll')}
                  <ArrowUpRight className='ml-2 h-4 w-4' />
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {recentTransactions.length === 0 ? (
                <div className='text-center py-8 text-gray-600 dark:text-gray-300'>
                  {t('recentBills.noTransactions')}
                </div>
              ) : (
                recentTransactions.map((transaction: any) => (
                  <div
                    key={transaction.id}
                    className={`p-4 border rounded-lg ${
                      isMobile
                        ? 'flex flex-col space-y-3'
                        : 'flex items-center justify-between'
                    }`}
                  >
                    <div className={`flex items-center gap-3 ${isMobile ? 'w-full' : ''}`}>
                      {getTransactionTypeIcon(transaction.type)}
                      <div className='flex-1 min-w-0'>
                        <div className={`font-medium ${isMobile ? 'text-sm' : ''} line-clamp-1`}>
                          {translateTransactionDescription(
                            transaction.description,
                            transaction.type,
                          )}
                        </div>
                        <div className='text-sm text-gray-600 dark:text-gray-300'>
                          {formatDateTime(new Date(transaction.createdAt))}
                          {transaction.reference && (
                            <span className={`ml-2 ${isMobile ? 'block mt-1 text-xs' : ''}`}>
                              · {t('recentTransactions.reference')}：
                              <span className='line-clamp-1 break-all'>
                                {transaction.reference}
                              </span>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className={`flex items-center gap-3 ${
                      isMobile
                        ? 'justify-between w-full'
                        : 'flex-shrink-0'
                    }`}>
                      <div className='text-right'>
                        <div
                          className={`font-semibold ${
                            transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                          } ${isMobile ? 'text-lg' : ''}`}
                        >
                          {transaction.amount > 0 ? '+' : ''}$
                          {Math.abs(transaction.amount).toFixed(2)}
                        </div>
                      </div>

                      <Badge
                        className={`${getTransactionStatusColor(
                          transaction.status,
                        )} ${isMobile ? 'text-xs' : ''}`}
                      >
                        {getTransactionStatusIcon(transaction.status)}
                        <span className='ml-1'>
                          {t(
                            `recentTransactions.status.${transaction.status}`,
                          ) || t('recentTransactions.status.PENDING')}
                        </span>
                      </Badge>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* 快捷操作 */}
        <Card className='lg:col-span-3'>
          <CardHeader>
            <CardTitle>{t('quickActions.title')}</CardTitle>
            <CardDescription>{t('quickActions.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className={`grid gap-3 ${
              isMobile
                ? 'grid-cols-1'
                : isTablet
                  ? 'grid-cols-2'
                  : 'grid-cols-1'
            }`}>
              {quickActions.map(action => (
                <Button
                  key={action.title}
                  variant='ghost'
                  className={`h-auto justify-start p-4 ${
                    isMobile ? 'min-h-[60px]' : ''
                  }`}
                  asChild
                >
                  <Link href={action.href}>
                    <div className={`mr-3 rounded-md p-2 ${action.color} flex-shrink-0`}>
                      <action.icon className='h-4 w-4 text-white' />
                    </div>
                    <div className='text-left flex-1 min-w-0'>
                      <div className={`font-medium ${isMobile ? 'text-sm' : ''} line-clamp-1`}>
                        {action.title}
                      </div>
                      <div className={`text-xs text-gray-600 dark:text-gray-300 ${
                        isMobile ? 'line-clamp-2' : 'line-clamp-1'
                      }`}>
                        {action.description}
                      </div>
                    </div>
                  </Link>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 推荐委托 */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle className='flex items-center gap-2'>
                <Target className='h-5 w-5 text-blue-500' />
                {t('recommendedTasks.title')}
              </CardTitle>
              <CardDescription>
                {t('recommendedTasks.description')}
              </CardDescription>
            </div>
            <Button variant='outline' size='sm' asChild>
              <Link href='/tasks'>
                {t('recommendedTasks.viewMore')}
                <ArrowUpRight className='ml-2 h-4 w-4' />
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {recommendedTasks.length > 0 ? (
            <div className='grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6'>
              {recommendedTasks.map((task: any) => (
                <TaskCard key={task.id} task={task} />
              ))}
            </div>
          ) : (
            <div className='text-center py-8'>
              <div className='mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4'>
                <Target className='h-8 w-8 text-gray-400' />
              </div>
              <h3 className='text-lg font-medium text-gray-900 dark:text-gray-100 mb-2'>
                {t('recommendedTasks.empty')}
              </h3>
              <p className='text-gray-600 dark:text-gray-300'>
                {t('recommendedTasks.emptyDescription')}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
