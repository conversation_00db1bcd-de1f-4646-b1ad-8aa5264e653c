import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderNo: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { orderNo } = await params;
    const userId = (session.user as any).id;

    // 查找支付订单
    const order = await prisma.paymentOrder.findUnique({
      where: { orderNo },
      select: {
        id: true,
        orderNo: true,
        provider: true,
        paymentMethod: true,
        amount: true,
        currency: true,
        status: true,
        description: true,
        paymentUrl: true,
        qrCode: true,
        paidAt: true,
        expiredAt: true,
        createdAt: true,
        userId: true,
      },
    });

    if (!order) {
      return NextResponse.json({ error: '订单不存在' }, { status: 404 });
    }

    // 检查订单所有权（管理员可以查看所有订单）
    const userRole = (session.user as any).role;
    if (userRole !== 'ADMIN' && order.userId !== userId) {
      return NextResponse.json({ error: '无权限查看此订单' }, { status: 403 });
    }

    // 检查订单是否已过期
    const isExpired = order.expiredAt && new Date() > order.expiredAt;
    let currentStatus = order.status;

    if (isExpired && currentStatus === 'PENDING') {
      // 更新过期状态
      await prisma.paymentOrder.update({
        where: { orderNo },
        data: { status: 'EXPIRED' },
      });
      currentStatus = 'EXPIRED';
    }

    // 返回订单状态信息
    return NextResponse.json({
      success: true,
      order: {
        ...order,
        status: currentStatus,
        isExpired: isExpired && currentStatus === 'EXPIRED',
        remainingTime: order.expiredAt
          ? Math.max(0, order.expiredAt.getTime() - Date.now())
          : 0,
      },
    });
  } catch (error) {
    return NextResponse.json({ error: '查询支付状态失败' }, { status: 500 });
  }
}
