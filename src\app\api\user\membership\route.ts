import { NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        memberPlan: true,
        memberPlanExpiry: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    // 根据用户的会员套餐类型获取对应的会员套餐详情
    let membershipPlan = null;

    // 根据枚举值映射到套餐名称
    const planNameMap = {
      FREE: '免费版',
      PRO: '专业版',
      BUSINESS: '商业版',
    };

    const planName = planNameMap[user.memberPlan as keyof typeof planNameMap];

    if (planName) {
      membershipPlan = await prisma.membershipPlan.findFirst({
        where: {
          name: planName,
          isActive: true,
        },
      });
    }

    // 获取本月委托使用量
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const tasksUsedThisMonth = await prisma.task.count({
      where: {
        publisherId: user.id,
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    // 获取白名单使用量
    const whitelistSlotsUsed = 0;

    return NextResponse.json({
      success: true,
      data: {
        id: user.id,
        memberPlan: user.memberPlan,
        memberPlanExpiry: user.memberPlanExpiry,
        membershipPlan,
        tasksUsedThisMonth,
        whitelistSlotsUsed,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
