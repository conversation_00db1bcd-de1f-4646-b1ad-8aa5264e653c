{"cancel": {"title": "支付已取消", "description": "您已取消支付，如有疑问请联系客服。", "backToHome": "返回首页", "retryPayment": "重新支付"}, "success": {"confirmingStatus": "正在确认支付状态...", "title": "支付成功", "confirming": "支付确认中", "successMessage": "您的支付已成功处理，感谢您的购买！", "processingMessage": "支付正在处理中，请稍候或联系客服确认。", "orderId": "订单号：", "backToHome": "返回首页", "viewOrders": "查看订单", "loading": "正在加载..."}, "error": {"title": "支付处理错误", "description": "支付过程中遇到了问题。请不要担心，您的资金是安全的。请尝试重新处理或联系客服。", "detailsTitle": "支付错误详情:", "errorId": "错误ID:", "retryPayment": "重试支付", "backToWallet": "返回钱包", "backToHome": "返回首页", "security": {"title": "资金安全保障", "points": {"encryption": "您的支付信息受到银行级加密保护", "noCharges": "未完成的交易不会产生费用", "support": "如有疑问，请及时联系客服"}}, "customerService": "遇到问题？联系客服获得帮助"}, "loading": {"title": "处理支付信息...", "description": "请稍候，正在安全地处理您的支付请求", "securityNotice": "您的支付信息受到 SSL 加密保护", "steps": {"verify": "验证信息", "process": "处理支付", "complete": "完成"}}}