import { NextRequest, NextResponse } from 'next/server';

import { refreshSession } from '@/lib/session-management';

export async function POST(request: NextRequest) {
  try {
    const success = await refreshSession();

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Session refreshed successfully',
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Failed to refresh session' },
        { status: 401 },
      );
    }
  } catch (error) {
    console.error('Session refresh error:', error);
    return NextResponse.json(
      { success: false, message: 'Session refresh failed' },
      { status: 500 },
    );
  }
}
