// 测试服务条款和隐私政策页面UI一致性修复
const fs = require('fs');
const path = require('path');

console.log('=== 服务条款和隐私政策页面UI一致性验证 ===\n');

// 读取相关文件
const termsPath = path.join(
  __dirname,
  '../../../src/app/[locale]/(main)/terms/page.tsx'
);
const privacyPath = path.join(
  __dirname,
  '../../../src/app/[locale]/(main)/privacy/page.tsx'
);
const simpleBackgroundPath = path.join(
  __dirname,
  '../../../src/components/simple-background.tsx'
);
const homepageCssPath = path.join(
  __dirname,
  '../../../src/app/[locale]/(main)/homepage.css'
);

try {
  const termsContent = fs.readFileSync(termsPath, 'utf8');
  const privacyContent = fs.readFileSync(privacyPath, 'utf8');
  const simpleBackgroundContent = fs.readFileSync(simpleBackgroundPath, 'utf8');
  const homepageCssContent = fs.readFileSync(homepageCssPath, 'utf8');

  // 检查修复的问题
  const fixes = [
    {
      category: '页面布局统一',
      checks: [
        {
          description: '服务条款页面使用max-w-7xl容器',
          test: () => termsContent.includes('max-w-7xl mx-auto'),
          expected: true,
        },
        {
          description: '隐私政策页面使用max-w-7xl容器',
          test: () => privacyContent.includes('max-w-7xl mx-auto'),
          expected: true,
        },
        {
          description: '服务条款页面使用section-spacing',
          test: () => termsContent.includes('section-spacing px-4'),
          expected: true,
        },
        {
          description: '隐私政策页面使用section-spacing',
          test: () => privacyContent.includes('section-spacing px-4'),
          expected: true,
        },
      ],
    },
    {
      category: '导航栏样式统一',
      checks: [
        {
          description: '服务条款页面使用白色背景导航栏',
          test: () =>
            termsContent.includes(
              'bg-white/95 backdrop-blur-md border-b-3 border-black navbar-mobile'
            ),
          expected: true,
        },
        {
          description: '隐私政策页面使用白色背景导航栏',
          test: () =>
            privacyContent.includes(
              'bg-white/95 backdrop-blur-md border-b-3 border-black navbar-mobile'
            ),
          expected: true,
        },
        {
          description: '服务条款页面移除glass-linear导航栏',
          test: () =>
            !termsContent.includes('glass-linear border-b border-white/10'),
          expected: true,
        },
        {
          description: '隐私政策页面移除glass-linear导航栏',
          test: () =>
            !privacyContent.includes('glass-linear border-b border-white/10'),
          expected: true,
        },
      ],
    },
    {
      category: '背景组件统一',
      checks: [
        {
          description: '服务条款页面使用SimpleBackground',
          test: () =>
            termsContent.includes('SimpleBackground') &&
            termsContent.includes('import SimpleBackground'),
          expected: true,
        },
        {
          description: '隐私政策页面使用SimpleBackground',
          test: () =>
            privacyContent.includes('SimpleBackground') &&
            privacyContent.includes('import SimpleBackground'),
          expected: true,
        },
        {
          description: 'SimpleBackground组件存在',
          test: () => fs.existsSync(simpleBackgroundPath),
          expected: true,
        },
        {
          description: 'SimpleBackground使用StarBackground',
          test: () => simpleBackgroundContent.includes('StarBackground'),
          expected: true,
        },
      ],
    },
    {
      category: '视觉元素统一',
      checks: [
        {
          description: '服务条款页面使用card-enhanced卡片',
          test: () => termsContent.includes('card-enhanced hover:shadow-lg'),
          expected: true,
        },
        {
          description: '隐私政策页面使用card-enhanced卡片',
          test: () => privacyContent.includes('card-enhanced hover:shadow-lg'),
          expected: true,
        },
        {
          description: '服务条款页面移除glass-linear卡片',
          test: () => !termsContent.includes('glass-linear border-white/10'),
          expected: true,
        },
        {
          description: '隐私政策页面移除glass-linear卡片',
          test: () => !privacyContent.includes('glass-linear border-white/10'),
          expected: true,
        },
      ],
    },
    {
      category: '文字颜色统一',
      checks: [
        {
          description: '服务条款页面使用深色标题',
          test: () =>
            termsContent.includes('text-gray-900') &&
            !termsContent.includes('gradient-text-tech'),
          expected: true,
        },
        {
          description: '隐私政策页面使用深色标题',
          test: () =>
            privacyContent.includes('text-gray-900') &&
            !privacyContent.includes('gradient-text-tech'),
          expected: true,
        },
        {
          description: '服务条款页面使用深色文字',
          test: () =>
            termsContent.includes('text-gray-600') &&
            !termsContent.includes('text-gray-300'),
          expected: true,
        },
        {
          description: '隐私政策页面使用深色文字',
          test: () =>
            privacyContent.includes('text-gray-600') &&
            !termsContent.includes('text-gray-300'),
          expected: true,
        },
      ],
    },
    {
      category: '页面主题统一',
      checks: [
        {
          description: '服务条款页面使用homepage-theme',
          test: () =>
            termsContent.includes('homepage-theme') &&
            termsContent.includes('homepage-body'),
          expected: true,
        },
        {
          description: '隐私政策页面使用homepage-theme',
          test: () =>
            privacyContent.includes('homepage-theme') &&
            privacyContent.includes('homepage-body'),
          expected: true,
        },
        {
          description: '服务条款页面包含背景装饰元素',
          test: () =>
            termsContent.includes('brand-bg') &&
            termsContent.includes('tech-grid'),
          expected: true,
        },
        {
          description: '隐私政策页面包含背景装饰元素',
          test: () =>
            privacyContent.includes('brand-bg') &&
            privacyContent.includes('tech-grid'),
          expected: true,
        },
      ],
    },
  ];

  let allPassed = true;
  let totalChecks = 0;
  let passedChecks = 0;

  fixes.forEach(category => {
    console.log(`📋 ${category.category}:`);

    category.checks.forEach((check, index) => {
      totalChecks++;
      const result = check.test();
      const status = result === check.expected ? '✅' : '❌';

      if (result === check.expected) {
        passedChecks++;
      } else {
        allPassed = false;
      }

      console.log(`   ${index + 1}. ${check.description}: ${status}`);
    });

    console.log('');
  });

  console.log('=== 修复结果总结 ===');
  console.log(`总检查项: ${totalChecks}`);
  console.log(`通过检查: ${passedChecks}`);
  console.log(`通过率: ${Math.round((passedChecks / totalChecks) * 100)}%`);

  if (allPassed) {
    console.log('✅ 所有UI一致性修复都已完成！');
    console.log('\n🎯 修复内容总结：');
    console.log('1. 页面布局已统一：使用max-w-7xl容器和section-spacing');
    console.log('2. 导航栏样式已统一：白色背景，与首页一致');
    console.log('3. 背景组件已统一：使用SimpleBackground和StarBackground');
    console.log('4. 视觉元素已统一：card-enhanced卡片，移除glass-linear');
    console.log('5. 文字颜色已统一：深色文字，移除白色文字');
    console.log('6. 页面主题已统一：homepage-theme和homepage-body');
  } else {
    console.log('❌ 仍有部分问题需要修复');
  }

  console.log('\n=== 样式对比 ===');
  console.log('修改前 → 修改后');
  console.log('---------------------');
  console.log('max-w-4xl → max-w-7xl');
  console.log('py-20 px-4 → section-spacing px-4');
  console.log('glass-linear → card-enhanced');
  console.log('text-white → text-gray-900');
  console.log('text-gray-300 → text-gray-600');
  console.log('gradient-text-tech → text-gray-900');
  console.log('Background3D → SimpleBackground');
  console.log('暗色导航栏 → 白色导航栏');

  console.log('\n=== 一致性特征 ===');
  console.log('✓ 相同的页面容器宽度 (max-w-7xl)');
  console.log('✓ 相同的页面间距 (section-spacing)');
  console.log('✓ 相同的导航栏样式 (白色背景)');
  console.log('✓ 相同的背景组件 (SimpleBackground)');
  console.log('✓ 相同的卡片样式 (card-enhanced)');
  console.log('✓ 相同的文字颜色 (深色主题)');
  console.log('✓ 相同的页面主题 (homepage-theme)');
} catch (error) {
  console.error('测试脚本执行失败:', error.message);
}
