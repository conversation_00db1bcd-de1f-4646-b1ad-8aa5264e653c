generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model User {
  id                                           String              @id @default(cuid())
  name                                         String?
  email                                        String?             @unique
  emailVerified                                DateTime?
  image                                        String?
  username                                     String?             @unique
  password                                     String?
  role                                         UserRole            @default(USER)
  isActive                                     Boolean             @default(true)
  lastLoginAt                                  DateTime?
  memberPlan                                   MemberPlan          @default(FREE)
  memberPlanExpiry                             DateTime?
  balance                                      Float               @default(0)
  frozenAmount                                 Float               @default(0)
  totalIncome                                  Float               @default(0)
  totalExpense                                 Float               @default(0)
  status                                       UserStatus          @default(ACTIVE)
  completedTasks                               Int                 @default(0)
  publishedTasks                               Int                 @default(0)
  createdAt                                    DateTime            @default(now())
  updatedAt                                    DateTime            @updatedAt
  registrationLanguage                         String              @default("en")
  accounts                                     Account[]
  sessions                                     Session[]
  paymentOrders                                PaymentOrder[]      @relation("UserPaymentOrders")
  reviewedWhitelists                           ShopWhitelist[]     @relation("WhitelistReviewer")
  shopWhitelist                                ShopWhitelist[]
  task_evidence_task_evidence_reviewedByToUser task_evidence[]     @relation("task_evidence_reviewedByToUser")
  task_evidence_task_evidence_uploadedByToUser task_evidence[]     @relation("task_evidence_uploadedByToUser")
  acceptedTasksList                            Task[]              @relation("AcceptedTasks")
  publishedTasksList                           Task[]              @relation("PublishedTasks")
  ticketReplies                                TicketReply[]       @relation("TicketReplies")
  assignedTickets                              Ticket[]            @relation("AssignedTickets")
  userTickets                                  Ticket[]            @relation("UserTickets")
  walletTransactions                           WalletTransaction[]
  reviewedWithdrawals                          WithdrawalRequest[] @relation("WithdrawalReviewer")
  withdrawalRequests                           WithdrawalRequest[]
}

model Platform {
  id        String   @id @default(cuid())
  name      String   @unique
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  tasks     Task[]
}

model Category {
  id        String   @id @default(cuid())
  name      String   @unique
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  tasks     Task[]
}

model ChargebackType {
  id        String   @id @default(cuid())
  name      String   @unique
  rate      Float
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model PaymentMethod {
  id        String   @id @default(cuid())
  name      String   @unique
  rate      Float
  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SystemRate {
  id                      String   @id @default(cuid())
  noEvidenceExtraRate     Float
  depositRatio            Float
  bankWithdrawalRate      Float    @default(0)
  erc20WithdrawalRate     Float    @default(0)
  trc20WithdrawalRate     Float    @default(0)
  minimumWithdrawalAmount Float    @default(50)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  @@map("system_rates")
}

model MembershipPlan {
  id             String   @id @default(cuid())
  name           String   @unique
  price          Float
  period         String   @default("月")
  maxTasks       Int?
  taskTypes      String[]
  platformRate   Float
  whitelistSlots Int      @default(0)
  supportLevel   String
  features       String[]
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("membership_plans")
}

model Task {
  id                      String             @id
  title                   String?
  productUrl              String
  productDescription      String
  quantity                Int
  unitPrice               Float
  totalAmount             Float
  listingTime             String
  recipientName           String
  recipientPhone          String
  shippingAddress         String
  evidenceUploadType      EvidenceUploadType
  evidenceStatus          EvidenceStatus?
  evidenceRejectReason    String?
  cartScreenshots         String[]
  evidenceFiles           String[]
  platformId              String
  categoryId              String
  chargebackTypeIds       String[]
  paymentMethodIds        String[]
  finalTotal              Float
  status                  TaskStatus         @default(PENDING)
  rejectedAt              DateTime?
  approvedAt              DateTime?
  publishedAt             DateTime?
  acceptedAt              DateTime?
  expiresAt               DateTime?
  completedAt             DateTime?
  logisticsDeadline       DateTime?
  orderReviewDeadline     DateTime?
  logisticsReviewDeadline DateTime?
  deliveryDeadline        DateTime?
  orderNumber             String?
  orderScreenshot         String?
  trackingNumber          String?
  logisticsScreenshots    String[]           @default([])
  reviewedAt              DateTime?
  reviewRejectReason      String?
  publisherId             String
  accepterId              String?
  createdAt               DateTime           @default(now())
  updatedAt               DateTime           @updatedAt
  logisticsTracking       LogisticsTracking?
  task_evidence           task_evidence[]
  accepter                User?              @relation("AcceptedTasks", fields: [accepterId], references: [id])
  category                Category           @relation(fields: [categoryId], references: [id])
  platform                Platform           @relation(fields: [platformId], references: [id])
  publisher               User               @relation("PublishedTasks", fields: [publisherId], references: [id])

  @@map("tasks")
}

model LogisticsTracking {
  id                     String           @id @default(cuid())
  taskId                 String           @unique
  trackingNumber         String
  carrierCode            Int?
  carrierName            String?
  tag                    String?
  trackingStatus         TrackingStatus   @default(TRACKING)
  packageStatus          String?
  packageSubStatus       String?
  statusDescription      String?
  latestEventTime        DateTime?
  latestEventDescription String?
  latestEventLocation    String?
  daysAfterOrder         Int?             @default(0)
  daysOfTransit          Int?             @default(0)
  daysAfterLastUpdate    Int?             @default(0)
  estimatedDeliveryFrom  DateTime?
  estimatedDeliveryTo    DateTime?
  estimatedSource        String?
  shipperCountry         String?
  shipperState           String?
  shipperCity            String?
  shipperPostalCode      String?
  recipientCountry       String?
  recipientState         String?
  recipientCity          String?
  recipientPostalCode    String?
  serviceType            String?
  weight                 String?
  dimensions             String?
  pieces                 String?
  providersHash          String?
  registeredAt           DateTime?
  lastSyncTime           DateTime?
  lastSyncStatus         String?
  rawTrackingData        String?
  createdAt              DateTime         @default(now())
  updatedAt              DateTime         @updatedAt
  events                 LogisticsEvent[]
  task                   Task             @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("logistics_tracking")
}

model LogisticsEvent {
  id                     String            @id @default(cuid())
  logisticsTrackingId    String
  timeIso                String
  timeUtc                DateTime
  timeRaw                String?
  description            String
  descriptionTranslation String?
  location               String?
  stage                  String?
  subStatus              String?
  country                String?
  state                  String?
  city                   String?
  street                 String?
  postalCode             String?
  longitude              String?
  latitude               String?
  createdAt              DateTime          @default(now())
  logisticsTracking      LogisticsTracking @relation(fields: [logisticsTrackingId], references: [id], onDelete: Cascade)

  @@map("logistics_events")
}

model WalletTransaction {
  id             String            @id @default(cuid())
  userId         String
  type           TransactionType
  amount         Float
  status         TransactionStatus @default(PENDING)
  description    String
  reference      String?
  depositMethod  DepositMethod?
  withdrawMethod WithdrawMethod?
  createdAt      DateTime          @default(now())
  completedAt    DateTime?
  user           User              @relation(fields: [userId], references: [id])

  @@map("wallet_transactions")
}

model WithdrawalRequest {
  id              String           @id @default(cuid())
  userId          String
  amount          Float
  fee             Float
  actualAmount    Float
  currency        String           @default("USD")
  withdrawMethod  WithdrawMethod
  reference       String?
  accountHolder   String?
  bankCard        String?
  bankName        String?
  bankSwift       String?
  branchAddress   String?
  bankCountry     String?
  walletAddress   String?
  cryptoCurrency  String?
  blockchain      String?
  status          WithdrawalStatus @default(PENDING)
  rejectionReason String?
  reviewedBy      String?
  reviewedAt      DateTime?
  completedAt     DateTime?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  reviewer        User?            @relation("WithdrawalReviewer", fields: [reviewedBy], references: [id])
  user            User             @relation(fields: [userId], references: [id])

  @@map("withdrawal_requests")
}

model ShopWhitelist {
  id         String          @id @default(cuid())
  userId     String
  shopName   String
  shopUrl    String
  platform   String
  isActive   Boolean         @default(false)
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt
  reviewNote String?
  reviewedAt DateTime?
  reviewedBy String?
  status     WhitelistStatus @default(PENDING)
  reviewer   User?           @relation("WhitelistReviewer", fields: [reviewedBy], references: [id])
  user       User            @relation(fields: [userId], references: [id])

  @@map("shop_whitelist")
}

model Ticket {
  id          String         @id @default(cuid())
  title       String
  description String
  type        TicketType
  priority    TicketPriority
  status      TicketStatus   @default(PENDING)
  userId      String
  assignedTo  String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  resolvedAt  DateTime?
  closedAt    DateTime?
  replies     TicketReply[]
  assignee    User?          @relation("AssignedTickets", fields: [assignedTo], references: [id])
  user        User           @relation("UserTickets", fields: [userId], references: [id])

  @@map("tickets")
}

model TicketReply {
  id         String   @id @default(cuid())
  ticketId   String
  content    String
  isStaff    Boolean  @default(false)
  authorId   String
  authorName String
  createdAt  DateTime @default(now())
  author     User     @relation("TicketReplies", fields: [authorId], references: [id])
  ticket     Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  @@map("ticket_replies")
}

model PaymentConfig {
  id        String   @id @default(cuid())
  provider  String   @unique
  name      String
  isEnabled Boolean
  settings  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("payment_configs")
}

model PaymentOrder {
  id                String        @id @default(cuid())
  orderNo           String        @unique
  provider          String
  paymentMethod     String?
  amount            Decimal       @db.Decimal(10, 2)
  currency          String        @default("CNY")
  // 货币转换相关字段
  originalAmount    Decimal?      @db.Decimal(10, 2)  // 原始金额（通常是USD）
  originalCurrency  String?       @default("USD")     // 原始货币
  convertedAmount   Decimal?      @db.Decimal(10, 2)  // 转换后金额（通常是CNY）
  convertedCurrency String?       @default("CNY")     // 转换后货币
  exchangeRate      Decimal?      @db.Decimal(10, 6)  // 汇率（6位小数精度）
  exchangeRateSource String?                          // 汇率来源
  status            PaymentStatus @default(PENDING)
  description       String?
  notifyUrl         String?
  returnUrl         String?
  paymentUrl        String?
  qrCode            String?
  thirdOrderNo      String?
  paidAt            DateTime?
  expiredAt         DateTime?
  userId            String?
  metadata          Json?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  user              User?         @relation("UserPaymentOrders", fields: [userId], references: [id])

  @@map("payment_orders")
}

model PaymentLog {
  id        String   @id @default(cuid())
  orderNo   String
  action    String
  request   Json?
  response  Json?
  status    String?
  message   String?
  createdAt DateTime @default(now())

  @@index([orderNo])
  @@map("payment_logs")
}

model EmailLog {
  id             String   @id @default(cuid())
  orderNo        String?
  recipientEmail String
  emailType      String
  status         String   // 'SUCCESS', 'FAILED', 'RETRY'
  sentAt         DateTime @default(now())
  error          String?
  retryCount     Int      @default(0)

  @@index([orderNo])
  @@index([recipientEmail])
  @@index([emailType])
  @@index([sentAt])
  @@map("email_logs")
}

model task_evidence {
  id                                  String         @id
  taskId                              String
  evidenceType                        EvidenceType
  fileName                            String
  fileUrl                             String
  fileSize                            Int?
  mimeType                            String?
  description                         String?
  status                              EvidenceStatus @default(PENDING_SUBMISSION)
  reviewNote                          String?
  reviewedBy                          String?
  reviewedAt                          DateTime?      @db.Timestamp(6)
  uploadedBy                          String
  createdAt                           DateTime       @default(now()) @db.Timestamp(6)
  updatedAt                           DateTime       @default(now()) @db.Timestamp(6)
  User_task_evidence_reviewedByToUser User?          @relation("task_evidence_reviewedByToUser", fields: [reviewedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  tasks                               Task           @relation(fields: [taskId], references: [id], onDelete: Cascade, onUpdate: NoAction)
  User_task_evidence_uploadedByToUser User           @relation("task_evidence_uploadedByToUser", fields: [uploadedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

enum UserRole {
  USER
  ADMIN
}

enum MemberPlan {
  FREE
  PRO
  BUSINESS
}

enum UserStatus {
  ACTIVE
  FROZEN
}

enum Status {
  ACTIVE
  INACTIVE
}

enum TaskStatus {
  PENDING
  REJECTED
  RECRUITING
  IN_PROGRESS
  PENDING_LOGISTICS
  PENDING_REVIEW
  PENDING_DELIVERY
  COMPLETED
  EXPIRED
  CANCELLED
}

enum EvidenceUploadType {
  IMMEDIATE
  LATER
  NONE
}

enum EvidenceStatus {
  PENDING_SUBMISSION
  UNDER_REVIEW
  NO_EVIDENCE
  REVIEWED
  REJECTED
}

enum TicketType {
  TECHNICAL
  BILLING
  ACCOUNT
  FEATURE_REQUEST
  BUG_REPORT
  OTHER
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TicketStatus {
  PENDING
  IN_PROGRESS
  RESOLVED
  CLOSED
  CANCELLED
}

enum WhitelistStatus {
  PENDING
  APPROVED
  REJECTED
}

enum TransactionType {
  DEPOSIT
  WITHDRAW
  TASK_FEE
  COMMISSION
  MEMBERSHIP
  REFUND
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum DepositMethod {
  ALIPAY
  WECHAT
  BANK_CARD
  PAYPAL
}

enum WithdrawMethod {
  ALIPAY
  WECHAT
  BANK_CARD
  USDT_ERC20
  USDT_TRC20
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  CANCELLED
  EXPIRED
  REFUNDING
  REFUNDED
}

enum WithdrawalStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
}

enum TrackingStatus {
  TRACKING
  STOPPED
}

enum EvidenceType {
  CART_SCREENSHOT
  ORDER_CONFIRMATION
  PAYMENT_PROOF
  SHIPPING_LABEL
  DELIVERY_PROOF
  PRODUCT_PHOTO
  COMMUNICATION_RECORD
  REFUND_PROOF
  OTHER
}
