'use client';

import {
  Shield,
  Home,
  Users,
  ClipboardList,
  FileCheck,
  List,
  FileImage,
  ShieldCheck,
  Ticket,
  Settings,
  Calculator,
  Crown,
  CreditCard,
  DollarSign,
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import * as React from 'react';

import { NavMain } from '@/components/nav-main';
import { AdminNavUser } from '@/components/nav-user-admin';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

// 管理后台的导航数据
const getAdminData = (user: any) => ({
  user: {
    name: user?.name || '管理员',
    email: user?.email || '<EMAIL>',
    avatar: user?.image || '/avatars/admin.jpg',
  },
  navMain: [
    {
      title: '仪表盘',
      url: '/admin',
      icon: Home,
      isActive: true,
    },
    {
      title: '用户管理',
      url: '/admin/users',
      icon: Users,
    },
    {
      title: '白名单审核',
      url: '/admin/whitelist',
      icon: ShieldCheck,
    },
    {
      title: '委托管理',
      url: '#',
      icon: ClipboardList,
      items: [
        {
          title: '委托审核',
          url: '/admin/tasks/review',
          icon: FileCheck,
        },
        {
          title: '委托列表',
          url: '/admin/tasks',
          icon: List,
        },
        {
          title: '证据管理',
          url: '/admin/tasks/evidence',
          icon: FileImage,
        },
      ],
    },
    {
      title: '工单管理',
      url: '/admin/tickets',
      icon: Ticket,
    },
    {
      title: '提现审核',
      url: '/admin/withdrawals',
      icon: DollarSign,
    },
    {
      title: '系统设置',
      url: '#',
      icon: Settings,
      items: [
        {
          title: '费率设置',
          url: '/admin/settings/rates',
          icon: Calculator,
        },
        {
          title: '会员套餐',
          url: '/admin/settings/membership',
          icon: Crown,
        },
        {
          title: '支付配置',
          url: '/admin/payment-configs',
          icon: CreditCard,
        },
      ],
    },
  ],
});

export function AdminSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  const data = getAdminData(session?.user);

  return (
    <Sidebar collapsible='icon' {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size='lg'
              className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground'
            >
              <div className='flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground'>
                <Shield className='size-4' />
              </div>
              <div className='grid flex-1 text-left text-sm leading-tight'>
                <span className='truncate font-semibold'>管理后台</span>
                <span className='truncate text-xs'>系统管理</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <AdminNavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
