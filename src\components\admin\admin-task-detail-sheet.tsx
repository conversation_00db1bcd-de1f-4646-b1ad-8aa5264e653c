'use client';

import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  Clock,
  DollarSign,
  Calendar,
  Tag,
  CreditCard,
  Building2,
  FileText,
  Package,
  AlertCircle,
  CheckCircle,
  Info,
  User,
  Mail,
  Phone,
  MapPin,
  Link2,
  Truck,
  FileImage,
  MessageSquare,
} from 'lucide-react';
// import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

interface AdminTaskDetailSheetProps {
  task: any;
  children: React.ReactNode;
}

export function AdminTaskDetailSheet({
  task,
  children,
}: AdminTaskDetailSheetProps) {
  // 管理员面板固定文本（不使用国际化）
  const t = (key: string) => {
    const texts: Record<string, string> = {
      'taskDetail.title': '委托详情',
      'taskDetail.description': '查看委托的详细信息',
      'taskDetail.basicInfo': '基础信息',
      'taskDetail.taskId': '委托ID',
      'taskDetail.taskStatus': '委托状态',
      'taskDetail.platform': '平台',
      'taskDetail.category': '分类',
      'taskDetail.publishTime': '发布时间',
      'taskDetail.evidenceStatus.label': '证据状态',
      'taskDetail.publisherInfo': '发布者信息',
      'taskDetail.username': '用户名',
      'taskDetail.email': '邮箱',
      'taskDetail.productInfo': '商品信息',
      'taskDetail.productUrl': '商品链接',
      'taskDetail.productDescription': '商品描述',
      'taskDetail.quantity': '数量',
      'taskDetail.pieces': '件',
      'taskDetail.unitPrice': '单价',
      'taskDetail.totalPrice': '总价',
      'taskDetail.taskConfig': '委托配置',
      'taskDetail.listingDuration': '上架时长',
      'taskDetail.hours': '小时',
      'taskDetail.days': '天',
      'taskDetail.evidenceUploadType': '证据上传类型',
      'taskDetail.shippingInfo': '收货信息',
      'taskDetail.recipientName': '收件人',
      'taskDetail.contactPhone': '联系电话',
      'taskDetail.shippingAddress': '收货地址',
      'taskDetail.chargebackTypes': '退款类型',
      'taskDetail.paymentMethods': '支付方式',
      'taskDetail.costDetails': '费用详情',
      'taskDetail.userPayment': '用户支付',
      'taskDetail.commission': '平台抽成',
      'taskDetail.accepterInfo': '接受者信息',
      'taskDetail.evidenceRejectReason': '证据拒绝原因',
      'taskDetail.timeInfo': '时间信息',
      'taskDetail.createTime': '创建时间',
      'taskDetail.updateTime': '更新时间',
      'taskDetail.expiresAt': '过期时间',
      'taskDetail.completedAt': '完成时间',
      'taskDetail.status.pending': '待审核',
      'taskDetail.status.recruiting': '招募中',
      'taskDetail.status.inProgress': '进行中',
      'taskDetail.status.completed': '已完成',
      'taskDetail.status.rejected': '已拒绝',
      'taskDetail.evidenceStatus.pendingSubmission': '待提交',
      'taskDetail.evidenceStatus.underReview': '审核中',
      'taskDetail.evidenceStatus.reviewed': '已审核',
      'taskDetail.evidenceStatus.rejected': '已拒绝',
      'taskDetail.evidenceStatus.noEvidence': '无证据',
      'taskDetail.unknownPlatform': '未知平台',
      'taskDetail.unknownCategory': '未知分类',
      'taskDetail.unknownUser': '未知用户',
      'taskDetail.unknownEmail': '未知邮箱',
      'taskDetail.noUrl': '无链接',
      'taskDetail.noDescription': '无描述',
      'taskDetail.noInfo': '无信息',
      'taskDetail.noAddress': '无地址',
      'taskDetail.noChargebackTypes': '未设置退款类型',
      'taskDetail.noPaymentMethods': '未设置支付方式',
      'taskDetail.uploadType.immediate': '立即上传',
      'taskDetail.uploadType.delayed': '延迟上传',
      'taskDetail.uploadType.none': '无需上传',
      'taskDetail.uploadType.unknown': '未知类型',
    };
    return texts[key] || key;
  };
  const [isOpen, setIsOpen] = useState(false);

  // 获取委托状态标签
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return t('taskDetail.status.pending');
      case 'RECRUITING':
        return t('taskDetail.status.recruiting');
      case 'IN_PROGRESS':
        return t('taskDetail.status.inProgress');
      case 'COMPLETED':
        return t('taskDetail.status.completed');
      case 'REJECTED':
        return t('taskDetail.status.rejected');
      default:
        return status;
    }
  };

  // 获取证据状态标签
  const getEvidenceStatusLabel = (evidenceStatus: string) => {
    switch (evidenceStatus) {
      case 'PENDING_SUBMISSION':
        return t('taskDetail.evidenceStatus.pendingSubmission');
      case 'UNDER_REVIEW':
        return t('taskDetail.evidenceStatus.underReview');
      case 'REVIEWED':
        return t('taskDetail.evidenceStatus.reviewed');
      case 'REJECTED':
        return t('taskDetail.evidenceStatus.rejected');
      case 'NO_EVIDENCE':
        return t('taskDetail.evidenceStatus.noEvidence');
      default:
        return evidenceStatus;
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='w-[600px] sm:w-[800px] overflow-y-auto'>
        <SheetHeader>
          <SheetTitle>{t('taskDetail.title')}</SheetTitle>
          <SheetDescription>{t('taskDetail.description')}</SheetDescription>
        </SheetHeader>

        {task && (
          <div className='mt-6 space-y-6'>
            {/* 基本信息 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.basicInfo')}
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.taskId')}
                  </label>
                  <p className='font-mono text-sm bg-muted p-2 rounded'>
                    {task.id}
                  </p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.taskStatus')}
                  </label>
                  <Badge variant='outline' className='w-fit'>
                    {getStatusLabel(task.status)}
                  </Badge>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.platform')}
                  </label>
                  <p>{task.platform || t('taskDetail.unknownPlatform')}</p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.category')}
                  </label>
                  <p>{task.category || t('taskDetail.unknownCategory')}</p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.publishTime')}
                  </label>
                  <p>{new Date(task.createdAt).toLocaleString('zh-CN')}</p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.evidenceStatus.label')}
                  </label>
                  <Badge variant='outline' className='w-fit'>
                    {getEvidenceStatusLabel(task.evidenceStatus)}
                  </Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* 发布者信息 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.publisherInfo')}
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.username')}
                  </label>
                  <p>
                    {task.publisher?.nickname || t('taskDetail.unknownUser')}
                  </p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.email')}
                  </label>
                  <p>{task.publisher?.email || t('taskDetail.unknownEmail')}</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 商品信息 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.productInfo')}
              </h3>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.productUrl')}
                  </label>
                  <p className='text-sm break-all bg-muted p-2 rounded'>
                    {task.productUrl || t('taskDetail.noUrl')}
                  </p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.productDescription')}
                  </label>
                  <p className='text-sm bg-muted p-2 rounded whitespace-pre-wrap'>
                    {task.productDescription || t('taskDetail.noDescription')}
                  </p>
                </div>
                <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='space-y-2'>
                    <label className='text-sm font-medium text-muted-foreground'>
                      {t('taskDetail.quantity')}
                    </label>
                    <p className='text-lg font-medium'>
                      {task.quantity || 0} {t('taskDetail.pieces')}
                    </p>
                  </div>
                  <div className='space-y-2'>
                    <label className='text-sm font-medium text-muted-foreground'>
                      {t('taskDetail.unitPrice')}
                    </label>
                    <p className='text-lg font-medium text-green-600'>
                      ${(task.unitPrice || 0).toFixed(2)}
                    </p>
                  </div>
                  <div className='space-y-2'>
                    <label className='text-sm font-medium text-muted-foreground'>
                      {t('taskDetail.totalPrice')}
                    </label>
                    <p className='text-lg font-medium'>
                      $
                      {((task.quantity || 0) * (task.unitPrice || 0)).toFixed(
                        2,
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 委托配置 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.taskConfig')}
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.listingDuration')}
                  </label>
                  <p>
                    {task.listingTime || 0} {t('taskDetail.hours')} (
                    {Math.ceil(parseInt(task.listingTime || '0', 10) / 24)}{' '}
                    {t('taskDetail.days')})
                  </p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.evidenceUploadType')}
                  </label>
                  <p>
                    {task.evidenceUploadType === 'IMMEDIATE'
                      ? t('taskDetail.uploadType.immediate')
                      : task.evidenceUploadType === 'DELAYED'
                        ? t('taskDetail.uploadType.delayed')
                        : task.evidenceUploadType === 'NONE'
                          ? t('taskDetail.uploadType.none')
                          : t('taskDetail.uploadType.unknown')}
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* 收货信息 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.shippingInfo')}
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.recipientName')}
                  </label>
                  <p>{task.recipientName || t('taskDetail.noInfo')}</p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.contactPhone')}
                  </label>
                  <p>{task.recipientPhone || t('taskDetail.noInfo')}</p>
                </div>
              </div>
              <div className='space-y-2'>
                <label className='text-sm font-medium text-muted-foreground'>
                  {t('taskDetail.shippingAddress')}
                </label>
                <p className='bg-muted p-2 rounded'>
                  {task.shippingAddress || t('taskDetail.noAddress')}
                </p>
              </div>
            </div>

            <Separator />

            {/* 拒付类型 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.chargebackTypes')}
              </h3>
              <div className='flex flex-wrap gap-2'>
                {task.chargebackTypes && task.chargebackTypes.length > 0 ? (
                  task.chargebackTypes.map((type: string, index: number) => (
                    <Badge
                      key={index}
                      variant='outline'
                      className='bg-orange-100 text-orange-800'
                    >
                      {type}
                    </Badge>
                  ))
                ) : (
                  <p className='text-sm text-muted-foreground'>
                    {t('taskDetail.noChargebackTypes')}
                  </p>
                )}
              </div>
            </div>

            <Separator />

            {/* 支付方式 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.paymentMethods')}
              </h3>
              <div className='flex flex-wrap gap-2'>
                {task.paymentMethods && task.paymentMethods.length > 0 ? (
                  task.paymentMethods.map((method: string, index: number) => (
                    <Badge
                      key={index}
                      variant='outline'
                      className='bg-blue-100 text-blue-800'
                    >
                      {method}
                    </Badge>
                  ))
                ) : (
                  <p className='text-sm text-muted-foreground'>
                    {t('taskDetail.noPaymentMethods')}
                  </p>
                )}
              </div>
            </div>

            <Separator />

            {/* 费用详情 */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.costDetails')}
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.userPayment')}
                  </label>
                  <p className='text-lg font-medium text-blue-600'>
                    ${(task.finalTotal || 0).toFixed(2)}
                  </p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.commission')}
                  </label>
                  <p className='text-lg font-medium text-green-600'>
                    ${(task.commission || 0).toFixed(2)}
                  </p>
                </div>
              </div>
            </div>

            {/* 接单者信息 */}
            {task.accepter && (
              <>
                <Separator />
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>
                    {t('taskDetail.accepterInfo')}
                  </h3>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        {t('taskDetail.username')}
                      </label>
                      <p>
                        {task.accepter.nickname || t('taskDetail.unknownUser')}
                      </p>
                    </div>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium text-muted-foreground'>
                        {t('taskDetail.email')}
                      </label>
                      <p>
                        {task.accepter.email || t('taskDetail.unknownEmail')}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* 证据拒绝原因 */}
            {task.evidenceRejectReason && (
              <>
                <Separator />
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>
                    {t('taskDetail.evidenceRejectReason')}
                  </h3>
                  <div className='bg-red-50 p-3 rounded-lg border border-red-200'>
                    <p className='text-sm text-red-800 whitespace-pre-wrap'>
                      {task.evidenceRejectReason}
                    </p>
                  </div>
                </div>
              </>
            )}

            {/* 时间信息 */}
            <Separator />
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>
                {t('taskDetail.timeInfo')}
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.createTime')}
                  </label>
                  <p>{new Date(task.createdAt).toLocaleString('zh-CN')}</p>
                </div>
                <div className='space-y-2'>
                  <label className='text-sm font-medium text-muted-foreground'>
                    {t('taskDetail.updateTime')}
                  </label>
                  <p>{new Date(task.updatedAt).toLocaleString('zh-CN')}</p>
                </div>
                {task.expiresAt && (
                  <div className='space-y-2'>
                    <label className='text-sm font-medium text-muted-foreground'>
                      {t('taskDetail.expiresAt')}
                    </label>
                    <p>{new Date(task.expiresAt).toLocaleString('zh-CN')}</p>
                  </div>
                )}
                {task.completedAt && (
                  <div className='space-y-2'>
                    <label className='text-sm font-medium text-muted-foreground'>
                      {t('taskDetail.completedAt')}
                    </label>
                    <p>{new Date(task.completedAt).toLocaleString('zh-CN')}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
