{"navigation": {"title": "Shop Whitelist", "description": "Manage whitelist shop list to prevent users from publishing tasks related to whitelisted shops"}, "form": {"addButton": "Add Whitelist Shop", "dialogTitle": "Add Shop to Whitelist", "dialogDescription": "After adding a shop to the whitelist, users will not be able to publish tasks related to this shop", "shopName": "Shop Name", "shopNamePlaceholder": "Enter shop name", "platform": "Platform", "platformPlaceholder": "Select platform", "shopUrl": "Shop URL", "shopUrlPlaceholder": "https://...", "cancel": "Cancel", "submit": "Add to Whitelist", "submitting": "Adding..."}, "validation": {"shopNameRequired": "Shop name cannot be empty", "shopUrlInvalid": "Please enter a valid shop URL", "platformRequired": "Please select a platform"}, "platforms": {"dhgate": "DHgate", "other": "Independent website"}, "status": {"pending": "Pending Review", "approved": "Approved", "rejected": "Rejected", "unknown": "Unknown Status"}, "stats": {"totalShops": "Total Whitelist Shops", "totalShopsDesc": "Added whitelist shops", "availableSlots": "Available Slots", "availableSlotsDesc": "Remaining slots to add", "totalSlots": "Total Slots", "totalSlotsDesc": "Membership level limit"}, "list": {"title": "Whitelist Shop List", "empty": "No whitelist shops", "emptyDesc": "Click the \"Add Whitelist Shop\" button above to start adding", "addedOn": "Added on", "currentStatus": "Current Status"}, "actions": {"withdraw": "Withdraw Application", "delete": "Delete", "processing": "Processing..."}, "dialogs": {"withdrawTitle": "Confirm Withdraw Application", "deleteTitle": "Confirm Delete", "withdrawDesc": "Are you sure you want to withdraw this whitelist application? You will need to reapply after withdrawal.", "deleteDesc": "Are you sure you want to delete this whitelist entry? You can re-add it after deletion.", "confirmWithdraw": "Confirm Withdraw", "confirmDelete": "Confirm Delete", "cancel": "Cancel"}, "instructions": {"title": "Feature Instructions", "whitelistEffect": "Whitelist Effect: Approved whitelist shops will be prohibited from publishing tasks, users cannot publish related tasks for these shops in the task hall.", "reviewProcess": "Review Process: After submitting a whitelist application, you need to wait for administrator review, it will take effect only after approval.", "quotaLimit": "Quota Limit: Different membership levels have different whitelist quotas, upgrade membership to get more whitelist shop slots.", "quickReview": "Quick Review: Administrators will review based on shop information and history, usually completed within 24 hours."}, "messages": {"loading": "Loading...", "loadError": "Failed to load whitelist", "loadErrorDesc": "Please refresh the page and try again", "quotaInsufficient": "Insufficient whitelist quota", "quotaInsufficientDesc": "Please upgrade membership plan to get more whitelist quota", "timeLoading": "Loading..."}, "hooks": {"create": {"error": "Failed to create whitelist", "success": "Added successfully", "successDesc": "Shop has been added to whitelist, users will not be able to publish tasks for this shop"}, "delete": {"error": "Failed to delete whitelist", "success": "Deleted successfully", "successDesc": "Shop has been removed from whitelist, users can now publish tasks for this shop"}}}