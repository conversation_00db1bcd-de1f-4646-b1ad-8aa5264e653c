import { NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 获取启用的支付方式列表
    const paymentMethods = await prisma.paymentMethod.findMany({
      where: { status: 'ACTIVE' },
      select: {
        id: true,
        name: true,
        rate: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { name: 'asc' },
    });

    return NextResponse.json({
      success: true,
      data: paymentMethods,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
