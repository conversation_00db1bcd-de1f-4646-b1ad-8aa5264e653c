import { useQuery } from '@tanstack/react-query';

interface AdminTaskFilters {
  search?: string;
  platform?: string;
  taskStatus?: string;
  page?: number;
  limit?: number;
}

interface AdminTask {
  id: string;
  title: string;
  platform: string;
  category: string;
  productUrl: string;
  productDescription: string;
  chargebackTypes: string[];
  paymentMethods: string[];
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  finalTotal: number;
  commission: number;
  listingTime: string;
  recipientName: string;
  recipientPhone: string;
  shippingAddress: string;
  publisher: {
    id: string;
    nickname: string;
    email: string;
  };
  accepter: {
    id: string;
    nickname: string;
    email: string;
  } | null;
  status: string;
  evidenceStatus: string | null;
  evidenceUploadType: string;
  evidenceFiles: string[];
  evidenceRejectReason: string | null;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  completedAt?: string;
}

interface AdminTaskStats {
  totalTasks: number;
  pendingTasks: number;
  recruitingTasks: number;
  activeTasks: number;
  completedTasks: number;
  rejectedTasks: number;
  expiredTasks: number;
  totalValue: number;
  pendingSubmissionEvidence: number;
  underReviewEvidence: number;
  reviewedEvidence: number;
  noEvidence: number;
}

interface AdminTaskResponse {
  success: boolean;
  data: {
    tasks: AdminTask[];
    stats: AdminTaskStats;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export function useAdminAllTasks(filters: AdminTaskFilters = {}) {
  return useQuery<AdminTaskResponse>({
    queryKey: ['admin-all-tasks', filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.search) params.append('search', filters.search);
      if (filters.platform) params.append('platform', filters.platform);
      if (filters.taskStatus) params.append('taskStatus', filters.taskStatus);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/admin/tasks/all?${params.toString()}`);

      if (!response.ok) {
        throw new Error('获取委托列表失败');
      }

      return response.json();
    },
    staleTime: 30 * 1000, // 30秒
    refetchOnWindowFocus: false,
  });
}

export type { AdminTask, AdminTaskStats, AdminTaskFilters };
