'use client';

import Image from 'next/image';
import React, { ReactNode } from 'react';

import {
  useResponsive,
  useMediaQuery,
  type Breakpoint,
} from '@/hooks/use-responsive';
import { cn } from '@/lib/utils';

// 响应式显示/隐藏组件
interface ShowHideProps {
  children: ReactNode;
  above?: Breakpoint;
  below?: Breakpoint;
  only?: Breakpoint | Breakpoint[];
  className?: string;
}

export function Show({
  children,
  above,
  below,
  only,
  className,
}: ShowHideProps) {
  const { isBreakpointUp, isBreakpointDown, isBreakpoint } = useResponsive();

  let shouldShow = true;

  if (above && !isBreakpointUp(above)) {
    shouldShow = false;
  }

  if (below && !isBreakpointDown(below)) {
    shouldShow = false;
  }

  if (only) {
    const breakpoints = Array.isArray(only) ? only : [only];
    shouldShow = breakpoints.some(bp => isBreakpoint(bp));
  }

  if (!shouldShow) return null;

  return <div className={className}>{children}</div>;
}

export function Hide({
  children,
  above,
  below,
  only,
  className,
}: ShowHideProps) {
  const { isBreakpointUp, isBreakpointDown, isBreakpoint } = useResponsive();

  let shouldHide = false;

  if (above && isBreakpointUp(above)) {
    shouldHide = true;
  }

  if (below && isBreakpointDown(below)) {
    shouldHide = true;
  }

  if (only) {
    const breakpoints = Array.isArray(only) ? only : [only];
    shouldHide = breakpoints.some(bp => isBreakpoint(bp));
  }

  if (shouldHide) return null;

  return <div className={className}>{children}</div>;
}

// 响应式容器组件
interface ResponsiveContainerProps {
  children: ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: boolean;
  className?: string;
}

export function ResponsiveContainer({
  children,
  maxWidth = 'lg',
  padding = true,
  className,
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  return (
    <div
      className={cn(
        'mx-auto',
        maxWidthClasses[maxWidth],
        padding && 'px-4 sm:px-6 lg:px-8',
        className,
      )}
    >
      {children}
    </div>
  );
}

// 响应式网格组件
interface ResponsiveGridProps {
  children: ReactNode;
  cols?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: number | string;
  className?: string;
}

export function ResponsiveGrid({
  children,
  cols = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = 4,
  className,
}: ResponsiveGridProps) {
  const gridClasses = [];

  // 添加列数类
  if (cols.xs) gridClasses.push(`grid-cols-${cols.xs}`);
  if (cols.sm) gridClasses.push(`sm:grid-cols-${cols.sm}`);
  if (cols.md) gridClasses.push(`md:grid-cols-${cols.md}`);
  if (cols.lg) gridClasses.push(`lg:grid-cols-${cols.lg}`);
  if (cols.xl) gridClasses.push(`xl:grid-cols-${cols.xl}`);
  if (cols['2xl']) gridClasses.push(`2xl:grid-cols-${cols['2xl']}`);

  // 添加间距类
  const gapClass = typeof gap === 'number' ? `gap-${gap}` : gap;

  return (
    <div className={cn('grid', gapClass, ...gridClasses, className)}>
      {children}
    </div>
  );
}

// 响应式文本组件
interface ResponsiveTextProps {
  children: ReactNode;
  size?: {
    xs?:
      | 'xs'
      | 'sm'
      | 'base'
      | 'lg'
      | 'xl'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl';
    sm?:
      | 'xs'
      | 'sm'
      | 'base'
      | 'lg'
      | 'xl'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl';
    md?:
      | 'xs'
      | 'sm'
      | 'base'
      | 'lg'
      | 'xl'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl';
    lg?:
      | 'xs'
      | 'sm'
      | 'base'
      | 'lg'
      | 'xl'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl';
    xl?:
      | 'xs'
      | 'sm'
      | 'base'
      | 'lg'
      | 'xl'
      | '2xl'
      | '3xl'
      | '4xl'
      | '5xl'
      | '6xl';
  };
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
  className?: string;
}

export function ResponsiveText({
  children,
  size = { xs: 'base', md: 'lg' },
  as: Component = 'div',
  className,
}: ResponsiveTextProps) {
  const sizeClasses = [];

  if (size.xs) sizeClasses.push(`text-${size.xs}`);
  if (size.sm) sizeClasses.push(`sm:text-${size.sm}`);
  if (size.md) sizeClasses.push(`md:text-${size.md}`);
  if (size.lg) sizeClasses.push(`lg:text-${size.lg}`);
  if (size.xl) sizeClasses.push(`xl:text-${size.xl}`);

  return (
    <Component className={cn(...sizeClasses, className)}>{children}</Component>
  );
}

// 响应式间距组件
interface ResponsiveSpacingProps {
  children: ReactNode;
  p?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  m?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  className?: string;
}

export function ResponsiveSpacing({
  children,
  p,
  m,
  className,
}: ResponsiveSpacingProps) {
  const spacingClasses = [];

  // 内边距
  if (p?.xs) spacingClasses.push(`p-${p.xs}`);
  if (p?.sm) spacingClasses.push(`sm:p-${p.sm}`);
  if (p?.md) spacingClasses.push(`md:p-${p.md}`);
  if (p?.lg) spacingClasses.push(`lg:p-${p.lg}`);
  if (p?.xl) spacingClasses.push(`xl:p-${p.xl}`);

  // 外边距
  if (m?.xs) spacingClasses.push(`m-${m.xs}`);
  if (m?.sm) spacingClasses.push(`sm:m-${m.sm}`);
  if (m?.md) spacingClasses.push(`md:m-${m.md}`);
  if (m?.lg) spacingClasses.push(`lg:m-${m.lg}`);
  if (m?.xl) spacingClasses.push(`xl:m-${m.xl}`);

  return <div className={cn(...spacingClasses, className)}>{children}</div>;
}

// 移动端优化组件
interface MobileOptimizedProps {
  children: ReactNode;
  touchOptimized?: boolean;
  swipeEnabled?: boolean;
  className?: string;
}

export function MobileOptimized({
  children,
  touchOptimized = true,
  swipeEnabled = false,
  className,
}: MobileOptimizedProps) {
  const { isMobile, isTouch } = useResponsive();

  const mobileClasses = [];

  if (isMobile || isTouch) {
    if (touchOptimized) {
      mobileClasses.push('touch-manipulation', 'select-none');
    }
    if (swipeEnabled) {
      mobileClasses.push('overflow-x-auto', 'scrollbar-hide');
    }
  }

  return <div className={cn(...mobileClasses, className)}>{children}</div>;
}

// 响应式图片组件
interface ResponsiveImageProps {
  src: string;
  alt: string;
  sizes?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  };
  className?: string;
  priority?: boolean;
}

export function ResponsiveImage({
  src,
  alt,
  sizes,
  className,
  priority = false,
}: ResponsiveImageProps) {
  const { breakpoint } = useResponsive();

  // 根据当前断点选择合适的尺寸
  const currentSize = sizes?.[breakpoint] || sizes?.md || '100vw';

  return (
    <Image
      src={src}
      alt={alt}
      width={800}
      height={600}
      sizes={currentSize}
      className={cn('w-full h-auto', className)}
      priority={priority}
    />
  );
}

// 响应式导航组件
interface ResponsiveNavProps {
  children: ReactNode;
  mobileBreakpoint?: Breakpoint;
  mobileContent?: ReactNode;
  className?: string;
}

export function ResponsiveNav({
  children,
  mobileBreakpoint = 'md',
  mobileContent,
  className,
}: ResponsiveNavProps) {
  const { isBreakpointDown } = useResponsive();
  const isMobile = isBreakpointDown(mobileBreakpoint);

  if (isMobile && mobileContent) {
    return <div className={className}>{mobileContent}</div>;
  }

  return <div className={className}>{children}</div>;
}

// 响应式侧边栏组件
interface ResponsiveSidebarProps {
  children: ReactNode;
  isOpen: boolean;
  onClose: () => void;
  side?: 'left' | 'right';
  overlay?: boolean;
  className?: string;
}

export function ResponsiveSidebar({
  children,
  isOpen,
  onClose,
  side = 'left',
  overlay = true,
  className,
}: ResponsiveSidebarProps) {
  const { isMobile } = useResponsive();

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <>
      {/* 遮罩层 */}
      {overlay && isOpen && (
        <div className='fixed inset-0 bg-black/50 z-40' onClick={onClose} />
      )}

      {/* 侧边栏 */}
      <div
        className={cn(
          'fixed top-0 h-full bg-background border-r z-50 transform transition-transform duration-300',
          side === 'left' ? 'left-0' : 'right-0',
          isOpen
            ? 'translate-x-0'
            : side === 'left'
              ? '-translate-x-full'
              : 'translate-x-full',
          className,
        )}
      >
        {children}
      </div>
    </>
  );
}

// 响应式模态框组件
interface ResponsiveModalProps {
  children: ReactNode;
  isOpen: boolean;
  onClose: () => void;
  fullScreenOnMobile?: boolean;
  className?: string;
}

export function ResponsiveModal({
  children,
  isOpen,
  onClose,
  fullScreenOnMobile = true,
  className,
}: ResponsiveModalProps) {
  const { isMobile } = useResponsive();

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center'>
      {/* 遮罩层 */}
      <div className='absolute inset-0 bg-black/50' onClick={onClose} />

      {/* 模态框内容 */}
      <div
        className={cn(
          'relative bg-background rounded-lg shadow-lg',
          isMobile && fullScreenOnMobile
            ? 'w-full h-full rounded-none'
            : 'max-w-md mx-4',
          className,
        )}
      >
        {children}
      </div>
    </div>
  );
}

// 设备信息显示组件 (开发用)
export function DeviceInfo() {
  const responsive = useResponsive();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className='fixed bottom-4 left-4 p-2 bg-background border rounded text-xs space-y-1 z-50'>
      <div>
        <strong>断点:</strong> {responsive.breakpoint}
      </div>
      <div>
        <strong>设备:</strong> {responsive.deviceType}
      </div>
      <div>
        <strong>尺寸:</strong> {responsive.width}x{responsive.height}
      </div>
      <div>
        <strong>方向:</strong> {responsive.orientation}
      </div>
      <div>
        <strong>触摸:</strong> {responsive.isTouch ? '是' : '否'}
      </div>
    </div>
  );
}

export default ResponsiveContainer;
