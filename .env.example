# to generate a next-auth secret run the following command in your terminal, type below in your terminal
# node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=

# go to google cloud console and create a new project
# do a quick search and you will figure out how to get the credentials below
# for callback url, use http://localhost:3000/api/auth/callback/google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# go to supabase or whichever database of your choice and create a new project and follow their instructions to get the credentials below
DATABASE_URL=
DIRECT_URL=

# for sending transactional emails, you can use either sendgrid or resend
RESEND_API_KEY=
SENDGRID_APIKEY=

# to get local stripe webhook secret run the following command in your terminal
# stripe login
# stripe listen --forward-to localhost:3000/api/webhooks
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# change this to hosted url on vercel settings/env variables
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# for cron job authentication (generate a random secret)
CRON_SECRET=63w7FrM3McjkHHnzbX+GPvAOrYLA1l9yEiFekC9QNyU=