import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { updatePlatformSchema } from '@/types/rates';

// GET /api/admin/platforms/[id] - 获取单个平台
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const platform = await prisma.platform.findUnique({
      where: { id },
    });

    if (!platform) {
      return NextResponse.json(
        { success: false, message: '平台不存在' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: platform,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// PUT /api/admin/platforms/[id] - 更新平台
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const body = await request.json();

    // 验证请求数据
    const validatedData = updatePlatformSchema.parse(body);

    // 检查平台是否存在
    const existingPlatform = await prisma.platform.findUnique({
      where: { id },
    });

    if (!existingPlatform) {
      return NextResponse.json(
        { success: false, message: '平台不存在' },
        { status: 404 },
      );
    }

    // 检查名称是否已被其他平台使用
    if (validatedData.name !== existingPlatform.name) {
      const nameExists = await prisma.platform.findFirst({
        where: {
          name: validatedData.name,
          id: { not: id },
        },
      });

      if (nameExists) {
        return NextResponse.json(
          { success: false, message: '平台名称已存在' },
          { status: 400 },
        );
      }
    }

    // 更新平台
    const updatedPlatform = await prisma.platform.update({
      where: { id },
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: updatedPlatform,
      message: '平台更新成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('更新平台失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}

// DELETE /api/admin/platforms/[id] - 删除平台
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;

    // 检查平台是否存在
    const existingPlatform = await prisma.platform.findUnique({
      where: { id },
    });

    if (!existingPlatform) {
      return NextResponse.json(
        { success: false, message: '平台不存在' },
        { status: 404 },
      );
    }

    // 检查是否有委托关联到此平台
    const taskCount = await prisma.task.count({
      where: { platformId: id },
    });

    if (taskCount > 0) {
      return NextResponse.json(
        {
          success: false,
          message: `无法删除平台，还有 ${taskCount} 个委托与此平台关联`,
        },
        { status: 400 },
      );
    }

    // 删除平台
    await prisma.platform.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: '平台删除成功',
    });
  } catch (error) {
    console.error('删除平台失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}
