'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Truck, CheckCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner'; // cspell:disable-line
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { FileUpload } from '@/components/ui/file-upload';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UploadedFile } from '@/hooks/use-file-upload';

const submitLogisticsSchema = z.object({
  trackingNumber: z
    .string()
    .min(1, 'validation.trackingNumberRequired')
    .min(6, 'validation.trackingNumberMinLength'),
  logisticsScreenshots: z
    .array(z.any())
    .min(1, 'validation.logisticsScreenshotsRequired')
    .max(5, 'validation.logisticsScreenshotsMaxCount'),
});

type SubmitLogisticsForm = z.infer<typeof submitLogisticsSchema>;

interface SubmitLogisticsDialogProps {
  children?: React.ReactNode;
  taskId: string;
  onSubmit?: (data: SubmitLogisticsForm) => void;
}

export function SubmitLogisticsDialog({
  children,
  taskId,
  onSubmit,
}: SubmitLogisticsDialogProps) {
  const t = useTranslations('MyAcceptedTasks');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    clearErrors,
  } = useForm<SubmitLogisticsForm>({
    resolver: zodResolver(submitLogisticsSchema),
    defaultValues: {
      logisticsScreenshots: [],
    },
  });

  const handleFileChange = (files: UploadedFile[]) => {
    setUploadedFiles(files);
    setValue(
      'logisticsScreenshots',
      files.map(file => file.fileUrl),
    );
    if (files.length > 0) {
      clearErrors('logisticsScreenshots');
    }
  };

  const onFormSubmit = async (data: SubmitLogisticsForm) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/user/accepted-tasks/${taskId}/submit-logistics`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            trackingNumber: data.trackingNumber,
            logisticsScreenshots: uploadedFiles.map(file => file.fileUrl),
          }),
        },
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || t('messages.submitLogisticsError'));
      }

      toast.success(t('messages.submitLogisticsSuccess'), {
        description: result.message,
        duration: 6000,
      });

      // 调用父组件的提交处理函数
      onSubmit?.(data);

      // 重置表单和状态
      reset();
      setUploadedFiles([]);
      setIsOpen(false);

      // 刷新页面或重新获取数据
      window.location.reload();
    } catch (error) {
      toast.error(t('messages.submitLogisticsError'), {
        description:
          error instanceof Error ? error.message : t('messages.networkError'),
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialogClose = (open: boolean) => {
    setIsOpen(open);
    if (!open && !isLoading) {
      reset();
      setUploadedFiles([]);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Truck className='h-5 w-5' />
            {t('submitLogistics.title')}
          </DialogTitle>
          <DialogDescription>
            {t('submitLogistics.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-4'>
          {/* 物流单号输入 */}
          <div className='space-y-2'>
            <Label htmlFor='trackingNumber'>
              {t('submitLogistics.trackingNumber')} *
            </Label>
            <Input
              id='trackingNumber'
              placeholder={t('submitLogistics.trackingNumberPlaceholder')}
              {...register('trackingNumber')}
              disabled={isLoading}
            />
            {errors.trackingNumber && (
              <p className='text-sm text-red-600'>
                {errors.trackingNumber.message}
              </p>
            )}
          </div>

          {/* 物流截图上传 */}
          <div className='space-y-2'>
            <Label>
              {t('submitLogistics.logisticsScreenshots')} * (
              {t('submitLogistics.logisticsScreenshotsNote')})
            </Label>
            <FileUpload
              value={uploadedFiles}
              onChange={handleFileChange}
              accept='image/*'
              multiple={true}
              maxFiles={5}
              maxSize={5}
              placeholder={t('upload.dragAndDrop')}
              description={t('upload.formats.image')}
              disabled={isLoading}
            />
            {errors.logisticsScreenshots && (
              <p className='text-sm text-red-600'>
                {errors.logisticsScreenshots.message}
              </p>
            )}
          </div>

          {/* 提交按钮 */}
          <div className='flex justify-end gap-3 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              {t('actions.cancel')}
            </Button>
            <Button
              type='submit'
              disabled={isLoading || uploadedFiles.length === 0}
              className='min-w-[100px]'
            >
              {isLoading ? (
                <div className='flex items-center gap-2'>
                  <div className='h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                  {t('actions.submitting')}
                </div>
              ) : (
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4' />
                  {t('submitLogistics.submitButton')}
                </div>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
