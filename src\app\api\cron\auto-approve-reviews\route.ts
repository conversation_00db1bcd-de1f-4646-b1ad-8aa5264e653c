import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';
import { sendTaskReviewApprovedAccepterEmail } from '@/lib/email';
import { logisticsService } from '@/lib/logistics-service';
import { getTaskCommission } from '@/lib/utils/commission';

// 自动审核逻辑
async function processAutoApprove() {
  const now = new Date();
  let autoApprovedCount = 0;

  // 查找超过24小时未审核的委托（使用原生SQL避免类型问题）
  const pendingReviewTasks = await prisma.$queryRaw<
    Array<{
      id: string;
      publisherId: string;
      accepterId: string;
      trackingNumber: string | null;
      orderNumber: string | null;
      createdAt: Date;
    }>
  >`
    SELECT id, "publisherId", "accepterId", "trackingNumber", "orderNumber", "createdAt"
    FROM tasks 
    WHERE status = 'PENDING_REVIEW' 
      AND "logisticsReviewDeadline" < ${now}
  `;

  if (pendingReviewTasks.length > 0) {
    await prisma.$transaction(async tx => {
      for (const task of pendingReviewTasks) {
        // 自动通过审核，设置30天确认收货期限
        const deliveryDeadline = new Date(
          now.getTime() + 30 * 24 * 60 * 60 * 1000,
        ); // 30天后

        await tx.$executeRaw`
          UPDATE tasks 
          SET status = 'PENDING_DELIVERY',
              "reviewedAt" = ${now},
              "reviewRejectReason" = NULL,
              "deliveryDeadline" = ${deliveryDeadline}
          WHERE id = ${task.id}
        `;

        autoApprovedCount++;

        // 如果有物流单号，自动注册到17TRACK
        if (task.trackingNumber) {
          try {
            console.log(
              '自动审核通过，开始注册物流单号到17TRACK:',
              task.trackingNumber,
            );

            const logisticsData = {
              taskId: task.id,
              trackingNumber: task.trackingNumber,
              carrierCode: undefined, // 让17TRACK自动检测
              orderNumber: task.orderNumber || undefined,
              orderTime: task.createdAt.toISOString().split('T')[0], // YYYY-MM-DD 格式
            };

            const registerResult =
              await logisticsService.registerTracking(logisticsData);

            if (registerResult.success) {
              console.log('物流单号自动注册成功:', task.trackingNumber);
            } else {
              console.error('物流单号自动注册失败:', registerResult.error);
              // 注册失败不影响自动审核流程，只记录错误
            }
          } catch (error) {
            console.error('自动注册物流单号时发生错误:', error);
            // 注册失败不影响自动审核流程，只记录错误
          }
        }
      }
    });
  }

  // 发送邮件通知给自动审核通过的接单者
  if (pendingReviewTasks.length > 0) {
    const [systemRate, chargebackTypes, paymentMethods] = await Promise.all([
      prisma.systemRate.findFirst(),
      prisma.chargebackType.findMany(),
      prisma.paymentMethod.findMany(),
    ]);

    for (const task of pendingReviewTasks) {
      try {
        // 获取完整的委托信息包括关联数据
        const fullTask = await prisma.task.findUnique({
          where: { id: task.id },
          include: {
            accepter: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            platform: {
              select: {
                name: true,
              },
            },
            category: {
              select: {
                name: true,
              },
            },
          },
        });

        if (fullTask?.accepter?.email) {
          const taskWithEvidenceStatus = {
            ...fullTask,
            evidenceStatus: fullTask.evidenceStatus?.toString(),
          };

          const estimatedCommission = systemRate
            ? getTaskCommission(
                taskWithEvidenceStatus,
                chargebackTypes,
                paymentMethods,
                systemRate,
              )
            : fullTask.unitPrice * fullTask.quantity * 0.1; // 默认10%酬金

          const deliveryDeadline = new Date(
            now.getTime() + 30 * 24 * 60 * 60 * 1000,
          );

          await sendTaskReviewApprovedAccepterEmail(fullTask.accepter.email, {
            accepterName: fullTask.accepter.name || '用户',
            accepterEmail: fullTask.accepter.email,
            taskId: fullTask.id,
            platform: fullTask.platform?.name || '未知平台',
            category: fullTask.category?.name || '未知分类',
            quantity: fullTask.quantity,
            unitPrice: fullTask.unitPrice,
            totalAmount: fullTask.unitPrice * fullTask.quantity,
            reviewedAt: now.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false,
            }),
            orderNumber: fullTask.orderNumber || undefined,
            trackingNumber: fullTask.trackingNumber || undefined,
            deliveryDeadline: deliveryDeadline.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            }),
            estimatedCommission,
          });
        }
      } catch (emailError) {
        console.error(
          `发送自动审核通过邮件失败 (委托ID: ${task.id}):`,
          emailError,
        );
        // 邮件发送失败不影响主流程
      }
    }
  }

  console.log(`自动审核委托完成：${autoApprovedCount} 个委托被自动通过审核`);

  return {
    success: true,
    message: '自动审核委托完成',
    data: {
      autoApprovedCount,
    },
  };
}

// 开发环境手动触发
export async function GET(request: NextRequest) {
  try {
    // 只在开发环境允许GET请求
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: '生产环境不支持GET请求' },
        { status: 405 },
      );
    }

    const result = await processAutoApprove();
    return NextResponse.json(result);
  } catch (error) {
    console.error('自动审核委托失败:', error);
    return NextResponse.json({ error: '自动审核委托失败' }, { status: 500 });
  }
}

// 定时委托自动审核
export async function POST(request: NextRequest) {
  try {
    // 验证请求来源
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const result = await processAutoApprove();
    return NextResponse.json(result);
  } catch (error) {
    console.error('自动审核委托失败:', error);
    return NextResponse.json({ error: '自动审核委托失败' }, { status: 500 });
  }
}
