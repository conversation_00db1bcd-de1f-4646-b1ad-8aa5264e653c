/**
 * Email Logging System
 * Provides functionality for tracking email delivery status and debugging email issues
 */

import prisma from '@/lib/db';

export interface EmailLogData {
  orderNo?: string;
  recipientEmail: string;
  emailType: string;
  status: 'SUCCESS' | 'FAILED' | 'RETRY';
  error?: string;
  retryCount?: number;
}

export interface EmailDeliveryResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Log email delivery attempt to database
 */
export async function logEmailDelivery(
  logData: EmailLogData,
  deliveryResult: EmailDeliveryResult,
): Promise<void> {
  try {
    const status = deliveryResult.success ? 'SUCCESS' : 'FAILED';

    await prisma.emailLog.create({
      data: {
        orderNo: logData.orderNo,
        recipientEmail: logData.recipientEmail,
        emailType: logData.emailType,
        status,
        error: deliveryResult.error,
        retryCount: logData.retryCount || 0,
        sentAt: new Date(),
      },
    });

    // Log to console for immediate debugging
    if (deliveryResult.success) {
      console.log(`✅ Email sent successfully: ${logData.emailType} to ${logData.recipientEmail}${logData.orderNo ? ` (Order: ${logData.orderNo})` : ''}`);
    } else {
      console.error(`❌ Email failed: ${logData.emailType} to ${logData.recipientEmail}${logData.orderNo ? ` (Order: ${logData.orderNo})` : ''} - ${deliveryResult.error}`);
    }
  } catch (error) {
    // Don't let logging failures break the main flow
    console.error('Failed to log email delivery:', error);
  }
}

/**
 * Log email delivery for deposit transactions
 */
export async function logDepositEmailDelivery(
  orderNo: string,
  recipientEmail: string,
  emailType: 'deposit-success' | 'deposit-failed',
  deliveryResult: EmailDeliveryResult,
): Promise<void> {
  await logEmailDelivery(
    {
      orderNo,
      recipientEmail,
      emailType,
      status: deliveryResult.success ? 'SUCCESS' : 'FAILED',
    },
    deliveryResult,
  );
}

/**
 * Get email delivery statistics for monitoring
 */
export async function getEmailDeliveryStats(
  timeRange: 'hour' | 'day' | 'week' = 'day',
): Promise<{
  total: number;
  successful: number;
  failed: number;
  successRate: number;
  byType: Record<string, { total: number; successful: number; failed: number }>;
}> {
  const now = new Date();
  const startTime = new Date();

  switch (timeRange) {
    case 'hour':
      startTime.setHours(now.getHours() - 1);
      break;
    case 'day':
      startTime.setDate(now.getDate() - 1);
      break;
    case 'week':
      startTime.setDate(now.getDate() - 7);
      break;
  }

  const logs = await prisma.emailLog.findMany({
    where: {
      sentAt: {
        gte: startTime,
      },
    },
    select: {
      emailType: true,
      status: true,
    },
  });

  const total = logs.length;
  const successful = logs.filter(log => log.status === 'SUCCESS').length;
  const failed = total - successful;
  const successRate = total > 0 ? (successful / total) * 100 : 0;

  // Group by email type
  const byType: Record<string, { total: number; successful: number; failed: number }> = {};

  logs.forEach(log => {
    if (!byType[log.emailType]) {
      byType[log.emailType] = { total: 0, successful: 0, failed: 0 };
    }

    byType[log.emailType].total++;
    if (log.status === 'SUCCESS') {
      byType[log.emailType].successful++;
    } else {
      byType[log.emailType].failed++;
    }
  });

  return {
    total,
    successful,
    failed,
    successRate: Math.round(successRate * 100) / 100,
    byType,
  };
}

/**
 * Get recent failed emails for debugging
 */
export async function getRecentFailedEmails(limit: number = 10): Promise<Array<{
  id: string;
  orderNo: string | null;
  recipientEmail: string;
  emailType: string;
  error: string | null;
  sentAt: Date;
  retryCount: number;
}>> {
  return await prisma.emailLog.findMany({
    where: {
      status: 'FAILED',
    },
    orderBy: {
      sentAt: 'desc',
    },
    take: limit,
    select: {
      id: true,
      orderNo: true,
      recipientEmail: true,
      emailType: true,
      error: true,
      sentAt: true,
      retryCount: true,
    },
  });
}

/**
 * Check if email delivery success rate is below threshold
 */
export async function checkEmailHealthAlert(
  threshold: number = 90,
  timeRange: 'hour' | 'day' = 'hour',
): Promise<{
  isHealthy: boolean;
  currentSuccessRate: number;
  threshold: number;
  recommendation?: string;
}> {
  const stats = await getEmailDeliveryStats(timeRange);
  const isHealthy = stats.successRate >= threshold;

  let recommendation: string | undefined;
  if (!isHealthy) {
    if (stats.successRate < 50) {
      recommendation = 'Critical: Email service may be down. Check Resend API status and configuration.';
    } else if (stats.successRate < 80) {
      recommendation = 'Warning: High email failure rate. Check recent error logs and email templates.';
    } else {
      recommendation = 'Monitor: Email success rate below threshold. Review failed email logs.';
    }
  }

  return {
    isHealthy,
    currentSuccessRate: stats.successRate,
    threshold,
    recommendation,
  };
}

/**
 * Utility function to create email log data for different email types
 */
export function createEmailLogData(
  emailType: string,
  recipientEmail: string,
  orderNo?: string,
): EmailLogData {
  return {
    orderNo,
    recipientEmail,
    emailType,
    status: 'SUCCESS', // Will be updated based on actual result
  };
}
