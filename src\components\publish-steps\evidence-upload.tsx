'use client';

import { useTranslations } from 'next-intl';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUpload } from '@/components/ui/file-upload';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useUserSystemRate } from '@/hooks/use-rates-api';
import { PublishTaskForm, EvidenceUploadType } from '@/lib/types/publish';

interface EvidenceUploadStepProps {
  formData: PublishTaskForm;
  updateFormData: (updates: Partial<PublishTaskForm>) => void;
}

export function EvidenceUploadStep({
  formData,
  updateFormData,
}: EvidenceUploadStepProps) {
  const t = useTranslations('Publish');
  const { data: systemRate } = useUserSystemRate();

  // 动态获取证据费率（所有类型都收取证据费用，审核通过后退还）
  const getEvidenceRate = (type: EvidenceUploadType) => {
    // 所有证据上传类型都收取证据费用
    return systemRate?.noEvidenceExtraRate || 0;
  };

  // 动态获取证据标签
  const getEvidenceLabel = (type: EvidenceUploadType) => {
    switch (type) {
      case EvidenceUploadType.IMMEDIATE:
        return t('evidenceUpload.types.immediate.label');
      case EvidenceUploadType.LATER:
        return t('evidenceUpload.types.later.label');
      case EvidenceUploadType.NONE:
        return t('evidenceUpload.types.none.label');
      default:
        return '';
    }
  };

  return (
    <div className='space-y-6'>
      {/* 证据上传方式选择 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('evidenceUpload.title')}</CardTitle>
          <p className='text-sm text-muted-foreground'>
            {t('evidenceUpload.description')}
          </p>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={formData.evidenceUploadType}
            onValueChange={value =>
              updateFormData({
                evidenceUploadType: value as EvidenceUploadType,
              })
            }
            className='space-y-3'
          >
            {Object.values(EvidenceUploadType).map(type => {
              const rate = getEvidenceRate(type);
              const label = getEvidenceLabel(type);

              return (
                <div
                  key={type}
                  className='flex items-center space-x-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors'
                >
                  <RadioGroupItem value={type} id={type} />
                  <div className='flex-1'>
                    <Label
                      htmlFor={type}
                      className='text-sm font-medium cursor-pointer'
                    >
                      {label}
                    </Label>
                    <p className='text-xs text-muted-foreground mt-1'>
                      {type === EvidenceUploadType.IMMEDIATE &&
                        t('evidenceUpload.types.immediate.description')}
                      {type === EvidenceUploadType.LATER &&
                        t('evidenceUpload.types.later.description')}
                      {type === EvidenceUploadType.NONE &&
                        t('evidenceUpload.types.none.description')}
                    </p>
                  </div>
                  <div className='px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'>
                    {type === EvidenceUploadType.IMMEDIATE &&
                      `${rate}% (${t('evidenceUpload.types.immediate.feeNote')})`}
                    {type === EvidenceUploadType.LATER &&
                      `${rate}% (${t('evidenceUpload.types.later.feeNote')})`}
                    {type === EvidenceUploadType.NONE && `${rate}%`}
                  </div>
                  {type === EvidenceUploadType.IMMEDIATE && (
                    <div className='bg-primary text-primary-foreground text-xs px-2 py-1 rounded'>
                      {t('common.recommended')}
                    </div>
                  )}
                </div>
              );
            })}
          </RadioGroup>
        </CardContent>
      </Card>

      {/* 文件上传区域 */}
      {formData.evidenceUploadType === EvidenceUploadType.IMMEDIATE && (
        <Card>
          <CardHeader>
            <CardTitle>{t('evidenceUpload.fileUpload.title')}</CardTitle>
            <p className='text-sm text-muted-foreground'>
              {t('evidenceUpload.fileUpload.description')}
            </p>
          </CardHeader>
          <CardContent>
            <FileUpload
              value={formData.evidenceFiles || []}
              onChange={files => updateFormData({ evidenceFiles: files })}
              accept='image/*,video/*'
              multiple={true}
              maxFiles={10}
              maxSize={50}
              placeholder={t('evidenceUpload.fileUpload.placeholder')}
              description={t('evidenceUpload.fileUpload.formats')}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
