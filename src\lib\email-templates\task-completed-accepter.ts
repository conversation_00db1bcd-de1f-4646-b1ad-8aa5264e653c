export interface TaskCompletedAccepterEmailData {
  accepterName: string;
  accepterEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  completedAt: string;
  commissionEarned: number;
  depositReleased: number;
  totalEarned: number;
}

export const taskCompletedAccepterTemplate = (
  data: TaskCompletedAccepterEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>委托完成通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #333; margin-bottom: 20px; text-align: center;">🎉 恭喜！委托已完成</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.accepterName}！您接受的委托已经完成，酬金已发放到您的账户。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">完成时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.completedAt}</span>
        </div>
      </div>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">酬金详情</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托酬金：</span>
          <span style="color: #28a745; font-weight: bold; font-size: 16px; margin-left: 10px;">
            +$${data.commissionEarned}
          </span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">押金释放：</span>
          <span style="color: #28a745; font-weight: bold; font-size: 16px; margin-left: 10px;">
            +$${data.depositReleased}
          </span>
        </div>
        
        <div style="border-top: 1px solid #eee; padding-top: 15px; margin-top: 15px;">
          <div style="margin-bottom: 15px;">
            <span style="color: #666; font-size: 14px;">总酬金：</span>
            <span style="color: #28a745; font-weight: bold; font-size: 18px; margin-left: 10px;">
              +$${data.totalEarned}
            </span>
          </div>
        </div>
      </div>
      

      
      <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #155724; margin: 0; font-size: 14px;">
          💡 <strong>温馨提示：</strong><br>
          • 酬金和押金已发放到您的账户余额<br>
          • 可以在钱包页面查看详细的酬金记录<br>
          • 感谢您的辛勤工作！
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
