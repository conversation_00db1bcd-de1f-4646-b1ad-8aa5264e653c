import { NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 获取系统费率配置
    const systemRate = await prisma.systemRate.findFirst({
      select: {
        id: true,
        noEvidenceExtraRate: true,
        depositRatio: true,
        bankWithdrawalRate: true,
        erc20WithdrawalRate: true,
        trc20WithdrawalRate: true,
        minimumWithdrawalAmount: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!systemRate) {
      return NextResponse.json(
        { success: false, message: '系统费率配置不存在' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: systemRate,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
