import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

// 白名单审核状态类型
export type WhitelistStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

// 白名单条目类型定义
export interface WhitelistItem {
  id: string;
  userId: string;
  shopName: string;
  shopUrl: string;
  platform: string;
  status: WhitelistStatus;
  isActive: boolean;
  reviewedBy?: string | null;
  reviewedAt?: string | null;
  reviewNote?: string | null;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string | null;
    email: string | null;
  };
  reviewer?: {
    id: string;
    name: string | null;
  };
}

// 会员信息类型定义
export interface MembershipInfo {
  tier: string;
  whitelistSlots: number;
  whitelistSlotsUsed: number;
  availableSlots: number;
}

// 创建白名单输入类型
export interface CreateWhitelistInput {
  shopName: string;
  shopUrl: string;
  platform: string;
}

// 白名单列表响应类型
export interface WhitelistResponse {
  whitelistItems: WhitelistItem[];
  membership: MembershipInfo;
}

// 管理端白名单列表响应类型
export interface AdminWhitelistResponse {
  whitelistItems: WhitelistItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  stats: {
    total: number;
    active: number;
    inactive: number;
    todayAdded: number;
  };
}

// 查询参数类型
export interface WhitelistQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  platform?: string;
  status?: string;
}

// ===== 用户端 Hooks =====

// 获取用户白名单列表
export function useWhitelist() {
  return useQuery<WhitelistResponse>({
    queryKey: ['whitelist'],
    queryFn: async () => {
      const response = await fetch('/api/whitelist');
      if (!response.ok) {
        throw new Error('获取白名单失败');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5分钟
  });
}

// 创建白名单条目
export function useCreateWhitelist() {
  const queryClient = useQueryClient();
  const t = useTranslations('shop-whitelist');

  return useMutation({
    mutationFn: async (data: CreateWhitelistInput) => {
      const response = await fetch('/api/whitelist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || t('hooks.create.error'));
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新白名单列表
      queryClient.invalidateQueries({ queryKey: ['whitelist'] });
      toast.success(t('hooks.create.success'), {
        description: t('hooks.create.successDesc'),
      });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 删除白名单条目
export function useDeleteWhitelist() {
  const queryClient = useQueryClient();
  const t = useTranslations('shop-whitelist');

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/whitelist/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || t('hooks.delete.error'));
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新白名单列表
      queryClient.invalidateQueries({ queryKey: ['whitelist'] });
      toast.success(t('hooks.delete.success'), {
        description: t('hooks.delete.successDesc'),
      });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// ===== 管理端 Hooks =====

// 获取管理端白名单列表
export function useAdminWhitelist(params: WhitelistQueryParams = {}) {
  return useQuery<AdminWhitelistResponse>({
    queryKey: ['admin-whitelist', params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(
        `/api/admin/whitelist?${searchParams.toString()}`,
      );
      if (!response.ok) {
        throw new Error('获取白名单列表失败');
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 2, // 2分钟
  });
}

// 获取管理端白名单详情
export function useAdminWhitelistItem(id: string) {
  return useQuery<WhitelistItem>({
    queryKey: ['admin-whitelist-item', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/whitelist/${id}`);
      if (!response.ok) {
        throw new Error('获取白名单详情失败');
      }
      return response.json();
    },
    enabled: !!id,
  });
}

// 管理端更新白名单状态
export function useAdminUpdateWhitelistStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }) => {
      const response = await fetch(`/api/admin/whitelist/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '更新白名单状态失败');
      }

      return response.json();
    },
    onSuccess: data => {
      // 刷新相关数据
      queryClient.invalidateQueries({ queryKey: ['admin-whitelist'] });
      toast.success(data.message);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 管理端删除白名单条目
export function useAdminDeleteWhitelist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/admin/whitelist/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '删除白名单失败');
      }

      return response.json();
    },
    onSuccess: () => {
      // 刷新白名单列表
      queryClient.invalidateQueries({ queryKey: ['admin-whitelist'] });
      toast.success('删除成功', {
        description: '白名单条目已删除',
      });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 管理端审核白名单（通过/拒绝）
export function useAdminReviewWhitelist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      action,
      reviewNote,
    }: {
      id: string;
      action: 'approve' | 'reject';
      reviewNote?: string;
    }) => {
      const response = await fetch(`/api/admin/whitelist/${id}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, reviewNote }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '审核失败');
      }

      return response.json();
    },
    onSuccess: data => {
      // 刷新相关数据
      queryClient.invalidateQueries({ queryKey: ['admin-whitelist'] });
      toast.success(data.message);
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
