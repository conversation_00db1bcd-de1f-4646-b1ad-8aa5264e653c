export interface TaskCancelledAccepterEmailData {
  accepterName: string;
  accepterEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  cancelledAt: string;
  cancelReason: string;
  depositPenalty: number;
}

export const taskCancelledAccepterTemplate = (
  data: TaskCancelledAccepterEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>委托取消通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #dc3545; margin-bottom: 20px; text-align: center;">⚠️ 委托已取消</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.accepterName}！您接受的委托已被取消。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">取消时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.cancelledAt}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">取消原因：</span>
          <span style="color: #333; margin-left: 10px;">${data.cancelReason}</span>
        </div>
      </div>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">违约处理</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">违约金扣除：</span>
          <span style="color: #dc3545; font-weight: bold; font-size: 16px; margin-left: 10px;">
            -$${data.depositPenalty}
          </span>
        </div>
      </div>
      
      <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
          ⚠️ <strong>重要提醒：</strong><br>
          • 取消委托将产生违约金，已从您的押金中扣除<br>
          • 违约金将作为补偿转给委托发布者<br>
          • 委托已重新回到委托大厅，其他用户可以接单<br>
          • 请谨慎接单，避免不必要的损失
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
