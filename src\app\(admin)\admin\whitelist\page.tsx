import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { AdminWhitelistContent } from '@/components/admin/admin-whitelist-content';
import { constructMetadata } from '@/lib/utils';

export const metadata = constructMetadata({
  title: '白名单管理 - 管理后台',
  description: '管理平台商铺白名单，查看用户添加的白名单店铺',
});

export default function AdminWhitelistPage() {
  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='白名单管理' href='/admin'>
      <div className='space-y-6'>
        {/* 页面标题 */}
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
          <div className='space-y-1'>
            <h1 className='text-2xl font-bold tracking-tight'>白名单管理</h1>
            <p className='text-sm text-muted-foreground'>
              管理平台商铺白名单，查看和控制用户添加的白名单店铺
            </p>
          </div>
        </div>

        {/* 白名单内容 */}
        <AdminWhitelistContent />
      </div>
    </AdminPageLayout>
  );
}
