#!/usr/bin/env node

/**
 * Validation Script Runner
 *
 * This script runs all validation checks for the RefundGo system.
 * These are not Jest tests but system integrity and validation scripts.
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('=== RefundGo System Validation ===\n');

// Define validation scripts to run
const validationScripts = [
  {
    name: 'Financial Email System Check',
    script: 'system-checks/financial-email-system-check.js',
    description: 'Quick check of financial email system files',
  },
  {
    name: 'Financial Email System Integrity',
    script: 'system-checks/financial-email-system-integrity.js',
    description: 'Comprehensive financial email system validation',
  },
  {
    name: 'Basic I18n Check',
    script: 'system-checks/basic-i18n-check.js',
    description: 'Basic internationalization file validation',
  },
  {
    name: 'Email Internationalization Check',
    script: 'system-checks/email-internationalization-check.js',
    description: 'Email internationalization system validation',
  },
  {
    name: 'Email Style Unification Check',
    script: 'system-checks/email-style-unification-check.js',
    description: 'Email styling consistency validation',
  },
  {
    name: 'Payment Methods Check',
    script: 'system-checks/payment-methods-check.js',
    description: 'Payment methods system validation',
  },
  {
    name: 'Ticket System Check',
    script: 'system-checks/ticket-system-check.js',
    description: 'Ticket system functionality validation',
  },
  {
    name: 'UI Consistency Check',
    script: 'system-checks/ui-consistency-check.js',
    description: 'UI consistency and styling validation',
  },
  {
    name: 'State Transitions Check',
    script: 'system-checks/state-transitions-check.js',
    description: 'State transition logic validation',
  },
  {
    name: 'Security Translations Check',
    script: 'translation-checks/security-translations.js',
    description: 'Security page translation validation',
  },
  {
    name: 'Ticket Translations Check',
    script: 'translation-checks/ticket-translations-check.js',
    description: 'Ticket system translation validation',
  },
];

async function runValidationScript(scriptInfo) {
  return new Promise(resolve => {
    console.log(`\n🔍 Running: ${scriptInfo.name}`);
    console.log(`   ${scriptInfo.description}`);
    console.log('   ' + '─'.repeat(50));

    const scriptPath = path.join(__dirname, scriptInfo.script);

    if (!fs.existsSync(scriptPath)) {
      console.log(`   ❌ Script not found: ${scriptPath}`);
      resolve({ success: false, error: 'Script not found' });
      return;
    }

    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: path.join(__dirname, '../../..'),
    });

    child.on('close', code => {
      if (code === 0) {
        console.log(`   ✅ ${scriptInfo.name} completed successfully`);
        resolve({ success: true });
      } else {
        console.log(`   ❌ ${scriptInfo.name} failed with code ${code}`);
        resolve({ success: false, error: `Exit code ${code}` });
      }
    });

    child.on('error', error => {
      console.log(`   ❌ ${scriptInfo.name} error: ${error.message}`);
      resolve({ success: false, error: error.message });
    });
  });
}

async function runAllValidations() {
  console.log(`Found ${validationScripts.length} validation scripts to run\n`);

  const results = [];

  for (const script of validationScripts) {
    const result = await runValidationScript(script);
    results.push({ script: script.name, ...result });
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('VALIDATION SUMMARY');
  console.log('='.repeat(60));

  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${results.length}`);

  if (failed > 0) {
    console.log('\n❌ Failed validations:');
    results
      .filter(r => !r.success)
      .forEach(r => {
        console.log(`   • ${r.script}: ${r.error}`);
      });
  }

  console.log('\n' + '='.repeat(60));

  if (failed === 0) {
    console.log('🎉 All validations passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some validations failed. Please review the output above.');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runAllValidations().catch(error => {
    console.error('Error running validations:', error);
    process.exit(1);
  });
}

module.exports = { runAllValidations, runValidationScript };
