'use client';

import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usePublishConfig } from '@/hooks/use-publish-config';
import {
  PublishTaskForm,
  ProductCategory,
  PRODUCT_CATEGORY_LABELS,
} from '@/lib/types/publish';
import { Platform, ChargebackType, PaymentMethod } from '@/lib/types/task';

interface PlatformSelectionStepProps {
  formData: PublishTaskForm;
  updateFormData: (updates: Partial<PublishTaskForm>) => void;
}

export function PlatformSelectionStep({
  formData,
  updateFormData,
}: PlatformSelectionStepProps) {
  const t = useTranslations('publish');
  const {
    platforms,
    categories,
    chargebackTypes,
    paymentMethods,
    isLoading,
    error,
  } = usePublishConfig();

  const handleChargebackTypeChange = (typeId: string, checked: boolean) => {
    const currentTypes = formData.chargebackTypes || [];
    const newTypes = checked
      ? [...currentTypes, typeId]
      : currentTypes.filter(t => t !== typeId);

    updateFormData({ chargebackTypes: newTypes });
  };

  const handlePaymentMethodChange = (methodId: string, checked: boolean) => {
    const currentMethods = formData.paymentMethods || [];
    const newMethods = checked
      ? [...currentMethods, methodId]
      : currentMethods.filter(m => m !== methodId);

    updateFormData({ paymentMethods: newMethods });
  };

  const handlePlatformChange = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    updateFormData({
      platform: platformId,
      platformName: platform?.name || '',
    });
  };

  const handleCategoryChange = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    updateFormData({
      category: categoryId,
      categoryName: category?.name || '',
    });
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-12'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2 text-sm text-muted-foreground'>
          {t('platformSelection.loading')}
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className='text-center py-12'>
        <p className='text-sm text-red-600'>{t('platformSelection.error')}</p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 平台选择和商品分类并排显示 */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {/* 平台选择 */}
        <Card>
          <CardHeader>
            <CardTitle>{t('platformSelection.selectPlatform')}</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Select
                value={formData.platform}
                onValueChange={handlePlatformChange}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t(
                      'platformSelection.selectPlatformPlaceholder',
                    )}
                  />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map(platform => (
                    <SelectItem key={platform.id} value={platform.id}>
                      {t(`platformSelection.platformLabels.${platform.name}`) ||
                        platform.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* 商品分类 */}
        <Card>
          <CardHeader>
            <CardTitle>{t('platformSelection.selectCategory')}</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Select
                value={formData.category}
                onValueChange={handleCategoryChange}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t(
                      'platformSelection.selectCategoryPlaceholder',
                    )}
                  />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {t(`platformSelection.categoryLabels.${category.name}`) ||
                        category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 拒付类型 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('platformSelection.chargebackTypes')}</CardTitle>
          <p className='text-sm text-muted-foreground'>
            {t('platformSelection.chargebackTypesDescription')}
          </p>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3'>
            {chargebackTypes.map(type => (
              <div
                key={type.id}
                className='group relative flex items-start space-x-3 p-4 border-2 rounded-xl transition-all duration-200 hover:border-primary/50 hover:bg-primary/5 cursor-pointer'
                onClick={() =>
                  handleChargebackTypeChange(
                    type.id,
                    !formData.chargebackTypes.includes(type.id),
                  )
                }
              >
                <Checkbox
                  id={`chargeback-${type.id}`}
                  checked={formData.chargebackTypes.includes(type.id)}
                  onCheckedChange={checked =>
                    handleChargebackTypeChange(type.id, checked as boolean)
                  }
                  className='mt-0.5 pointer-events-none'
                />
                <div className='flex-1 space-y-2 min-w-0'>
                  <Label
                    htmlFor={`chargeback-${type.id}`}
                    className='text-sm font-medium cursor-pointer leading-tight block'
                  >
                    {t(`platformSelection.chargebackTypeLabels.${type.name}`) ||
                      type.name}
                  </Label>
                  <div className='flex items-center gap-2'>
                    <Badge
                      variant={
                        formData.chargebackTypes.includes(type.id)
                          ? 'default'
                          : 'secondary'
                      }
                      className='text-xs font-semibold'
                    >
                      {type.rate}%
                    </Badge>
                  </div>
                </div>
                {/* 选中状态指示器 */}
                {formData.chargebackTypes.includes(type.id) && (
                  <div className='absolute top-2 right-2 w-2 h-2 bg-primary rounded-full'></div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 支付方式 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('platformSelection.paymentMethods')}</CardTitle>
          <p className='text-sm text-muted-foreground'>
            {t('platformSelection.paymentMethodsDescription')}
          </p>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
            {paymentMethods.map(method => (
              <div
                key={method.id}
                className='group relative flex items-start space-x-3 p-4 border-2 rounded-xl transition-all duration-200 hover:border-primary/50 hover:bg-primary/5 cursor-pointer'
                onClick={() =>
                  handlePaymentMethodChange(
                    method.id,
                    !formData.paymentMethods.includes(method.id),
                  )
                }
              >
                <Checkbox
                  id={`payment-${method.id}`}
                  checked={formData.paymentMethods.includes(method.id)}
                  onCheckedChange={checked =>
                    handlePaymentMethodChange(method.id, checked as boolean)
                  }
                  className='mt-0.5 pointer-events-none'
                />
                <div className='flex-1 space-y-2 min-w-0'>
                  <Label
                    htmlFor={`payment-${method.id}`}
                    className='text-sm font-medium cursor-pointer leading-tight block'
                  >
                    {t(
                      `platformSelection.paymentMethodLabels.${method.name}`,
                    ) || method.name}
                  </Label>
                  <div className='flex items-center gap-2'>
                    <Badge
                      variant={
                        formData.paymentMethods.includes(method.id)
                          ? 'default'
                          : 'secondary'
                      }
                      className='text-xs font-semibold'
                    >
                      {method.rate}%
                    </Badge>
                  </div>
                </div>
                {/* 选中状态指示器 */}
                {formData.paymentMethods.includes(method.id) && (
                  <div className='absolute top-2 right-2 w-2 h-2 bg-primary rounded-full'></div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
