import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import { createWithdrawalRequestSchema } from '@/types/withdrawal';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问',
    serviceUnavailable: '提现服务暂时不可用',
  },
  en: {
    unauthorized: 'Unauthorized',
    serviceUnavailable: 'Withdrawal service temporarily unavailable',
  },
};

export async function POST(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: t.unauthorized,
        },
        { status: 401 },
      );
    }

    const body = await request.json();

    // 将旧的钱包提现API调用重定向到新的提现申请API
    const withdrawalRequestData = {
      amount: body.amount,
      withdrawMethod:
        body.method === 'bank_card'
          ? 'BANK_CARD'
          : body.method === 'usdt_erc20'
            ? 'USDT_ERC20'
            : body.method === 'usdt_trc20'
              ? 'USDT_TRC20'
              : 'BANK_CARD', // 默认美元账户
      // 这里可以添加更多的字段映射
    };

    // 调用新的提现申请API
    const response = await fetch(
      `${request.nextUrl.origin}/api/user/withdrawals`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: request.headers.get('cookie') || '',
        },
        body: JSON.stringify(withdrawalRequestData),
      },
    );

    const result = await response.json();
    return NextResponse.json(result, { status: response.status });
  } catch (error) {
    console.error('提现API错误:', error);
    return NextResponse.json(
      {
        success: false,
        message: messages.zh.serviceUnavailable, // 错误情况下使用默认语言
      },
      { status: 500 },
    );
  }
}
