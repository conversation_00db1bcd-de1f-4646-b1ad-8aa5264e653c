/* RefundGo Logo Performance Optimizations and Accessibility */

/* GPU acceleration for smooth animations */
.refund-go-logo {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  /* Fix overflow issues */
  max-width: 100%;
  overflow: hidden;
}

/* Optimized gradient animation */
.refund-go-logo .gradient-sweep {
  transform: translate3d(-100%, 0, 0);
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.refund-go-logo:hover .gradient-sweep {
  transform: translate3d(100%, 0, 0);
}

/* Reduced motion support for accessibility */
@media (prefers-reduced-motion: reduce) {
  .refund-go-logo .gradient-sweep,
  .refund-go-logo .icon-rotate,
  .refund-go-logo .accent-scale,
  .refund-go-logo .text-color-change {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
  
  .refund-go-logo:hover .gradient-sweep {
    transform: translate3d(-100%, 0, 0);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .refund-go-logo {
    border: 2px solid currentColor;
  }
  
  .refund-go-logo .icon-bg {
    background: currentColor !important;
  }
  
  .refund-go-logo .accent-bg {
    background: currentColor !important;
  }
}

/* Focus styles for keyboard navigation */
.refund-go-logo:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
  border-radius: 0.75rem;
}

/* Print styles */
@media print {
  .refund-go-logo .gradient-sweep {
    display: none;
  }
  
  .refund-go-logo {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .refund-go-logo {
    background: rgba(17, 24, 39, 0.8);
    backdrop-filter: blur(8px);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .refund-go-logo {
    transform: scale(0.9);
    transform-origin: left center;
  }
}

/* Loading state for logo */
.refund-go-logo.loading {
  opacity: 0.7;
  pointer-events: none;
}

.refund-go-logo.loading .icon-rotate {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover state optimizations */
.refund-go-logo:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease-out;
}

/* Logo variants specific styles */
.refund-go-logo--compact {
  padding: 0.5rem 0.75rem;
  /* Prevent overflow in navigation */
  max-width: fit-content;
}

.refund-go-logo--icon-only {
  padding: 0;
  background: transparent;
  box-shadow: none;
  /* Ensure icon stays within bounds */
  flex-shrink: 0;
}

.refund-go-logo--static {
  cursor: default;
}

.refund-go-logo--static:hover {
  transform: none;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .refund-go-logo {
    background: rgba(17, 24, 39, 0.8);
    backdrop-filter: blur(8px);
  }

  .refund-go-logo--compact,
  .refund-go-logo--icon-only {
    background: transparent;
  }
}

/* Force logo text colors to override any global styles */
.refund-go-logo span {
  color: inherit !important;
}

/* Dark mode text color fixes for navigation */
.dark .refund-go-logo span[class*="text-white"] {
  color: white !important;
}

.dark .refund-go-logo span[class*="text-blue-300"] {
  color: rgb(147 197 253) !important;
}

.dark .refund-go-logo span[class*="text-gray-300"] {
  color: rgb(209 213 219) !important;
}

/* Light mode text color fixes */
.refund-go-logo span[class*="text-gray-900"] {
  color: rgb(17 24 39) !important;
}

.refund-go-logo span[class*="text-blue-600"] {
  color: rgb(37 99 235) !important;
}

.refund-go-logo span[class*="text-gray-500"] {
  color: rgb(107 114 128) !important;
}

/* Navigation specific fixes */
.navbar .refund-go-logo {
  max-width: 280px;
  overflow: hidden;
}

/* Prevent logo from breaking layout */
.refund-go-logo {
  min-width: 0;
  flex-shrink: 1;
}

/* Navbar-specific logo containment */
nav .refund-go-logo,
.navbar .refund-go-logo {
  max-height: 48px; /* Ensure logo fits within h-16 navbar with margin */
  overflow: hidden;
  contain: layout style;
  flex-shrink: 0;
}

/* Ensure navbar logo doesn't overflow vertically */
nav .refund-go-logo > *,
.navbar .refund-go-logo > * {
  max-height: inherit;
  overflow: hidden;
}

/* Prevent logo animations from causing overflow */
nav .refund-go-logo .gradient-sweep,
.navbar .refund-go-logo .gradient-sweep {
  contain: layout;
}

/* Mobile responsive navbar logo */
@media (max-width: 768px) {
  nav .refund-go-logo,
  .navbar .refund-go-logo {
    max-height: 40px;
    max-width: 200px;
  }
}

/* Ensure proper vertical alignment in navbar */
nav .refund-go-logo,
.navbar .refund-go-logo {
  align-self: center;
  display: flex;
  align-items: center;
}

/* Additional dark mode text fixes for navigation context */
nav .refund-go-logo span,
.navbar .refund-go-logo span,
[data-theme="dark"] .refund-go-logo span {
  color: inherit !important;
}

/* Ensure dark mode text visibility in navigation */
.dark nav .refund-go-logo span[class*="text-white"],
.dark .navbar .refund-go-logo span[class*="text-white"],
[data-theme="dark"] .refund-go-logo span[class*="text-white"] {
  color: white !important;
}

.dark nav .refund-go-logo span[class*="text-blue-300"],
.dark .navbar .refund-go-logo span[class*="text-blue-300"],
[data-theme="dark"] .refund-go-logo span[class*="text-blue-300"] {
  color: rgb(147 197 253) !important;
}

/* Fallback for any remaining dark mode issues */
html.dark .refund-go-logo span:first-child {
  color: white !important;
}

html.dark .refund-go-logo span:nth-child(2) {
  color: rgb(147 197 253) !important;
}
