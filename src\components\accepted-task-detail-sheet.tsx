'use client';

import {
  Eye,
  ExternalLink,
  Package,
  FileText,
  AlertTriangle,
  Hash,
  Activity,
  Building2,
  CreditCard,
  DollarSign,
  Clock,
  MapPin,
  Timer,
  ShoppingCart,
  Camera,
  CheckCircle,
  Play,
  Image,
  Truck,
  XCircle,
  Info,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { EvidenceFileViewer } from '@/components/evidence-file-viewer';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import {
  Task,
  TaskStatus,
  EvidenceStatus,
  PLATFORM_LABELS,
  CHARGEBACK_TYPE_LABELS,
  PAYMENT_METHOD_LABELS,
  TASK_STATUS_LABELS,
  EVIDENCE_STATUS_LABELS,
} from '@/lib/types/task';
import {
  getTaskCommission,
  getTaskBaseCommission,
} from '@/lib/utils/commission';

interface AcceptedTaskDetailSheetProps {
  task: Task;
  children?: React.ReactNode;
}

export function AcceptedTaskDetailSheet({
  task,
  children,
}: AcceptedTaskDetailSheetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const tPublish = useTranslations('Publish');
  const t = useTranslations('MyAcceptedTasks');

  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取费率数据
  const { chargebackTypes, paymentMethods, systemRate } = useCommissionRates();

  // 计算酬金（使用真实的费率数据，包含证据状态）
  const taskWithEvidenceStatus = {
    ...task,
    evidenceStatus: task.evidenceStatus?.toString(),
  };
  const commission = getTaskCommission(
    taskWithEvidenceStatus,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算基础酬金（不包含证据费用）
  const baseCommission = getTaskBaseCommission(
    taskWithEvidenceStatus,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算产品总价：单价 × 数量
  const calculateTotalPrice = () => {
    const unitPrice = task.unitPrice || 0;
    const quantity = task.quantity || 1;
    return unitPrice * quantity;
  };

  // 格式化倒计时文本的通用函数
  const formatCountdownTime = (diffMs: number) => {
    if (diffMs <= 0) {
      return {
        text: t('detail.timeExpired'),
        isExpired: true,
        isUrgent: false,
      };
    }

    const totalHours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (totalHours >= 24) {
      const days = Math.floor(totalHours / 24);
      const remainingHours = totalHours % 24;
      return {
        text:
          remainingHours > 0
            ? `${days}${t('detail.timeDays')}${remainingHours}${t('detail.timeHours')}`
            : `${days}${t('detail.timeDays')}`,
        isExpired: false,
        isUrgent: days < 1,
      };
    } else if (totalHours > 0) {
      return {
        text:
          minutes > 0
            ? `${totalHours}${t('detail.timeHours')}${minutes}${t('detail.timeMinutes')}`
            : `${totalHours}${t('detail.timeHours')}`,
        isExpired: false,
        isUrgent: totalHours < 2,
      };
    } else {
      return {
        text: `${minutes}${t('detail.timeMinutes')}`,
        isExpired: false,
        isUrgent: true,
      };
    }
  };

  // 计算订单提交倒计时
  const getOrderCountdown = () => {
    if (!mounted || !task.deadline) {
      return {
        text: t('detail.timeCalculating'),
        isExpired: false,
        isUrgent: false,
        type: 'order',
      };
    }

    const now = new Date();
    let deadline: Date;

    if (task.reviewRejectReason && task.reviewedAt) {
      // 被驳回委托：从审核时间开始计算24小时
      deadline = new Date(
        new Date(task.reviewedAt).getTime() + 24 * 60 * 60 * 1000,
      );
    } else {
      // 正常委托：从接单时间开始计算24小时
      deadline = new Date(task.deadline);
    }

    const diffMs = deadline.getTime() - now.getTime();
    const result = formatCountdownTime(diffMs);

    return { ...result, type: 'order' };
  };

  // 计算物流提交倒计时
  const getLogisticsCountdown = () => {
    if (!mounted || !task.logisticsDeadline) {
      return {
        text: t('detail.timeCalculating'),
        isExpired: false,
        isUrgent: false,
        type: 'logistics',
      };
    }

    const now = new Date();
    const deadline = new Date(task.logisticsDeadline);
    const diffMs = deadline.getTime() - now.getTime();
    const result = formatCountdownTime(diffMs);

    return { ...result, type: 'logistics' };
  };

  // 计算审核倒计时
  const getReviewCountdown = () => {
    if (!mounted) {
      return {
        text: t('detail.timeCalculating'),
        isExpired: false,
        isUrgent: false,
        type: 'review',
      };
    }

    const now = new Date();

    // 优先检查物流审核截止时间
    if (task.logisticsReviewDeadline) {
      const deadline = new Date(task.logisticsReviewDeadline);
      const diffMs = deadline.getTime() - now.getTime();

      if (diffMs <= 0) {
        return {
          text: t('detail.timeReviewExpired'),
          isExpired: true,
          isUrgent: false,
          type: 'logistics_review',
        };
      }

      const hours = Math.floor(diffMs / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      return {
        text:
          hours > 0
            ? `${hours}${t('detail.timeHours')}${minutes}${t('detail.timeMinutes')}`
            : `${minutes}${t('detail.timeMinutes')}`,
        isExpired: false,
        isUrgent: hours < 2,
        type: 'logistics_review',
      };
    }

    // 检查订单审核截止时间
    if (task.orderReviewDeadline) {
      const deadline = new Date(task.orderReviewDeadline);
      const diffMs = deadline.getTime() - now.getTime();

      if (diffMs <= 0) {
        return {
          text: t('detail.timeReviewExpired'),
          isExpired: true,
          isUrgent: false,
          type: 'order_review',
        };
      }

      const hours = Math.floor(diffMs / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      return {
        text:
          hours > 0
            ? `${hours}${t('detail.timeHours')}${minutes}${t('detail.timeMinutes')}`
            : `${minutes}${t('detail.timeMinutes')}`,
        isExpired: false,
        isUrgent: hours < 1,
        type: 'order_review',
      };
    }

    return {
      text: t('detail.timeWaitingReview'),
      isExpired: false,
      isUrgent: false,
      type: 'review',
    };
  };

  // 计算自动收货倒计时
  const getDeliveryCountdown = () => {
    if (!mounted || !task.deliveryDeadline) {
      return {
        text: t('detail.timeCalculating'),
        isExpired: false,
        isUrgent: false,
        type: 'delivery',
      };
    }

    const now = new Date();
    const deadline = new Date(task.deliveryDeadline);
    const diffMs = deadline.getTime() - now.getTime();

    if (diffMs <= 0) {
      return {
        text: t('detail.autoDeliveryImminent'),
        isExpired: true,
        isUrgent: false,
        type: 'delivery',
      };
    }

    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
    );

    return {
      text:
        days > 0
          ? `${days}${t('detail.timeDays')}${hours}${t('detail.timeHours')}`
          : hours > 0
            ? `${hours}${t('detail.timeHours')}`
            : t('detail.timeLessThanOneHour'),
      isExpired: false,
      isUrgent: days < 3,
      type: 'delivery',
    };
  };

  // 根据委托状态获取对应的倒计时
  const getCountdown = () => {
    switch (task.status) {
      case TaskStatus.IN_PROGRESS:
        return getOrderCountdown();
      case TaskStatus.PENDING_LOGISTICS:
        return getLogisticsCountdown();
      case TaskStatus.PENDING_REVIEW:
        return getReviewCountdown();
      case TaskStatus.PENDING_DELIVERY:
        return getDeliveryCountdown();
      default:
        return {
          text: t('detail.timeNoCountdown'),
          isExpired: false,
          isUrgent: false,
          type: 'none',
        };
    }
  };

  const countdown = getCountdown();

  // 获取状态颜色
  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300 dark:hover:bg-yellow-900';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-900';
      case TaskStatus.PENDING_LOGISTICS:
        return 'bg-orange-100 text-orange-800 hover:bg-orange-100 dark:bg-orange-900 dark:text-orange-300 dark:hover:bg-orange-900';
      case TaskStatus.PENDING_REVIEW:
        return 'bg-indigo-100 text-indigo-800 hover:bg-indigo-100 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-900';
      case TaskStatus.PENDING_DELIVERY:
        return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-100 dark:bg-cyan-900 dark:text-cyan-300 dark:hover:bg-cyan-900';
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-900';
      case TaskStatus.EXPIRED:
        return 'bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-900';
      case TaskStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-900 dark:text-gray-300 dark:hover:bg-gray-900';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100 dark:bg-gray-900 dark:text-gray-300 dark:hover:bg-gray-900';
    }
  };

  // 获取状态标签
  const getStatusLabel = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('detail.statusPending');
      case TaskStatus.REJECTED:
        return t('detail.statusRejected');
      case TaskStatus.RECRUITING:
        return t('detail.statusRecruiting');
      case TaskStatus.IN_PROGRESS:
        return t('detail.statusInProgress');
      case TaskStatus.PENDING_LOGISTICS:
        return t('detail.statusPendingLogistics');
      case TaskStatus.PENDING_REVIEW:
        return t('detail.statusPendingReview');
      case TaskStatus.PENDING_DELIVERY:
        return t('detail.statusPendingDelivery');
      case TaskStatus.COMPLETED:
        return t('detail.statusCompleted');
      case TaskStatus.EXPIRED:
        return t('detail.statusExpired');
      case TaskStatus.CANCELLED:
        return t('detail.statusCancelled');
      default:
        return status;
    }
  };

  // 已移除模拟数据，现在使用真实委托数据

  // 判断文件类型的工具函数
  const getFileType = (filePath: string): 'image' | 'video' | 'other' => {
    // 处理URL，移除查询参数
    let fileName = filePath;
    try {
      const url = new URL(filePath, window.location.origin);
      fileName = url.pathname;
    } catch {
      // 如果不是完整URL，直接使用原路径
      fileName = filePath.split('?')[0]; // 移除查询参数
    }

    // 获取文件扩展名
    const extension = fileName.toLowerCase().split('.').pop() || '';

    const imageExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'bmp',
      'svg',
      'tiff',
      'tif',
    ];
    const videoExtensions = [
      'mp4',
      'avi',
      'mov',
      'wmv',
      'flv',
      'webm',
      'mkv',
      '3gp',
      'm4v',
      'ogv',
    ];

    if (imageExtensions.includes(extension)) {
      return 'image';
    } else if (videoExtensions.includes(extension)) {
      return 'video';
    } else {
      return 'other';
    }
  };

  // 格式化日期时间的安全函数
  const formatDateTime = (date: Date) => {
    if (!mounted) {
      return t('detail.timeLoading');
    }
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {children || (
          <Button size='sm' variant='outline' className='flex-1'>
            <Eye className='h-4 w-4 mr-1' />
            {t('detail.viewDetails')}
          </Button>
        )}
      </SheetTrigger>
      <SheetContent className='w-full sm:max-w-lg'>
        <SheetHeader>
          <SheetTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            {t('detail.taskDetails')}
          </SheetTitle>
          <SheetDescription>
            {t('detail.taskDetailsDescription')}
          </SheetDescription>
        </SheetHeader>

        <div className='h-[calc(100vh-120px)] mt-6 overflow-y-auto scrollbar-hide'>
          <div className='space-y-6'>
            {/* 基本信息 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <Hash className='h-4 w-4' />
                {t('detail.basicInfo')}
              </h3>

              <div className='grid grid-cols-2 gap-4 text-sm'>
                <div>
                  <span className='text-gray-600 dark:text-gray-300'>
                    {t('detail.taskId')}
                  </span>
                  <div className='font-medium'>{task.id}</div>
                </div>
                <div>
                  <span className='text-muted-foreground'>
                    {t('detail.status')}
                  </span>
                  <div>
                    <Badge className={getStatusColor(task.status)}>
                      {getStatusLabel(task.status)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <span className='text-muted-foreground'>
                    {t('detail.platform')}
                  </span>
                  <div className='font-medium'>
                    {typeof task.platform === 'object'
                      ? tPublish(
                          `platformSelection.platformLabels.${task.platform.name}`,
                        ) || task.platform.name
                      : tPublish(
                          `platformSelection.platformLabels.${task.platform}`,
                        ) || task.platform}
                  </div>
                </div>
                <div>
                  <span className='text-muted-foreground'>
                    {t('detail.category')}
                  </span>
                  <div className='font-medium'>
                    {typeof task.category === 'object'
                      ? tPublish(
                          `platformSelection.categoryLabels.${task.category.name}`,
                        ) || task.category.name
                      : tPublish(
                          `platformSelection.categoryLabels.${task.category}`,
                        ) || task.category}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 商品信息 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <Package className='h-4 w-4' />
                {t('detail.productInfo')}
              </h3>

              <div className='space-y-3'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='text-muted-foreground'>
                      {t('detail.quantity')}
                    </span>
                    <div className='font-semibold text-lg'>
                      {task.quantity || 1} {t('detail.pieces')}
                    </div>
                  </div>
                  <div>
                    <span className='text-muted-foreground'>
                      {t('detail.unitPrice')}
                    </span>
                    <div className='font-semibold text-lg'>
                      ${task.unitPrice?.toFixed(2) || '0.00'}
                    </div>
                  </div>
                </div>

                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.productUrl')}
                  </span>
                  <div className='flex items-center gap-2 mt-1'>
                    {task.productUrl ? (
                      <>
                        <a
                          href={task.productUrl}
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-xs font-mono bg-muted px-2 py-1 rounded break-all hover:bg-muted/80 text-blue-600 hover:text-blue-800'
                        >
                          {task.productUrl}
                        </a>
                        <Button
                          size='sm'
                          variant='ghost'
                          className='h-6 w-6 p-0'
                          asChild
                        >
                          <a
                            href={task.productUrl}
                            target='_blank'
                            rel='noopener noreferrer'
                          >
                            <ExternalLink className='h-3 w-3' />
                          </a>
                        </Button>
                      </>
                    ) : (
                      <span className='text-sm text-muted-foreground'>
                        {t('detail.noProductUrl')}
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.productDescription')}
                  </span>
                  <div className='text-sm mt-1 p-3 bg-muted/50 rounded-lg'>
                    {task.productDescription ||
                      t('detail.noProductDescription')}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 拒付信息 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <AlertTriangle className='h-4 w-4' />
                {t('detail.chargebackInfo')}
              </h3>

              <div className='space-y-3'>
                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.chargebackType')}
                  </span>
                  <div className='flex flex-wrap gap-2 mt-1'>
                    {task.chargebackTypes?.map((type: any) => (
                      <Badge key={type} variant='outline' className='text-xs'>
                        {typeof type === 'string'
                          ? tPublish(
                              `platformSelection.chargebackTypeLabels.${type}`,
                            ) || type
                          : tPublish(
                              `platformSelection.chargebackTypeLabels.${type.name}`,
                            ) ||
                            type.name ||
                            type}
                      </Badge>
                    )) || (
                      <span className='text-xs text-muted-foreground'>
                        {t('detail.none')}
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.supportedPaymentMethods')}
                  </span>
                  <div className='flex flex-wrap gap-2 mt-1'>
                    {task.paymentMethods?.map((method: any) => (
                      <Badge
                        key={method}
                        variant='secondary'
                        className='text-xs'
                      >
                        {typeof method === 'string'
                          ? tPublish(
                              `platformSelection.paymentMethodLabels.${method}`,
                            ) || method
                          : tPublish(
                              `platformSelection.paymentMethodLabels.${method.name}`,
                            ) ||
                            method.name ||
                            method}
                      </Badge>
                    )) || (
                      <span className='text-xs text-muted-foreground'>
                        {t('detail.none')}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 金额信息 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <DollarSign className='h-4 w-4' />
                {t('detail.amountInfo')}
              </h3>

              <div className='grid grid-cols-2 gap-4 text-sm'>
                <div>
                  <span className='text-muted-foreground'>
                    {t('detail.orderAmount')}
                  </span>
                  <div className='font-semibold text-lg'>
                    ${calculateTotalPrice().toFixed(2) || '0.00'}
                  </div>
                </div>
                <div>
                  <span className='text-muted-foreground'>
                    {t('detail.reward')}
                  </span>
                  <div className='font-semibold text-lg text-green-600'>
                    ${baseCommission.toFixed(2)}
                  </div>

                  {/* 证据状态对酬金的影响说明 */}
                  {task.evidenceUploadType &&
                    task.evidenceUploadType !== 'NONE' && (
                      <div className='text-xs text-muted-foreground mt-1'>
                        {task.evidenceStatus === 'REVIEWED' ? (
                          <span className='text-green-600'>
                            ✓ {t('detail.evidenceApproved')}
                          </span>
                        ) : (
                          <span className='text-amber-600'>
                            {t('detail.canGetEvidenceFee')} $
                            {(
                              (calculateTotalPrice() *
                                (systemRate?.noEvidenceExtraRate || 0)) /
                              100
                            ).toFixed(2)}
                          </span>
                        )}
                      </div>
                    )}
                </div>
              </div>
            </div>

            <Separator />

            {/* 证据状态对酬金影响的详细说明 */}
            {task.evidenceUploadType && task.evidenceUploadType !== 'NONE' && (
              <div className='space-y-4'>
                <h3 className='font-semibold flex items-center gap-2'>
                  <Info className='h-4 w-4' />
                  {t('detail.evidenceStatusAndReward')}
                </h3>

                <div
                  className={`p-4 rounded-lg border ${
                    task.evidenceStatus === 'REVIEWED'
                      ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800'
                      : task.evidenceStatus === 'REJECTED'
                        ? 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800'
                        : 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800'
                  }`}
                >
                  <div className='space-y-2'>
                    <div className='flex items-center gap-2'>
                      {task.evidenceStatus === 'REVIEWED' ? (
                        <>
                          <CheckCircle className='h-4 w-4 text-green-600' />
                          <span className='font-medium text-green-800 dark:text-green-300'>
                            {t('detail.evidenceApproved')}
                          </span>
                        </>
                      ) : task.evidenceStatus === 'REJECTED' ? (
                        <>
                          <X className='h-4 w-4 text-red-600' />
                          <span className='font-medium text-red-800 dark:text-red-300'>
                            {t('detail.evidenceRejected')}
                          </span>
                        </>
                      ) : (
                        <>
                          <Clock className='h-4 w-4 text-blue-600' />
                          <span className='font-medium text-blue-800 dark:text-blue-300'>
                            {task.evidenceStatus === 'UNDER_REVIEW'
                              ? t('detail.evidenceUnderReview')
                              : t('detail.evidenceWaitingSubmission')}
                          </span>
                        </>
                      )}
                    </div>

                    <div className='text-sm space-y-1'>
                      {task.evidenceStatus === 'REVIEWED' ? (
                        <>
                          <div className='text-green-700 dark:text-green-400'>
                            ✓ {t('detail.publisherEvidenceApproved')}
                          </div>
                          <div className='text-green-700 dark:text-green-400'>
                            ✓ {t('detail.evidenceFeeReturned')}
                          </div>
                          <div className='text-green-700 dark:text-green-400'>
                            ✓ {t('detail.finalReward')}: $
                            {baseCommission.toFixed(2)}
                          </div>
                        </>
                      ) : (
                        <>
                          <div
                            className={`${
                              task.evidenceStatus === 'REJECTED'
                                ? 'text-red-700 dark:text-red-400'
                                : 'text-blue-700 dark:text-blue-400'
                            }`}
                          >
                            {task.evidenceStatus === 'REJECTED'
                              ? `✗ ${t('detail.publisherEvidenceRejected')}`
                              : `⏳ ${t('detail.publisherEvidencePending')}`}
                          </div>
                          <div
                            className={`${
                              task.evidenceStatus === 'REJECTED'
                                ? 'text-red-700 dark:text-red-400'
                                : 'text-blue-700 dark:text-blue-400'
                            }`}
                          >
                            ✓ {t('detail.willGetExtraFee')}
                          </div>
                          <div
                            className={`${
                              task.evidenceStatus === 'REJECTED'
                                ? 'text-red-700 dark:text-red-400'
                                : 'text-blue-700 dark:text-blue-400'
                            }`}
                          >
                            ✓ {t('detail.estimatedFinalReward')}: $
                            {(
                              baseCommission +
                              (calculateTotalPrice() *
                                (systemRate?.noEvidenceExtraRate || 0)) /
                                100
                            ).toFixed(2)}{' '}
                            ({t('detail.baseReward')} $
                            {baseCommission.toFixed(2)} +{' '}
                            {t('detail.evidenceFee')} $
                            {(
                              (calculateTotalPrice() *
                                (systemRate?.noEvidenceExtraRate || 0)) /
                              100
                            ).toFixed(2)}
                            )
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <Separator />

            {/* 时间信息 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <Clock className='h-4 w-4' />
                {t('detail.timeInfo')}
              </h3>

              <div className='grid grid-cols-1 gap-4 text-sm'>
                <div>
                  <span className='text-muted-foreground'>
                    {t('detail.acceptedAt')}
                  </span>
                  <div className='font-medium'>
                    {task.acceptedAt
                      ? formatDateTime(new Date(task.acceptedAt))
                      : t('detail.noData')}
                  </div>
                </div>

                {/* 根据委托状态显示不同的倒计时 */}
                {countdown.type !== 'none' && (
                  <div>
                    <span className='text-muted-foreground'>
                      {countdown.type === 'order' &&
                        t('detail.orderSubmissionLimit')}
                      {countdown.type === 'logistics' &&
                        t('detail.logisticsSubmission')}
                      {countdown.type === 'order_review' &&
                        t('detail.orderReview')}
                      {countdown.type === 'logistics_review' &&
                        t('detail.logisticsReview')}
                      {countdown.type === 'review' && t('detail.reviewLimit')}
                      {countdown.type === 'delivery' &&
                        t('detail.autoDeliveryLimit')}
                    </span>
                    <div
                      className={`font-medium ${
                        countdown.isExpired
                          ? 'text-red-600'
                          : countdown.isUrgent
                            ? 'text-orange-600'
                            : countdown.type === 'delivery'
                              ? 'text-green-600'
                              : 'text-blue-600'
                      }`}
                    >
                      {countdown.isExpired
                        ? countdown.text
                        : `${t('detail.remaining')} ${countdown.text}`}
                    </div>

                    {/* 添加阶段说明 */}
                    <div className='text-xs text-muted-foreground mt-1'>
                      {countdown.type === 'order' &&
                        t('detail.submitOrderInfoWithinDeadline')}
                      {countdown.type === 'logistics' &&
                        t('detail.submitLogisticsInfoWithinDeadline')}
                      {countdown.type === 'order_review' &&
                        t('detail.publisherReviewingOrderInfo')}
                      {countdown.type === 'logistics_review' &&
                        t('detail.publisherReviewingLogisticsInfo')}
                      {countdown.type === 'review' &&
                        t('detail.waitingForPublisherReview')}
                      {countdown.type === 'delivery' &&
                        t('detail.publisherConfirmReceiptOrAutoReceive')}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* 收货地址 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <MapPin className='h-4 w-4' />
                {t('detail.shippingAddress')}
              </h3>

              <div className='space-y-3'>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='text-muted-foreground'>
                      {t('detail.recipient')}
                    </span>
                    <div className='font-medium'>
                      {task.recipientName || t('detail.none')}
                    </div>
                  </div>
                  <div>
                    <span className='text-muted-foreground'>
                      {t('detail.phoneNumber')}
                    </span>
                    <div className='font-medium'>
                      {task.recipientPhone || t('detail.none')}
                    </div>
                  </div>
                </div>
                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.detailedAddress')}
                  </span>
                  <div className='text-sm mt-1 p-3 bg-muted/50 rounded-lg'>
                    {task.shippingAddress || t('detail.noShippingAddress')}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* 示范材料 */}
            <div className='space-y-4'>
              <h3 className='font-semibold flex items-center gap-2'>
                <Camera className='h-4 w-4' />
                {t('detail.demoMaterials')}
              </h3>

              <div className='space-y-3'>
                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.cartDemoScreenshot')}
                  </span>
                  <div className='mt-2'>
                    <EvidenceFileViewer files={task.cartScreenshots || []} />
                  </div>
                </div>

                <div>
                  <span className='text-muted-foreground text-sm'>
                    {t('detail.chargebackEvidence')}
                  </span>
                  {task.evidenceFiles && task.evidenceFiles.length > 0 ? (
                    <div className='space-y-3 mt-2'>
                      {/* 证据状态提示 */}
                      {task.evidenceStatus === 'REJECTED' && (
                        <div className='p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950 dark:border-red-800'>
                          <div className='flex items-start gap-2'>
                            <AlertTriangle className='h-4 w-4 text-red-600 mt-0.5 flex-shrink-0' />
                            <div>
                              <div className='text-sm font-medium text-red-800 dark:text-red-300'>
                                {t('detail.evidenceRejected')}
                              </div>
                              {task.evidenceRejectReason && (
                                <div className='text-sm text-red-700 dark:text-red-400 mt-1'>
                                  {task.evidenceRejectReason}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                      {task.evidenceStatus === 'UNDER_REVIEW' && (
                        <div className='p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-950 dark:border-blue-800'>
                          <div className='flex items-center gap-2'>
                            <Clock className='h-4 w-4 text-blue-600' />
                            <div className='text-sm font-medium text-blue-800 dark:text-blue-300'>
                              {t('detail.evidenceUnderReview')}
                            </div>
                          </div>
                        </div>
                      )}
                      {task.evidenceStatus === 'REVIEWED' && (
                        <div className='p-3 bg-green-50 border border-green-200 rounded-lg dark:bg-green-950 dark:border-green-800'>
                          <div className='flex items-center gap-2'>
                            <CheckCircle className='h-4 w-4 text-green-600' />
                            <div className='text-sm font-medium text-green-800 dark:text-green-300'>
                              {t('detail.evidenceApproved')}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 证据文件列表 - 只有审核通过后才显示具体内容 */}
                      {task.evidenceStatus === 'REVIEWED' ? (
                        <EvidenceFileViewer files={task.evidenceFiles || []} />
                      ) : (
                        <div className='mt-2 p-4 border-2 border-dashed border-amber-200 rounded-lg text-center bg-amber-50 dark:bg-amber-950 dark:border-amber-800'>
                          <FileText className='h-8 w-8 mx-auto text-amber-600 mb-2' />
                          <div className='text-sm text-amber-700 dark:text-amber-300 font-medium'>
                            {task.evidenceStatus === 'UNDER_REVIEW'
                              ? t('detail.evidenceReviewingCanViewAfter')
                              : task.evidenceStatus === 'REJECTED'
                                ? t('detail.evidenceRejectedCannotView')
                                : t('detail.evidenceCanViewAfterApproval')}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    // 没有证据文件时的状态显示
                    <>
                      {task.evidenceStatus === 'PENDING_SUBMISSION' ||
                      task.evidenceStatus === 'REJECTED' ? (
                        <div className='mt-2 p-4 border-2 border-dashed border-gray-200 rounded-lg text-center bg-gray-50 dark:bg-gray-950 dark:border-gray-800'>
                          <FileText className='h-8 w-8 mx-auto text-gray-500 mb-2' />
                          <div className='text-sm text-gray-600 dark:text-gray-400 font-medium'>
                            {task.evidenceStatus === 'REJECTED'
                              ? t('detail.evidenceNeedReupload')
                              : t('detail.noEvidence')}
                          </div>
                          <div className='text-xs text-gray-500 dark:text-gray-500 mt-1'>
                            {task.evidenceStatus === 'REJECTED'
                              ? t('detail.publisherEvidenceFailedNeedResubmit')
                              : t('detail.publisherNotProvidedEvidence')}
                          </div>
                        </div>
                      ) : task.evidenceStatus === 'UNDER_REVIEW' ? (
                        <div className='mt-2 p-4 border-2 border-dashed border-blue-200 rounded-lg text-center bg-blue-50 dark:bg-blue-950 dark:border-blue-800'>
                          <Clock className='h-8 w-8 mx-auto text-blue-500 mb-2' />
                          <div className='text-sm text-blue-600 dark:text-blue-400 font-medium'>
                            {t('detail.evidencePendingReview')}
                          </div>
                          <div className='text-xs text-blue-500 dark:text-blue-500 mt-1'>
                            {t('detail.publisherSubmittedWaitingReview')}
                          </div>
                        </div>
                      ) : task.evidenceStatus === 'REVIEWED' ? (
                        <div className='mt-2 p-4 border-2 border-dashed border-green-200 rounded-lg text-center bg-green-50 dark:bg-green-950 dark:border-green-800'>
                          <CheckCircle className='h-8 w-8 mx-auto text-green-500 mb-2' />
                          <div className='text-sm text-green-600 dark:text-green-400 font-medium'>
                            {t('detail.evidenceApproved')}
                          </div>
                          <div className='text-xs text-green-500 dark:text-green-500 mt-1'>
                            {t('detail.publisherEvidenceApproved')}
                          </div>
                        </div>
                      ) : (
                        <div className='mt-2 p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg text-center'>
                          <FileText className='h-8 w-8 mx-auto text-muted-foreground mb-2' />
                          <div className='text-sm text-muted-foreground'>
                            {t('detail.noEvidenceMaterials')}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* 订单和物流信息 - 在等待审核、等待收货、已完成状态时显示 */}
            {(task.status === TaskStatus.PENDING_REVIEW ||
              task.status === TaskStatus.PENDING_DELIVERY ||
              task.status === TaskStatus.COMPLETED) &&
              (task.orderNumber ||
                task.trackingNumber ||
                (task.logisticsScreenshots &&
                  task.logisticsScreenshots.length > 0)) && (
                <>
                  <Separator />
                  <div className='space-y-4'>
                    <h3 className='font-semibold flex items-center gap-2'>
                      <Truck className='h-4 w-4' />
                      {t('detail.orderLogisticsInfo')}
                    </h3>

                    <div className='space-y-3'>
                      {/* 订单号 */}
                      {task.orderNumber && (
                        <div>
                          <span className='text-muted-foreground text-sm'>
                            {t('detail.orderNumber')}
                          </span>
                          <div className='text-sm mt-1 p-3 bg-muted/50 rounded-lg font-mono'>
                            {task.orderNumber}
                          </div>
                        </div>
                      )}

                      {/* 订单截图 */}
                      {task.orderScreenshot && (
                        <div>
                          <span className='text-muted-foreground text-sm'>
                            {t('detail.orderScreenshot')}
                          </span>
                          <div className='mt-2'>
                            <EvidenceFileViewer
                              files={[task.orderScreenshot]}
                            />
                          </div>
                        </div>
                      )}

                      {/* 物流单号 */}
                      {task.trackingNumber && (
                        <div>
                          <span className='text-muted-foreground text-sm'>
                            {t('detail.trackingNumber')}
                          </span>
                          <div className='text-sm mt-1 p-3 bg-muted/50 rounded-lg font-mono'>
                            {task.trackingNumber}
                          </div>
                        </div>
                      )}

                      {/* 物流截图 */}
                      {task.logisticsScreenshots &&
                        task.logisticsScreenshots.length > 0 && (
                          <div>
                            <span className='text-muted-foreground text-sm'>
                              {t('detail.logisticsScreenshots', {
                                count: task.logisticsScreenshots.length,
                              })}
                            </span>
                            <div className='mt-2'>
                              <EvidenceFileViewer
                                files={task.logisticsScreenshots}
                              />
                            </div>
                          </div>
                        )}

                      {/* 状态信息 */}
                      {task.status === TaskStatus.PENDING_REVIEW && (
                        <div className='p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-950 dark:border-blue-800'>
                          <div className='flex items-center gap-2'>
                            <Clock className='h-4 w-4 text-blue-600' />
                            <div className='text-sm font-medium text-blue-800 dark:text-blue-300'>
                              {t('detail.waitingForPublisherReview')}
                            </div>
                          </div>
                          <div className='text-xs text-blue-600 dark:text-blue-400 mt-1'>
                            {t('detail.publisherReviewingOrderLogistics')}
                          </div>
                        </div>
                      )}

                      {task.status === TaskStatus.PENDING_DELIVERY && (
                        <div className='p-3 bg-cyan-50 border border-cyan-200 rounded-lg dark:bg-cyan-950 dark:border-cyan-800'>
                          <div className='flex items-center gap-2'>
                            <CheckCircle className='h-4 w-4 text-cyan-600' />
                            <div className='text-sm font-medium text-cyan-800 dark:text-cyan-300'>
                              {t('detail.waitingForDeliveryConfirmation')}
                            </div>
                          </div>
                          <div className='text-xs text-cyan-600 dark:text-cyan-400 mt-1'>
                            {t('detail.reviewPassedWaitingDelivery')}
                          </div>
                          {/* 自动收货倒计时 */}
                          {task.deliveryDeadline && (
                            <div className='mt-2 pt-2 border-t border-cyan-200 dark:border-cyan-700'>
                              <div className='flex items-center gap-2'>
                                <Clock className='h-3 w-3 text-cyan-600' />
                                <span className='text-xs text-cyan-600 dark:text-cyan-400'>
                                  {t('detail.autoDeliveryTime')}:{' '}
                                  {(() => {
                                    const deadline = new Date(
                                      task.deliveryDeadline,
                                    );
                                    const now = new Date();
                                    const diffMs =
                                      deadline.getTime() - now.getTime();

                                    if (diffMs <= 0) {
                                      return t('detail.autoDeliveryImminent');
                                    }

                                    const days = Math.floor(
                                      diffMs / (1000 * 60 * 60 * 24),
                                    );
                                    const hours = Math.floor(
                                      (diffMs % (1000 * 60 * 60 * 24)) /
                                        (1000 * 60 * 60),
                                    );

                                    if (days > 0) {
                                      return `${days}${t('detail.timeDays')}${hours}${t('detail.timeHours')}${t('detail.timeAfter')}`;
                                    } else if (hours > 0) {
                                      return `${hours}${t('detail.timeHours')}${t('detail.timeAfter')}`;
                                    } else {
                                      const minutes = Math.floor(
                                        (diffMs % (1000 * 60 * 60)) /
                                          (1000 * 60),
                                      );
                                      return `${minutes}${t('detail.timeMinutes')}${t('detail.timeAfter')}`;
                                    }
                                  })()}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {task.status === TaskStatus.COMPLETED && (
                        <div className='p-3 bg-green-50 border border-green-200 rounded-lg dark:bg-green-950 dark:border-green-800'>
                          <div className='flex items-center gap-2'>
                            <CheckCircle className='h-4 w-4 text-green-600' />
                            <div className='text-sm font-medium text-green-800 dark:text-green-300'>
                              {t('detail.taskCompleted')}
                            </div>
                          </div>
                          <div className='text-xs text-green-600 dark:text-green-400 mt-1'>
                            {t('detail.congratulationsCompleted')}
                          </div>
                        </div>
                      )}

                      {/* 审核拒绝信息 */}
                      {task.reviewRejectReason && (
                        <div className='p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950 dark:border-red-800'>
                          <div className='flex items-center gap-2'>
                            <XCircle className='h-4 w-4 text-red-600' />
                            <div className='text-sm font-medium text-red-800 dark:text-red-300'>
                              {t('detail.reviewFailed')}
                            </div>
                          </div>
                          <div className='text-xs text-red-600 dark:text-red-400 mt-1'>
                            {t('detail.rejectionReason')}:{' '}
                            {task.reviewRejectReason}
                          </div>
                          <div className='text-xs text-red-500 dark:text-red-500 mt-1'>
                            {t('detail.resubmitCorrectInfo')}
                          </div>
                        </div>
                      )}

                      {/* 审核时间信息 */}
                      {task.reviewedAt && (
                        <div className='text-xs text-muted-foreground'>
                          {t('detail.reviewTime')}:{' '}
                          {new Date(task.reviewedAt).toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
