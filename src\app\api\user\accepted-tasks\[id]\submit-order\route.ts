import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { getTaskCommission } from '@/lib/utils/commission';

// 提交订单请求验证Schema
const submitOrderSchema = z.object({
  orderNumber: z.string().min(6, '订单号至少6位'),
  orderScreenshot: z.string().optional(), // 图片文件路径
});

// 提交订单API
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;
    const userId = session.user.id;

    // 2. 解析请求数据
    const body = await request.json();
    const validation = submitOrderSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: '数据验证失败',
          details: validation.error.issues,
        },
        { status: 400 },
      );
    }

    const { orderNumber, orderScreenshot } = validation.data;

    // 3. 查找委托并验证
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            name: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 4. 验证用户权限
    if (task.accepterId !== userId) {
      return NextResponse.json(
        { error: '只能提交自己接受的委托' },
        { status: 403 },
      );
    }

    // 5. 检查委托状态
    if (task.status !== TaskStatus.IN_PROGRESS) {
      return NextResponse.json(
        { error: '只能提交进行中的委托' },
        { status: 400 },
      );
    }

    // 6. 检查是否已经超过24小时截止时间
    const deadline = task.acceptedAt
      ? new Date(task.acceptedAt.getTime() + 24 * 60 * 60 * 1000)
      : new Date(Date.now() + 24 * 60 * 60 * 1000);

    if (new Date() > deadline) {
      return NextResponse.json(
        { error: '已超过提交截止时间，委托自动过期' },
        { status: 400 },
      );
    }

    // 7. 获取系统费率配置和其他费率数据
    const [systemRate, chargebackTypes, paymentMethods] = await Promise.all([
      prisma.systemRate.findFirst(),
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    if (!systemRate) {
      return NextResponse.json({ error: '系统配置错误' }, { status: 500 });
    }

    // 8. 计算相关金额
    const totalPrice = task.unitPrice * task.quantity;
    const depositAmount = totalPrice * (systemRate.depositRatio / 100);

    // 使用真实的酬金计算函数（包含证据状态）
    const taskWithEvidenceStatus = {
      ...task,
      evidenceStatus: task.evidenceStatus?.toString(),
    };
    const commissionAmount = getTaskCommission(
      taskWithEvidenceStatus,
      chargebackTypes,
      paymentMethods,
      systemRate,
    );

    // 9. 计算物流号提交截止时间（使用时间戳方式确保精确120小时）
    const now = new Date();

    // 方案对比：
    // 当前方案：基于本地日期边界计算（可能导致时区偏差）
    // const submitDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    // const logisticsDeadline = new Date(submitDate.getTime() + 5 * 24 * 60 * 60 * 1000 + ...)

    // 新方案：直接使用时间戳计算（避免时区问题）
    const logisticsDeadline = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000); // 精确120小时后

    // 10. 使用事务处理提交订单的所有操作
    const result = await prisma.$transaction(async tx => {
      // 10.1 更新委托状态为等待物流单号，设置5天物流提交截止时间
      await tx.task.update({
        where: { id: taskId },
        data: {
          status: 'PENDING_LOGISTICS' as TaskStatus,
        },
      });

      // 单独更新订单信息和物流截止时间（传递UTC字符串避免时区转换）
      const logisticsDeadlineUTC = logisticsDeadline.toISOString();
      await tx.$executeRaw`
        UPDATE tasks 
        SET "orderNumber" = ${orderNumber}, 
            "orderScreenshot" = ${orderScreenshot},
            "logisticsDeadline" = ${logisticsDeadlineUTC}::timestamptz
        WHERE id = ${taskId}
      `;

      return {
        orderNumber,
        logisticsDeadline,
      };
    });

    // 11. 返回成功响应
    return NextResponse.json({
      success: true,
      message: `订单信息已提交！请在5天内提交物流单号`,
      data: {
        task: {
          id: taskId,
          status: 'PENDING_LOGISTICS',
          platform: task.platform?.name || 'Unknown',
          category: task.category?.name || 'Unknown',
          logisticsDeadline: result.logisticsDeadline,
        },
        order: {
          orderNumber: result.orderNumber,
          submitTime: new Date().toISOString(),
        },
        notice: {
          message:
            '请提交完整的物流单号，待发布者确认收货后，系统将自动释放押金并支付酬金',
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
