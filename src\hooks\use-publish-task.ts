import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

import { PublishTaskForm } from '@/lib/types/publish';

// 发布委托请求数据类型
interface PublishTaskData {
  // 基本信息
  productUrl: string;
  productDescription: string;
  quantity: number;
  unitPrice: number;
  listingTime: string;

  // 收货信息
  recipientName: string;
  recipientPhone: string;
  shippingAddress: string;

  // 关联信息
  platformId: string;
  categoryId: string;
  chargebackTypeIds: string[];
  paymentMethodIds: string[];

  // 证据相关
  evidenceUploadType: string;
  cartScreenshots?: string[];
  evidenceFiles?: string[];

  // 费用信息
  finalTotal: number;
}

// API响应类型
interface PublishTaskResponse {
  success: boolean;
  message: string;
  data: {
    taskId: string;
    title: string;
    finalTotal: number;
    expiresAt: string;
    remainingBalance: number;
  };
}

// 发布委托API调用函数
async function publishTask(
  data: PublishTaskData,
): Promise<PublishTaskResponse> {
  const response = await fetch('/api/tasks/publish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || '发布委托失败');
  }

  return response.json();
}

// 自定义hook
export function usePublishTask() {
  const t = useTranslations('Messages');
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: publishTask,
    onSuccess: data => {
      // 清除相关缓存
      queryClient.invalidateQueries({ queryKey: ['user-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['published-tasks'] });
      // 刷新钱包相关缓存
      queryClient.invalidateQueries({ queryKey: ['user-wallet'] });
      queryClient.invalidateQueries({ queryKey: ['wallet-transactions'] });

      // 显示成功提示（包含支付信息）
      toast.success(data.message, {
        description: `
          委托ID: ${data.data.taskId}
                        扣费: $${data.data.finalTotal.toFixed(2)}
                      余额: $${data.data.remainingBalance.toFixed(2)}
        `,
        duration: 4000,
        action: {
          label: '查看委托',
          onClick: () => router.push('/my-published-tasks'),
        },
      });

      // 延迟跳转，避免与toast冲突
      setTimeout(() => {
        router.push('/my-published-tasks');
      }, 1500);
    },
    onError: (error: Error) => {
      // 根据错误类型显示不同的提示
      if (error.message.includes('余额不足')) {
        toast.error(t('warning.lowBalance'), {
          description: error.message,
          duration: 8000,
          action: {
            label: '去充值',
            onClick: () => router.push('/wallet'),
          },
        });
      } else {
        toast.error(t('error.publish'), {
          description: error.message,
          duration: 5000,
        });
      }
    },
  });
}

// 准备发布数据的辅助函数
export function preparePublishData(
  formData: PublishTaskForm,
  finalTotal: number,
): PublishTaskData {
  // 处理文件路径
  const cartScreenshots =
    formData.cartScreenshot?.map(file => file.fileUrl) || [];
  const evidenceFiles = formData.evidenceFiles?.map(file => file.fileUrl) || [];

  const data = {
    // 基本信息
    productUrl: formData.productUrl!,
    productDescription: formData.productDescription!,
    quantity: Number(formData.quantity!),
    unitPrice: Number(formData.unitPrice!),
    listingTime: formData.listingTime!,

    // 收货信息
    recipientName: formData.recipientName!,
    recipientPhone: formData.recipientPhone!,
    shippingAddress: formData.shippingAddress!,

    // 关联信息
    platformId: formData.platform!,
    categoryId: formData.category!,
    chargebackTypeIds: formData.chargebackTypes,
    paymentMethodIds: formData.paymentMethods,

    // 证据相关
    evidenceUploadType: formData.evidenceUploadType!,
    cartScreenshots,
    evidenceFiles,

    // 费用信息
    finalTotal: Number(finalTotal),
  };

  return data;
}
