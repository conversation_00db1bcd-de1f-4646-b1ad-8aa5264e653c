const fs = require('fs');
const path = require('path');

console.log('=== 财务邮件系统快速检查 ===\n');

const files = [
  'src/lib/email-templates/deposit-success-i18n.ts',
  'src/lib/email-templates/deposit-failed-i18n.ts',
  'src/lib/email-templates/index.ts',
  'src/lib/email.ts',
  'src/app/api/send-email/route.ts',
  'src/app/api/email/preview/route.ts',
  'src/app/api/email/test/route.ts',
  'src/lib/financial-email-integration.ts',
];

let existingFiles = 0;

files.forEach(file => {
  try {
    const filePath = path.join(__dirname, '../../../', file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      console.log(`✅ ${file} (${content.length} 字符)`);
      existingFiles++;
    } else {
      console.log(`❌ ${file} - 文件不存在`);
    }
  } catch (error) {
    console.log(`❌ ${file} - 读取错误: ${error.message}`);
  }
});

console.log(`\n📊 文件检查结果: ${existingFiles}/${files.length} 个文件存在`);

if (existingFiles === files.length) {
  console.log('\n🎉 财务邮件系统文件完整！');
  console.log('\n✨ 已实现功能：');
  console.log('   • 充值成功确认邮件模板（中英文）');
  console.log('   • 充值失败通知邮件模板（中英文）');
  console.log('   • 邮件发送API集成');
  console.log('   • 邮件预览和测试功能');
  console.log('   • 财务邮件集成工具');
  console.log('   • 重试机制和错误处理');

  console.log('\n🔧 使用方式：');
  console.log(
    '   • 预览邮件: GET /api/email/preview?type=deposit-success&language=zh'
  );
  console.log('   • 测试发送: POST /api/email/test');
  console.log('   • 发送邮件: POST /api/send-email');

  console.log('\n📧 邮件类型：');
  console.log('   • deposit-success: 充值成功确认');
  console.log('   • deposit-failed: 充值失败通知');
} else {
  console.log('\n⚠️ 部分文件缺失，请检查实现');
}

console.log('\n=== 检查完成 ===');
