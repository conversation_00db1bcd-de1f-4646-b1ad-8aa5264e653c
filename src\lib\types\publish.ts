import { UploadedFile } from '@/hooks/use-file-upload';
import {
  Platform as AdminPlatform,
  Category as AdminCategory,
  ChargebackType as AdminChargebackType,
  PaymentMethod as AdminPaymentMethod,
} from '@/types/rates';

import { Platform, ChargebackType, PaymentMethod } from './task';

// 商品分类 - 保留原有枚举作为后备
export enum ProductCategory {
  ELECTRONICS = 'electronics',
  CLOTHING = 'clothing',
  HOME = 'home',
  BEAUTY = 'beauty',
  SPORTS = 'sports',
  BOOKS = 'books',
  TOYS = 'toys',
  OTHER = 'other',
}

export const PRODUCT_CATEGORY_LABELS: Record<ProductCategory, string> = {
  [ProductCategory.ELECTRONICS]: '电子产品',
  [ProductCategory.CLOTHING]: '服装鞋帽',
  [ProductCategory.HOME]: '家居用品',
  [ProductCategory.BEAUTY]: '美妆护肤',
  [ProductCategory.SPORTS]: '运动户外',
  [ProductCategory.BOOKS]: '图书音像',
  [ProductCategory.TOYS]: '玩具母婴',
  [ProductCategory.OTHER]: '其他',
};

// 证据上传类型
export enum EvidenceUploadType {
  IMMEDIATE = 'IMMEDIATE',
  LATER = 'LATER',
  NONE = 'NONE',
}

export const EVIDENCE_UPLOAD_LABELS: Record<
  EvidenceUploadType,
  { label: string }
> = {
  [EvidenceUploadType.IMMEDIATE]: { label: '立即上传' },
  [EvidenceUploadType.LATER]: { label: '稍后上传' },
  [EvidenceUploadType.NONE]: { label: '无证据' },
};

// 发布委托表单数据
export interface PublishTaskForm {
  // 步骤1: 选择平台和分类 - 支持后台配置的数据
  platform?: string; // 平台ID
  platformName?: string; // 平台名称（用于显示）
  category?: string; // 分类ID
  categoryName?: string; // 分类名称（用于显示）
  chargebackTypes: string[]; // 拒付类型ID数组
  paymentMethods: string[]; // 支付方式ID数组

  // 步骤2: 基本信息
  productUrl?: string;
  productDescription?: string;
  quantity?: number;
  unitPrice?: number;
  listingTime?: string;
  recipientName?: string;
  recipientPhone?: string;
  shippingAddress?: string;
  cartScreenshot?: UploadedFile[]; // 购物车截图（已上传）
  cartScreenshotFiles?: File[]; // 购物车截图（待上传）

  // 步骤3: 证据上传
  evidenceUploadType?: EvidenceUploadType;
  evidenceFiles?: UploadedFile[]; // 证据文件（已上传）
  evidenceUploadFiles?: File[]; // 证据文件（待上传）
}

// 费用计算结果
export interface FeeCalculation {
  productPrice: number;
  // 以下费用归属接单者（作为酬金）
  paymentMethodFee: number; // 支付方式费
  paymentMethodRate: number;
  chargebackTypeFee: number; // 拒付类型费
  chargebackTypeRate: number;
  evidenceFee: number; // 证据费
  evidenceRate: number;
  // 以下费用归属平台
  memberServiceFee: number; // 会员服务费（平台收入）
  memberServiceRate: number;
  originalTotal: number;
  memberDiscount: number;
  memberDiscountRate: number;
  finalTotal: number;
}

// 发布步骤
export enum PublishStep {
  PLATFORM_SELECTION = 1,
  BASIC_INFO = 2,
  EVIDENCE_UPLOAD = 3,
  SUMMARY = 4,
}

export const PUBLISH_STEP_LABELS: Record<PublishStep, string> = {
  [PublishStep.PLATFORM_SELECTION]: '选择平台',
  [PublishStep.BASIC_INFO]: '基本信息',
  [PublishStep.EVIDENCE_UPLOAD]: '上传证据',
  [PublishStep.SUMMARY]: '确认提交',
};
