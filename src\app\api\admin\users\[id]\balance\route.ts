import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 更新用户余额（充值/扣款）
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份和管理员权限
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { id: userId } = await params;
    const body = await request.json();
    const {
      type, // 'charge' | 'deduct'
      amount, // 金额
      reason, // 操作原因（可选）
    } = body;

    // 验证必填字段
    if (!type || !amount) {
      return NextResponse.json(
        { error: '操作类型和金额为必填项' },
        { status: 400 },
      );
    }

    if (!['charge', 'deduct'].includes(type)) {
      return NextResponse.json({ error: '无效的操作类型' }, { status: 400 });
    }

    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      return NextResponse.json({ error: '金额必须大于0' }, { status: 400 });
    }

    if (amountValue > 999999) {
      return NextResponse.json(
        { error: '金额不能超过999,999' },
        { status: 400 },
      );
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    // 如果是扣款，检查余额是否足够
    if (type === 'deduct' && existingUser.balance < amountValue) {
      return NextResponse.json({ error: '用户余额不足' }, { status: 400 });
    }

    // 计算新余额
    const newBalance =
      type === 'charge'
        ? existingUser.balance + amountValue
        : existingUser.balance - amountValue;

    // 更新用户余额
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { balance: newBalance },
    });

    // 记录交易历史（这里可以扩展为专门的交易记录表）
    // 可以在这里添加交易记录到专门的表中
    console.log(
      `用户余额操作 - 用户ID: ${userId}, 操作类型: ${type}, 金额: ${amountValue}, 操作员: ${currentUser.email}, 原因: ${reason || '无'}`,
    );

    // 返回更新后的用户信息
    return NextResponse.json(
      {
        success: true,
        message: `${type === 'charge' ? '充值' : '扣款'}成功`,
        user: {
          id: updatedUser.id,
          nickname: updatedUser.name || '',
          email: updatedUser.email || '',
          avatar: updatedUser.image || '',
          balance: updatedUser.balance,
          memberPlan: updatedUser.memberPlan,
          memberPlanExpiry: updatedUser.memberPlanExpiry?.toISOString() || '',
          status: updatedUser.status,
          completedTasks: updatedUser.completedTasks,
          publishedTasks: updatedUser.publishedTasks,
          registerDate: updatedUser.createdAt.toISOString(),
        },
        operation: {
          type,
          amount: amountValue,
          previousBalance: existingUser.balance,
          newBalance,
          reason: reason || '管理员操作',
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('用户余额操作失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
