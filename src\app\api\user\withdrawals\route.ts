import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { createWithdrawalRequestSchema } from '@/types/withdrawal';

// GET /api/user/withdrawals - 用户获取自己的提现申请列表
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const userId = (session.user as any).id;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

    // 构建查询条件
    const where: any = { userId };

    if (status && status !== 'all') {
      where.status = status;
    }

    // 获取用户的提现申请
    const withdrawalRequests = await prisma.withdrawalRequest.findMany({
      where,
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 获取总数
    const total = await prisma.withdrawalRequest.count({ where });

    // 获取统计信息
    const userStats = await prisma.withdrawalRequest.groupBy({
      by: ['status'],
      where: { userId },
      _count: {
        status: true,
      },
    });

    const statsMap = userStats.reduce(
      (acc: Record<string, number>, item: any) => {
        acc[item.status] = item._count.status;
        return acc;
      },
      {} as Record<string, number>,
    );

    return NextResponse.json({
      success: true,
      data: {
        withdrawalRequests,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
        stats: {
          total,
          pending: statsMap['PENDING'] || 0,
          approved: statsMap['APPROVED'] || 0,
          rejected: statsMap['REJECTED'] || 0,
          completed: statsMap['COMPLETED'] || 0,
        },
      },
    });
  } catch (error) {
    console.error('获取用户提现申请列表失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}

// POST /api/user/withdrawals - 用户创建提现申请
// 在POST函数中添加验证逻辑
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const userId = (session.user as any).id;
    const body = await request.json();

    // 验证提现授权token
    const { withdrawalToken, ...withdrawalData } = body;

    if (!withdrawalToken) {
      return NextResponse.json(
        { success: false, message: '请先完成邮箱验证' },
        { status: 400 },
      );
    }

    // 验证提现授权token
    const authRecord = await prisma.verificationToken.findFirst({
      where: {
        identifier: `${userId}:WITHDRAWAL_AUTH`,
        token: withdrawalToken,
        expires: {
          gt: new Date(),
        },
      },
    });

    if (!authRecord) {
      return NextResponse.json(
        { success: false, message: '邮箱验证已过期，请重新验证' },
        { status: 400 },
      );
    }

    // 删除使用过的授权token
    await prisma.verificationToken.delete({
      where: {
        identifier_token: {
          identifier: authRecord.identifier,
          token: authRecord.token,
        },
      },
    });

    // 验证请求数据
    const validatedData = createWithdrawalRequestSchema.parse(withdrawalData);

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { balance: true },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    // 检查余额是否足够
    if (user.balance < validatedData.amount) {
      return NextResponse.json(
        { success: false, message: '余额不足' },
        { status: 400 },
      );
    }

    // 获取系统费率配置
    const systemRate = await prisma.systemRate.findFirst();
    if (!systemRate) {
      return NextResponse.json(
        { success: false, message: '系统费率配置不存在' },
        { status: 500 },
      );
    }

    // 检查最低提现金额
    if (validatedData.amount < systemRate.minimumWithdrawalAmount) {
      return NextResponse.json(
        {
          success: false,
          message: `最低提现金额为$${systemRate.minimumWithdrawalAmount.toFixed(2)}`,
        },
        { status: 400 },
      );
    }

    // 计算手续费
    let feeRate = 0;
    if (['USDT_ERC20'].includes(validatedData.withdrawMethod)) {
      feeRate = systemRate.erc20WithdrawalRate;
    } else if (['USDT_TRC20'].includes(validatedData.withdrawMethod)) {
      feeRate = systemRate.trc20WithdrawalRate;
    } else {
      feeRate = systemRate.bankWithdrawalRate;
    }

    const fee = (validatedData.amount * feeRate) / 100;
    const actualAmount = validatedData.amount - fee;

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx: any) => {
      // 1. 扣除用户余额
      await tx.user.update({
        where: { id: userId },
        data: {
          balance: {
            decrement: validatedData.amount,
          },
        },
      });

      // 2. 创建钱包交易记录
      const transaction = await tx.walletTransaction.create({
        data: {
          userId,
          type: 'WITHDRAW',
          amount: -validatedData.amount, // 负数表示支出
          status: 'PENDING',
          description: `提现申请 - ${
            validatedData.withdrawMethod === 'BANK_CARD'
              ? '美元账户'
              : validatedData.withdrawMethod === 'USDT_ERC20'
                ? 'USDT ERC20'
                : validatedData.withdrawMethod === 'USDT_TRC20'
                  ? 'USDT TRC20'
                  : '其他方式'
          }（手续费${fee.toFixed(2)}）`,
          withdrawMethod:
            validatedData.withdrawMethod === 'BANK_CARD'
              ? 'BANK_CARD'
              : validatedData.withdrawMethod === 'USDT_ERC20'
                ? 'USDT_ERC20'
                : validatedData.withdrawMethod === 'USDT_TRC20'
                  ? 'USDT_TRC20'
                  : undefined,
        },
      });

      // 3. 创建提现申请
      const withdrawalRequest = await tx.withdrawalRequest.create({
        data: {
          userId,
          amount: validatedData.amount,
          fee,
          actualAmount,
          currency: 'USD',
          withdrawMethod: validatedData.withdrawMethod,

          // 美元账户信息
          accountHolder: validatedData.accountHolder,
          bankCard: validatedData.bankCard,
          bankName: validatedData.bankName,
          bankSwift: validatedData.bankSwift,
          branchAddress: validatedData.branchAddress,
          bankCountry: validatedData.bankCountry,

          // 加密货币信息
          walletAddress: validatedData.walletAddress,
          cryptoCurrency: validatedData.cryptoCurrency,
          blockchain: validatedData.blockchain,

          status: 'PENDING',
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              username: true,
            },
          },
        },
      });

      // 4. 关联交易记录ID到提现申请
      await tx.withdrawalRequest.update({
        where: { id: withdrawalRequest.id },
        data: {
          reference: transaction.id,
        },
      });

      return { withdrawalRequest, transaction };
    });

    return NextResponse.json({
      success: true,
      data: result.withdrawalRequest,
      message: '提现申请提交成功，请等待审核',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('创建提现申请失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}
