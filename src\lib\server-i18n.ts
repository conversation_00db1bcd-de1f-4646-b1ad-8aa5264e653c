import { NextRequest } from 'next/server';

// 服务器端翻译消息
const serverMessages = {
  zh: {
    // 认证相关
    loginRequired: '请先登录',
    unauthorized: '未授权访问',
    permissionDenied: '权限不足',

    // 工单相关
    ticketNotFound: '工单不存在或无权限访问',
    invalidTransition: '不允许的状态转换',
    ticketStatusUpdated: '工单状态已更新',
    ticketCreated: '工单创建成功',
    ticketClosed: '此工单已关闭，无法添加回复',

    // 通用错误
    invalidData: '请求数据格式错误',
    serverError: '服务器内部错误',
    networkError: '网络错误，请重试',
  },
  en: {
    // 认证相关
    loginRequired: 'Please login first',
    unauthorized: 'Unauthorized access',
    permissionDenied: 'Permission denied',

    // 工单相关
    ticketNotFound: 'Ticket not found or no permission to access',
    invalidTransition: 'Invalid status transition',
    ticketStatusUpdated: 'Ticket status updated successfully',
    ticketCreated: 'Ticket created successfully',
    ticketClosed: 'This ticket is closed and cannot be replied to',

    // 通用错误
    invalidData: 'Invalid request data format',
    serverError: 'Internal server error',
    networkError: 'Network error, please try again',
  },
};

/**
 * 从请求头检测用户语言偏好
 */
export function detectUserLanguage(request: NextRequest): 'zh' | 'en' {
  // 1. 检查 URL 路径中的语言设置
  const pathname = request.nextUrl.pathname;
  if (pathname.startsWith('/en')) {
    return 'en';
  }
  if (pathname.startsWith('/zh')) {
    return 'zh';
  }

  // 2. 检查 URL 参数中的语言设置
  const langParam = request.nextUrl.searchParams.get('lang');
  if (langParam === 'zh' || langParam === 'en') {
    return langParam;
  }

  // 3. 检查 Accept-Language 头
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    // 简单的语言检测逻辑
    if (acceptLanguage.includes('zh')) {
      return 'zh';
    }
    if (acceptLanguage.includes('en')) {
      return 'en';
    }
  }

  // 4. 默认返回中文
  return 'zh';
}

/**
 * 获取服务器端翻译消息
 */
export function getServerMessage(
  key: keyof typeof serverMessages.zh,
  language: 'zh' | 'en' = 'zh',
): string {
  return serverMessages[language][key] || serverMessages.zh[key];
}

/**
 * 创建带翻译的服务器端翻译器
 */
export function createServerTranslator(request: NextRequest) {
  const language = detectUserLanguage(request);

  return {
    language,
    t: (key: keyof typeof serverMessages.zh): string => {
      return getServerMessage(key, language);
    },
  };
}
