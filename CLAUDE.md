# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this
repository.

## Commands

### Development

```bash
npm run dev              # Start development server on localhost:3000
npm run build           # Production build
npm run start           # Start production server
```

### Code Quality

```bash
npm run lint            # Check linting issues
npm run lint:fix        # Auto-fix linting issues
npm run format          # Format code with Prettier
npm run fix             # Run both lint:fix and format
```

### Testing

```bash
npm test                # Run Jest tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Generate coverage report
npm run test:ci         # CI mode (no watch, with coverage)
```

### Database

```bash
npx prisma studio       # Open database GUI
npx prisma generate     # Generate Prisma client
npx prisma db push      # Push schema changes (development)
npm run db:seed         # Seed database with test data
npm run db:reset        # Reset database and reseed
```

### Internationalization

```bash
node scripts/migrate-translations.js    # Migrate translation files
node scripts/validate-translations.js   # Check translation completeness
npx tsx scripts/migrate-user-language.ts # Migrate user language data
```

### Single Test

```bash
npm test -- --testNamePattern="specific test name"
npm test -- ComponentName.test.tsx
```

## Project Documentation (`docs/`)

The `docs/` directory contains comprehensive project documentation. It is highly recommended to
consult these documents before starting work to gain a deep understanding of the project
architecture, development workflows, feature specifications, and coding standards.

Key documents include:

- `project-context.md`: High-level overview of the project, tech stack, and features.
- `system-architecture.md`: Detailed breakdown of the system architecture.
- `development-guide.md`: Instructions on setting up the development environment and common
  commands.
- `feature-modules.md`: In-depth explanation of core application modules.
- `user-roles.md`: Description of user permissions and roles.
- `testing-guide.md`: Guidelines for writing and running tests.

## Architecture Overview

### Application Type

Refundgo is a multilingual task marketplace platform where users can publish tasks (primarily
e-commerce refund/chargeback related) and other users can accept and complete them for rewards. It
includes user authentication, payment processing, membership tiers, comprehensive admin management,
and full internationalization support for Chinese and English users.

### Core Technology Stack

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **UI & Styling**: Tailwind CSS, shadcn/ui, Radix UI, Framer Motion, next-themes
- **3D Graphics**: React Three Fiber + Drei
- **State Management**: TanStack Query (server state), Zustand (client state)
- **Forms**: React Hook Form + Zod validation
- **Backend & Data**: Prisma ORM (PostgreSQL), NextAuth v5 (beta), Server Actions
- **Email**: Resend with multilingual templates
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Vitest, Playwright
- **Dev Tools**: ESLint, Prettier, Husky, lint-staged, Augment

### Authentication & Authorization

Authentication is handled by NextAuth v5 with:

- Credentials provider for email/password login
- OAuth providers (Google, GitHub configured in auth config)
- Role-based access control (USER, ADMIN)
- Session management through database
- Middleware handles route protection and admin access

Key files:

- `/src/lib/auth.ts` - NextAuth configuration
- `/src/lib/admin-auth.ts` - Admin-specific auth helpers
- `/src/middleware.ts` - Route protection and i18n routing

### Database Architecture

The Prisma schema includes these key model relationships:

**User System:**

- User (core user data)
- Account/Session (NextAuth tables)
- Wallet (user balances)
- Membership (subscription tiers)

**Task System:**

- Task (job postings)
- TaskApplication (user applications to tasks)
- TaskEvidence (submission proofs)
- TaskReview (completion reviews)
- Category/Platform/ChargebackType (task categorization)

**Financial:**

- Transaction (all financial movements)
- Withdrawal (payout requests)
- PaymentMethod/PaymentConfig (payment processing)

**Support:**

- Ticket/TicketMessage (customer support)
- Whitelist (user verification system)

### Route Architecture

**Internationalized Structure:**

```
src/app/[locale]/ (en/zh)
├── (main)/              # Public pages (homepage, auth, privacy, terms)
├── (user)/              # User dashboard (dashboard, tasks, profile, wallet, etc.)
└── payment/             # Payment processing flows (success, cancel)
```

**Admin Panel:**

```
src/app/(admin)/         # Admin-only routes (separate layout, no i18n)
```

**API Routes:**

```
src/app/api/
├── admin/               # Admin management endpoints
├── auth/                # Authentication endpoints
├── cron/                # Scheduled tasks
├── payments/            # Payment processing
├── tasks/               # Core task operations
├── user/                # User-specific operations
└── ...                  # Other utility endpoints (upload, email, etc.)
```

### Key Architectural Patterns

**Server Components First:**

- Default to Server Components for better performance
- Use 'use client' only when necessary (interactivity, browser APIs)
- Data fetching in Server Components with proper caching

**Form Handling:**

- Prefer Server Actions over API routes for mutations
- Use React Hook Form + Zod for client-side validation
- Implement proper error handling and loading states

**Database Operations:**

- All database access through Prisma
- Use transactions for complex operations
- Implement proper error handling
- Follow the repository pattern in `/src/lib/`

**Email System:**

- Resend for transactional emails
- Email templates in `/src/lib/email-templates/`
- Triggered by business logic events

**Payment Integration:**

- Multiple payment providers (NowPayments, YunPay)
- Payment manager pattern in `/src/lib/payment/`
- Webhook handling for payment status updates

### Critical Business Logic

**Task Lifecycle:**

1. DRAFT → ACTIVE (published)
2. ACTIVE → IN_PROGRESS (accepted)
3. IN_PROGRESS → PENDING_REVIEW (evidence submitted)
4. PENDING_REVIEW → COMPLETED/REJECTED (admin review)

**User Permissions:**

- Middleware enforces route protection
- Admin functions require ADMIN role
- Some operations require membership tiers

**Commission System:**

- System takes commission on completed tasks
- Commission rates configurable by admin
- Membership tiers affect commission rates

### Development Practices

**Component Organization:**

- `/src/components/ui/` - shadcn/ui components
- `/src/components/admin/` - Admin-specific components
- `/src/components/` - Shared application components

**Type Safety:**

- Strict TypeScript configuration
- Zod schemas for runtime validation
- Database types generated by Prisma

**State Management:**

- TanStack Query for server state
- React state for local UI state
- Avoid global client state when possible

**Internationalization:**

- next-intl for translations
- Messages in `/messages/` directory
- Middleware handles locale routing

**File Upload:**

- Custom upload handling in `/api/upload`
- File serving through `/api/files/[...path]`
- Security validation for file types and sizes

### Environment Variables Required

```env
DATABASE_URL                    # PostgreSQL connection
NEXTAUTH_SECRET                # JWT signing secret
NEXTAUTH_URL                   # Application URL
GOOGLE_CLIENT_ID/SECRET        # OAuth providers
RESEND_API_KEY                 # Email service
STRIPE_SECRET_KEY              # Payment processing
UPLOAD_MAX_SIZE                # File upload limits
OPENAI_API_KEY                 # AI features (optional)
```

### Common Gotchas

- Always run `npx prisma generate` after schema changes
- Use `npm run db:reset` for clean database state in development
- Server Actions require 'use server' directive
- Middleware handles both auth and i18n - order matters
- Admin routes use separate layout from user routes
- Payment webhooks require proper CORS handling
