'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import {
  ChevronDown,
  Languages,
  Menu,
  X,
  Shield,
  Zap,
  Users,
  TrendingUp,
  Upload,
  Search,
  CheckCircle,
  DollarSign,
  Play,
  Star,
  Crown,
  BarChart3,
  MessageCircle,
  Mail,
  Phone,
  MapPin,
  Building,
  Tag,
  FileText,
  ArrowRight,
  Twitter,
  Facebook,
  Instagram,
  Linkedin,
  Github,
  Globe,
  Heart,
  Award,
  Clock,
  Sparkles,
  Target,
} from 'lucide-react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useEffect } from 'react';

import { BrandIntroSection } from '@/components/brand-intro-section';
import { ChatWidget } from '@/components/chat-widget';
import { Comments } from '@/components/comments';
import { CoreFeaturesSection } from '@/components/core-features-section';
import { FAQSection } from '@/components/faq-section';
import { FinalCTASection } from '@/components/final-cta-section';
import { Footer } from '@/components/footer';
import { HeroSection } from '@/components/hero-section';
import { LanguageSwitcher } from '@/components/language-switcher';
import { MembershipSection } from '@/components/membership-section';
import { ModernNavbar } from '@/components/modern-navbar';
import { ModernProcessSection } from '@/components/modern-process-section';
import { ProcessSection } from '@/components/process-section';
import { SkipToMainContent } from '@/components/skip-to-main-content';
import { StatsTrustSection } from '@/components/stats-trust-section';
import { TaskHallSection } from '@/components/task-hall-section';
import TestimonialsCarousel, {
  TestimonialsDoubleRowScroll,
  TestimonialsVerticalScroll,
} from '@/components/testimonials-carousel';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import UserAccountNav from '@/components/UserAccountNav';
import { VideoTutorialSection } from '@/components/video-tutorial-section';
import { getTestimonials } from '@/data/testimonials';
import { Link, useRouter } from '@/i18n/navigation';
import { useKeyboardNavigation, useRTLSupport } from '@/lib/accessibility';
// import './homepage-refactored.css';
// import '@/styles/dhgate-theme.css';

// 动态导入 3D 背景组件，避免 SSR 问题
const StarBackground = dynamic(() => import('./StarBackground'), {
  ssr: false,
  loading: () => <div className='fixed inset-0 -z-10 bg-background' />,
});

// 简洁的背景组件
function SimpleBackground() {
  return (
    <div className='fixed inset-0 -z-10'>
      <StarBackground />
    </div>
  );
}



export default function HomePage() {
  const { data: session } = useSession();
  const locale = useLocale();

  // 启用键盘导航支持
  useKeyboardNavigation();

  // 启用RTL语言支持
  const { isRTL } = useRTLSupport(locale);

  return (
    <div className={`homepage-theme ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className='homepage-body'>
        {/* 跳转到主内容的无障碍链接 */}
        <SkipToMainContent />

        <main
          id="main-content"
          className='relative'
          role="main"
          aria-label="主要内容"
          tabIndex={-1}
        >
          {/* 简洁的背景装饰元素 */}
          <div className='brand-bg' aria-hidden="true" />
          <div className='tech-grid' aria-hidden="true" />

          <SimpleBackground />
          <ModernNavbar showMenuItems={true} />
          <HeroSection />
          <BrandIntroSection />
          <StatsTrustSection />
          <CoreFeaturesSection />
          <ModernProcessSection />
          <VideoTutorialSection />
          <TaskHallSection isAuthenticated={session?.user ? true : false} />
          <Comments />
          <MembershipSection />
          <FAQSection />
          <FinalCTASection />
          <Footer />
        </main>
      </div>
    </div>
  );
}
