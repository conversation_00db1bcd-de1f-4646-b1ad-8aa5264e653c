import { z } from 'zod';

// 会员套餐接口
export interface MembershipPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  maxTasks: number | null;
  taskTypes: string[];
  platformRate: number;
  whitelistSlots: number;
  supportLevel: string;
  features: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 创建会员套餐验证模式
export const CreateMembershipPlanSchema = z.object({
  name: z.string().min(1, '套餐名称不能为空'),
  price: z.number().min(0, '价格不能为负数'),
  period: z.string().min(1, '计费周期不能为空'),
  maxTasks: z.number().nullable().optional(),
  taskTypes: z.array(z.string()),
  platformRate: z.number().min(0, '平台费率不能为负数'),
  whitelistSlots: z.number().int().min(0, '白名单名额不能为负数'),
  supportLevel: z.string().min(1, '客服支持级别不能为空'),
  features: z.array(z.string()),
  isActive: z.boolean(),
});

// 更新会员套餐验证模式
export const UpdateMembershipPlanSchema = CreateMembershipPlanSchema.partial();

export type CreateMembershipPlanInput = z.infer<
  typeof CreateMembershipPlanSchema
>;
export type UpdateMembershipPlanInput = z.infer<
  typeof UpdateMembershipPlanSchema
>;

// 委托类型选项
export const TASK_TYPES = ['基本委托类型', '纠纷委托类型'] as const;

// 客服支持级别选项
export const SUPPORT_LEVELS = [
  '普通客服支持',
  '优先客服支持',
  'VIP专属客服支持',
  '24/7专属客服支持',
] as const;
