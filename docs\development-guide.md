# Development Guide

## Quick Start

```bash
# Install dependencies
npm install

# Setup database
npx prisma generate
npx prisma db push
npm run db:seed

# Start development server
npm run dev

# Optional: Run i18n migration scripts
node scripts/migrate-translations.js
node scripts/validate-translations.js
```

## Tech Stack Overview

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **UI & Styling**: Tailwind CSS, shadcn/ui, Radix UI, Framer Motion, next-themes
- **3D Graphics**: React Three Fiber + Drei
- **State Management**: TanStack Query (server state), Zustand (client state)
- **Forms**: React Hook Form + Zod validation
- **Backend & Data**: Prisma ORM (PostgreSQL), NextAuth v5 (beta), Server Actions
- **Email**: Resend with multilingual templates
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Vitest, Playwright
- **Dev Tools**: E<PERSON><PERSON>, Prettier, <PERSON><PERSON>, lint-staged, Augment

## Common Commands

```bash
# Development
npm run dev              # Start dev server (localhost:3000)
npm run build           # Production build
npm run start           # Start production server

# Code Quality
npm run lint            # Check linting issues
npm run lint:fix        # Auto-fix linting issues
npm run format          # Format code with Prettier
npm run format:check    # Check formatting
npm run fix             # Fix linting + formatting

# Testing
npm test                # Run tests
npm run test:watch      # Watch mode
npm run test:coverage   # Coverage report
npm run test:ci         # CI mode (no watch, coverage)

# Database
npx prisma studio       # Database GUI
npx prisma generate     # Generate Prisma client
npx prisma db push      # Push schema changes
npm run db:seed         # Seed database
npm run db:reset        # Reset + seed

# Git Hooks
npm run prepare         # Setup husky hooks
```

## Project Structure

```
src/
├── app/
│   ├── (admin)/                 # Admin panel routes
│   ├── [locale]/                # Internationalized routes (en, zh)
│   │   ├── (main)/              # Public routes
│   │   ├── (user)/              # User dashboard routes
│   │   └── payment/             # Payment flow routes
│   ├── api/                     # API routes & webhooks
│   └── ...
├── components/                  # Reusable UI components
│   ├── ui/                      # shadcn/ui components
│   └── ...
├── hooks/                       # Custom React hooks
├── i18n/                        # next-intl configuration
├── lib/                         # Utilities, config, core logic
└── middleware.ts                # i18n and auth middleware
messages/                        # Translation files
prisma/                          # Database schema
public/                          # Static assets
scripts/                         # Utility scripts
__tests__/                        # Tests
```

## Development Patterns

### Server Components (Default)

```typescript
// app/dashboard/page.tsx
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"

export default async function DashboardPage() {
  const session = await auth()

  // Direct database query in Server Component
  const tasks = await db.task.findMany({
    where: { userId: session?.user.id },
    orderBy: { createdAt: 'desc' }
  })

  return (
    <div>
      <h1>Dashboard</h1>
      <TaskList tasks={tasks} />
    </div>
  )
}
```

### Client Components

```typescript
'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'

export function InteractiveComponent() {
  const [filter, setFilter] = useState('')

  const { data, isLoading } = useQuery({
    queryKey: ['tasks', filter],
    queryFn: () => fetch(`/api/tasks?filter=${filter}`).then(r => r.json())
  })

  return (
    <div>
      <input
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
      />
      {isLoading ? 'Loading...' : <TaskGrid tasks={data} />}
    </div>
  )
}
```

### API Route Pattern

```typescript
// app/api/tasks/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';

const createTaskSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(10),
  reward: z.number().positive(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, reward } = createTaskSchema.parse(body);

    const task = await db.task.create({
      data: {
        title,
        description,
        reward,
        userId: session.user.id,
      },
    });

    return NextResponse.json(task);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 });
    }

    console.error('Task creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### Form Handling with Server Actions

```typescript
// app/(user)/publish/page.tsx
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { redirect } from "next/navigation"
import { createTaskSchema } from "@/lib/validations/task"

async function createTask(formData: FormData) {
  'use server'

  const session = await auth()
  if (!session?.user) {
    throw new Error("Unauthorized")
  }

  try {
    const data = createTaskSchema.parse({
      title: formData.get('title'),
      description: formData.get('description'),
      reward: Number(formData.get('reward'))
    })

    await db.task.create({
      data: {
        ...data,
        userId: session.user.id
      }
    })

    redirect('/my-published-tasks')
  } catch (error) {
    // Handle validation errors
    throw error
  }
}

export default function PublishPage() {
  return (
    <form action={createTask}>
      <input name="title" required />
      <textarea name="description" required />
      <input name="reward" type="number" required />
      <button type="submit">Publish Task</button>
    </form>
  )
}
```

### Component Creation Pattern

```typescript
// components/ui/task-card.tsx
import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

interface TaskCardProps {
  task: {
    id: string
    title: string
    description: string
    reward: number
    status: string
  }
  className?: string
  onAccept?: (taskId: string) => void
}

export function TaskCard({ task, className, onAccept }: TaskCardProps) {
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{task.title}</CardTitle>
          <Badge variant={task.status === 'active' ? 'default' : 'secondary'}>
            {task.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">{task.description}</p>
        <div className="flex items-center justify-between">
          <span className="font-semibold text-primary">
            ${task.reward}
          </span>
          {onAccept && (
            <Button onClick={() => onAccept(task.id)}>
              Accept Task
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
```

### Database Operations

```typescript
// lib/data/tasks.ts
import { db } from '@/lib/db';
import { Prisma } from '@prisma/client';

export async function getAvailableTasks(userId: string) {
  return await db.task.findMany({
    where: {
      status: 'ACTIVE',
      userId: { not: userId }, // Don't show own tasks
      applications: {
        none: { userId }, // Not already applied
      },
    },
    include: {
      user: {
        select: { name: true, avatar: true },
      },
      category: true,
      platform: true,
    },
    orderBy: { createdAt: 'desc' },
  });
}

export async function acceptTask(taskId: string, userId: string) {
  return await db.$transaction(async tx => {
    // Check if task is still available
    const task = await tx.task.findUnique({
      where: { id: taskId },
      include: { applications: true },
    });

    if (!task || task.status !== 'ACTIVE') {
      throw new Error('Task not available');
    }

    // Create application
    const application = await tx.taskApplication.create({
      data: {
        taskId,
        userId,
        status: 'ACCEPTED',
      },
    });

    // Update task status
    await tx.task.update({
      where: { id: taskId },
      data: { status: 'IN_PROGRESS' },
    });

    return application;
  });
}
```

## Testing Patterns

### Component Testing

```typescript
// __tests__/components/task-card.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { TaskCard } from '@/components/ui/task-card'

const mockTask = {
  id: '1',
  title: 'Test Task',
  description: 'Test description',
  reward: 100,
  status: 'active'
}

describe('TaskCard', () => {
  it('renders task information correctly', () => {
    render(<TaskCard task={mockTask} />)

    expect(screen.getByText('Test Task')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
    expect(screen.getByText('$100')).toBeInTheDocument()
  })

  it('calls onAccept when accept button is clicked', () => {
    const onAccept = jest.fn()
    render(<TaskCard task={mockTask} onAccept={onAccept} />)

    fireEvent.click(screen.getByText('Accept Task'))
    expect(onAccept).toHaveBeenCalledWith('1')
  })
})
```

### API Route Testing

```typescript
// __tests__/api/tasks.test.ts
import { POST } from '@/app/api/tasks/route';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/db');

describe('/api/tasks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('creates a task for authenticated user', async () => {
    // Mock authenticated user
    (auth as jest.Mock).mockResolvedValue({
      user: { id: 'user1' },
    });

    // Mock database response
    const mockTask = { id: 'task1', title: 'Test Task' };
    (db.task.create as jest.Mock).mockResolvedValue(mockTask);

    const request = new Request('http://localhost', {
      method: 'POST',
      body: JSON.stringify({
        title: 'Test Task',
        description: 'Test description',
        reward: 100,
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual(mockTask);
  });
});
```

## Environment Setup

### Required Environment Variables

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/refundgo"

# NextAuth v5
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
AUTH_TRUST_HOST="true"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email
RESEND_API_KEY="re_your-resend-key"
FROM_EMAIL="<EMAIL>"

# Payments
STRIPE_SECRET_KEY="sk_test_your-stripe-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-key"

# File Upload
UPLOAD_MAX_SIZE="10485760"  # 10MB
STORAGE_PATH="./public/uploads"

# AI Features (Optional)
OPENAI_API_KEY="sk-your-openai-key"
```

### Local Development Setup

1. **Database Setup**:

   ```bash
   # Using Docker (recommended)
   docker run --name refundgo-db \
     -e POSTGRES_DB=refundgo \
     -e POSTGRES_USER=refundgo \
     -e POSTGRES_PASSWORD=password \
     -p 5432:5432 -d postgres:14

   # Or install PostgreSQL locally
   ```

2. **Environment Configuration**:

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your values
   ```

3. **Database Migration**:

   ```bash
   npx prisma migrate dev
   npm run db:seed
   ```

## Code Quality Guidelines

### ESLint Configuration

Key rules enforced:

- TypeScript strict mode
- React hooks rules
- Import/export consistency
- No unused variables
- Consistent code formatting

### Prettier Configuration

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80
}
```

### Git Workflow

1. **Pre-commit Hooks** (Husky + lint-staged):
   - Runs ESLint on staged files
   - Formats code with Prettier
   - Prevents commits with errors

2. **Commit Convention**:

   ```
   feat(component): add new task card component
   fix(api): resolve authentication error
   docs(readme): update setup instructions
   ```

3. **Branch Naming**:

   ```
   feature/task-management
   bugfix/payment-processing
   hotfix/security-patch
   ```

## Performance Considerations

### Next.js Optimizations

- **Server Components**: Default for better performance
- **Image Optimization**: Use `next/image` for all images
- **Font Optimization**: Use `next/font` for custom fonts
- **Bundle Analysis**: Monitor with `npm run build`

### Database Optimizations

- **Prisma Relations**: Use `include` and `select` strategically
- **Connection Pooling**: Configure for production
- **Query Optimization**: Use database indexes

### Caching Strategy

- **Static Generation**: For public pages
- **ISR**: For dynamic content with caching
- **TanStack Query**: For client-side caching

## Deployment

### Production Build

```bash
# Build application
npm run build

# Start production server
npm run start
```

### Database Deployment

```bash
# Run migrations
npx prisma migrate deploy

# Generate client
npx prisma generate
```

### Environment Variables

Ensure all production environment variables are configured:

- Database connection string
- Authentication secrets
- Payment provider keys
- Email service credentials

## Troubleshooting

### Common Issues

1. **Prisma Client Errors**:

   ```bash
   npx prisma generate
   npx prisma db push
   ```

2. **TypeScript Errors**:

   ```bash
   npm run lint:fix
   # Check tsconfig.json
   ```

3. **Build Errors**:

   ```bash
   rm -rf .next
   npm run build
   ```

4. **Database Connection**:
   - Check DATABASE_URL format
   - Verify database is running
   - Check network connectivity

### Performance Debugging

1. **Bundle Analysis**:

   ```bash
   npm run build -- --analyze
   ```

2. **Development Tools**:
   - React Developer Tools
   - Next.js DevTools
   - Prisma Studio

## Internationalization (i18n)

### Overview

The project uses next-intl for comprehensive internationalization support with Chinese (zh) and
English (en) locales.

### Key Features

- **Locale Routing**: `/zh/dashboard` and `/en/dashboard`
- **User Language Tracking**: Automatic detection and database storage
- **Multilingual Emails**: Language-specific email templates
- **Modular Translations**: Organized by feature modules

### Translation Files Structure

```
messages/
├── en.json                 # Main English translations
├── zh.json                 # Main Chinese translations
├── en/                     # English modules
│   ├── common.json         # Shared UI text
│   ├── homepage.json       # Homepage content
│   ├── dashboard.json      # Dashboard content
│   └── [other modules]
└── zh/                     # Chinese modules
    ├── common.json         # 共享界面文本
    ├── homepage.json       # 首页内容
    └── [other modules]
```

### Development Commands

```bash
# Validate translation completeness
node scripts/validate-translations.js

# Migrate translation files
node scripts/migrate-translations.js

# Migrate user language data
npx tsx scripts/migrate-user-language.ts
```

### Usage in Components

```tsx
import { useTranslations } from 'next-intl';

export default function MyComponent() {
  const t = useTranslations('Common');
  return <h1>{t('welcome')}</h1>;
}
```

### Email Internationalization

```tsx
import { getUserRegistrationLanguage } from '@/lib/user-language';
import { verificationCodeTemplateI18n } from '@/lib/email-templates/verification-code-i18n';

const userLanguage = await getUserRegistrationLanguage(email);
const emailContent = verificationCodeTemplateI18n(code, userLanguage);
```

## Best Practices

1. **Type Safety**: Always use TypeScript interfaces
2. **Error Handling**: Implement proper error boundaries
3. **Security**: Validate all inputs with Zod
4. **Performance**: Use Server Components by default
5. **Testing**: Write tests for critical functionality
6. **Documentation**: Keep docs updated with changes
