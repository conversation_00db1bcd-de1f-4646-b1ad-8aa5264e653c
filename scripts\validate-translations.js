#!/usr/bin/env node

/**
 * 翻译文件验证脚本
 * 检查英文和中文翻译文件的一致性
 */

const fs = require('fs');
const path = require('path');

const LOCALES = ['en', 'zh'];
const MESSAGES_DIR = 'messages';

/**
 * 递归获取对象的所有键路径
 */
function getObjectKeys(obj, prefix = '') {
  const keys = [];

  for (const [key, value] of Object.entries(obj)) {
    const currentPath = prefix ? `${prefix}.${key}` : key;

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...getObjectKeys(value, currentPath));
    } else {
      keys.push(currentPath);
    }
  }

  return keys;
}

/**
 * 加载翻译文件
 */
function loadTranslationFile(locale, filename) {
  const filePath = path.join(MESSAGES_DIR, locale, filename);

  if (!fs.existsSync(filePath)) {
    return null;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ 解析文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 验证单个翻译文件
 */
function validateTranslationFile(filename) {
  console.log(`\n📄 验证文件: ${filename}`);

  const translations = {};
  const allKeys = {};

  // 加载所有语言的翻译文件
  for (const locale of LOCALES) {
    translations[locale] = loadTranslationFile(locale, filename);

    if (translations[locale]) {
      allKeys[locale] = new Set(getObjectKeys(translations[locale]));
    } else {
      console.warn(`⚠️  ${locale} 语言的 ${filename} 文件不存在或无法解析`);
      allKeys[locale] = new Set();
    }
  }

  // 检查键的一致性
  const enKeys = allKeys.en || new Set();
  const zhKeys = allKeys.zh || new Set();

  // 找出缺失的键
  const missingInZh = [...enKeys].filter(key => !zhKeys.has(key));
  const missingInEn = [...zhKeys].filter(key => !enKeys.has(key));

  let hasIssues = false;

  if (missingInZh.length > 0) {
    console.error(`❌ 中文翻译中缺失的键 (${missingInZh.length} 个):`);
    missingInZh.forEach(key => console.error(`   - ${key}`));
    hasIssues = true;
  }

  if (missingInEn.length > 0) {
    console.error(`❌ 英文翻译中缺失的键 (${missingInEn.length} 个):`);
    missingInEn.forEach(key => console.error(`   - ${key}`));
    hasIssues = true;
  }

  if (!hasIssues) {
    console.log(`✅ ${filename} 翻译完整 (${enKeys.size} 个键)`);
  }

  return {
    filename,
    enKeys: enKeys.size,
    zhKeys: zhKeys.size,
    missingInZh: missingInZh.length,
    missingInEn: missingInEn.length,
    hasIssues,
  };
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始验证翻译文件...');
  console.log(`📂 翻译目录: ${MESSAGES_DIR}`);
  console.log(`🌐 支持语言: ${LOCALES.join(', ')}`);

  // 获取所有翻译文件
  const enDir = path.join(MESSAGES_DIR, 'en');
  if (!fs.existsSync(enDir)) {
    console.error(`❌ 英文翻译目录不存在: ${enDir}`);
    process.exit(1);
  }

  const translationFiles = fs
    .readdirSync(enDir)
    .filter(file => file.endsWith('.json'))
    .sort();

  console.log(`📋 找到 ${translationFiles.length} 个翻译文件`);

  const results = [];
  let totalIssues = 0;

  // 验证每个文件
  for (const filename of translationFiles) {
    const result = validateTranslationFile(filename);
    results.push(result);

    if (result.hasIssues) {
      totalIssues++;
    }
  }

  // 输出总结
  console.log('\n📊 验证总结:');
  console.log('='.repeat(60));

  results.forEach(result => {
    const status = result.hasIssues ? '❌' : '✅';
    const issues = result.hasIssues
      ? ` (缺失: zh=${result.missingInZh}, en=${result.missingInEn})`
      : '';

    console.log(
      `${status} ${result.filename.padEnd(25)} EN:${result.enKeys.toString().padStart(3)} ZH:${result.zhKeys.toString().padStart(3)}${issues}`
    );
  });

  console.log('='.repeat(60));

  if (totalIssues === 0) {
    console.log('🎉 所有翻译文件验证通过！');
    console.log(
      `📈 总计: ${results.length} 个文件，${results.reduce((sum, r) => sum + r.enKeys, 0)} 个翻译键`
    );
  } else {
    console.error(`❌ 发现 ${totalIssues} 个文件存在翻译问题`);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  validateTranslationFile,
  getObjectKeys,
  loadTranslationFile,
};
