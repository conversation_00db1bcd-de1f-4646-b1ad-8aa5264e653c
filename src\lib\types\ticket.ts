import { z } from 'zod';

// 枚举类型
export enum TicketType {
  TECHNICAL = 'TECHNICAL',
  BILLING = 'BILLING',
  ACCOUNT = 'ACCOUNT',
  FEATURE_REQUEST = 'FEATURE_REQUEST',
  BUG_REPORT = 'BUG_REPORT',
  OTHER = 'OTHER',
}

export enum TicketPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export enum TicketStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED',
}

// 工单类型标签
export const TICKET_TYPE_LABELS = {
  FEATURE_REQUEST: '发布委托',
  BUG_REPORT: '承接委托',
  TECHNICAL: '技术支持',
  BILLING: '账单问题',
  ACCOUNT: '账户问题',
  OTHER: '其他',
} as const;

// 工单优先级标签
export const TICKET_PRIORITY_LABELS = {
  LOW: '低',
  MEDIUM: '中',
  HIGH: '高',
  URGENT: '紧急',
} as const;

// 工单状态标签
export const TICKET_STATUS_LABELS = {
  PENDING: '待处理',
  IN_PROGRESS: '处理中',
  RESOLVED: '已解决',
  CLOSED: '已关闭',
  CANCELLED: '已取消',
} as const;

// 创建工单验证模式的工厂函数
export const createTicketSchema = (t: (key: string) => string) =>
  z.object({
    title: z
      .string()
      .min(1, t('validation.required'))
      .max(200, t('validation.maxLength').replace('{max}', '200')),
    description: z
      .string()
      .min(10, t('validation.minLength').replace('{min}', '10'))
      .max(5000, t('validation.maxLength').replace('{max}', '5000')),
    type: z.enum([
      'TECHNICAL',
      'BILLING',
      'ACCOUNT',
      'FEATURE_REQUEST',
      'BUG_REPORT',
      'OTHER',
    ]),
    priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  });

// 创建工单回复验证模式的工厂函数
export const createReplyTicketSchema = (t: (key: string) => string) =>
  z.object({
    content: z
      .string()
      .min(1, t('validation.required'))
      .max(5000, t('validation.maxLength').replace('{max}', '5000')),
  });

// 向后兼容的默认模式
export const defaultCreateTicketSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符'),
  description: z
    .string()
    .min(10, '描述至少需要10个字符')
    .max(5000, '描述不能超过5000个字符'),
  type: z.enum([
    'TECHNICAL',
    'BILLING',
    'ACCOUNT',
    'FEATURE_REQUEST',
    'BUG_REPORT',
    'OTHER',
  ]),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
});

export const replyTicketSchema = z.object({
  content: z
    .string()
    .min(1, '回复内容不能为空')
    .max(5000, '回复内容不能超过5000个字符'),
});

// 工单状态更新验证
export const updateTicketStatusSchema = z.object({
  status: z.enum(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'CANCELLED']),
});

// 工单分配验证
export const assignTicketSchema = z.object({
  assignedTo: z.string().min(1, '必须指定分配的用户').nullable(),
});

// 工单查询参数验证
export const ticketQuerySchema = z.object({
  page: z.coerce.number().min(1),
  limit: z.coerce.number().min(1).max(100),
  search: z
    .string()
    .optional()
    .nullable()
    .transform(val => val || undefined),
  type: z
    .enum([
      'TECHNICAL',
      'BILLING',
      'ACCOUNT',
      'FEATURE_REQUEST',
      'BUG_REPORT',
      'OTHER',
    ])
    .optional()
    .nullable()
    .transform(val => val || undefined),
  status: z
    .enum(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'CANCELLED'])
    .optional()
    .nullable()
    .transform(val => val || undefined),
  priority: z
    .enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT'])
    .optional()
    .nullable()
    .transform(val => val || undefined),
  sortBy: z.enum(['createdAt', 'updatedAt', 'priority', 'status']),
  sortOrder: z.enum(['asc', 'desc']),
});

// 响应类型
export interface TicketWithUser {
  id: string;
  title: string;
  description: string;
  type: string;
  priority: string;
  status: string;
  userId: string;
  assignedTo: string | null;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt: Date | null;
  closedAt: Date | null;
  user: {
    id: string;
    name: string | null;
    email: string | null;
  };
  assignee?: {
    id: string;
    name: string | null;
    email: string | null;
  } | null;
  replies: TicketReplyWithAuthor[];
  _count?: {
    replies: number;
  };
}

export interface TicketReplyWithAuthor {
  id: string;
  content: string;
  isStaff: boolean;
  authorId: string;
  authorName: string;
  createdAt: Date;
  author: {
    id: string;
    name: string | null;
    email: string | null;
  };
}

export interface TicketListResponse {
  tickets: TicketWithUser[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface TicketStatsResponse {
  totalTickets: number;
  pendingTickets: number;
  inProgressTickets: number;
  resolvedTickets: number;
  closedTickets: number;
  cancelledTickets: number;
  unassignedTickets: number;
}

// 表单类型
export type CreateTicketInput = z.infer<typeof defaultCreateTicketSchema>;
export type ReplyTicketInput = z.infer<typeof replyTicketSchema>;
export type UpdateTicketStatusInput = z.infer<typeof updateTicketStatusSchema>;
export type AssignTicketInput = z.infer<typeof assignTicketSchema>;
export type TicketQueryParams = z.infer<typeof ticketQuerySchema>;
