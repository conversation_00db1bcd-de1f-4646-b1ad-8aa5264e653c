'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState, useCallback } from 'react';

interface CSRFTokenData {
  token: string;
  expires: number;
}

export function useCSRF() {
  const { data: session, status } = useSession();
  const [csrfToken, setCSRFToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取 CSRF Token
  const fetchCSRFToken = useCallback(async () => {
    if (status !== 'authenticated') {
      setCSRFToken(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch CSRF token: ${response.statusText}`);
      }

      const data: CSRFTokenData = await response.json();
      setCSRFToken(data.token);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to fetch CSRF token:', err);
    } finally {
      setIsLoading(false);
    }
  }, [status]);

  // 初始化和会话变化时获取 token
  useEffect(() => {
    fetchCSRFToken();
  }, [fetchCSRFToken]);

  // 创建带有 CSRF 保护的 fetch 函数
  const csrfFetch = useCallback(
    async (url: string, options: RequestInit = {}) => {
      if (!csrfToken) {
        throw new Error('CSRF token not available');
      }

      const headers = new Headers(options.headers);
      headers.set('x-csrf-token', csrfToken);
      headers.set('Content-Type', 'application/json');

      return fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });
    },
    [csrfToken],
  );

  // 创建带有 CSRF 保护的表单提交函数
  const csrfFormSubmit = useCallback(
    async (url: string, formData: FormData | Record<string, any>) => {
      if (!csrfToken) {
        throw new Error('CSRF token not available');
      }

      let body: FormData | string;
      let contentType: string;

      if (formData instanceof FormData) {
        formData.append('_csrf', csrfToken);
        body = formData;
        contentType = 'multipart/form-data';
      } else {
        body = JSON.stringify({ ...formData, _csrf: csrfToken });
        contentType = 'application/json';
      }

      const headers: Record<string, string> = {
        'x-csrf-token': csrfToken,
      };

      // 只有在不是 FormData 时才设置 Content-Type
      if (!(formData instanceof FormData)) {
        headers['Content-Type'] = contentType;
      }

      return fetch(url, {
        method: 'POST',
        headers,
        body,
        credentials: 'include',
      });
    },
    [csrfToken],
  );

  // 刷新 CSRF Token
  const refreshToken = useCallback(() => {
    fetchCSRFToken();
  }, [fetchCSRFToken]);

  return {
    csrfToken,
    isLoading,
    error,
    csrfFetch,
    csrfFormSubmit,
    refreshToken,
    isReady: !!csrfToken && !isLoading,
  };
}

// CSRF 保护的 API 调用 Hook
export function useCSRFAPI() {
  const { csrfFetch, isReady, error } = useCSRF();

  const apiCall = useCallback(
    async <T = any>(url: string, options: RequestInit = {}): Promise<T> => {
      if (!isReady) {
        throw new Error('CSRF protection not ready');
      }

      const response = await csrfFetch(url, options);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `API call failed: ${response.statusText}`,
        );
      }

      return response.json();
    },
    [csrfFetch, isReady],
  );

  const post = useCallback(
    <T = any>(url: string, data?: any): Promise<T> =>
      apiCall<T>(url, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      }),
    [apiCall],
  );

  const put = useCallback(
    <T = any>(url: string, data?: any): Promise<T> =>
      apiCall<T>(url, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      }),
    [apiCall],
  );

  const patch = useCallback(
    <T = any>(url: string, data?: any): Promise<T> =>
      apiCall<T>(url, {
        method: 'PATCH',
        body: data ? JSON.stringify(data) : undefined,
      }),
    [apiCall],
  );

  const del = useCallback(
    <T = any>(url: string): Promise<T> =>
      apiCall<T>(url, {
        method: 'DELETE',
      }),
    [apiCall],
  );

  return {
    get: apiCall,
    post,
    put,
    patch,
    delete: del,
    isReady,
    error,
  };
}

// CSRF 保护的表单 Hook
export function useCSRFForm() {
  const { csrfFormSubmit, csrfToken, isReady } = useCSRF();

  const submitForm = useCallback(
    async (url: string, formData: FormData | Record<string, any>) => {
      if (!isReady) {
        throw new Error('CSRF protection not ready');
      }

      const response = await csrfFormSubmit(url, formData);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Form submission failed: ${response.statusText}`,
        );
      }

      return response.json();
    },
    [csrfFormSubmit, isReady],
  );

  return {
    submitForm,
    csrfToken,
    isReady,
  };
}
