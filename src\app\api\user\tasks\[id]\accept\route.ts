import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份
    const session = await getAuthSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const userId = session.user.id;
    const resolvedParams = await params;
    const taskId = resolvedParams.id;

    // 查找委托并验证状态
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: true,
        accepter: true,
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 验证委托状态 - 只有招募中的委托可以被接受
    if (task.status !== 'RECRUITING') {
      return NextResponse.json(
        { error: '该委托当前无法接受' },
        { status: 400 },
      );
    }

    // 验证委托是否已经被接受
    if (task.accepterId) {
      return NextResponse.json(
        { error: '该委托已被其他用户接受' },
        { status: 400 },
      );
    }

    // 验证用户不能接受自己发布的委托
    if (task.publisherId === userId) {
      return NextResponse.json(
        { error: '不能接受自己发布的委托' },
        { status: 400 },
      );
    }

    // 检查委托是否已过期
    if (task.expiresAt && new Date(task.expiresAt) < new Date()) {
      return NextResponse.json({ error: '委托已过期' }, { status: 400 });
    }

    // 获取用户账户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    // 计算押金金额（商品总价 * 押金比例）
    const systemRate = await prisma.systemRate.findFirst();
    const depositRatio = systemRate?.depositRatio || 10.0; // 默认10%
    const depositAmount = task.totalAmount * (depositRatio / 100);

    // 检查用户余额是否足够支付押金
    if (user.balance < depositAmount) {
      return NextResponse.json(
        {
          error: '账户余额不足',
          details: `需要押金 $${depositAmount.toFixed(2)}，当前余额 $${user.balance.toFixed(2)}`,
        },
        { status: 400 },
      );
    }

    // 使用事务处理：冻结押金和更新委托状态
    const result = await prisma.$transaction(async tx => {
      // 1. 冻结用户押金
      await tx.user.update({
        where: { id: userId },
        data: {
          balance: {
            decrement: depositAmount,
          },
          frozenAmount: {
            increment: depositAmount,
          },
        },
      });

      // 2. 更新委托状态和接受者信息
      const updatedTask = await tx.task.update({
        where: { id: taskId },
        data: {
          status: 'IN_PROGRESS',
          accepterId: userId,
          acceptedAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // 3. 创建钱包交易记录（押金冻结）
      await tx.walletTransaction.create({
        data: {
          userId,
          type: 'TASK_FEE',
          amount: -depositAmount,
          status: 'COMPLETED',
          description: `接受委托押金冻结 - ${task.title || task.productDescription.slice(0, 20)}...`,
          reference: taskId,
        },
      });

      return updatedTask;
    });

    return NextResponse.json({
      success: true,
      message: '委托接受成功！押金已冻结，请在24小时内提交订单信息',
      data: {
        taskId: result.id,
        status: result.status,
        acceptedAt: result.acceptedAt,
        depositAmount,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
