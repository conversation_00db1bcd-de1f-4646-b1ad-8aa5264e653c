'use client';

import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import React from 'react';

import { Button } from '@/components/ui/button';

interface MainErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Main page area error boundary component
 * Handles errors in main pages (homepage, login, register, etc.)
 */
export default function MainError({ error, reset }: MainErrorProps) {
  const t = useTranslations('common.mainError');

  React.useEffect(() => {
    // Log main page errors
    console.error('Main area error:', error);
  }, [error]);

  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center'>
        <div className='mb-6'>
          <div className='mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20'>
            <AlertTriangle className='h-8 w-8 text-orange-600' />
          </div>
        </div>

        <h1 className='mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100'>
          {t('title')}
        </h1>

        <p className='mb-6 text-gray-600 dark:text-gray-400'>
          {t('description')}
        </p>

        {/* Show error details in development environment */}
        {process.env.NODE_ENV === 'development' && (
          <div className='mb-6 rounded-lg bg-orange-50 p-4 text-left dark:bg-orange-900/20'>
            <h3 className='mb-2 font-semibold text-orange-800 dark:text-orange-200'>
              {t('devDetails')}
            </h3>
            <pre className='text-xs text-orange-700 dark:text-orange-300 overflow-auto'>
              {error.message}
            </pre>
            {error.digest && (
              <p className='mt-2 text-xs text-orange-600 dark:text-orange-400'>
                {t('errorId')} {error.digest}
              </p>
            )}
          </div>
        )}

        <div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
          <Button
            onClick={reset}
            variant='default'
            className='flex items-center gap-2'
          >
            <RefreshCw className='h-4 w-4' />
            {t('buttons.retry')}
          </Button>

          <Button
            onClick={() => (window.location.href = '/')}
            variant='outline'
            className='flex items-center gap-2'
          >
            <Home className='h-4 w-4' />
            {t('buttons.home')}
          </Button>
        </div>

        {/* Quick navigation */}
        <div className='mt-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50'>
          <h3 className='mb-2 font-semibold text-gray-900 dark:text-gray-100'>
            {t('quickNav.title')}
          </h3>
          <div className='flex flex-wrap justify-center gap-2 text-sm'>
            <Link
              href='/sign-in'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              {t('quickNav.signIn')}
            </Link>
            <span className='text-gray-400'>•</span>
            <Link
              href='/sign-up'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              {t('quickNav.signUp')}
            </Link>
            <span className='text-gray-400'>•</span>
            <Link
              href='/about'
              className='text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
            >
              {t('quickNav.about')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
