# Refundgo Web 2

A comprehensive multilingual task publishing and completion platform with integrated payment
processing, user management, and administrative features.

## 🚀 Features

- **Multilingual Support**: Complete internationalization with Chinese and English locales
- **User Authentication**: NextAuth v5 with OAuth providers (Google, GitHub)
- **Task Management**: Publishing, accepting, completing, and reviewing tasks
- **Payment System**: Multi-provider payment processing with wallet system
- **Admin Dashboard**: Comprehensive user, task, and system management
- **Membership System**: Tiered user subscriptions with privileges
- **Support System**: Customer support and issue tracking
- **File Uploads**: Secure document and media handling
- **3D Components**: Interactive UI elements with React Three Fiber

## 🛠️ Tech Stack

### Core Framework

- **Next.js 14+** with App Router
- **TypeScript 5.8+** with strict type checking
- **React 18** with Server Components
- **next-intl** for internationalization

### Frontend

- **Tailwind CSS** + **shadcn/ui** components
- **Radix U<PERSON>** primitives for accessibility
- **Framer Motion** for animations
- **React Three Fiber** for 3D graphics
- **TanStack Query** for server state management

### Backend

- **Prisma ORM** with PostgreSQL
- **NextAuth v5** for authentication
- **Resend** for multilingual emails
- **Zod** for schema validation
- **bcryptjs** for password hashing

### Development Tools

- **Augment** AI-powered coding assistant
- **ESLint** + **Prettier** for code quality
- **Jest** + **Testing Library** for testing
- **Husky** + **lint-staged** for git hooks

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd refundgo-web
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Setup environment variables**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Setup database**

   ```bash
   npx prisma generate
   npx prisma db push
   npm run db:seed
   ```

5. **Start development server**

   ```bash
   npm run dev
   ```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   │   ├── (main)/        # Public routes
│   │   ├── (user)/        # User dashboard
│   │   └── payment/       # Payment flow
│   ├── (admin)/           # Admin panel
│   └── api/               # API routes
├── components/            # Reusable components
├── lib/                   # Utilities & configuration
├── i18n/                  # Internationalization
└── types/                 # TypeScript types
```

## 🌐 Internationalization

The project supports multiple languages with automatic detection:

- **English** (`/en/`)
- **Chinese** (`/zh/`)

Translation files are organized in `messages/` directory with modular structure for maintainability.

## 🧪 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Production build
npm run start           # Start production server

# Code Quality
npm run lint            # Check linting issues
npm run lint:fix        # Auto-fix linting issues
npm run format          # Format code with Prettier
npm run fix             # Fix linting + formatting

# Testing
npm test                # Run tests
npm run test:watch      # Watch mode
npm run test:coverage   # Coverage report

# Database
npx prisma studio       # Database GUI
npx prisma generate     # Generate Prisma client
npm run db:seed         # Seed database
npm run db:reset        # Reset + seed

# Cron Jobs
npm run setup:cron      # Setup cron-job.org tasks
npm run test:cron       # Test cron endpoints
```

### Code Quality

The project enforces high code quality standards:

- **TypeScript** strict mode
- **ESLint** with comprehensive rules
- **Prettier** for consistent formatting
- **Husky** pre-commit hooks
- **Jest** for testing

## 📚 Documentation

Detailed documentation is available in the `docs/` directory:

- [Development Guide](docs/development-guide.md) - Comprehensive development instructions
- [Project Context](docs/project-context.md) - Detailed project overview
- [AI Assistant Guide](docs/ai-assistant-guide.md) - Guidelines for AI assistance
- [ESLint Configuration](docs/eslint-configuration.md) - Linting setup
- [Testing Guide](docs/testing-guide.md) - Testing strategies
- [VSCode Setup](docs/vscode-setup.md) - IDE configuration

## 🚀 Deployment

### Production Build

```bash
npm run build
npm run start
```

### Database Deployment

```bash
npx prisma migrate deploy
npx prisma generate
```

### Environment Variables

Ensure all production environment variables are configured:

- Database connection string
- Authentication secrets
- Payment provider keys
- Email service credentials

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the documentation in `docs/`
