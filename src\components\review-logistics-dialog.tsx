'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { CheckCircle, XCircle, Eye } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const reviewSchema = z
  .object({
    approved: z.boolean(),
    rejectReason: z.string().optional(),
  })
  .refine(
    data => {
      if (!data.approved && !data.rejectReason?.trim()) {
        return false;
      }
      return true;
    },
    {
      message: '审核不通过时必须提供拒绝原因',
      path: ['rejectReason'],
    },
  );

type ReviewForm = z.infer<typeof reviewSchema>;

interface ReviewLogisticsDialogProps {
  children?: React.ReactNode;
  taskId: string;
  trackingNumber: string;
  logisticsScreenshots: string[];
  onReview?: (approved: boolean) => void;
}

export function ReviewLogisticsDialog({
  children,
  taskId,
  trackingNumber,
  logisticsScreenshots,
  onReview,
}: ReviewLogisticsDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [reviewDecision, setReviewDecision] = useState<boolean | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<ReviewForm>({
    resolver: zodResolver(reviewSchema),
  });

  const approved = watch('approved');

  const onFormSubmit = async (data: ReviewForm) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/publisher/tasks/${taskId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approved: data.approved,
          rejectReason: data.approved ? undefined : data.rejectReason,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || '审核失败');
      }

      toast.success(data.approved ? '审核通过！' : '审核不通过', {
        description: result.message,
        duration: 6000,
      });

      // 调用父组件的审核处理函数
      onReview?.(data.approved);

      // 重置表单和状态
      reset();
      setReviewDecision(null);
      setIsOpen(false);

      // 刷新页面或重新获取数据
      window.location.reload();
    } catch (error) {
      toast.error('审核失败', {
        description:
          error instanceof Error ? error.message : '网络错误，请稍后重试',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = () => {
    setReviewDecision(true);
    setValue('approved', true);
  };

  const handleReject = () => {
    setReviewDecision(false);
    setValue('approved', false);
  };

  const handleDialogClose = (open: boolean) => {
    setIsOpen(open);
    if (!open && !isLoading) {
      reset();
      setReviewDecision(null);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Eye className='h-5 w-5' />
            审核物流信息
          </DialogTitle>
          <DialogDescription>
            请审核接单者提交的物流单号和物流截图是否符合要求
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* 物流单号展示 */}
          <div className='space-y-2'>
            <Label>物流单号</Label>
            <div className='p-3 bg-muted/50 rounded-lg font-mono text-sm'>
              {trackingNumber}
            </div>
          </div>

          {/* 物流截图展示 */}
          <div className='space-y-3'>
            <Label>物流截图 ({logisticsScreenshots.length}张)</Label>
            {logisticsScreenshots.length > 0 ? (
              <div className='grid grid-cols-2 sm:grid-cols-3 gap-3'>
                {logisticsScreenshots.map((screenshot, index) => (
                  <Dialog key={index}>
                    <DialogTrigger asChild>
                      <div className='cursor-pointer group'>
                        <div className='aspect-square border rounded-lg overflow-hidden bg-gray-50 hover:shadow-md transition-shadow'>
                          <Image
                            src={screenshot}
                            alt={`物流截图 ${index + 1}`}
                            width={200}
                            height={200}
                            className='w-full h-full object-cover group-hover:scale-105 transition-transform'
                          />
                        </div>
                        <div className='text-center mt-1'>
                          <span className='text-xs text-muted-foreground'>
                            截图 {index + 1}
                          </span>
                        </div>
                      </div>
                    </DialogTrigger>
                    <DialogContent className='max-w-4xl max-h-[90vh] overflow-hidden'>
                      <DialogHeader>
                        <DialogTitle>物流截图 {index + 1}</DialogTitle>
                      </DialogHeader>
                      <div className='flex items-center justify-center p-4 overflow-auto'>
                        <Image
                          src={screenshot}
                          alt={`物流截图 ${index + 1}`}
                          width={800}
                          height={600}
                          className='max-w-full max-h-full object-contain'
                        />
                      </div>
                    </DialogContent>
                  </Dialog>
                ))}
              </div>
            ) : (
              <div className='p-4 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500'>
                暂无物流截图
              </div>
            )}
          </div>

          {/* 审核决定按钮 */}
          {reviewDecision === null && (
            <div className='flex gap-3'>
              <Button
                type='button'
                onClick={handleApprove}
                className='flex-1 bg-green-600 hover:bg-green-700'
                disabled={isLoading}
              >
                <CheckCircle className='h-4 w-4 mr-2' />
                审核通过
              </Button>
              <Button
                type='button'
                onClick={handleReject}
                variant='destructive'
                className='flex-1'
                disabled={isLoading}
              >
                <XCircle className='h-4 w-4 mr-2' />
                审核不通过
              </Button>
            </div>
          )}

          {/* 审核表单 */}
          {reviewDecision !== null && (
            <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-4'>
              <input type='hidden' {...register('approved')} />

              {/* 审核结果显示 */}
              <div
                className={`p-3 rounded-lg border ${
                  reviewDecision
                    ? 'bg-green-50 border-green-200 text-green-800'
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}
              >
                <div className='flex items-center gap-2'>
                  {reviewDecision ? (
                    <CheckCircle className='h-4 w-4' />
                  ) : (
                    <XCircle className='h-4 w-4' />
                  )}
                  <span className='font-medium'>
                    {reviewDecision ? '审核通过' : '审核不通过'}
                  </span>
                </div>
              </div>

              {/* 拒绝原因输入 */}
              {!reviewDecision && (
                <div className='space-y-2'>
                  <Label htmlFor='rejectReason'>拒绝原因 *</Label>
                  <Textarea
                    id='rejectReason'
                    placeholder='请详细说明拒绝原因，以便接单者改进...'
                    {...register('rejectReason')}
                    disabled={isLoading}
                    rows={3}
                  />
                  {errors.rejectReason && (
                    <p className='text-sm text-red-600'>
                      {errors.rejectReason.message}
                    </p>
                  )}
                </div>
              )}

              {/* 操作按钮 */}
              <div className='flex gap-2 pt-2'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setReviewDecision(null)}
                  disabled={isLoading}
                  className='flex-1'
                >
                  重新选择
                </Button>
                <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                      提交中...
                    </>
                  ) : (
                    <>
                      <CheckCircle className='h-4 w-4 mr-1' />
                      确认提交
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
