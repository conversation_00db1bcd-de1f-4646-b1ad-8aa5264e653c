---
type: 'agent_requested'
description: '项目'
---

# Refundgo Web - Cursor Rules

## Project Overview

Next.js 14+ full-stack application with TypeScript, Tailwind CSS, Prisma ORM, NextAuth v5, and React
Three Fiber for 3D components. Built with App Router, Server Components, and modern React patterns.

## Tech Stack

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript with strict type checking
- **Styling**: Tailwind CSS + shadcn/ui components + DHgate theme
- **Database**: Prisma ORM with user language tracking
- **Authentication**: NextAuth v5 (beta)
- **Internationalization**: next-intl with locale routing (zh/en)
- **3D Graphics**: React Three Fiber + Drei
- **State Management**: Zustand, TanStack Query
- **Forms**: React Hook Form + Zod validation + Server Actions
- **Testing**: Jest + Testing Library + Vitest
- **Email**: Resend with multilingual templates
- **UI Components**: Radix UI primitives

## Directory Structure

```
src/
├── app/                          # App Router pages & layouts
│   ├── [locale]/                 # Internationalized routes (zh/en)
│   │   ├── (main)/              # Public routes (homepage, auth)
│   │   ├── (user)/              # User dashboard routes
│   │   └── payment/             # Payment flow routes
│   ├── (admin)/                 # Admin panel routes (no i18n)
│   ├── api/                     # API routes & webhooks
│   │   ├── admin/               # Admin API endpoints
│   │   ├── auth/                # Authentication endpoints (i18n email)
│   │   ├── payments/            # Payment processing
│   │   ├── tasks/               # Task management
│   │   ├── user/                # User operations
│   │   └── webhook/             # External webhooks
│   ├── globals.css              # Global styles + DHgate theme
│   └── layout.tsx               # Root layout with locale detection
├── components/                   # Reusable UI components
│   ├── ui/                      # shadcn/ui components
│   ├── admin/                   # Admin-specific components
│   ├── payment/                 # Payment components
│   ├── publish-steps/           # Multi-step form components
│   └── StarBackground.tsx       # 3D background component
├── hooks/                       # Custom React hooks
│   ├── useEmailTranslation.ts   # Email i18n hook
│   └── [other hooks]
├── i18n/                        # next-intl configuration
│   ├── navigation.ts            # Internationalized navigation
│   ├── request.ts               # Message loading configuration
│   └── routing.ts               # Locale routing setup
├── lib/                         # Utilities & configurations
│   ├── email-templates/         # Email template functions
│   │   ├── verification-code-i18n.ts  # Multilingual email templates
│   │   └── task-completed-i18n.ts     # Task completion emails
│   ├── payment/                 # Payment provider integrations
│   ├── types/                   # Type definitions
│   ├── utils/                   # Utility functions
│   └── user-language.ts         # User language management
├── styles/                      # Additional stylesheets
│   └── dhgate-theme.css         # DHgate brand styling
└── middleware.ts                # i18n + auth middleware
messages/                        # Translation files (next-intl)
├── en.json                      # English main translations
├── zh.json                      # Chinese main translations
├── en/                          # English modular translations
│   ├── common.json              # Shared UI text
│   ├── homepage.json            # Homepage content
│   ├── navigation.json          # Menu items
│   ├── dashboard.json           # Dashboard content
│   ├── tasks.json               # Task management
│   ├── wallet.json              # Wallet features
│   ├── email.json               # Email templates
│   └── [other modules]
└── zh/                          # Chinese modular translations
    ├── common.json              # 共享界面文本
    ├── homepage.json            # 首页内容
    ├── navigation.json          # 菜单项目
    └── [other modules]
scripts/                         # Development & migration scripts
├── migrate-translations.js      # Translation file migration
├── migrate-user-language.ts     # User language data migration
└── validate-translations.js     # Translation completeness check
│   └── validations/             # Zod validation schemas
├── i18n/                        # Internationalization config
├── types/                       # Global TypeScript types
└── middleware.ts                # Next.js middleware
```

### Additional Project Files

```
├── prisma/                      # Database schema & migrations
├── messages/                    # i18n translation files
│   ├── en/                     # English translations
│   └── zh/                     # Chinese translations
├── docs/                       # Project documentation
└── public/                     # Static assets
    └── uploads/                # User uploaded files
```

## Coding Standards

### Project Documentation (`docs/`)

The `docs/` directory contains comprehensive project documentation. It is highly recommended to
consult these documents before starting work to gain a deep understanding of the project
architecture, development workflows, feature specifications, and coding standards.

Key documents include:

- `project-context.md`: High-level overview of the project, tech stack, and features.
- `system-architecture.md`: Detailed breakdown of the system architecture.
- `development-guide.md`: Instructions on setting up the development environment and common
  commands.
- `feature-modules.md`: In-depth explanation of core application modules.
- `user-roles.md`: Description of user permissions and roles.
- `testing-guide.md`: Guidelines for writing and running tests.

### TypeScript

- Use strict TypeScript with proper type definitions
- Prefer interfaces over types for object shapes
- Use Zod schemas for runtime validation
- Export types from dedicated files in `/types`

### React/Next.js

- **Server Components First**: Use React Server Components by default for better performance
- **Strategic Client Boundaries**: Add 'use client' only when necessary (state, effects, browser
  APIs)
- **Composition Patterns**: Follow Server/Client Component composition patterns
- **Data Fetching**: Use fetch() with proper caching in Server Components
- **Async/Await**: Prefer async/await over .then() for better readability
- **Error Handling**: Use proper error boundaries (error.tsx) and not-found.tsx
- **Loading States**: Implement loading.tsx and Suspense for better UX
- **Navigation**: Use Link component for client-side navigation and prefetching
- **Layouts**: Leverage layout.tsx for shared UI and partial rendering

### Styling

- Use Tailwind CSS utility classes
- Follow shadcn/ui component patterns
- Use CSS variables for theming
- Responsive design with mobile-first approach

### Database

- Use Prisma schema for all database operations
- Follow proper migration practices
- Use transactions for complex operations
- Implement proper error handling

### API Routes & Server Actions

- **Route Handlers**: Use app/api/route.ts for backend integration
- **Server Actions**: Prefer Server Actions for form submissions and mutations
- **Validation**: Implement request validation with Zod schemas
- **Error Handling**: Handle errors gracefully with proper status codes
- **Authentication**: Use middleware for auth checks
- **Authorization**: Ensure users are authorized for Server Actions
- **Caching**: Use proper fetch caching strategies (force-cache, no-store, revalidate)

## File Naming

- Components: PascalCase (e.g., `UserProfile.tsx`)
- Pages: lowercase with hyphens (e.g., `user-profile/page.tsx`)
- Utilities: camelCase (e.g., `formatDate.ts`)
- Types: PascalCase with descriptive names

## Component Patterns

- **Composition Over Inheritance**: Favor component composition patterns
- **Server Component by Default**: Start with Server Components, add 'use client' when needed
- **Prop Types**: Implement proper TypeScript prop interfaces
- **forwardRef**: Use forwardRef for reusable component libraries
- **Accessibility**: Follow WCAG guidelines and use semantic HTML
- **Context Providers**: Use Client Components for context providers
- **Third-party Integration**: Wrap client-only libraries in Client Components

## State Management

- Use React state for local component state
- Use Zustand for global client state
- Use TanStack Query for server state
- Avoid prop drilling with context when needed

## Testing

- Write unit tests for utilities and hooks
- Use integration tests for components
- Mock external dependencies
- Aim for meaningful test coverage

## Performance

- **Images**: Use Next.js Image component for automatic optimization and modern formats
- **Fonts**: Use next/font for font optimization and reduced CLS
- **Scripts**: Use Next.js Script component for third-party script optimization
- **Caching**: Implement proper data caching with fetch() and unstable_cache
- **Code Splitting**: Use dynamic imports and lazy loading for large components
- **Bundle Analysis**: Monitor bundle size with built-in analyzer
- **Static Assets**: Store in public/ directory for automatic caching
- **Streaming**: Use Suspense for progressive UI rendering
- **Parallel Data Fetching**: Fetch data in parallel to reduce waterfalls

## Security

- Validate all user inputs
- Use CSRF protection
- Implement proper authentication checks
- Sanitize data before database operations

## Modern Next.js 14 Patterns

- **Data Fetching**: Use fetch() with caching options (force-cache, no-store, revalidate)
- **Dynamic APIs**: Use cookies() and headers() functions carefully, wrap in Suspense
- **Server Actions**: Implement form handling with Server Actions instead of API routes
- **Metadata API**: Use generateMetadata for dynamic SEO optimization
- **Route Groups**: Organize routes with (group) folders for better structure
- **Parallel Routes**: Use @folder convention for advanced routing patterns
- **Intercepting Routes**: Use (..) convention for modal-like experiences

## Best Practices

- **Principle of Least Privilege**: Apply minimal permissions and access
- **Environment Variables**: Use .env files for configuration
- **Error Logging**: Implement comprehensive error tracking
- **Code Documentation**: Write self-documenting code with meaningful names
- **Commit Messages**: Use conventional commit format
- **Type Safety**: Leverage TypeScript and Next.js plugin for early error detection
- **SEO**: Use Metadata API for proper page titles and descriptions
- **Accessibility**: Test with screen readers and follow ARIA guidelines

## Code Examples

### Server Component Data Fetching

```tsx
// Prefer this pattern for data fetching
export default async function Page() {
  const data = await fetch('https://api.example.com', {
    next: { revalidate: 3600 }, // Cache for 1 hour
  });
  const result = await data.json();

  return <div>{result.title}</div>;
}
```

### Client Component Pattern

```tsx
'use client';

import { useState, useEffect } from 'react';

export default function ClientComponent({ initialData }) {
  const [state, setState] = useState(initialData);

  // Client-side logic here
  return <div>Interactive content</div>;
}
```

### Server Action Form Handling

```tsx
import { redirect } from 'next/navigation';

async function createUser(formData: FormData) {
  'use server';

  const name = formData.get('name') as string;
  // Validate with Zod, save to database
  redirect('/success');
}

export default function Form() {
  return (
    <form action={createUser}>
      <input name='name' required />
      <button type='submit'>Submit</button>
    </form>
  );
}
```

### Layout with Metadata

```tsx
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Refundgo',
  description: 'Modern refund management platform',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang='en'>
      <body>{children}</body>
    </html>
  );
}
```
