'use client';

import { <PERSON>, Sun } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function ModeToggle() {
  const { setTheme } = useTheme();
  const pathname = usePathname();
  const isAdmin = pathname.startsWith('/admin');

  // 硬编码的中文文本用于后台管理系统
  const adminTexts = {
    toggle: '切换主题',
    light: '浅色',
    dark: '深色',
    system: '系统',
  };

  // Always call the hook - React hooks must be called unconditionally
  const t = useTranslations('Common');

  // 获取文本的辅助函数
  const getText = (key: 'toggle' | 'light' | 'dark' | 'system') => {
    if (isAdmin) {
      return adminTexts[key];
    }
    const translationKey = `theme.${key}` as const;
    try {
      return t(translationKey);
    } catch (error) {
      // Fallback to admin texts if translation fails
      return adminTexts[key];
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='outline' size='icon'>
          <Sun className='h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90' />
          <Moon className='absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0' />
          <span className='sr-only'>{getText('toggle')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuItem onClick={() => setTheme('light')}>
          {getText('light')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          {getText('dark')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          {getText('system')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
