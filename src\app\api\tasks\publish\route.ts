import { EvidenceStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { getUserTaskLimit } from '@/lib/utils/membership';
import { generateTaskIdWithRetry } from '@/lib/utils/task-id';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问，请先登录',
    validationFailed: '数据验证失败',
    userNotFound: '用户不存在',
    accountStatusError: '账户状态异常，无法发布委托',
    insufficientBalance: '账户余额不足，请先充值',
    taskLimitExceeded:
      '您的会员套餐每月最多可发布{max}个委托，当前已发布{used}个。请升级会员套餐获得更多发布权限。',
    platformNotFound: '选择的平台不存在',
    categoryNotFound: '选择的分类不存在',
    invalidChargebackTypes: '选择的拒付类型包含无效项',
    invalidPaymentMethods: '选择的支付方式包含无效项',
    publishSuccess: '委托发布成功',
    publishFailed: '发布委托失败，请重试',
    taskFeeDescription: '发布委托费用 - {title}',
    evidenceFeeDescription: '发布委托证据费（冻结中）- {title}',
  },
  en: {
    unauthorized: 'Unauthorized access, please login first',
    validationFailed: 'Data validation failed',
    userNotFound: 'User not found',
    accountStatusError: 'Account status error, cannot publish task',
    insufficientBalance: 'Insufficient account balance, please deposit first',
    taskLimitExceeded:
      'Your membership plan allows maximum {max} tasks per month, currently published {used}. Please upgrade membership plan for more publishing permissions.',
    platformNotFound: 'Selected platform does not exist',
    categoryNotFound: 'Selected category does not exist',
    invalidChargebackTypes: 'Selected chargeback types contain invalid items',
    invalidPaymentMethods: 'Selected payment methods contain invalid items',
    publishSuccess: 'Task published successfully',
    publishFailed: 'Failed to publish task, please try again',
    taskFeeDescription: 'Task publishing fee - {title}',
    evidenceFeeDescription: 'Task evidence fee (frozen) - {title}',
  },
};

// 发布委托请求验证Schema
const publishTaskSchema = z.object({
  // 基本信息
  title: z.string().optional(),
  productUrl: z.string().url(),
  productDescription: z.string().min(1),
  quantity: z.number().int().min(1),
  unitPrice: z.number().min(0.01),
  listingTime: z.string().min(1),

  // 收货信息
  recipientName: z.string().min(1),
  recipientPhone: z.string().min(1),
  shippingAddress: z.string().min(1),

  // 关联信息
  platformId: z.string().min(1),
  categoryId: z.string().min(1),
  chargebackTypeIds: z.array(z.string()).min(1),
  paymentMethodIds: z.array(z.string()).min(1),

  // 证据相关
  evidenceUploadType: z.enum(['IMMEDIATE', 'LATER', 'NONE']),
  cartScreenshots: z.array(z.string()).optional(),
  evidenceFiles: z.array(z.string()).optional(),

  // 费用信息
  finalTotal: z.number().min(0),
});

export async function POST(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    // 1. 验证用户身份
    const session = await getAuthSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: t.unauthorized }, { status: 401 });
    }

    // 2. 解析请求数据
    const body = await request.json();
    const validation = publishTaskSchema.safeParse(body);

    if (!validation.success) {
      const errors = validation.error.errors
        .map((err: any) => err.message)
        .join(', ');
      return NextResponse.json(
        { error: `${t.validationFailed}: ${errors}` },
        { status: 400 },
      );
    }

    const data = validation.data;

    // 3. 查找用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        balance: true,
        memberPlan: true,
        status: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: t.userNotFound }, { status: 404 });
    }

    // 4. 检查用户状态
    if (user.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: t.accountStatusError },
        { status: 403 },
      );
    }

    // 5. 检查用户余额
    if (user.balance < data.finalTotal) {
      return NextResponse.json(
        { error: t.insufficientBalance },
        { status: 400 },
      );
    }

    // 6. 检查每月委托发布限制
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const tasksUsedThisMonth = await prisma.task.count({
      where: {
        publisherId: user.id,
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
      },
    });

    // 获取用户会员套餐的委托发布限制
    const maxTasksPerMonth = await getUserTaskLimit(user.memberPlan);

    // 检查委托发布限制
    if (maxTasksPerMonth !== null && tasksUsedThisMonth >= maxTasksPerMonth) {
      return NextResponse.json(
        {
          error: t.taskLimitExceeded
            .replace('{max}', maxTasksPerMonth.toString())
            .replace('{used}', tasksUsedThisMonth.toString()),
        },
        { status: 400 },
      );
    }

    // 7. 验证关联数据是否存在
    const [platform, category, chargebackTypes, paymentMethods] =
      await Promise.all([
        prisma.platform.findUnique({ where: { id: data.platformId } }),
        prisma.category.findUnique({ where: { id: data.categoryId } }),
        prisma.chargebackType.findMany({
          where: { id: { in: data.chargebackTypeIds } },
        }),
        prisma.paymentMethod.findMany({
          where: { id: { in: data.paymentMethodIds } },
        }),
      ]);

    if (!platform) {
      return NextResponse.json({ error: t.platformNotFound }, { status: 400 });
    }

    if (!category) {
      return NextResponse.json({ error: t.categoryNotFound }, { status: 400 });
    }

    if (chargebackTypes.length !== data.chargebackTypeIds.length) {
      return NextResponse.json(
        { error: t.invalidChargebackTypes },
        { status: 400 },
      );
    }

    if (paymentMethods.length !== data.paymentMethodIds.length) {
      return NextResponse.json(
        { error: t.invalidPaymentMethods },
        { status: 400 },
      );
    }

    // 8. 生成委托ID
    const taskId = await generateTaskIdWithRetry();

    // 9. 计算委托到期时间
    const expiresAt = new Date();
    const hours = parseInt(data.listingTime.replace('h', ''), 10);
    expiresAt.setHours(expiresAt.getHours() + hours);

    // 10. 创建委托
    // 根据证据上传类型设置证据状态
    let evidenceStatus: EvidenceStatus = EvidenceStatus.PENDING_SUBMISSION; // 默认状态

    if (data.evidenceUploadType === 'NONE') {
      evidenceStatus = EvidenceStatus.NO_EVIDENCE;
    } else if (
      data.evidenceUploadType === 'IMMEDIATE' &&
      data.evidenceFiles &&
      data.evidenceFiles.length > 0
    ) {
      evidenceStatus = EvidenceStatus.UNDER_REVIEW;
    } else if (data.evidenceUploadType === 'LATER') {
      evidenceStatus = EvidenceStatus.PENDING_SUBMISSION;
    }

    const taskData = {
      id: taskId,
      title:
        data.title ||
        `${platform.name} - ${data.productDescription.substring(0, 50)}...`,
      productUrl: data.productUrl,
      productDescription: data.productDescription,
      quantity: data.quantity,
      unitPrice: data.unitPrice,
      totalAmount: data.quantity * data.unitPrice,
      finalTotal: data.finalTotal,
      listingTime: data.listingTime,
      expiresAt,

      // 收货信息
      recipientName: data.recipientName,
      recipientPhone: data.recipientPhone,
      shippingAddress: data.shippingAddress,

      // 关联信息
      publisherId: user.id,
      platformId: data.platformId,
      categoryId: data.categoryId,
      chargebackTypeIds: data.chargebackTypeIds,
      paymentMethodIds: data.paymentMethodIds,

      // 证据相关
      evidenceUploadType: data.evidenceUploadType,
      evidenceStatus,
      cartScreenshots: data.cartScreenshots || [],
      evidenceFiles: data.evidenceFiles || [],

      // 时间戳
      createdAt: now,
      updatedAt: now,
    };

    // 11. 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx: any) => {
      // 11.1 创建委托
      const task = await tx.task.create({
        data: taskData,
      });

      // 11.2 获取系统费率配置来计算证据费
      const systemRate = await tx.systemRate.findFirst();
      const evidenceRate = systemRate?.noEvidenceExtraRate || 2.0;
      const totalAmount = data.quantity * data.unitPrice;
      const evidenceFee = (totalAmount * evidenceRate) / 100;

      // 11.3 计算费用分配：证据费进入冻结金额，其他费用从可用余额扣除
      const nonEvidenceFee = data.finalTotal - evidenceFee;

      // 11.4 扣除用户余额并分配证据费到冻结金额
      await tx.user.update({
        where: { id: user.id },
        data: {
          balance: {
            decrement: nonEvidenceFee, // 扣除非证据费部分
          },
          frozenAmount: {
            increment: evidenceFee, // 证据费进入冻结金额
          },
          totalExpense: {
            increment: data.finalTotal,
          },
        },
      });

      // 11.5 创建非证据费用的钱包交易记录
      if (nonEvidenceFee > 0) {
        await tx.walletTransaction.create({
          data: {
            userId: user.id,
            type: 'TASK_FEE',
            amount: -nonEvidenceFee,
            status: 'COMPLETED',
            description: t.taskFeeDescription.replace(
              '{title}',
              task.title || '',
            ),
            reference: task.id,
            completedAt: new Date(),
          },
        });
      }

      // 11.6 创建证据费冻结记录
      if (evidenceFee > 0) {
        await tx.walletTransaction.create({
          data: {
            userId: user.id,
            type: 'TASK_FEE',
            amount: -evidenceFee,
            status: 'COMPLETED',
            description: t.evidenceFeeDescription.replace(
              '{title}',
              task.title || '',
            ),
            reference: task.id,
            completedAt: new Date(),
          },
        });
      }

      return {
        task,
        newBalance: user.balance - nonEvidenceFee,
        evidenceFee,
      };
    });

    // 12. 返回成功响应
    return NextResponse.json({
      success: true,
      message: t.publishSuccess,
      data: {
        taskId: result.task.id,
        title: result.task.title,
        finalTotal: result.task.finalTotal,
        expiresAt: result.task.expiresAt,
        remainingBalance: result.newBalance,
      },
    });
  } catch (error) {
    console.error('发布委托失败:', error);
    // 在错误情况下使用默认语言
    return NextResponse.json(
      { error: messages.zh.publishFailed },
      { status: 500 },
    );
  }
}
