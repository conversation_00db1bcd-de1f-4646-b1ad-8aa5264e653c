/* RTL (Right-to-Left) 语言支持样式 */

/* 基础RTL支持 */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* 边距和内边距RTL支持 */
[dir="rtl"] .ml-2 { margin-right: 0.5rem; margin-left: 0; }
[dir="rtl"] .mr-2 { margin-left: 0.5rem; margin-right: 0; }
[dir="rtl"] .ml-4 { margin-right: 1rem; margin-left: 0; }
[dir="rtl"] .mr-4 { margin-left: 1rem; margin-right: 0; }
[dir="rtl"] .ml-6 { margin-right: 1.5rem; margin-left: 0; }
[dir="rtl"] .mr-6 { margin-left: 1.5rem; margin-right: 0; }
[dir="rtl"] .ml-8 { margin-right: 2rem; margin-left: 0; }
[dir="rtl"] .mr-8 { margin-left: 2rem; margin-right: 0; }

[dir="rtl"] .pl-2 { padding-right: 0.5rem; padding-left: 0; }
[dir="rtl"] .pr-2 { padding-left: 0.5rem; padding-right: 0; }
[dir="rtl"] .pl-4 { padding-right: 1rem; padding-left: 0; }
[dir="rtl"] .pr-4 { padding-left: 1rem; padding-right: 0; }
[dir="rtl"] .pl-6 { padding-right: 1.5rem; padding-left: 0; }
[dir="rtl"] .pr-6 { padding-left: 1.5rem; padding-right: 0; }
[dir="rtl"] .pl-8 { padding-right: 2rem; padding-left: 0; }
[dir="rtl"] .pr-8 { padding-left: 2rem; padding-right: 0; }

/* 定位RTL支持 */
[dir="rtl"] .left-0 { right: 0; left: auto; }
[dir="rtl"] .right-0 { left: 0; right: auto; }
[dir="rtl"] .left-4 { right: 1rem; left: auto; }
[dir="rtl"] .right-4 { left: 1rem; right: auto; }

/* Flexbox RTL支持 */
[dir="rtl"] .flex-row { flex-direction: row-reverse; }
[dir="rtl"] .flex-row-reverse { flex-direction: row; }

/* 边框RTL支持 */
[dir="rtl"] .border-l { border-right-width: 1px; border-left-width: 0; }
[dir="rtl"] .border-r { border-left-width: 1px; border-right-width: 0; }
[dir="rtl"] .border-l-2 { border-right-width: 2px; border-left-width: 0; }
[dir="rtl"] .border-r-2 { border-left-width: 2px; border-right-width: 0; }

/* 圆角RTL支持 */
[dir="rtl"] .rounded-l { border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-top-left-radius: 0; border-bottom-left-radius: 0; }
[dir="rtl"] .rounded-r { border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; border-top-right-radius: 0; border-bottom-right-radius: 0; }
[dir="rtl"] .rounded-tl { border-top-right-radius: 0.25rem; border-top-left-radius: 0; }
[dir="rtl"] .rounded-tr { border-top-left-radius: 0.25rem; border-top-right-radius: 0; }
[dir="rtl"] .rounded-bl { border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0; }
[dir="rtl"] .rounded-br { border-bottom-left-radius: 0.25rem; border-bottom-right-radius: 0; }

/* 变换RTL支持 */
[dir="rtl"] .transform { transform: scaleX(-1); }
[dir="rtl"] .rotate-90 { transform: rotate(-90deg); }
[dir="rtl"] .rotate-180 { transform: rotate(180deg); }
[dir="rtl"] .-rotate-90 { transform: rotate(90deg); }

/* 图标RTL支持 */
[dir="rtl"] .icon-flip {
  transform: scaleX(-1);
}

/* 导航RTL支持 */
[dir="rtl"] .navbar {
  direction: rtl;
}

[dir="rtl"] .navbar .logo {
  margin-right: 0;
  margin-left: auto;
}

[dir="rtl"] .navbar .menu-items {
  flex-direction: row-reverse;
}

/* 按钮RTL支持 */
[dir="rtl"] .btn-with-icon {
  flex-direction: row-reverse;
}

[dir="rtl"] .btn-with-icon .icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* 卡片RTL支持 */
[dir="rtl"] .card {
  text-align: right;
}

[dir="rtl"] .card-header {
  text-align: right;
}

[dir="rtl"] .card-content {
  text-align: right;
}

/* 表单RTL支持 */
[dir="rtl"] .form-group {
  text-align: right;
}

[dir="rtl"] .form-label {
  text-align: right;
}

[dir="rtl"] .form-input {
  text-align: right;
}

[dir="rtl"] .form-checkbox {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* 模态框RTL支持 */
[dir="rtl"] .modal {
  direction: rtl;
}

[dir="rtl"] .modal-header {
  text-align: right;
}

[dir="rtl"] .modal-close {
  right: auto;
  left: 1rem;
}

/* 下拉菜单RTL支持 */
[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
  text-align: right;
}

[dir="rtl"] .dropdown-item {
  text-align: right;
}

/* 工具提示RTL支持 */
[dir="rtl"] .tooltip {
  direction: rtl;
}

[dir="rtl"] .tooltip-arrow {
  transform: scaleX(-1);
}

/* 面包屑RTL支持 */
[dir="rtl"] .breadcrumb {
  flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-separator {
  transform: scaleX(-1);
}

/* 分页RTL支持 */
[dir="rtl"] .pagination {
  flex-direction: row-reverse;
}

[dir="rtl"] .pagination-prev {
  order: 2;
}

[dir="rtl"] .pagination-next {
  order: 1;
}

/* 表格RTL支持 */
[dir="rtl"] .table {
  direction: rtl;
}

[dir="rtl"] .table th,
[dir="rtl"] .table td {
  text-align: right;
}

/* 进度条RTL支持 */
[dir="rtl"] .progress-bar {
  direction: rtl;
}

[dir="rtl"] .progress-fill {
  right: 0;
  left: auto;
}

/* 标签页RTL支持 */
[dir="rtl"] .tabs {
  direction: rtl;
}

[dir="rtl"] .tab-list {
  flex-direction: row-reverse;
}

/* 手风琴RTL支持 */
[dir="rtl"] .accordion {
  direction: rtl;
}

[dir="rtl"] .accordion-trigger {
  text-align: right;
}

[dir="rtl"] .accordion-icon {
  margin-right: 0;
  margin-left: auto;
}

/* 滑块RTL支持 */
[dir="rtl"] .slider {
  direction: rtl;
}

[dir="rtl"] .slider-track {
  direction: rtl;
}

[dir="rtl"] .slider-thumb {
  right: 0;
  left: auto;
}

/* 开关RTL支持 */
[dir="rtl"] .switch {
  direction: rtl;
}

[dir="rtl"] .switch-thumb {
  right: 0;
  left: auto;
}

/* 特殊组件RTL支持 */
[dir="rtl"] .hero-section {
  direction: rtl;
}

[dir="rtl"] .features-grid {
  direction: rtl;
}

[dir="rtl"] .stats-grid {
  direction: rtl;
}

[dir="rtl"] .testimonials {
  direction: rtl;
}

[dir="rtl"] .footer {
  direction: rtl;
}

/* 动画RTL支持 */
[dir="rtl"] .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式RTL支持 */
@media (max-width: 768px) {
  [dir="rtl"] .mobile-menu {
    right: 0;
    left: auto;
  }
  
  [dir="rtl"] .mobile-menu-item {
    text-align: right;
  }
}

/* 打印样式RTL支持 */
@media print {
  [dir="rtl"] * {
    direction: rtl !important;
    text-align: right !important;
  }
}
