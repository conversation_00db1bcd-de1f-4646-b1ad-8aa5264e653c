'use client';

import { Copy, ExternalLink, Image as ImageIcon } from 'lucide-react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { ReactNode } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ViewOrderDialogProps {
  children: ReactNode;
  orderNumber?: string | null;
  orderScreenshot?: string | null;
  submittedAt?: string | null;
}

export function ViewOrderDialog({
  children,
  orderNumber,
  orderScreenshot,
  submittedAt,
}: ViewOrderDialogProps) {
  const t = useTranslations('my-accepted-tasks');

  const copyOrderNumber = () => {
    if (orderNumber) {
      navigator.clipboard.writeText(orderNumber);
      toast.success(t('messages.orderNumberCopied'));
    }
  };

  const openScreenshot = () => {
    if (orderScreenshot) {
      window.open(orderScreenshot, '_blank');
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent className='w-full max-w-md mx-4 sm:mx-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <ExternalLink className='h-5 w-5' />
            {t('viewOrder.title')}
          </DialogTitle>
          <DialogDescription>{t('viewOrder.description')}</DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 订单号 */}
          <Card>
            <CardContent className='p-4'>
              <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm font-medium text-muted-foreground'>
                    {t('viewOrder.orderNumber')}
                  </span>
                  <Badge
                    variant='secondary'
                    className='bg-green-100 text-green-800'
                  >
                    {t('viewOrder.submitted')}
                  </Badge>
                </div>

                {orderNumber ? (
                  <div className='flex items-center gap-2'>
                    <div className='flex-1 p-2 bg-muted rounded border font-mono text-sm'>
                      {orderNumber}
                    </div>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={copyOrderNumber}
                      className='shrink-0'
                    >
                      <Copy className='h-4 w-4' />
                    </Button>
                  </div>
                ) : (
                  <div className='text-sm text-muted-foreground'>
                    {t('viewOrder.noOrderNumber')}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 订单截图 */}
          <Card>
            <CardContent className='p-4'>
              <div className='space-y-3'>
                <span className='text-sm font-medium text-muted-foreground'>
                  {t('viewOrder.orderScreenshot')}
                </span>

                {orderScreenshot ? (
                  <div className='space-y-2'>
                    <div
                      className='relative aspect-video bg-muted rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity'
                      onClick={openScreenshot}
                    >
                      <Image
                        src={orderScreenshot}
                        alt={t('viewOrder.orderScreenshot')}
                        width={400}
                        height={225}
                        className='w-full h-full object-cover'
                      />
                      <div className='absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity'>
                        <div className='bg-white/90 rounded-lg p-2'>
                          <ExternalLink className='h-4 w-4' />
                        </div>
                      </div>
                    </div>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={openScreenshot}
                      className='w-full'
                    >
                      <ImageIcon className='h-4 w-4 mr-2' />
                      {t('viewOrder.viewScreenshot')}
                    </Button>
                  </div>
                ) : (
                  <div className='text-sm text-muted-foreground'>
                    {t('viewOrder.noOrderScreenshot')}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 提交时间 */}
          {submittedAt && (
            <Card>
              <CardContent className='p-4'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm font-medium text-muted-foreground'>
                    {t('viewOrder.submittedAt')}
                  </span>
                  <span className='text-sm'>
                    {new Date(submittedAt).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 下一步提示 */}
          <div className='p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-950 dark:border-blue-800'>
            <div className='text-sm text-blue-800 dark:text-blue-300'>
              <div className='font-medium mb-1'>{t('viewOrder.nextSteps')}</div>
              <div>{t('viewOrder.nextStepsDescription')}</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
