# RefundGo Component Library

## Overview

RefundGo uses a modern component architecture built on shadcn/ui, Radix UI, and Tailwind CSS. The component library provides consistent, accessible, and responsive UI elements across the application.

## Core Technologies

- **Base Components**: shadcn/ui + Radix UI
- **Styling**: Tailwind CSS with custom design tokens
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **3D Graphics**: React Three Fiber + Drei

## Component Categories

### 1. Brand Components

#### RefundGo Logo System

The logo system provides multiple variants for different contexts with VIP integration and theme support.

**Available Variants:**
- `RefundGoLogo` - Full logo with all features
- `RefundGoLogoCompact` - Compact version for navigation
- `RefundGoLogoIcon` - Icon-only version
- `RefundGoLogoHomepage` - Homepage-specific variant

**Props Interface:**
```typescript
interface RefundGoLogoProps {
  variant?: 'static' | 'full' | 'compact' | 'icon';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  showTagline?: boolean;
  darkMode?: boolean;
  vipLevel?: 'basic' | 'premium' | 'vip' | 'enterprise';
  forceWhiteText?: boolean;
  className?: string;
}
```

**VIP Color System:**
```typescript
const vipColors = {
  basic: { 
    icon: 'from-blue-500 to-blue-600', 
    accent: 'bg-green-500' 
  },
  premium: { 
    icon: 'from-purple-500 to-purple-600', 
    accent: 'bg-yellow-500' 
  },
  vip: { 
    icon: 'from-yellow-500 to-orange-500', 
    accent: 'bg-red-500' 
  },
  enterprise: { 
    icon: 'from-gray-700 to-gray-900', 
    accent: 'bg-emerald-500' 
  }
};
```

**Usage Examples:**
```tsx
// Navigation logo with theme support
<RefundGoLogoCompact 
  animated={true} 
  darkMode={theme === 'dark'} 
/>

// Sidebar with VIP integration
<RefundGoLogoIcon 
  size="sm" 
  vipLevel={getVipLevel()}
  darkMode={theme === 'dark'}
/>

// Footer with forced white text
<RefundGoLogo 
  variant="static" 
  showTagline={true}
  forceWhiteText={true}
  className="bg-transparent shadow-none"
/>
```

### 2. Navigation Components

#### Modern Navbar

Responsive navigation with theme support and mobile optimization.

**Features:**
- Mobile-first responsive design
- Dark/light theme integration
- User authentication state
- Dropdown menus with proper accessibility
- Logo integration with VIP support

#### App Sidebar

Dashboard sidebar with dynamic VIP theming.

**Features:**
- Collapsible design
- VIP-based logo colors
- Navigation state management
- Responsive behavior

### 3. Form Components

#### Enhanced Form Elements

Built on React Hook Form + Zod validation with consistent styling.

**Available Components:**
- `Input` - Text inputs with validation
- `Textarea` - Multi-line text input
- `Select` - Dropdown selection
- `Checkbox` - Boolean input
- `RadioGroup` - Single selection
- `DatePicker` - Date selection
- `FileUpload` - File input with preview

**Form Pattern:**
```tsx
const form = useForm<FormData>({
  resolver: zodResolver(schema),
  defaultValues: {}
});

<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)}>
    <FormField
      control={form.control}
      name="email"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input placeholder="Enter email" {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  </form>
</Form>
```

### 4. Data Display Components

#### Task Cards

Responsive task display with status indicators.

```tsx
interface TaskCardProps {
  task: {
    id: string;
    title: string;
    description: string;
    reward: number;
    status: string;
  };
  className?: string;
  onAccept?: (taskId: string) => void;
}
```

#### Data Tables

Server-side pagination with sorting and filtering.

**Features:**
- TanStack Table integration
- Server-side operations
- Responsive design
- Export functionality

### 5. Feedback Components

#### Toast Notifications

Consistent notification system using sonner.

```tsx
import { toast } from 'sonner';

// Success notification
toast.success('Task completed successfully!');

// Error notification
toast.error('Failed to process payment');

// Loading notification
toast.loading('Processing...');
```

#### Loading States

Skeleton loaders and spinners for better UX.

```tsx
// Skeleton loader
<Skeleton className="h-4 w-[250px]" />

// Spinner
<Spinner size="sm" />

// Loading button
<Button disabled>
  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
  Please wait
</Button>
```

## Responsive Design System

### Breakpoint Strategy

```css
/* Mobile-first approach */
.component {
  /* Mobile: 320px+ */
  @apply text-sm;
  
  /* Tablet: 768px+ */
  @screen md {
    @apply text-base;
  }
  
  /* Desktop: 1024px+ */
  @screen lg {
    @apply text-lg;
  }
}
```

### Grid Layouts

```tsx
// Responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {items.map(item => <Card key={item.id} {...item} />)}
</div>

// Auto-fit grid
<div className="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-4">
  {items.map(item => <Card key={item.id} {...item} />)}
</div>
```

### Container System

```tsx
// Page container
<div className="container mx-auto px-4 py-8">
  <div className="max-w-4xl mx-auto">
    {/* Content */}
  </div>
</div>

// Section container
<section className="py-16 lg:py-24">
  <div className="container mx-auto px-4">
    {/* Section content */}
  </div>
</section>
```

## Theme System

### Dark Mode Implementation

```tsx
import { useTheme } from 'next-themes';

function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
    >
      {theme === 'dark' ? <Sun /> : <Moon />}
    </Button>
  );
}
```

### CSS Variables

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  /* ... other variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  /* ... other variables */
}
```

## Animation System

### Framer Motion Integration

```tsx
import { motion } from 'framer-motion';

// Fade in animation
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.3 }}
>
  {content}
</motion.div>

// Stagger children
<motion.div
  variants={{
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }}
  initial="hidden"
  animate="show"
>
  {items.map(item => (
    <motion.div
      key={item.id}
      variants={{
        hidden: { opacity: 0, y: 20 },
        show: { opacity: 1, y: 0 }
      }}
    >
      {item.content}
    </motion.div>
  ))}
</motion.div>
```

### CSS Animations

```css
/* Gradient sweep animation */
@keyframes gradient-sweep {
  0% { background-position: -200% center; }
  100% { background-position: 200% center; }
}

.gradient-sweep {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: gradient-sweep 2s ease-in-out infinite;
}
```

## Accessibility Features

### ARIA Support

```tsx
// Proper ARIA labels
<Button
  aria-label="Close dialog"
  aria-expanded={isOpen}
  aria-controls="dialog-content"
>
  <X className="h-4 w-4" />
</Button>

// Screen reader support
<div role="status" aria-live="polite">
  {statusMessage}
</div>
```

### Keyboard Navigation

```tsx
// Focus management
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Escape') {
    onClose();
  }
  if (e.key === 'Enter' || e.key === ' ') {
    onSelect();
  }
};

<div
  tabIndex={0}
  onKeyDown={handleKeyDown}
  className="focus:outline-none focus:ring-2 focus:ring-primary"
>
  {content}
</div>
```

## Performance Optimizations

### Code Splitting

```tsx
// Lazy load heavy components
const Chart = lazy(() => import('./Chart'));
const ThreeScene = lazy(() => import('./ThreeScene'));

// Usage with Suspense
<Suspense fallback={<Skeleton className="h-64" />}>
  <Chart data={chartData} />
</Suspense>
```

### Image Optimization

```tsx
import Image from 'next/image';

// Optimized images
<Image
  src="/hero-image.jpg"
  alt="Hero image"
  width={1200}
  height={600}
  priority
  className="object-cover"
/>
```

## Testing Components

### Component Testing

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

---

**Component Count**: 50+ reusable components  
**Design System**: shadcn/ui + Tailwind CSS  
**Accessibility**: WCAG 2.1 AA compliant  
**Last Updated**: 2025-01-29
