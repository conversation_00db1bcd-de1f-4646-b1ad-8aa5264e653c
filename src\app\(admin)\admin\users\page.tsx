import { AdminPageLayout } from '@/components/admin/admin-page-layout';
import { UsersManagement } from '@/components/admin/users-management';
import { requireAdmin } from '@/lib/admin-auth';
import { getMemberPlanText, getUserStatusText } from '@/lib/constants';
import prisma from '@/lib/db';
import { constructMetadata } from '@/lib/utils';

export const metadata = constructMetadata({
  title: '用户管理 - 管理后台',
  description: '管理系统中的所有用户，查看用户信息、调整会员套餐和账户余额',
});

export default async function UsersPage() {
  // 验证管理员权限
  await requireAdmin();

  // 直接从数据库获取用户数据
  const dbUsers = await prisma.user.findMany({
    select: {
      id: true,
      name: true,
      email: true,
      image: true,
      role: true,
      isActive: true,
      memberPlan: true,
      memberPlanExpiry: true,
      balance: true,
      status: true,
      completedTasks: true,
      publishedTasks: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // 转换数据格式以匹配前端需求
  const users = dbUsers.map(user => ({
    id: user.id,
    nickname: user.name || '未设置',
    email: user.email || '',
    avatar: user.image || '',
    memberPlan: user.memberPlan,
    memberPlanExpiry:
      user.memberPlanExpiry?.toISOString() || '2099-12-31T23:59:59.000Z',
    balance: user.balance,
    completedTasks: user.completedTasks,
    publishedTasks: user.publishedTasks,
    status: user.status,
    registerDate: user.createdAt.toISOString().split('T')[0],
  }));

  // 统计数据
  const stats = {
    totalUsers: users.length,
    normalUsers: users.filter((u: any) => u.status === 'ACTIVE').length,
    frozenUsers: users.filter((u: any) => u.status === 'FROZEN').length,
    freeUsers: users.filter((u: any) => u.memberPlan === 'FREE').length,
    proUsers: users.filter((u: any) => u.memberPlan === 'PRO').length,
    businessUsers: users.filter((u: any) => u.memberPlan === 'BUSINESS').length,
    totalBalance: users.reduce((sum: number, u: any) => sum + u.balance, 0),
    totalCompletedTasks: users.reduce(
      (sum: number, u: any) => sum + u.completedTasks,
      0,
    ),
    totalPublishedTasks: users.reduce(
      (sum: number, u: any) => sum + u.publishedTasks,
      0,
    ),
  };

  return (
    <AdminPageLayout title='管理后台' breadcrumbPage='用户管理' href='/admin'>
      <UsersManagement initialUsers={users} stats={stats} />
    </AdminPageLayout>
  );
}
