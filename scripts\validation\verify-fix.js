const fs = require('fs');

console.log('=== 验证邮件国际化修复 ===\n');

try {
  // 检查email.ts文件
  const emailContent = fs.readFileSync('src/lib/email.ts', 'utf8');

  // 检查是否有重复的函数定义
  const functions = [
    'sendTaskCancelledPublisherEmail',
    'sendTaskCancelledAccepterEmail',
    'sendTaskAcceptedPublisherEmail',
  ];

  let duplicateFound = false;

  functions.forEach(funcName => {
    const matches = emailContent.match(
      new RegExp(`export async function ${funcName}`, 'g')
    );
    if (matches && matches.length > 1) {
      console.log(`❌ 发现重复函数: ${funcName} (${matches.length}次)`);
      duplicateFound = true;
    } else {
      console.log(`✅ ${funcName}: 正常`);
    }
  });

  // 检查是否包含国际化模板导入
  const i18nImports = [
    'withdrawalApprovedTemplateI18n',
    'withdrawalRejectedTemplateI18n',
    'taskCancelledPublisherTemplateI18n',
    'taskCancelledAccepterTemplateI18n',
    'taskAcceptedPublisherTemplateI18n',
  ];

  console.log('\n📦 国际化模板导入检查:');
  i18nImports.forEach(importName => {
    if (emailContent.includes(importName)) {
      console.log(`✅ ${importName}: 已导入`);
    } else {
      console.log(`❌ ${importName}: 未导入`);
    }
  });

  // 检查是否包含语言支持
  console.log('\n🌐 语言支持检查:');
  if (emailContent.includes("const language = data.language || 'zh';")) {
    console.log('✅ 语言检测逻辑: 正常');
  } else {
    console.log('❌ 语言检测逻辑: 缺失');
  }

  if (emailContent.includes('subjects = {')) {
    console.log('✅ 邮件主题国际化: 正常');
  } else {
    console.log('❌ 邮件主题国际化: 缺失');
  }

  if (!duplicateFound) {
    console.log('\n🎉 所有重复函数问题已修复！');
    console.log('✨ RefundGo邮件国际化系统运行正常');
  } else {
    console.log('\n⚠️ 仍有重复函数需要处理');
  }
} catch (error) {
  console.log(`❌ 检查失败: ${error.message}`);
}

console.log('\n=== 验证完成 ===');
