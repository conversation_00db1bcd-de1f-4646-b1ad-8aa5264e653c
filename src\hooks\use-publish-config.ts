import { useQuery } from '@tanstack/react-query';

import {
  Platform,
  Category,
  ChargebackType,
  PaymentMethod,
  ApiResponse,
} from '@/types/rates';

// 获取启用的平台列表
export function useActivePlatforms() {
  return useQuery({
    queryKey: ['active-platforms'],
    queryFn: async (): Promise<Platform[]> => {
      const response = await fetch('/api/user/platforms');
      const data: ApiResponse<Platform[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取平台列表失败');
      }

      // API已经只返回启用的平台
      return data.data || [];
    },
  });
}

// 获取启用的分类列表
export function useActiveCategories() {
  return useQuery({
    queryKey: ['active-categories'],
    queryFn: async (): Promise<Category[]> => {
      const response = await fetch('/api/user/categories');
      const data: ApiResponse<Category[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取分类列表失败');
      }

      // API已经只返回启用的分类
      return data.data || [];
    },
  });
}

// 获取启用的拒付类型列表
export function useActiveChargebackTypes() {
  return useQuery({
    queryKey: ['active-chargeback-types'],
    queryFn: async (): Promise<ChargebackType[]> => {
      const response = await fetch('/api/user/chargeback-types');
      const data: ApiResponse<ChargebackType[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取拒付类型列表失败');
      }

      // API已经只返回启用的拒付类型
      return data.data || [];
    },
  });
}

// 获取启用的支付方式列表
export function useActivePaymentMethods() {
  return useQuery({
    queryKey: ['active-payment-methods'],
    queryFn: async (): Promise<PaymentMethod[]> => {
      const response = await fetch('/api/user/payment-methods');
      const data: ApiResponse<PaymentMethod[]> = await response.json();

      if (!data.success) {
        throw new Error(data.message || '获取支付方式列表失败');
      }

      // API已经只返回启用的支付方式
      return data.data || [];
    },
  });
}

// 获取所有发布配置数据的组合 hook
export function usePublishConfig() {
  const platforms = useActivePlatforms();
  const categories = useActiveCategories();
  const chargebackTypes = useActiveChargebackTypes();
  const paymentMethods = useActivePaymentMethods();

  return {
    platforms: platforms.data || [],
    categories: categories.data || [],
    chargebackTypes: chargebackTypes.data || [],
    paymentMethods: paymentMethods.data || [],
    isLoading:
      platforms.isLoading ||
      categories.isLoading ||
      chargebackTypes.isLoading ||
      paymentMethods.isLoading,
    error:
      platforms.error ||
      categories.error ||
      chargebackTypes.error ||
      paymentMethods.error,
  };
}
