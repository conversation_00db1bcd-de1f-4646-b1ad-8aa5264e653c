export interface TaskReviewApprovedAccepterEmailData {
  accepterName: string;
  accepterEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  reviewedAt: string;
  orderNumber?: string;
  trackingNumber?: string;
  deliveryDeadline: string;
  estimatedCommission: number;
}

export const taskReviewApprovedAccepterTemplate = (
  data: TaskReviewApprovedAccepterEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>审核通过通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #28a745; margin-bottom: 20px; text-align: center;">✅ 审核通过！</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.accepterName}！您提交的订单和物流信息已通过审核。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">审核时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.reviewedAt}</span>
        </div>
        
        ${
          data.orderNumber
            ? `
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">订单号：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.orderNumber}</span>
        </div>
        `
            : ''
        }
        
        ${
          data.trackingNumber
            ? `
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">物流单号：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.trackingNumber}</span>
        </div>
        `
            : ''
        }
      </div>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">下一步操作</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">当前状态：</span>
          <span style="color: #28a745; font-weight: bold; margin-left: 10px;">等待发布者确认收货</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">确认收货期限：</span>
          <span style="color: #333; margin-left: 10px;">${data.deliveryDeadline}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">预计酬金：</span>
          <span style="color: #28a745; font-weight: bold; font-size: 16px; margin-left: 10px;">
            $${data.estimatedCommission}
          </span>
        </div>
      </div>
      
      <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #155724; margin: 0; font-size: 14px;">
          🎉 <strong>恭喜！您的委托进入收货阶段：</strong><br>
          • 订单和物流信息已通过发布者审核<br>
          • 现在等待发布者确认收货<br>
          • 物流单号已自动注册到跟踪系统<br>
          • 如发布者30天内未确认收货，系统将自动确认<br>
          • 确认收货后，押金和酬金将立即发放
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
