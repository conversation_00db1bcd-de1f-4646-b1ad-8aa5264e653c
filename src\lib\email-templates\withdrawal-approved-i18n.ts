import {
  emailStyles,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface WithdrawalApprovedEmailData {
  userName: string;
  userEmail: string;
  amount: number;
  fee: number;
  actualAmount: number;
  currency: string;
  withdrawalMethod: string;
  processedAt: string;
  transactionId?: string;
  estimatedArrival?: string;
  language?: 'zh' | 'en';
}

export const withdrawalApprovedTemplateI18n = (
  data: WithdrawalApprovedEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.processedAt, language);
  const formattedAmount = formatEmailAmount(
    data.amount,
    data.currency,
    language
  );
  const formattedFee = formatEmailAmount(data.fee, data.currency, language);
  const formattedActualAmount = formatEmailAmount(
    data.actualAmount,
    data.currency,
    language
  );

  // 脱敏处理交易ID
  const maskedTransactionId =
    data.transactionId && data.transactionId.length > 8
      ? `${data.transactionId.slice(0, 4)}****${data.transactionId.slice(-4)}`
      : data.transactionId || 'N/A';

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
          security: '为了您的账户安全，请勿向任何人透露您的账户信息。',
        },
        buttons: {
          viewWallet: '查看钱包',
          contactSupport: '联系客服',
        },
      },
      notifications: {
        withdrawalApproved: {
          title: '提现审核通过',
          greeting: '您的提现申请已通过审核，资金已成功到账！',
          description: '感谢您使用RefundGo，您的提现申请已经处理完成。',
          transactionDetails: '提现详情',
          amount: '申请金额：',
          fee: '手续费：',
          actualAmount: '实际到账：',
          withdrawalMethod: '提现方式：',
          transactionId: '交易编号：',
          processedAt: '处理时间：',
          estimatedArrival: '预计到账：',
          successNotice: '到账确认',
          successText: '资金已成功到账，请查收。如有疑问，请联系客服支持。',
          tips: '温馨提示',
          tipsList: {
            received: '资金已成功到账，请查收',
            contact: '如有疑问，请联系客服支持',
            thanks: '感谢您使用我们的服务',
          },
          thankYou: '感谢您选择RefundGo！',
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
          security:
            'For your account security, please do not share your account information with anyone.',
        },
        buttons: {
          viewWallet: 'View Wallet',
          contactSupport: 'Contact Support',
        },
      },
      notifications: {
        withdrawalApproved: {
          title: 'Withdrawal Approved',
          greeting:
            'Your withdrawal request has been approved and the funds have been successfully transferred!',
          description:
            'Thank you for using RefundGo. Your withdrawal request has been processed successfully.',
          transactionDetails: 'Withdrawal Details',
          amount: 'Requested Amount:',
          fee: 'Processing Fee:',
          actualAmount: 'Amount Received:',
          withdrawalMethod: 'Withdrawal Method:',
          transactionId: 'Transaction ID:',
          processedAt: 'Processed At:',
          estimatedArrival: 'Estimated Arrival:',
          successNotice: 'Transfer Confirmation',
          successText:
            'The funds have been successfully transferred. Please check your account. If you have any questions, please contact customer support.',
          tips: 'Important Notes',
          tipsList: {
            received:
              'Funds have been successfully transferred, please check your account',
            contact:
              'If you have any questions, please contact customer support',
            thanks: 'Thank you for using our service',
          },
          thankYou: 'Thank you for choosing RefundGo!',
        },
      },
    },
  };

  const t = translations[language];

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.withdrawalApproved.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .success-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #10b981, #059669);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .amount-highlight {
          font-size: 28px;
          font-weight: bold;
          color: #10b981;
          text-align: center;
          margin: 16px 0;
        }
        .transaction-card {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e2e8f0;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #64748b;
          font-weight: 500;
        }
        .info-value {
          color: #1e293b;
          font-weight: 600;
        }
        .success-notice {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 8px;
          padding: 16px;
          margin: 20px 0;
        }
        .tips-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .tips-list li {
          padding: 6px 0;
          padding-left: 20px;
          position: relative;
          color: #16a34a;
          font-size: 14px;
        }
        .tips-list li:before {
          content: "✓";
          position: absolute;
          left: 0;
          color: #16a34a;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Success Icon -->
          <div class="success-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}！
          </h2>
          
          <h3 style="text-align: center; color: #10b981; margin-bottom: 16px;">
            ${t.notifications.withdrawalApproved.greeting}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.withdrawalApproved.description}
          </p>

          <!-- Amount Highlight -->
          <div class="amount-highlight">
            ${formattedActualAmount}
          </div>

          <!-- Transaction Details -->
          <div class="transaction-card">
            <h4 style="margin-top: 0; color: #1e293b;">
              ${t.notifications.withdrawalApproved.transactionDetails}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.amount}</span>
              <span class="info-value">${formattedAmount}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.fee}</span>
              <span class="info-value">-${formattedFee}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.actualAmount}</span>
              <span class="info-value" style="color: #10b981; font-weight: bold;">${formattedActualAmount}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.withdrawalMethod}</span>
              <span class="info-value">${data.withdrawalMethod}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.transactionId}</span>
              <span class="info-value" style="font-family: monospace;">${maskedTransactionId}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.processedAt}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
            
            ${
              data.estimatedArrival
                ? `
            <div class="info-row">
              <span class="info-label">${t.notifications.withdrawalApproved.estimatedArrival}</span>
              <span class="info-value">${formatEmailDateTime(data.estimatedArrival, language)}</span>
            </div>
            `
                : ''
            }
          </div>

          <!-- Success Notice -->
          <div class="success-notice">
            <h4 style="margin-top: 0; color: #16a34a;">
              ✅ ${t.notifications.withdrawalApproved.successNotice}
            </h4>
            <p style="margin-bottom: 0; color: #16a34a;">
              ${t.notifications.withdrawalApproved.successText}
            </p>
          </div>

          <!-- Tips -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              💡 ${t.notifications.withdrawalApproved.tips}
            </h4>
            <ul class="tips-list">
              <li>${t.notifications.withdrawalApproved.tipsList.received}</li>
              <li>${t.notifications.withdrawalApproved.tipsList.contact}</li>
              <li>${t.notifications.withdrawalApproved.tipsList.thanks}</li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/dashboard/wallet" class="email-button email-button-primary">
              ${t.common.buttons.viewWallet}
            </a>
            <a href="${process.env.DOMAIN}/support" class="email-button email-button-secondary">
              ${t.common.buttons.contactSupport}
            </a>
          </div>

          <!-- Thank You -->
          <p style="text-align: center; color: #64748b; margin-top: 32px; font-style: italic;">
            ${t.notifications.withdrawalApproved.thankYou}
          </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.contact}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.security}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `.trim();
};
