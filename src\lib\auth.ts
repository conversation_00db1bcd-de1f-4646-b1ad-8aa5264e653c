import { PrismaAdapter } from '@auth/prisma-adapter';
import { redirect } from 'next/navigation';
import NextAuth from 'next-auth';
import type { NextAuthConfig } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';

import prisma from './db';
import { verifyPassword } from './password';
import { signInSchema } from './validations/auth';

interface Profile {
  email: string;
  name: string;
  picture: string;
}

interface Account {
  provider: string;
  providerAccountId: string;
  type: string;
}

async function upsertUserAndAccount(
  account: Account,
  profile: Profile,
): Promise<void> {
  const user = await prisma.user.upsert({
    where: {
      email: profile.email,
    },
    create: {
      email: profile.email,
      name: profile.name,
      image: profile.picture,
      role: 'USER',
    },
    update: {
      name: profile.name,
    },
  });

  if (user) {
    await prisma.account.upsert({
      where: {
        provider_providerAccountId: {
          provider: account.provider,
          providerAccountId: account.providerAccountId,
        },
      },
      create: {
        userId: user.id,
        type: account.type,
        provider: account.provider,
        providerAccountId: account.providerAccountId,
      },
      update: {},
    });
  }
}

export const authConfig: NextAuthConfig = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/sign-in',
  },
  trustHost: true,
  useSecureCookies: process.env.NEXTAUTH_URL?.startsWith('https://'),
  cookies: {
    sessionToken: {
      name: process.env.NEXTAUTH_URL?.startsWith('https://')
        ? '__Secure-next-auth.session-token'
        : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NEXTAUTH_URL?.startsWith('https://'),
        domain: undefined, // 让浏览器自动设置域名
      },
    },
  },
  debug: process.env.NODE_ENV === 'development',

  // 自定义日志处理
  logger: {
    error: error => {
      const isCredentialsError =
        error.message?.includes('CredentialsSignin') ||
        (error as any)?.type === 'CredentialsSignin' ||
        (error as any)?.name === 'CredentialsSignin' ||
        error.toString().includes('CredentialsSignin');

      if (!isCredentialsError && process.env.NODE_ENV === 'development') {
        console.error('NextAuth Error:', error);
      }
    },
    warn: code => {
      // 生产环境抑制警告
    },
    debug: () => {
      // 禁用debug日志
    },
  },
  providers: [
    Credentials({
      name: 'credentials',
      credentials: {
        email: { label: '邮箱', type: 'email' },
        password: { label: '密码', type: 'password' },
        verificationCode: { label: '验证码', type: 'text' },
      },
      async authorize(credentials) {
        try {
          const email = credentials?.email as string;
          const password = credentials?.password as string;
          const verificationCode = credentials?.verificationCode as string;

          if (!email) {
            return null;
          }

          // 查找用户
          const user = await prisma.user.findUnique({
            where: { email },
          });

          if (!user) {
            return null;
          }

          // 验证码登录
          if (verificationCode) {
            const loginIdentifier = `login:${email}`;

            // 验证码检查
            const verificationToken = await prisma.verificationToken.findFirst({
              where: {
                identifier: loginIdentifier,
                token: verificationCode,
                expires: {
                  gte: new Date(),
                },
              },
            });

            if (verificationToken) {
              // 删除已使用的验证码
              await prisma.verificationToken.delete({
                where: {
                  identifier_token: {
                    identifier: loginIdentifier,
                    token: verificationCode,
                  },
                },
              });

              // 更新登录时间
              await prisma.user.update({
                where: { id: user.id },
                data: { lastLoginAt: new Date() },
              });

              return {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role,
                image: user.image,
                balance: user.balance,
                createdAt: user.createdAt,
              };
            }
            return null;
          }

          // 密码登录
          if (!password) {
            return null;
          }

          // 验证输入
          const validationResult = signInSchema.safeParse({ email, password });
          if (!validationResult.success) {
            return null;
          }

          if (!(user as any).password) {
            return null;
          }

          // 验证密码
          const isPasswordValid = await verifyPassword(
            password,
            (user as any).password,
          );
          if (!isPasswordValid) {
            return null;
          }

          // 更新登录时间
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() },
          });

          return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            image: user.image,
            balance: user.balance,
            createdAt: user.createdAt,
          };
        } catch (error) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.balance = user.balance;
        token.createdAt = user.createdAt;
      }
      return token;
    },
    async session({ session, token }) {
      if (token.id) {
        session.user.id = token.id as string;
      }
      if (token.role) {
        session.user.role = token.role as string;
      }
      if (token.balance !== undefined) {
        session.user.balance = token.balance as number;
      }
      if (token.createdAt) {
        session.user.createdAt = token.createdAt as Date;
      }
      return session;
    },
    redirect({ url, baseUrl }) {
      console.log('NextAuth redirect callback:', { url, baseUrl });

      // Handle post-login redirects
      if (url.startsWith('/')) {
        // If it's a relative URL, make it absolute
        const finalUrl = `${baseUrl}${url}`;
        console.log('Relative URL redirect:', finalUrl);
        return finalUrl;
      } else if (url.startsWith('http')) {
        const urlObj = new URL(url);
        const baseUrlObj = new URL(baseUrl);

        console.log('URL comparison:', {
          urlOrigin: urlObj.origin,
          baseUrlOrigin: baseUrlObj.origin,
          urlHostname: urlObj.hostname,
          baseUrlHostname: baseUrlObj.hostname,
          urlPort: urlObj.port,
          baseUrlPort: baseUrlObj.port,
        });

        // Compare hostname and port, ignore protocol differences
        if (
          urlObj.hostname === baseUrlObj.hostname &&
          urlObj.port === baseUrlObj.port
        ) {
          console.log('Same origin redirect:', url);
          return url;
        }
      }
      // Default redirect to dashboard after login
      const defaultUrl = `${baseUrl}/zh/dashboard`;
      console.log('Default redirect to dashboard:', defaultUrl);
      return defaultUrl;
    },
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(authConfig);

export async function getAuthSession() {
  return await auth();
}

// 身份验证中间件函数
export async function requireAuth() {
  const session = await auth();

  if (!session?.user) {
    redirect('/sign-in');
  }

  return session;
}

// 角色权限验证
export async function requireRole(allowedRoles: string[]) {
  const session = await requireAuth();

  if (!allowedRoles.includes(session.user.role || '')) {
    throw new Error('Insufficient permissions');
  }

  return session;
}

// 会员等级验证
export async function requireMembership(requiredPlan: string) {
  const session = await requireAuth();

  // 获取用户会员信息
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { memberPlan: true, memberPlanExpiry: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  // 检查会员等级
  if (user.memberPlan !== requiredPlan) {
    redirect('/membership');
  }

  // 检查会员是否过期
  if (user.memberPlanExpiry && user.memberPlanExpiry < new Date()) {
    redirect('/membership');
  }

  return session;
}

// 检查用户是否有特定权限
export async function hasPermission(permission: string): Promise<boolean> {
  try {
    const session = await auth();
    if (!session?.user) return false;

    // 管理员拥有所有权限
    if (session.user.role === 'ADMIN') return true;

    // 根据权限类型检查
    switch (permission) {
      case 'publish:task':
        return true; // 所有用户都可以发布委托
      case 'manage:whitelist':
        return session.user.role === 'PREMIUM' || session.user.role === 'ADMIN';
      case 'access:premium':
        return session.user.role === 'PREMIUM' || session.user.role === 'ADMIN';
      default:
        return false;
    }
  } catch {
    return false;
  }
}

// 获取当前用户信息（可选）
export async function getCurrentUser() {
  const session = await auth();
  return session?.user || null;
}

// 检查是否为管理员
export async function isAdmin(): Promise<boolean> {
  const session = await auth();
  return session?.user?.role === 'ADMIN';
}

// 检查是否为高级用户
export async function isPremiumUser(): Promise<boolean> {
  const session = await auth();
  return session?.user?.role === 'PREMIUM' || session?.user?.role === 'ADMIN';
}
