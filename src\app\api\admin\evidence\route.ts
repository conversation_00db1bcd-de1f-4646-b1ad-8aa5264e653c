import { NextRequest, NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';

// 获取证据数据列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const search = searchParams.get('search');
    const platform = searchParams.get('platform');
    const evidenceStatus = searchParams.get('evidenceStatus');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // 构建where条件
    const where: any = {};

    // 搜索筛选（委托ID或发布者信息）
    if (search) {
      where.OR = [
        {
          id: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          publisher: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          publisher: {
            email: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // 平台筛选
    if (platform && platform !== 'all') {
      where.platformId = platform;
    }

    // 证据状态筛选（现在只支持已上传状态筛选）
    if (evidenceStatus && evidenceStatus === 'has_evidence') {
      where.NOT = {
        evidenceFiles: {
          isEmpty: true,
        },
      };
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询委托列表（只获取需要证据且已上传证据的委托）
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where: {
          ...where,
          evidenceUploadType: { not: 'NONE' }, // 排除无证据类型
          AND: [
            {
              OR: [
                // 有证据文件的委托
                {
                  evidenceFiles: {
                    isEmpty: false,
                  },
                },
                // 或者证据状态已审核
                {
                  evidenceStatus: 'UNDER_REVIEW',
                },
                // 或者证据状态已通过
                {
                  evidenceStatus: 'REVIEWED',
                },
                // 或者证据状态被拒绝
                {
                  evidenceStatus: 'REJECTED',
                },
              ],
            },
          ],
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.task.count({
        where: {
          ...where,
          evidenceUploadType: { not: 'NONE' },
          AND: [
            {
              OR: [
                {
                  evidenceFiles: {
                    isEmpty: false,
                  },
                },
                {
                  evidenceStatus: 'UNDER_REVIEW',
                },
                {
                  evidenceStatus: 'REVIEWED',
                },
                {
                  evidenceStatus: 'REJECTED',
                },
              ],
            },
          ],
        },
      }),
    ]);

    // 获取拒付类型名称
    const chargebackTypes = await prisma.chargebackType.findMany({
      where: { status: 'ACTIVE' },
    });

    // 创建ID到名称的映射
    const chargebackTypeMap = chargebackTypes.reduce(
      (map: Record<string, string>, type: any) => {
        map[type.id] = type.name;
        return map;
      },
      {} as Record<string, string>,
    );

    // 处理委托数据，添加证据状态
    const processedTasks = tasks.map((task: any) => ({
      taskId: task.id,
      platform: task.platform.name,
      productUrl: task.productUrl, // 添加产品链接字段
      chargebackTypes: task.chargebackTypeIds.map(
        (id: string) => chargebackTypeMap[id] || id,
      ),
      submitDate: task.createdAt.toISOString(),
      uploader: {
        nickname: task.publisher.name || '未知用户',
        email: task.publisher.email,
      },
      status: getEvidenceStatus(task),
      evidenceFiles: task.evidenceFiles,
      evidenceUploadType: task.evidenceUploadType,
      taskStatus: task.status,
      // 添加其他可能需要的字段
      rejectionReason: task.rejectionReason,
      evidenceUploadTime: task.evidenceUploadTime,
    }));

    // 计算统计数据
    const stats = {
      totalEvidence: processedTasks.length,
      pendingReview: processedTasks.filter((e: any) => e.status === '待审核')
        .length,
      reviewed: processedTasks.filter((e: any) => e.status === '已审核').length,
      rejected: processedTasks.filter((e: any) => e.status === '已拒绝').length,
    };

    return NextResponse.json({
      success: true,
      data: {
        evidence: processedTasks,
        stats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('获取证据数据失败:', error);
    return NextResponse.json({ error: '获取证据数据失败' }, { status: 500 });
  }
}

// 获取证据状态的辅助函数
function getEvidenceStatus(task: any) {
  // 如果委托没有证据要求
  if (task.evidenceUploadType === 'NONE') {
    return '无证据';
  }

  // 如果有证据状态字段，直接返回
  if (task.evidenceStatus) {
    switch (task.evidenceStatus) {
      case 'PENDING_SUBMISSION':
        return '待上传';
      case 'UNDER_REVIEW':
        return '待审核';
      case 'REVIEWED':
        return '已审核';
      case 'REJECTED':
        return '已拒绝';
      default:
        return '待上传';
    }
  }

  // 根据文件状态判断
  if (task.evidenceFiles && task.evidenceFiles.length > 0) {
    return '待审核'; // 有文件但没有明确状态，认为是待审核
  } else {
    if (task.evidenceUploadType === 'IMMEDIATE') {
      return '待审核'; // 立即上传类型，如果没有文件则认为是待审核状态
    } else {
      return '待上传'; // 稍后上传类型，默认为待上传
    }
  }
}
