import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import createMiddleware from 'next-intl/middleware';

import { routing } from './i18n/routing'; // eslint-disable-line import/no-unresolved

// 创建 next-intl 中间件
const handleI18nRouting = createMiddleware(routing);

export async function middleware(request: NextRequest) {
  const { nextUrl } = request;

  // 检查是否是 API 路由、静态资源或管理员路由，如果是则跳过 i18n 处理
  if (
    nextUrl.pathname.startsWith('/api') ||
    nextUrl.pathname.startsWith('/_next') ||
    nextUrl.pathname.startsWith('/_vercel') ||
    nextUrl.pathname.startsWith('/admin') ||
    nextUrl.pathname.includes('.')
  ) {
    // 对于 admin 路由，直接进行身份验证检查
    if (nextUrl.pathname.startsWith('/admin')) {
      try {
        const token = await getToken({
          req: request,
          secret: process.env.NEXTAUTH_SECRET,
          secureCookie: process.env.NEXTAUTH_URL?.startsWith('https://'),
          cookieName: process.env.NEXTAUTH_URL?.startsWith('https://')
            ? '__Secure-next-auth.session-token'
            : 'next-auth.session-token',
        });

        const isLoggedIn = !!token;
        const userRole = token?.role as string;

        console.log('Admin access attempt:', {
          isLoggedIn,
          userRole,
          pathname: nextUrl.pathname,
          tokenExists: !!token,
          tokenId: token?.id,
          tokenEmail: token?.email,
          tokenData: token
            ? {
                id: token.id,
                email: token.email,
                role: token.role,
                name: token.name,
              }
            : null,
          cookies: request.cookies.getAll().map(c => c.name),
        });

        if (!isLoggedIn) {
          console.log('Redirecting to sign-in: no token found');
          return NextResponse.redirect(new URL('/zh/sign-in', nextUrl));
        }

        // 检查管理员角色
        if (userRole !== 'ADMIN') {
          console.log('Access denied: user is not admin', { userRole });
          return NextResponse.redirect(new URL('/en/dashboard', nextUrl));
        }
      } catch (error) {
        console.error('Error checking admin token:', error);
        return NextResponse.redirect(new URL('/zh/sign-in', nextUrl));
      }
    }

    return NextResponse.next();
  }

  // 首先处理 i18n 路由
  const i18nResponse = handleI18nRouting(request);

  // 如果 i18n 中间件返回了重定向，直接返回
  if (i18nResponse.status === 307 || i18nResponse.status === 308) {
    return i18nResponse;
  }

  // 获取处理后的路径和语言信息
  const pathname = request.nextUrl.pathname;
  const locale = pathname.startsWith('/en')
    ? 'en'
    : pathname.startsWith('/zh')
      ? 'zh'
      : 'zh';

  // 获取token - 修复 cookie 名称匹配
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
    secureCookie: process.env.NEXTAUTH_URL?.startsWith('https://'),
    cookieName: process.env.NEXTAUTH_URL?.startsWith('https://')
      ? '__Secure-next-auth.session-token'
      : 'next-auth.session-token',
  });

  const isLoggedIn = !!token;

  // 调试信息：检查 cookies
  console.log('Token debug info:', {
    hasToken: !!token,
    tokenData: token ? { id: token.id, email: token.email } : null,
    cookies: request.cookies
      .getAll()
      .map(c => ({ name: c.name, value: `${c.value.substring(0, 20)}...` })),
    requestUrl: request.url,
    userAgent: request.headers.get('user-agent')?.substring(0, 50),
  });

  // 需要认证的路径（不包含语言前缀）
  const protectedPaths = [
    '/dashboard',
    '/profile',
    '/tickets',
    '/wallet',
    '/my-published-tasks',
    '/my-accepted-tasks',
    '/tasks',
    '/publish',
    '/membership',
    '/whitelist',
  ];

  // 获取不带语言前缀的路径
  const pathWithoutLocale =
    locale === 'en'
      ? pathname.replace('/en', '') || '/'
      : pathname.replace('/zh', '') || '/';

  console.log('Middleware processing:', {
    pathname,
    locale,
    pathWithoutLocale,
    isLoggedIn,
    hasToken: !!token,
    tokenId: token?.id,
  });

  // 检查路径
  const isProtectedPath = protectedPaths.some(path =>
    pathWithoutLocale.startsWith(path),
  );

  // 管理员路径检查已经在前面处理了，这里不需要重复检查

  // 受保护路径检查
  if (isProtectedPath) {
    if (!isLoggedIn) {
      const signInPath = locale === 'en' ? '/en/sign-in' : '/zh/sign-in';
      return NextResponse.redirect(new URL(signInPath, nextUrl));
    }
    return i18nResponse;
  }

  // 已登录用户访问登录页面重定向
  if (
    isLoggedIn &&
    (pathWithoutLocale === '/sign-in' || pathWithoutLocale === '/sign-up')
  ) {
    const dashboardPath = locale === 'en' ? '/en/dashboard' : '/zh/dashboard';
    console.log('Middleware redirect logged-in user from auth pages:', {
      from: pathname,
      to: dashboardPath,
      isLoggedIn,
      token: !!token,
      locale,
    });
    return NextResponse.redirect(new URL(dashboardPath, nextUrl));
  }

  return i18nResponse;
}

// 路径匹配配置
export const config = {
  matcher: [
    // 匹配所有路径，除了：
    // - API 路由 (/api)
    // - Next.js 内部路径 (/_next)
    // - Vercel 内部路径 (/_vercel)
    // - 包含点的文件 (如 favicon.ico)
    '/((?!api|_next|_vercel|.*\\..*).*)',
  ],
};
