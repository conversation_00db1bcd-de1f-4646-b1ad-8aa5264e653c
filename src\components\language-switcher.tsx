'use client';

import { Languages, Globe } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { useTransition } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRouter, usePathname } from '@/i18n/navigation';
import { routing } from '@/i18n/routing';

interface LanguageSwitcherProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

const localeNames = {
  zh: '中文',
  en: 'English',
} as const;

const localeFlags = {
  zh: '🇨🇳',
  en: '🇺🇸',
} as const;

export function LanguageSwitcher({
  variant = 'dropdown',
  size = 'default',
  className = '',
}: LanguageSwitcherProps) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const t = useTranslations('Common');

  const handleLocaleChange = (newLocale: string) => {
    startTransition(() => {
      router.replace(pathname, { locale: newLocale as 'zh' | 'en' });
    });
  };

  if (variant === 'button') {
    // 简单的切换按钮，在中英文之间切换
    const nextLocale = locale === 'zh' ? 'en' : 'zh';

    return (
      <Button
        variant='ghost'
        size={size}
        onClick={() => handleLocaleChange(nextLocale)}
        disabled={isPending}
        className={className}
        aria-label={`Switch to ${localeNames[nextLocale]}`}
        title={`Switch to ${localeNames[nextLocale]}`}
      >
        <Languages className='w-4 h-4' />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size={size}
          disabled={isPending}
          className={className}
          aria-label={`Current language: ${localeNames[locale as keyof typeof localeNames]}. Click to change language`}
          title={`Current language: ${localeNames[locale as keyof typeof localeNames]}`}
        >
          <Globe className='w-4 h-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        {routing.locales.map(loc => (
          <DropdownMenuItem
            key={loc}
            onClick={() => handleLocaleChange(loc)}
            className={locale === loc ? 'bg-accent' : ''}
          >
            <span className='mr-2'>
              {localeFlags[loc as keyof typeof localeFlags]}
            </span>
            {localeNames[loc as keyof typeof localeNames]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
