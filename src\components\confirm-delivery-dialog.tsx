'use client';

import { CheckCircle, Package, AlertTriangle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ConfirmDeliveryDialogProps {
  children?: React.ReactNode;
  taskId: string;
  commission: number;
  deposit: number;
  accepterName: string;
  onConfirm?: () => void;
}

export function ConfirmDeliveryDialog({
  children,
  taskId,
  commission,
  deposit,
  accepterName,
  onConfirm,
}: ConfirmDeliveryDialogProps) {
  const t = useTranslations('my-published-tasks');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirmDelivery = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/publisher/tasks/${taskId}/confirm-delivery`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || t('error.confirm'));
      }

      toast.success(t('success.confirm'), {
        description: result.message,
        duration: 6000,
      });

      // 调用父组件的确认处理函数
      onConfirm?.();

      // 关闭对话框
      setIsOpen(false);

      // 刷新页面或重新获取数据
      window.location.reload();
    } catch (error) {
      toast.error(t('error.confirm'), {
        description:
          error instanceof Error ? error.message : t('error.network'),
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialogClose = (open: boolean) => {
    setIsOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Package className='h-5 w-5' />
            {t('dialog.confirmDelivery.title')}
          </DialogTitle>
          <DialogDescription>
            {t('dialog.confirmDelivery.description')}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 警告提示 */}
          <div className='p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>
            <div className='flex items-start gap-3'>
              <AlertTriangle className='h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0' />
              <div className='text-sm'>
                <div className='font-medium text-yellow-800 dark:text-yellow-100 mb-1'>
                  {t('dialog.confirmDelivery.warning')}
                </div>
                <div className='text-yellow-700'>
                  {t('dialog.confirmDelivery.warningText')}
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className='flex gap-2 pt-2'>
            <Button
              type='button'
              variant='outline'
              onClick={() => handleDialogClose(false)}
              disabled={isLoading}
              className='flex-1'
            >
              {t('dialog.confirmDelivery.actions.cancel')}
            </Button>
            <Button
              type='button'
              onClick={handleConfirmDelivery}
              disabled={isLoading}
              className='flex-1 bg-green-600 hover:bg-green-700'
            >
              {isLoading ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                  {t('dialog.confirmDelivery.actions.processing')}
                </>
              ) : (
                <>
                  <CheckCircle className='h-4 w-4 mr-2' />
                  {t('dialog.confirmDelivery.actions.confirm')}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
