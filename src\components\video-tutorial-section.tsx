"use client";

import { motion } from "framer-motion";
import { Play } from "lucide-react";
import { useTranslations } from "next-intl";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { YouTubeEmbed } from "@/components/ui/youtube-embed";
import { videoTutorials } from "@/config/video-tutorials";

// Main Video Tutorial Section Component
export function VideoTutorialSection() {
  const t = useTranslations("HomePage");

  return (
    <section
      id="tutorial"
      className="py-16 md:py-24 bg-gradient-to-b from-background to-muted dark:from-background dark:to-muted"
      aria-labelledby="tutorial-heading"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <Badge className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white px-6 py-2 text-sm font-medium mb-6">
            <Play className="h-4 w-4 mr-2" />
            {t("tutorial.title")}
          </Badge>
          <h2
            id="tutorial-heading"
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary dark:text-text-primary mb-6"
          >
            {t("tutorial.title")}
          </h2>
          <p className="text-xl text-text-secondary dark:text-text-secondary max-w-3xl mx-auto">
            {t("tutorial.subtitle")}
          </p>
        </motion.div>

        {/* Single Video Container */}
        <div className="max-w-4xl mx-auto">
          {videoTutorials.map((video) => (
            <motion.div
              key={video.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="group"
            >
              <Card className="professional-card hover:shadow-xl transition-all duration-300 overflow-hidden">
                <CardContent className="p-0">
                  {/* Clean Video Container - No Overlay */}
                  <YouTubeEmbed
                    videoId={video.videoId}
                    title={t(video.titleKey)}
                    className="rounded-2xl"
                  />
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom Tip Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-16"
        >
          <div className="glass-linear rounded-2xl p-8 max-w-2xl mx-auto border border-border dark:border-border bg-card/80 dark:bg-card/80 backdrop-blur-sm">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to flex items-center justify-center">
                <Play className="w-6 h-6 text-white" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-text-primary dark:text-text-primary mb-2">
              {t("tutorial.quickStart")}
            </h3>
            <p className="text-text-secondary dark:text-text-secondary text-sm leading-relaxed">
              {t("tutorial.tip")}
            </p>
          </div>
        </motion.div>


      </div>
    </section>
  );
}
