import { getTranslations } from 'next-intl/server';
import React from 'react';

/**
 * Main page area loading component
 * Displays when main pages (homepage, login, register, etc.) are loading
 */
export default async function MainLoading() {
  const t = await getTranslations('Common.mainLoading');
  return (
    <div className='flex min-h-screen items-center justify-center'>
      <div className='flex flex-col items-center space-y-4'>
        {/* Main page loading animation */}
        <div className='relative'>
          <div className='h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-green-600'></div>
          <div className='absolute inset-0 flex items-center justify-center'>
            <div className='h-6 w-6 rounded-full bg-green-100'></div>
          </div>
        </div>

        {/* Loading text */}
        <div className='text-center'>
          <h2 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>
            {t('title')}
          </h2>
          <p className='text-sm text-gray-500 dark:text-gray-400'>
            {t('description')}
          </p>
        </div>

        {/* Brand loading indicator */}
        <div className='flex items-center space-x-2 text-sm text-gray-400'>
          <div className='h-2 w-2 bg-green-500 rounded-full animate-bounce'></div>
          <div className='h-2 w-2 bg-green-500 rounded-full animate-bounce delay-100'></div>
          <div className='h-2 w-2 bg-green-500 rounded-full animate-bounce delay-200'></div>
        </div>
      </div>
    </div>
  );
}
