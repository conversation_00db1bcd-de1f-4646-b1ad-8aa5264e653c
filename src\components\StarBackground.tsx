'use client';

import { Stars, OrbitControls } from '@react-three/drei';
import { Canvas } from '@react-three/fiber';

export default function StarBackground() {
  return (
    <div className='fixed inset-0 -z-10'>
      <Canvas camera={{ position: [0, 0, 1] }}>
        <Stars
          radius={200}
          depth={80}
          count={1500} // 减少粒子数量，提升性能
          factor={8} // 减少粒子大小变化
          saturation={0.3}
          fade
          speed={0.3} // 大幅降低移动速度，减少头晕感
        />
        <OrbitControls
          enableZoom={false}
          enablePan={false}
          enableRotate={true}
          autoRotate={true}
          autoRotateSpeed={0.1} // 大幅降低旋转速度，减少头晕感
        />
      </Canvas>
    </div>
  );
}
