'use client';

import { useEffect, useCallback, useRef, useState } from 'react';

import {
  PerformanceMonitor,
  PerformanceMetrics,
  PerformanceEvent,
  initPerformanceMonitoring,
  getPerformanceMonitor,
  type PerformanceConfig,
} from '@/lib/performance-monitor';

// 性能监控 Hook
export function usePerformanceMonitoring(config?: PerformanceConfig) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({ events: [] });
  const [events, setEvents] = useState<PerformanceEvent[]>([]);
  const monitorRef = useRef<PerformanceMonitor | null>(null);

  useEffect(() => {
    // 初始化性能监控
    const monitor = initPerformanceMonitoring({
      ...config,
      onMetric: (name, value, metadata) => {
        setMetrics(prev => ({ ...prev, [name]: value }));
        config?.onMetric?.(name, value, metadata);
      },
      onReport: report => {
        setMetrics(report);
        setEvents(report.events || []);
        config?.onReport?.(report);
      },
    });

    monitorRef.current = monitor;

    return () => {
      monitor?.cleanup();
    };
  }, [config]);

  // 手动记录性能标记
  const mark = useCallback((name: string) => {
    monitorRef.current?.mark(name);
  }, []);

  // 手动记录性能测量
  const measure = useCallback(
    (name: string, startMark?: string, endMark?: string) => {
      monitorRef.current?.measure(name, startMark, endMark);
    },
    [],
  );

  // 记录自定义指标
  const recordMetric = useCallback(
    (name: string, value: number, metadata?: any) => {
      monitorRef.current?.recordCustomMetric(name, value, metadata);
    },
    [],
  );

  // 记录用户交互
  const recordInteraction = useCallback(
    (type: string, target?: string, duration?: number) => {
      monitorRef.current?.recordInteraction(type, target, duration);
    },
    [],
  );

  // 记录错误
  const recordError = useCallback((error: Error, context?: string) => {
    monitorRef.current?.recordError(error, context);
  }, []);

  // 生成报告
  const generateReport = useCallback(() => {
    monitorRef.current?.report();
  }, []);

  return {
    metrics,
    events,
    mark,
    measure,
    recordMetric,
    recordInteraction,
    recordError,
    generateReport,
  };
}

// 组件性能监控 Hook
export function useComponentPerformance(componentName: string) {
  const mountTimeRef = useRef<number>(0);
  const renderCountRef = useRef<number>(0);
  const monitor = getPerformanceMonitor();

  useEffect(() => {
    // 记录组件挂载时间
    mountTimeRef.current = performance.now();
    monitor?.mark(`${componentName}-mount-start`);

    return () => {
      // 记录组件卸载
      const mountDuration = performance.now() - mountTimeRef.current;
      monitor?.recordCustomMetric(
        `${componentName}-mount-duration`,
        mountDuration,
      );
      monitor?.mark(`${componentName}-unmount`);
    };
  }, [componentName, monitor]);

  useEffect(() => {
    // 记录渲染次数
    renderCountRef.current++;
    monitor?.recordCustomMetric(
      `${componentName}-render-count`,
      renderCountRef.current,
    );
  });

  // 记录组件特定事件
  const recordEvent = useCallback(
    (eventName: string, duration?: number) => {
      monitor?.recordCustomMetric(
        `${componentName}-${eventName}`,
        duration || performance.now(),
      );
    },
    [componentName, monitor],
  );

  return { recordEvent };
}

// 页面性能监控 Hook
export function usePagePerformance(pageName: string) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadTime, setLoadTime] = useState<number>(0);
  const startTimeRef = useRef<number>(0);
  const monitor = getPerformanceMonitor();

  useEffect(() => {
    startTimeRef.current = performance.now();
    monitor?.mark(`${pageName}-page-start`);
    setIsLoading(true);

    // 页面加载完成检测
    const handleLoad = () => {
      const duration = performance.now() - startTimeRef.current;
      setLoadTime(duration);
      setIsLoading(false);
      monitor?.measure(`${pageName}-page-load`, `${pageName}-page-start`);
      monitor?.recordCustomMetric(`${pageName}-load-time`, duration);
    };

    // 检查页面是否已经加载完成
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
    }

    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, [pageName, monitor]);

  return { isLoading, loadTime };
}

// API 性能监控 Hook
export function useAPIPerformance() {
  const monitor = getPerformanceMonitor();

  const trackAPICall = useCallback(
    async <T>(apiName: string, apiCall: () => Promise<T>): Promise<T> => {
      const startTime = performance.now();
      monitor?.mark(`api-${apiName}-start`);

      try {
        const result = await apiCall();
        const duration = performance.now() - startTime;

        monitor?.measure(`api-${apiName}`, `api-${apiName}-start`);
        monitor?.recordCustomMetric(`api-${apiName}-duration`, duration);
        monitor?.recordCustomMetric(`api-${apiName}-success`, 1);

        return result;
      } catch (error) {
        const duration = performance.now() - startTime;

        monitor?.recordCustomMetric(`api-${apiName}-duration`, duration);
        monitor?.recordCustomMetric(`api-${apiName}-error`, 1);
        monitor?.recordError(error as Error, `API call: ${apiName}`);

        throw error;
      }
    },
    [monitor],
  );

  return { trackAPICall };
}

// 用户交互性能监控 Hook
export function useInteractionPerformance() {
  const monitor = getPerformanceMonitor();

  const trackClick = useCallback(
    (target: string) => {
      const startTime = performance.now();

      return () => {
        const duration = performance.now() - startTime;
        monitor?.recordInteraction('click', target, duration);
      };
    },
    [monitor],
  );

  const trackFormSubmit = useCallback(
    (formName: string) => {
      const startTime = performance.now();

      return () => {
        const duration = performance.now() - startTime;
        monitor?.recordInteraction('form-submit', formName, duration);
      };
    },
    [monitor],
  );

  const trackNavigation = useCallback(
    (from: string, to: string) => {
      const startTime = performance.now();

      return () => {
        const duration = performance.now() - startTime;
        monitor?.recordInteraction('navigation', `${from}->${to}`, duration);
      };
    },
    [monitor],
  );

  return {
    trackClick,
    trackFormSubmit,
    trackNavigation,
  };
}

// 资源加载性能监控 Hook
export function useResourcePerformance() {
  const [resourceMetrics, setResourceMetrics] = useState<{
    totalResources: number;
    totalSize: number;
    slowResources: Array<{ name: string; duration: number; size: number }>;
  }>({
    totalResources: 0,
    totalSize: 0,
    slowResources: [],
  });

  useEffect(() => {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();
      let totalSize = 0;
      const slowResources: Array<{
        name: string;
        duration: number;
        size: number;
      }> = [];

      entries.forEach((entry: any) => {
        if (entry.transferSize) {
          totalSize += entry.transferSize;
        }

        // 记录慢资源（超过1秒）
        if (entry.duration > 1000) {
          slowResources.push({
            name: entry.name,
            duration: entry.duration,
            size: entry.transferSize || 0,
          });
        }
      });

      setResourceMetrics(prev => ({
        totalResources: prev.totalResources + entries.length,
        totalSize: prev.totalSize + totalSize,
        slowResources: [...prev.slowResources, ...slowResources],
      }));
    });

    observer.observe({ entryTypes: ['resource'] });

    return () => observer.disconnect();
  }, []);

  return resourceMetrics;
}

// 内存使用监控 Hook
export function useMemoryMonitoring() {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  }>({
    usedJSHeapSize: 0,
    totalJSHeapSize: 0,
    jsHeapSizeLimit: 0,
  });

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000); // 每5秒更新

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
}

// 性能预算检查 Hook
export function usePerformanceBudget(budgets: Record<string, number>) {
  const [budgetStatus, setBudgetStatus] = useState<Record<string, boolean>>({});
  const { metrics } = usePerformanceMonitoring();

  useEffect(() => {
    const status: Record<string, boolean> = {};

    Object.entries(budgets).forEach(([metric, budget]) => {
      const value = metrics[metric as keyof PerformanceMetrics];
      status[metric] = typeof value === 'number' ? value <= budget : true;
    });

    setBudgetStatus(status);
  }, [metrics, budgets]);

  return budgetStatus;
}

export default usePerformanceMonitoring;
