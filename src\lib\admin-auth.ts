import { redirect } from 'next/navigation';

import { auth } from '@/lib/auth';

export async function requireAdmin() {
  const session = await auth();

  if (!session?.user) {
    redirect('/sign-in');
  }

  if ((session.user as any).role !== 'ADMIN') {
    redirect('/dashboard');
  }

  return session;
}

export async function isAdmin() {
  const session = await auth();
  return session?.user && (session.user as any).role === 'ADMIN';
}
