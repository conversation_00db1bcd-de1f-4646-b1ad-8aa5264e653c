import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';

import '@testing-library/jest-dom';
import { SkipToMainContent } from '../skip-to-main-content';

describe('SkipToMainContent', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders the skip link with correct attributes', () => {
    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    expect(skipLink).toBeInTheDocument();
    expect(skipLink).toHaveAttribute('href', '#main-content');
    expect(skipLink).toHaveAttribute('aria-label', '跳转到主要内容');
    expect(skipLink).toHaveClass('sr-only');
  });

  it('focuses and scrolls to main content when clicked', () => {
    // Create a mock main content element
    const mainContent = document.createElement('main');
    mainContent.id = 'main-content';
    document.body.appendChild(mainContent);

    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    fireEvent.click(skipLink);

    expect(mainContent.focus).toHaveBeenCalled();
    expect(mainContent.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });

    // Clean up
    document.body.removeChild(mainContent);
  });

  it('handles keyboard navigation (Enter key)', () => {
    // Create a mock main content element
    const mainContent = document.createElement('main');
    mainContent.id = 'main-content';
    document.body.appendChild(mainContent);

    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    fireEvent.keyDown(skipLink, { key: 'Enter' });

    expect(mainContent.focus).toHaveBeenCalled();
    expect(mainContent.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });

    // Clean up
    document.body.removeChild(mainContent);
  });

  it('handles keyboard navigation (Space key)', () => {
    // Create a mock main content element
    const mainContent = document.createElement('main');
    mainContent.id = 'main-content';
    document.body.appendChild(mainContent);

    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    fireEvent.keyDown(skipLink, { key: ' ' });

    expect(mainContent.focus).toHaveBeenCalled();
    expect(mainContent.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });

    // Clean up
    document.body.removeChild(mainContent);
  });

  it('does not trigger on other keys', () => {
    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    fireEvent.keyDown(skipLink, { key: 'Tab' });

    // Since no main content element exists, the mocks should not be called
    expect(HTMLElement.prototype.focus).not.toHaveBeenCalled();
    expect(Element.prototype.scrollIntoView).not.toHaveBeenCalled();
  });

  it('handles case when main content element does not exist', () => {
    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    // Should not throw error when main content doesn't exist
    expect(() => {
      fireEvent.click(skipLink);
    }).not.toThrow();

    // Since no main content element exists, the mocks should not be called
    expect(HTMLElement.prototype.focus).not.toHaveBeenCalled();
    expect(Element.prototype.scrollIntoView).not.toHaveBeenCalled();
  });

  it('has proper CSS classes for accessibility', () => {
    render(<SkipToMainContent />);

    const skipLink = screen.getByRole('link', { name: '跳转到主要内容' });

    // Check that it has the screen reader only class
    expect(skipLink).toHaveClass('sr-only');

    // Check that it has focus styles
    expect(skipLink).toHaveClass('focus:not-sr-only');
    expect(skipLink).toHaveClass('focus:absolute');
    expect(skipLink).toHaveClass('focus:top-4');
    expect(skipLink).toHaveClass('focus:left-4');
  });
});
