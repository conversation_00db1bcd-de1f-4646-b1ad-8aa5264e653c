// 会员套餐映射
export const MEMBER_PLAN_MAP = {
  FREE: '免费版',
  PRO: '专业版',
  BUSINESS: '商业版',
} as const;

// 用户状态映射
export const USER_STATUS_MAP = {
  ACTIVE: '正常',
  FROZEN: '冻结',
} as const;

// 用户角色映射
export const USER_ROLE_MAP = {
  USER: '普通用户',
  ADMIN: '管理员',
} as const;

// 反向映射
export const MEMBER_PLAN_REVERSE_MAP = {
  免费版: 'FREE',
  专业版: 'PRO',
  商业版: 'BUSINESS',
} as const;

export const USER_STATUS_REVERSE_MAP = {
  正常: 'ACTIVE',
  冻结: 'FROZEN',
} as const;

// 类型定义
export type MemberPlan = keyof typeof MEMBER_PLAN_MAP;
export type UserStatus = keyof typeof USER_STATUS_MAP;
export type UserRole = keyof typeof USER_ROLE_MAP;

// 辅助函数
export const getMemberPlanText = (plan: MemberPlan): string => {
  return MEMBER_PLAN_MAP[plan] || '未知套餐';
};

export const getUserStatusText = (status: UserStatus): string => {
  return USER_STATUS_MAP[status] || '未知状态';
};

export const getUserRoleText = (role: UserRole): string => {
  return USER_ROLE_MAP[role] || '未知角色';
};

// 获取所有选项
export const getMemberPlanOptions = () => {
  return Object.entries(MEMBER_PLAN_MAP).map(([value, label]) => ({
    value,
    label,
  }));
};

export const getUserStatusOptions = () => {
  return Object.entries(USER_STATUS_MAP).map(([value, label]) => ({
    value,
    label,
  }));
};
