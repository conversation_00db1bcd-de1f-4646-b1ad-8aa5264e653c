'use client';

import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Loader2,
  Smartphone,
  CreditCard,
  Bitcoin,
  MessageCircle,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface PaymentSelectorProps {
  amount: number;
  description?: string;
  onSuccess?: (orderNo: string) => void;
  onError?: (error: string) => void;
}

interface PaymentMethod {
  provider: string;
  method: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  feeRate: number;
  minAmount?: number;
}

const iconComponents = {
  Smartphone,
  CreditCard,
  Bitcoin,
  MessageCircle,
};

// 为不同支付方式设置颜色
const getIconColor = (method: string, category: string) => {
  const colorMap: Record<string, string> = {
    alipay: 'text-blue-600',
    paypal: 'text-blue-700',
    wxpay: 'text-green-600',
    crypto: 'text-orange-600',
  };

  if (category === 'crypto') return 'text-orange-600';
  return colorMap[method] || 'text-gray-600';
};

export default function PaymentSelector({
  amount,
  description,
  onSuccess,
  onError,
}: PaymentSelectorProps) {
  const t = useTranslations('messages');
  const [selectedMethod, setSelectedMethod] = useState('');
  const [paymentUrl, setPaymentUrl] = useState('');

  // 获取可用支付方式
  const { data: paymentMethodsData, isLoading: isLoadingMethods } = useQuery({
    queryKey: ['payment-methods'],
    queryFn: async () => {
      const res = await fetch('/api/payments/methods');
      if (!res.ok) throw new Error('获取支付方式失败');
      const data = await res.json();
      return data.methods as PaymentMethod[];
    },
  });

  const paymentMethods = paymentMethodsData || [];

  const createPaymentMutation = useMutation({
    mutationFn: async (data: { provider: string; paymentMethod: string }) => {
      const res = await fetch('/api/payments/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider: data.provider,
          amount,
          description,
          paymentMethod: data.paymentMethod,
          currency: data.provider === 'nowpayments' ? 'USD' : 'CNY',
        }),
      });

      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || '创建支付失败');
      }

      return res.json();
    },
    onSuccess: data => {
      if (data.success && data.paymentUrl) {
        // 对于NOWPayments，直接跳转到托管页面
        if (data.paymentUrl.includes('nowpayments.io')) {
          window.open(data.paymentUrl, '_blank');
          toast.success('已跳转到支付页面，请完成支付');
        } else {
          // 对于聚合易支付，显示二维码或跳转
          setPaymentUrl(data.paymentUrl);
          toast.success(t('success.create'));
        }
        onSuccess?.(data.orderNo);
      } else {
        throw new Error(data.message || '创建支付失败');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
      onError?.(error.message);
    },
  });

  const handlePay = () => {
    if (!selectedMethod) {
      toast.error('请选择支付方式');
      return;
    }

    const method = paymentMethods.find(
      (m: PaymentMethod) => `${m.provider}-${m.method}` === selectedMethod,
    );

    if (!method) return;

    // 验证最小支付金额
    if (method.minAmount && amount < method.minAmount) {
      toast.error(`该支付方式最小支付金额为 $${method.minAmount.toFixed(2)}`);
      return;
    }

    createPaymentMutation.mutate({
      provider: method.provider,
      paymentMethod: method.method,
    });
  };

  // 显示加载状态
  if (isLoadingMethods) {
    return (
      <div className='space-y-6'>
        <Card>
          <CardContent className='flex items-center justify-center py-8'>
            <Loader2 className='h-6 w-6 animate-spin mr-2' />
            加载支付方式...
          </CardContent>
        </Card>
      </div>
    );
  }

  // 获取图标组件
  const getIconComponent = (
    iconName: string,
    method: string,
    category: string,
  ) => {
    const IconComponent =
      iconComponents[iconName as keyof typeof iconComponents] || CreditCard;
    const colorClass = getIconColor(method, category);
    return <IconComponent className={`h-5 w-5 ${colorClass}`} />;
  };

  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            选择支付方式
            <span className='text-lg font-bold text-primary'>
              ¥{amount.toFixed(2)}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!Array.isArray(paymentMethods) || paymentMethods.length === 0 ? (
            <div className='text-center py-8 text-muted-foreground'>
              暂无可用的支付方式，请联系管理员配置
            </div>
          ) : (
            <RadioGroup
              value={selectedMethod}
              onValueChange={setSelectedMethod}
              className='space-y-3'
            >
              {paymentMethods.map((method: PaymentMethod) => {
                const value = `${method.provider}-${method.method}`;
                const isAmountTooLow =
                  method.minAmount !== undefined &&
                  method.minAmount > 0 &&
                  amount < method.minAmount;

                return (
                  <div
                    key={value}
                    className={`flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50 ${isAmountTooLow ? 'opacity-50' : ''}`}
                  >
                    <RadioGroupItem
                      value={value}
                      id={value}
                      disabled={isAmountTooLow}
                    />
                    <Label
                      htmlFor={value}
                      className={`flex items-center gap-3 cursor-pointer flex-1 ${isAmountTooLow ? 'cursor-not-allowed' : ''}`}
                    >
                      {getIconComponent(
                        method.icon,
                        method.method,
                        method.category,
                      )}
                      <div className='flex-1'>
                        <div className='flex items-center justify-between'>
                          <div className='font-medium'>{method.name}</div>
                          <div className='flex items-center gap-2'>
                            {method.feeRate > 0 && (
                              <span className='text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded'>
                                手续费 {method.feeRate}%
                              </span>
                            )}
                            {method.minAmount && method.minAmount > 0 && (
                              <span className='text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded'>
                                最低 ${method.minAmount.toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className='text-sm text-muted-foreground'>
                          {method.description}
                          {isAmountTooLow && (
                            <span className='text-red-600 ml-2'>
                              (支付金额低于最小限制)
                            </span>
                          )}
                        </div>
                      </div>
                    </Label>
                  </div>
                );
              })}
            </RadioGroup>
          )}

          <Button
            onClick={handlePay}
            disabled={!selectedMethod || createPaymentMutation.isPending}
            className='w-full mt-6'
            size='lg'
          >
            {createPaymentMutation.isPending ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                创建支付中...
              </>
            ) : (
              `支付 ¥${amount.toFixed(2)}`
            )}
          </Button>
        </CardContent>
      </Card>

      {/* 显示二维码支付（聚合易支付） */}
      {paymentUrl && !paymentUrl.includes('nowpayments.io') && (
        <Card>
          <CardHeader>
            <CardTitle>扫码支付</CardTitle>
          </CardHeader>
          <CardContent className='text-center'>
            <div className='p-4 bg-muted rounded-lg mb-4'>
              <p className='text-sm text-muted-foreground mb-2'>
                请使用对应APP扫描二维码完成支付
              </p>
              <div className='aspect-square max-w-xs mx-auto bg-white p-4 rounded-lg'>
                {/* 这里应该显示二维码，实际项目中可以使用 qrcode 库生成 */}
                <div className='w-full h-full bg-gray-100 flex items-center justify-center text-gray-500'>
                  二维码区域
                </div>
              </div>
            </div>
            <Button
              onClick={() => window.open(paymentUrl, '_blank')}
              variant='outline'
              size='sm'
            >
              在新窗口中打开支付页面
            </Button>
          </CardContent>
        </Card>
      )}

      <div className='text-xs text-muted-foreground text-center space-y-1'>
        <p>• 支付完成后页面将自动跳转</p>
        <p>• 如遇问题请联系客服</p>
        <p>• 订单15分钟内未支付将自动取消</p>
      </div>
    </div>
  );
}
