'use client';

import { useCallback } from 'react';

/**
 * A safe wrapper around useTranslations that handles missing NextIntlClientProvider context
 * Returns a fallback function when translation context is not available (e.g., in admin pages)
 *
 * Note: This hook cannot safely use useTranslations due to React's rules of hooks.
 * Components should use useTranslations directly when NextIntlClientProvider is available,
 * or handle missing translations at the component level.
 */
export function useSafeTranslations(namespace: string) {
  // Return a fallback function that returns empty strings
  // Components should provide their own fallback text
  const fallbackTranslation = useCallback(
    (key: string) => {
      console.warn(
        `Translation missing for key "${key}" in namespace "${namespace}"`,
      );
      return '';
    },
    [namespace],
  );

  return fallbackTranslation;
}
