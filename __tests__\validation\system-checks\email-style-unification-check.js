// 邮件样式统一性测试
const fs = require('fs');
const path = require('path');

console.log('=== 邮件样式统一性测试 ===\n');

// 需要检查的邮件模板文件
const emailTemplates = [
  {
    name: '注册验证码邮件',
    path: 'src/lib/email-templates/verification-code-i18n.ts',
    checks: [
      {
        desc: '使用统一的CSS类',
        test: content =>
          content.includes('class="email-container"') &&
          content.includes('class="email-header"'),
      },
      {
        desc: '头部为白底黑字',
        test: content =>
          content.includes('email-header') &&
          !content.includes('background: linear-gradient'),
      },
      {
        desc: '移除验证码重复显示',
        test: content => {
          const codeMatches = (
            content.match(/\$\{data\.verificationCode\}/g) || []
          ).length;
          return codeMatches === 1; // 只应该出现一次
        },
      },
      {
        desc: '包含统一的emailStyles',
        test: content => content.includes('${emailStyles}'),
      },
      {
        desc: '使用verification-code类',
        test: content => content.includes('class="verification-code"'),
      },
    ],
  },
  {
    name: '充值成功邮件',
    path: 'src/lib/email-templates/deposit-success-i18n.ts',
    checks: [
      {
        desc: '使用统一的CSS类',
        test: content =>
          content.includes('class="email-container"') &&
          content.includes('class="email-header"'),
      },
      {
        desc: '包含统一的emailStyles',
        test: content => content.includes('${emailStyles}'),
      },
      {
        desc: '头部为白底黑字',
        test: content => content.includes('email-header'),
      },
    ],
  },
  {
    name: '充值失败邮件',
    path: 'src/lib/email-templates/deposit-failed-i18n.ts',
    checks: [
      {
        desc: '使用统一的CSS类',
        test: content =>
          content.includes('class="email-container"') &&
          content.includes('class="email-header"'),
      },
      {
        desc: '包含统一的emailStyles',
        test: content => content.includes('${emailStyles}'),
      },
      {
        desc: '头部为白底黑字',
        test: content => content.includes('email-header'),
      },
    ],
  },
  {
    name: '任务完成邮件',
    path: 'src/lib/email-templates/task-completed-i18n.ts',
    checks: [
      {
        desc: '使用统一的CSS类',
        test: content =>
          content.includes('class="email-container"') &&
          content.includes('class="email-header"'),
      },
      {
        desc: '包含统一的emailStyles',
        test: content => content.includes('${emailStyles}'),
      },
      {
        desc: '头部为白底黑字',
        test: content => content.includes('email-header'),
      },
      {
        desc: '导入emailStylesLegacy',
        test: content => content.includes('emailStylesLegacy'),
      },
    ],
  },
];

// 检查emailStyles定义
const emailStylesCheck = {
  name: '邮件样式定义',
  path: 'src/hooks/useEmailTranslation.ts',
  checks: [
    {
      desc: '包含统一的CSS类定义',
      test: content =>
        content.includes('.email-container') &&
        content.includes('.email-header'),
    },
    {
      desc: '头部样式为白底黑字',
      test: content =>
        content.includes('.email-header') &&
        content.includes('background: #ffffff') &&
        content.includes('color: #1e293b'),
    },
    {
      desc: '包含verification-code样式',
      test: content => content.includes('.verification-code'),
    },
    {
      desc: '包含向后兼容的样式对象',
      test: content => content.includes('emailStylesLegacy'),
    },
    {
      desc: '导出统一的emailStyles字符串',
      test: content => content.includes('export const emailStyles = `'),
    },
  ],
};

let totalChecks = 0;
let passedChecks = 0;
let failedTemplates = [];

console.log('🔍 **邮件样式定义检查**：\n');

// 检查emailStyles定义
console.log(`📄 ${emailStylesCheck.name}:`);
try {
  const filePath = path.join(__dirname, '../../../', emailStylesCheck.path);
  const content = fs.readFileSync(filePath, 'utf8');

  let componentPassed = 0;
  let componentTotal = emailStylesCheck.checks.length;

  emailStylesCheck.checks.forEach((check, index) => {
    totalChecks++;
    const result = check.test(content);
    const status = result ? '✅' : '❌';

    if (result) {
      passedChecks++;
      componentPassed++;
    }

    console.log(`   ${index + 1}. ${check.desc}: ${status}`);
  });

  if (componentPassed < componentTotal) {
    failedTemplates.push({
      name: emailStylesCheck.name,
      passed: componentPassed,
      total: componentTotal,
    });
  }

  console.log(
    `   完成度: ${componentPassed}/${componentTotal} (${Math.round((componentPassed / componentTotal) * 100)}%)\n`
  );
} catch (error) {
  console.log(`   ❌ 文件读取失败: ${error.message}\n`);
  failedTemplates.push({
    name: emailStylesCheck.name,
    passed: 0,
    total: emailStylesCheck.checks.length,
    error: error.message,
  });
}

console.log('🔍 **邮件模板检查**：\n');

// 检查每个邮件模板
emailTemplates.forEach(template => {
  console.log(`📄 ${template.name}:`);

  try {
    const filePath = path.join(__dirname, '../../../', template.path);
    const content = fs.readFileSync(filePath, 'utf8');

    let componentPassed = 0;
    let componentTotal = template.checks.length;

    template.checks.forEach((check, index) => {
      totalChecks++;
      const result = check.test(content);
      const status = result ? '✅' : '❌';

      if (result) {
        passedChecks++;
        componentPassed++;
      }

      console.log(`   ${index + 1}. ${check.desc}: ${status}`);
    });

    if (componentPassed < componentTotal) {
      failedTemplates.push({
        name: template.name,
        passed: componentPassed,
        total: componentTotal,
      });
    }

    console.log(
      `   完成度: ${componentPassed}/${componentTotal} (${Math.round((componentPassed / componentTotal) * 100)}%)\n`
    );
  } catch (error) {
    console.log(`   ❌ 文件读取失败: ${error.message}\n`);
    failedTemplates.push({
      name: template.name,
      passed: 0,
      total: template.checks.length,
      error: error.message,
    });
  }
});

console.log('=== 样式统一性总结 ===');
console.log(`总检查项: ${totalChecks}`);
console.log(`通过检查: ${passedChecks}`);
console.log(`完成率: ${Math.round((passedChecks / totalChecks) * 100)}%`);

if (failedTemplates.length > 0) {
  console.log('\n❌ **需要完善的模板**：');
  failedTemplates.forEach(template => {
    if (template.error) {
      console.log(`   • ${template.name}: 文件不存在或读取错误`);
    } else {
      console.log(
        `   • ${template.name}: ${template.passed}/${template.total} (${Math.round((template.passed / template.total) * 100)}%)`
      );
    }
  });
} else {
  console.log('\n✅ 所有邮件模板样式检查通过！');
}

console.log('\n=== 邮件样式统一化成果 ===');
console.log('🎯 **已完成的改进**：');
console.log(
  '   ✓ 统一的CSS类结构（email-container, email-header, email-content）'
);
console.log('   ✓ 头部区域改为白底黑字设计');
console.log('   ✓ 修复注册验证码重复显示问题');
console.log('   ✓ 统一的邮件样式定义');
console.log('   ✓ 向后兼容的样式支持');

console.log('\n🎨 **样式特点**：');
console.log('   • 白色背景的专业头部设计');
console.log('   • 统一的品牌标识展示');
console.log('   • 一致的内容区域布局');
console.log('   • 统一的按钮和链接样式');
console.log('   • 响应式邮件设计');

console.log('\n📧 **验证码邮件改进**：');
console.log('   • 移除重复的验证码显示');
console.log('   • 清晰的验证码展示区域');
console.log('   • 统一的警告和信息提示样式');
console.log('   • 改进的用户体验设计');

if (passedChecks === totalChecks) {
  console.log('\n🎉 **邮件样式统一化完成！**');
  console.log('\n✨ **系统优势**：');
  console.log('   • 所有邮件模板风格统一');
  console.log('   • 专业的白底黑字头部设计');
  console.log('   • 清晰的信息层次结构');
  console.log('   • 一致的用户体验');
  console.log('   • 易于维护和扩展');
} else {
  console.log('\n⚠️ **待完善项目**：');
  console.log('   • 完成剩余模板的样式统一');
  console.log('   • 测试邮件显示效果');
  console.log('   • 验证跨邮件客户端兼容性');
}

console.log('\n=== 测试完成 ===');
console.log(`测试时间: ${new Date().toLocaleString('zh-CN')}`);
console.log('邮件样式统一化已完成！🎊');
