'use client';

import Image from 'next/image';
import { useState, useRef, useEffect } from 'react';

import { useIntersectionObserver } from '@/hooks/use-responsive';
import { cn } from '@/lib/utils';

// 图片优化配置
const IMAGE_CONFIG = {
  // 默认质量
  DEFAULT_QUALITY: 75,

  // 不同用途的质量设置
  QUALITY: {
    THUMBNAIL: 60,
    AVATAR: 70,
    CONTENT: 75,
    HERO: 85,
    PRINT: 95,
  },

  // 响应式断点
  BREAKPOINTS: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  },

  // 默认尺寸
  SIZES: {
    THUMBNAIL: '(max-width: 768px) 100px, 150px',
    AVATAR: '(max-width: 768px) 40px, 60px',
    CARD: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    HERO: '100vw',
    CONTENT: '(max-width: 768px) 100vw, 800px',
  },
} as const;

// 图片类型
export type ImageType =
  | 'thumbnail'
  | 'avatar'
  | 'card'
  | 'hero'
  | 'content'
  | 'custom';

// 优化图片组件属性
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  type?: ImageType;
  quality?: number;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  className?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  lazy?: boolean;
  aspectRatio?: string;
}

// 生成模糊占位符
function generateBlurDataURL(width: number = 10, height: number = 10): string {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;

  const ctx = canvas.getContext('2d');
  if (!ctx) return '';

  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#f3f4f6');
  gradient.addColorStop(1, '#e5e7eb');

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);

  return canvas.toDataURL();
}

// 获取图片配置
function getImageConfig(type: ImageType) {
  switch (type) {
    case 'thumbnail':
      return {
        quality: IMAGE_CONFIG.QUALITY.THUMBNAIL,
        sizes: IMAGE_CONFIG.SIZES.THUMBNAIL,
      };
    case 'avatar':
      return {
        quality: IMAGE_CONFIG.QUALITY.AVATAR,
        sizes: IMAGE_CONFIG.SIZES.AVATAR,
      };
    case 'card':
      return {
        quality: IMAGE_CONFIG.QUALITY.CONTENT,
        sizes: IMAGE_CONFIG.SIZES.CARD,
      };
    case 'hero':
      return {
        quality: IMAGE_CONFIG.QUALITY.HERO,
        sizes: IMAGE_CONFIG.SIZES.HERO,
      };
    case 'content':
      return {
        quality: IMAGE_CONFIG.QUALITY.CONTENT,
        sizes: IMAGE_CONFIG.SIZES.CONTENT,
      };
    default:
      return {
        quality: IMAGE_CONFIG.DEFAULT_QUALITY,
        sizes: '100vw',
      };
  }
}

// 优化图片组件
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  type = 'content',
  quality,
  priority = false,
  placeholder = 'blur',
  blurDataURL,
  sizes,
  className,
  objectFit = 'cover',
  loading = 'lazy',
  onLoad,
  onError,
  fallbackSrc,
  lazy = true,
  aspectRatio,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);
  const imageRef = useRef<HTMLDivElement>(null);

  // 懒加载检测
  const { isIntersecting } = useIntersectionObserver(
    imageRef as React.RefObject<Element>,
    {
      threshold: 0.1,
      rootMargin: '50px',
    },
  );

  const config = getImageConfig(type);
  const shouldLoad = !lazy || isIntersecting || priority;

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // 处理图片加载错误
  const handleError = () => {
    setHasError(true);
    setIsLoading(false);

    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    }

    onError?.();
  };

  // 生成默认模糊占位符
  const defaultBlurDataURL =
    blurDataURL || generateBlurDataURL(width || 400, height || 300);

  return (
    <div
      ref={imageRef}
      className={cn(
        'relative overflow-hidden',
        aspectRatio && `aspect-[${aspectRatio}]`,
        !aspectRatio && width && `w-[${width}px]`,
        !aspectRatio && height && `h-[${height}px]`,
        className,
      )}
    >
      {shouldLoad ? (
        <>
          {/* 加载状态 */}
          {isLoading && (
            <div className='absolute inset-0 bg-muted animate-pulse flex items-center justify-center'>
              <div className='text-muted-foreground text-sm'>加载中...</div>
            </div>
          )}

          {/* 错误状态 */}
          {hasError && !fallbackSrc && (
            <div className='absolute inset-0 bg-muted flex items-center justify-center'>
              <div className='text-muted-foreground text-sm'>图片加载失败</div>
            </div>
          )}

          {/* 实际图片 */}
          {!hasError && (
            <Image
              src={currentSrc}
              alt={alt}
              width={width}
              height={height}
              quality={quality || config.quality}
              priority={priority}
              placeholder={placeholder}
              blurDataURL={
                placeholder === 'blur' ? defaultBlurDataURL : undefined
              }
              sizes={sizes || config.sizes}
              loading={loading}
              onLoad={handleLoad}
              onError={handleError}
              className={cn(
                'transition-opacity duration-300',
                isLoading ? 'opacity-0' : 'opacity-100',
                objectFit === 'cover' && 'object-cover',
                objectFit === 'contain' && 'object-contain',
                objectFit === 'fill' && 'object-fill',
                objectFit === 'none' && 'object-none',
                objectFit === 'scale-down' && 'object-scale-down',
              )}
              style={{ width: '100%', height: '100%' }}
            />
          )}
        </>
      ) : (
        // 懒加载占位符
        <div className='absolute inset-0 bg-muted animate-pulse' />
      )}
    </div>
  );
}

// 头像组件
interface AvatarImageProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallback?: string;
  className?: string;
}

export function AvatarImage({
  src,
  alt,
  size = 'md',
  fallback,
  className,
}: AvatarImageProps) {
  const sizeMap = {
    sm: { width: 32, height: 32 },
    md: { width: 40, height: 40 },
    lg: { width: 56, height: 56 },
    xl: { width: 80, height: 80 },
  };

  const { width, height } = sizeMap[size];

  if (!src) {
    return (
      <div
        className={cn(
          'rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium',
          `w-[${width}px] h-[${height}px]`,
          className,
        )}
      >
        {fallback || alt.charAt(0).toUpperCase()}
      </div>
    );
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      type='avatar'
      className={cn('rounded-full', className)}
      fallbackSrc={fallback}
    />
  );
}

// 响应式图片组件
interface ResponsiveImageProps extends OptimizedImageProps {
  srcSet?: {
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
  };
}

export function ResponsiveImage({
  srcSet,
  src,
  ...props
}: ResponsiveImageProps) {
  // 根据屏幕尺寸选择合适的图片源
  const getResponsiveSrc = () => {
    if (!srcSet) return src;

    const width = typeof window !== 'undefined' ? window.innerWidth : 1024;

    if (width >= IMAGE_CONFIG.BREAKPOINTS.xl && srcSet.xl) return srcSet.xl;
    if (width >= IMAGE_CONFIG.BREAKPOINTS.lg && srcSet.lg) return srcSet.lg;
    if (width >= IMAGE_CONFIG.BREAKPOINTS.md && srcSet.md) return srcSet.md;
    if (width >= IMAGE_CONFIG.BREAKPOINTS.sm && srcSet.sm) return srcSet.sm;

    return src;
  };

  return <OptimizedImage {...props} src={getResponsiveSrc()} />;
}

// 图片画廊组件
interface ImageGalleryProps {
  images: Array<{
    src: string;
    alt: string;
    thumbnail?: string;
  }>;
  className?: string;
}

export function ImageGallery({ images, className }: ImageGalleryProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);

  return (
    <div className={cn('space-y-4', className)}>
      {/* 主图 */}
      <div className='aspect-video'>
        <OptimizedImage
          src={images[selectedIndex]?.src}
          alt={images[selectedIndex]?.alt}
          type='hero'
          className='rounded-lg'
        />
      </div>

      {/* 缩略图 */}
      {images.length > 1 && (
        <div className='flex gap-2 overflow-x-auto'>
          {images.map((image, index) => (
            <button
              key={index}
              type='button'
              onClick={() => setSelectedIndex(index)}
              aria-label={`查看图片: ${image.alt}`}
              className={cn(
                'flex-shrink-0 w-20 h-20 rounded border-2 transition-colors',
                selectedIndex === index
                  ? 'border-primary'
                  : 'border-transparent hover:border-muted-foreground',
              )}
            >
              <OptimizedImage
                src={image.thumbnail || image.src}
                alt={image.alt}
                type='thumbnail'
                className='rounded'
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

export default OptimizedImage;
