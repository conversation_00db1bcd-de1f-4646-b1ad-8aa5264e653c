'use client';

import { useEffect, useCallback, useRef, useState } from 'react';

// 键盘事件类型
export type KeyboardEventHandler = (event: KeyboardEvent) => void | boolean;

// 快捷键配置
export interface ShortcutConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  handler: () => void;
  description?: string;
  preventDefault?: boolean;
}

// 焦点管理配置
export interface FocusConfig {
  autoFocus?: boolean;
  trapFocus?: boolean;
  restoreFocus?: boolean;
  skipLinks?: boolean;
}

// 键盘导航 Hook
export function useKeyboardNavigation<T extends HTMLElement = HTMLElement>(
  shortcuts: ShortcutConfig[] = [],
  config: FocusConfig = {},
) {
  const containerRef = useRef<T>(null);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [focusableElements, setFocusableElements] = useState<HTMLElement[]>([]);

  // 获取可聚焦元素
  const getFocusableElements = useCallback(() => {
    if (!containerRef.current) return [];

    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    const elements = Array.from(
      containerRef.current.querySelectorAll(selector),
    ) as HTMLElement[];

    return elements.filter(el => {
      const style = window.getComputedStyle(el);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });
  }, []);

  // 更新可聚焦元素列表
  const updateFocusableElements = useCallback(() => {
    const elements = getFocusableElements();
    setFocusableElements(elements);
    return elements;
  }, [getFocusableElements]);

  // 聚焦到指定索引的元素
  const focusElement = useCallback(
    (index: number) => {
      const elements =
        focusableElements.length > 0
          ? focusableElements
          : updateFocusableElements();
      if (elements.length === 0) return;

      const targetIndex = Math.max(0, Math.min(index, elements.length - 1));
      const element = elements[targetIndex];

      if (element) {
        element.focus();
        setFocusedIndex(targetIndex);
      }
    },
    [focusableElements, updateFocusableElements],
  );

  // 聚焦到下一个元素
  const focusNext = useCallback(() => {
    const elements =
      focusableElements.length > 0
        ? focusableElements
        : updateFocusableElements();
    if (elements.length === 0) return;

    const nextIndex = focusedIndex < elements.length - 1 ? focusedIndex + 1 : 0;
    focusElement(nextIndex);
  }, [focusedIndex, focusElement, focusableElements, updateFocusableElements]);

  // 聚焦到上一个元素
  const focusPrevious = useCallback(() => {
    const elements =
      focusableElements.length > 0
        ? focusableElements
        : updateFocusableElements();
    if (elements.length === 0) return;

    const prevIndex = focusedIndex > 0 ? focusedIndex - 1 : elements.length - 1;
    focusElement(prevIndex);
  }, [focusedIndex, focusElement, focusableElements, updateFocusableElements]);

  // 聚焦到第一个元素
  const focusFirst = useCallback(() => {
    focusElement(0);
  }, [focusElement]);

  // 聚焦到最后一个元素
  const focusLast = useCallback(() => {
    const elements =
      focusableElements.length > 0
        ? focusableElements
        : updateFocusableElements();
    focusElement(elements.length - 1);
  }, [focusElement, focusableElements, updateFocusableElements]);

  // 检查快捷键匹配
  const matchesShortcut = useCallback(
    (event: KeyboardEvent, shortcut: ShortcutConfig) => {
      const key = event.key.toLowerCase();
      const shortcutKey = shortcut.key.toLowerCase();

      return (
        key === shortcutKey &&
        !!event.ctrlKey === !!shortcut.ctrl &&
        !!event.altKey === !!shortcut.alt &&
        !!event.shiftKey === !!shortcut.shift &&
        !!event.metaKey === !!shortcut.meta
      );
    },
    [],
  );

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // 处理快捷键
      for (const shortcut of shortcuts) {
        if (matchesShortcut(event, shortcut)) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          shortcut.handler();
          return;
        }
      }

      // 处理导航键
      switch (event.key) {
        case 'Tab':
          if (config.trapFocus) {
            event.preventDefault();
            if (event.shiftKey) {
              focusPrevious();
            } else {
              focusNext();
            }
          }
          break;

        case 'ArrowDown':
          if (
            event.target === containerRef.current ||
            (event.target as HTMLElement)?.getAttribute('role') === 'menu'
          ) {
            event.preventDefault();
            focusNext();
          }
          break;

        case 'ArrowUp':
          if (
            event.target === containerRef.current ||
            (event.target as HTMLElement)?.getAttribute('role') === 'menu'
          ) {
            event.preventDefault();
            focusPrevious();
          }
          break;

        case 'Home':
          if (
            event.ctrlKey ||
            (event.target as HTMLElement)?.getAttribute('role') === 'menu'
          ) {
            event.preventDefault();
            focusFirst();
          }
          break;

        case 'End':
          if (
            event.ctrlKey ||
            (event.target as HTMLElement)?.getAttribute('role') === 'menu'
          ) {
            event.preventDefault();
            focusLast();
          }
          break;

        case 'Escape':
          if (config.restoreFocus) {
            (document.activeElement as HTMLElement)?.blur();
          }
          break;
      }
    },
    [
      shortcuts,
      matchesShortcut,
      config,
      focusNext,
      focusPrevious,
      focusFirst,
      focusLast,
    ],
  );

  // 处理焦点变化
  const handleFocusChange = useCallback(() => {
    const elements = updateFocusableElements();
    const activeElement = document.activeElement as HTMLElement;
    const index = elements.indexOf(activeElement);
    setFocusedIndex(index);
  }, [updateFocusableElements]);

  // 设置事件监听器
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('keydown', handleKeyDown);
    container.addEventListener('focusin', handleFocusChange);

    // 初始化
    updateFocusableElements();

    if (config.autoFocus) {
      setTimeout(() => focusFirst(), 0);
    }

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
      container.removeEventListener('focusin', handleFocusChange);
    };
  }, [
    handleKeyDown,
    handleFocusChange,
    updateFocusableElements,
    config.autoFocus,
    focusFirst,
  ]);

  // 监听 DOM 变化
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const observer = new MutationObserver(() => {
      updateFocusableElements();
    });

    observer.observe(container, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['disabled', 'tabindex', 'hidden'],
    });

    return () => observer.disconnect();
  }, [updateFocusableElements]);

  return {
    containerRef,
    focusedIndex,
    focusableElements,
    focusElement,
    focusNext,
    focusPrevious,
    focusFirst,
    focusLast,
    updateFocusableElements,
  };
}

// 快捷键 Hook
export function useShortcuts(shortcuts: ShortcutConfig[]) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      for (const shortcut of shortcuts) {
        const key = event.key.toLowerCase();
        const shortcutKey = shortcut.key.toLowerCase();

        if (
          key === shortcutKey &&
          !!event.ctrlKey === !!shortcut.ctrl &&
          !!event.altKey === !!shortcut.alt &&
          !!event.shiftKey === !!shortcut.shift &&
          !!event.metaKey === !!shortcut.meta
        ) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          shortcut.handler();
          break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
}

// 焦点陷阱 Hook
export function useFocusTrap(isActive: boolean = true) {
  const containerRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    previousFocusRef.current = document.activeElement as HTMLElement;

    const getFocusableElements = () => {
      const selector = [
        'button:not([disabled])',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        'a[href]',
        '[tabindex]:not([tabindex="-1"])',
      ].join(', ');

      return Array.from(container.querySelectorAll(selector)) as HTMLElement[];
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      const focusableElements = getFocusableElements();
      if (focusableElements.length === 0) return;

      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);

    // 聚焦到第一个可聚焦元素
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }

    return () => {
      container.removeEventListener('keydown', handleKeyDown);

      // 恢复之前的焦点
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    };
  }, [isActive]);

  return containerRef;
}

// 跳转链接 Hook
export function useSkipLinks() {
  const [skipLinks, setSkipLinks] = useState<
    Array<{
      id: string;
      label: string;
      target: string;
    }>
  >([]);

  const addSkipLink = useCallback(
    (id: string, label: string, target: string) => {
      setSkipLinks(prev => [
        ...prev.filter(link => link.id !== id),
        { id, label, target },
      ]);
    },
    [],
  );

  const removeSkipLink = useCallback((id: string) => {
    setSkipLinks(prev => prev.filter(link => link.id !== id));
  }, []);

  const handleSkipToContent = useCallback((target: string) => {
    const element = document.querySelector(target) as HTMLElement;
    if (element) {
      element.focus();
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  return {
    skipLinks,
    addSkipLink,
    removeSkipLink,
    handleSkipToContent,
  };
}

// 方向键导航 Hook
export function useArrowNavigation<T extends HTMLElement = HTMLElement>(
  direction: 'horizontal' | 'vertical' | 'grid' = 'vertical',
  gridColumns?: number,
) {
  const containerRef = useRef<T>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!containerRef.current) return;

      const focusableElements = Array.from(
        containerRef.current.querySelectorAll(
          'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])',
        ),
      ) as HTMLElement[];

      if (focusableElements.length === 0) return;

      let newIndex = currentIndex;

      switch (direction) {
        case 'horizontal':
          if (event.key === 'ArrowLeft') {
            newIndex =
              currentIndex > 0
                ? currentIndex - 1
                : focusableElements.length - 1;
            event.preventDefault();
          } else if (event.key === 'ArrowRight') {
            newIndex =
              currentIndex < focusableElements.length - 1
                ? currentIndex + 1
                : 0;
            event.preventDefault();
          }
          break;

        case 'vertical':
          if (event.key === 'ArrowUp') {
            newIndex =
              currentIndex > 0
                ? currentIndex - 1
                : focusableElements.length - 1;
            event.preventDefault();
          } else if (event.key === 'ArrowDown') {
            newIndex =
              currentIndex < focusableElements.length - 1
                ? currentIndex + 1
                : 0;
            event.preventDefault();
          }
          break;

        case 'grid':
          if (!gridColumns) return;

          if (event.key === 'ArrowUp') {
            newIndex = Math.max(0, currentIndex - gridColumns);
            event.preventDefault();
          } else if (event.key === 'ArrowDown') {
            newIndex = Math.min(
              focusableElements.length - 1,
              currentIndex + gridColumns,
            );
            event.preventDefault();
          } else if (event.key === 'ArrowLeft') {
            newIndex =
              currentIndex > 0
                ? currentIndex - 1
                : focusableElements.length - 1;
            event.preventDefault();
          } else if (event.key === 'ArrowRight') {
            newIndex =
              currentIndex < focusableElements.length - 1
                ? currentIndex + 1
                : 0;
            event.preventDefault();
          }
          break;
      }

      if (newIndex !== currentIndex) {
        setCurrentIndex(newIndex);
        focusableElements[newIndex]?.focus();
      }
    },
    [currentIndex, direction, gridColumns],
  );

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { containerRef, currentIndex, setCurrentIndex };
}

export default useKeyboardNavigation;
