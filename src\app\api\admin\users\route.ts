import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import {
  getMemberPlanText,
  getUserStatusText,
  MEMBER_PLAN_REVERSE_MAP,
  USER_STATUS_REVERSE_MAP,
  type MemberPlan,
  type UserStatus,
} from '@/lib/constants';
import prisma from '@/lib/db';
import { hashPassword } from '@/lib/password';

// 获取管理员用户列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证和管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (currentUser?.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');

    // 获取管理员用户列表
    const users = await prisma.user.findMany({
      where: {
        role: role === 'admin' ? 'ADMIN' : undefined,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json(users);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 创建用户验证Schema - 使用与注册相同的密码规则
const createUserSchema = z.object({
  nickname: z
    .string()
    .min(2, '昵称至少2个字符')
    .max(50, '昵称不能超过50个字符'),
  email: z.string().email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(6, '密码长度至少6位')
    .max(32, '密码长度不能超过32位')
    .regex(/^[a-zA-Z0-9]+$/, '密码只能包含字母和数字'),
  memberPlan: z.enum(['FREE', 'PRO', 'BUSINESS']).default('FREE'),
  balance: z.number().default(0),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']).default('ACTIVE'),
  memberPlanExpiry: z.string().optional(),
});

// 创建新用户
export async function POST(request: NextRequest) {
  try {
    // 验证用户认证和管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    // 检查是否为管理员
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (currentUser?.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const validation = createUserSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: '输入数据无效',
          details: validation.error.flatten().fieldErrors,
        },
        { status: 400 },
      );
    }

    const {
      nickname,
      email,
      password,
      memberPlan,
      balance,
      status,
      memberPlanExpiry,
    } = validation.data;

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json({ error: '该邮箱已被注册' }, { status: 409 });
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 处理会员到期时间
    let expiryDate = null;
    if (memberPlan !== 'FREE' && memberPlanExpiry) {
      expiryDate = new Date(memberPlanExpiry);
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name: nickname,
        email,
        password: hashedPassword,
        role: 'USER',
        memberPlan: memberPlan as any,
        memberPlanExpiry: expiryDate,
        balance,
        status: status as any,
        isActive: status === 'ACTIVE',
        completedTasks: 0,
        publishedTasks: 0,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        memberPlan: true,
        memberPlanExpiry: true,
        balance: true,
        completedTasks: true,
        publishedTasks: true,
        status: true,
        createdAt: true,
        image: true,
      },
    });

    // 返回格式化的用户数据
    const formattedUser = {
      id: user.id,
      nickname: user.name || '未设置',
      email: user.email || '',
      avatar: user.image || '',
      memberPlan: user.memberPlan,
      memberPlanExpiry:
        user.memberPlan === 'FREE'
          ? '2099-12-31T23:59:59.000Z'
          : user.memberPlanExpiry?.toISOString() || '2099-12-31T23:59:59.000Z',
      balance: user.balance,
      completedTasks: user.completedTasks,
      publishedTasks: user.publishedTasks,
      status: user.status,
      registerDate: user.createdAt.toISOString().split('T')[0],
    };

    return NextResponse.json(
      {
        message: '用户创建成功',
        user: formattedUser,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
