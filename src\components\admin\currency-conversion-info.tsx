'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CurrencyConversionInfoProps {
  order: {
    amount: number;
    currency: string;
    originalAmount?: number;
    originalCurrency?: string;
    convertedAmount?: number;
    convertedCurrency?: string;
    exchangeRate?: number;
    exchangeRateSource?: string;
  };
}

export function CurrencyConversionInfo({ order }: CurrencyConversionInfoProps) {
  const hasConversion =
    order.originalAmount && order.convertedAmount && order.exchangeRate;

  if (!hasConversion) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='text-sm'>货币信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2'>
            <div className='flex justify-between'>
              <span className='text-sm text-muted-foreground'>金额:</span>
              <span className='font-medium'>
                {order.amount} {order.currency}
              </span>
            </div>
            <Badge variant='secondary' className='text-xs'>
              无货币转换
            </Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className='text-sm'>货币转换信息</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-3'>
          {/* 原始金额 */}
          <div className='flex justify-between'>
            <span className='text-sm text-muted-foreground'>原始金额:</span>
            <span className='font-medium'>
              {order.originalAmount} {order.originalCurrency}
            </span>
          </div>

          {/* 转换后金额 */}
          <div className='flex justify-between'>
            <span className='text-sm text-muted-foreground'>转换后金额:</span>
            <span className='font-medium'>
              {order.convertedAmount} {order.convertedCurrency}
            </span>
          </div>

          {/* 汇率 */}
          <div className='flex justify-between'>
            <span className='text-sm text-muted-foreground'>汇率:</span>
            <span className='font-medium'>
              1 {order.originalCurrency} = {order.exchangeRate?.toFixed(6)}{' '}
              {order.convertedCurrency}
            </span>
          </div>

          {/* 汇率来源 */}
          <div className='flex justify-between'>
            <span className='text-sm text-muted-foreground'>汇率来源:</span>
            <Badge
              variant={
                order.exchangeRateSource === 'fallback'
                  ? 'destructive'
                  : 'default'
              }
              className='text-xs'
            >
              {order.exchangeRateSource === 'fawaz-exchange-api' &&
                '免费汇率API'}
              {order.exchangeRateSource === 'fawaz-cloudflare' &&
                'Cloudflare镜像'}
              {order.exchangeRateSource === 'fallback' && '降级汇率'}
              {!['fawaz-exchange-api', 'fawaz-cloudflare', 'fallback'].includes(
                order.exchangeRateSource || '',
              ) && order.exchangeRateSource}
            </Badge>
          </div>

          {/* 转换说明 */}
          <div className='pt-2 border-t'>
            <p className='text-xs text-muted-foreground'>
              支付提供商收到的金额:{' '}
              <span className='font-medium'>
                {order.convertedAmount} {order.convertedCurrency}
              </span>
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
