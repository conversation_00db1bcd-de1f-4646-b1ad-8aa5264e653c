import { NextRequest, NextResponse } from 'next/server';

import { AIMessageRequest, AIMessageResponse } from '@/types/crisp';

// 翻译消息
const messages = {
  zh: {
    incompleteData: '请求数据不完整',
    generateFailed: '消息生成失败',
  },
  en: {
    incompleteData: 'Incomplete request data',
    generateFailed: 'Message generation failed',
  },
};

export async function POST(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    const data: AIMessageRequest = await request.json();

    // 验证请求数据
    if (!data.messageType || !data.userBehavior || !data.context) {
      return NextResponse.json(
        { success: false, error: t.incompleteData },
        { status: 400 },
      );
    }

    // 这里可以集成 OpenAI 或其他 AI 服务
    // 返回基于规则的个性化消息
    const message = generateRuleBasedMessage(data);

    const response: AIMessageResponse = {
      success: true,
      message,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('AI message generation failed:', error);
    return NextResponse.json(
      { success: false, error: messages.zh.generateFailed },
      { status: 500 },
    );
  }
}

function generateRuleBasedMessage(data: AIMessageRequest): string {
  const { userBehavior, context, messageType, tone } = data;

  // 时间问候语
  const now = new Date();
  const hour = now.getHours();
  let timeGreeting = '';

  if (hour >= 5 && hour < 12) {
    timeGreeting = '早上好';
  } else if (hour >= 12 && hour < 14) {
    timeGreeting = '中午好';
  } else if (hour >= 14 && hour < 18) {
    timeGreeting = '下午好';
  } else if (hour >= 18 && hour < 22) {
    timeGreeting = '晚上好';
  } else {
    timeGreeting = '夜深了，您好';
  }

  // 根据消息类型和用户行为生成消息
  let message = '';

  switch (messageType) {
    case 'welcome':
      if (userBehavior.visitCount === 1) {
        message = `👋 ${timeGreeting}！欢迎首次访问 RefundGo！\n\n🎯 我是您的专属AI助手，帮助您在委托平台上发布委托或接取委托赚取酬金！`;
      } else {
        message = `👋 ${timeGreeting}！欢迎回到 RefundGo！\n\n💰 看起来您对我们的委托平台很感兴趣，需要我为您介绍发布委托或接取委托的流程吗？`;
      }
      break;

    case 're_engagement':
      message = `👋 ${timeGreeting}！很高兴再次见到您！\n\n🚀 我们最近新增了更多高酬金委托，委托大厅里有很多优质机会等着您。需要我为您推荐一些吗？`;
      break;

    case 'feature_highlight':
      if (userBehavior.engagementScore > 70) {
        message = `👋 ${timeGreeting}！\n\n⭐ 我注意到您是我们的活跃用户，想了解一些高级委托类型或提升委托完成效率的技巧吗？`;
      } else {
        message = `👋 ${timeGreeting}！\n\n💡 看起来您对我们的平台很感兴趣！我可以为您介绍如何发布高效委托或接取优质委托来赚取酬金。`;
      }
      break;

    case 'milestone_celebration':
      message = `🎉 ${timeGreeting}！\n\n感谢您一直以来对 RefundGo 的支持！作为我们的忠实用户，现在有更多委托机会和更高酬金等着您！`;
      break;

    case 'support_offer':
      if (userBehavior.lifecycleStage === 'at_risk') {
        message = `👋 ${timeGreeting}！\n\n我注意到您有一段时间没有访问了。委托大厅有很多新委托上线，快来看看有没有适合您的机会！`;
      } else {
        message = `👋 ${timeGreeting}！\n\n需要任何关于委托发布或接取的帮助吗？我们的支持团队随时为您服务！`;
      }
      break;

    default:
      message = `👋 ${timeGreeting}！有什么关于委托发布或接取的问题可以帮助您的吗？`;
  }

  // 添加地理位置个性化
  if (context.userLocation) {
    const location = context.userLocation.trim();
    if (location && location !== '未知, 未知') {
      if (location.includes('中国') || location.includes('China')) {
        message += `\n\n🌍 来自${location}的朋友，平台有适合您当地的委托机会！`;
      } else {
        message += `\n\n🌍 Welcome from ${location}! We have tasks suitable for your region!`;
      }
    }
  }

  // 添加设备优化建议
  if (context.deviceType === 'mobile') {
    message += '\n\n📱 移动端也能轻松发布和管理委托，随时随地赚取酬金！';
  }

  // 根据用户参与度添加个性化内容
  if (userBehavior.engagementScore > 80) {
    message += '\n\n⭐ 作为我们的超级用户，您可以优先获得高酬金委托推荐！';
  } else if (userBehavior.engagementScore > 60) {
    message +=
      '\n\n🎯 您看起来对我们的委托平台很感兴趣，需要深入了解更多委托类型吗？';
  }

  message += '\n\n💬 有任何关于委托发布或接取的问题都可以随时咨询我！';

  return message;
}
