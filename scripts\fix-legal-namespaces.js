#!/usr/bin/env node

/**
 * 修复 Legal 相关页面的命名空间问题
 * 
 * 将 Legal.terms 和 Legal.privacy 转换为 legal.terms 和 legal.privacy
 */

const fs = require('fs');
const path = require('path');

/**
 * 修复单个文件
 */
function fixFile(filePath, oldNamespace, newNamespace) {
  console.log(`修复文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`  ⚠️  文件不存在，跳过`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 替换模式
  const patterns = [
    new RegExp(`useTranslations\\(['"]${oldNamespace.replace('.', '\\.')}['"]\\)`, 'g'),
    new RegExp(`useTranslations\\(["']${oldNamespace.replace('.', '\\.')}["']\\)`, 'g'),
  ];
  
  patterns.forEach(pattern => {
    if (pattern.test(content)) {
      content = content.replace(pattern, `useTranslations('${newNamespace}')`);
      modified = true;
      console.log(`  ✅ 替换: ${oldNamespace} -> ${newNamespace}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  💾 文件已保存`);
    return true;
  } else {
    console.log(`  ⏭️  无需修改`);
    return false;
  }
}

/**
 * 验证翻译文件结构
 */
function validateTranslationFiles() {
  console.log('🔍 验证翻译文件结构...\n');
  
  const locales = ['zh', 'en'];
  const legalFile = 'legal.json';
  
  locales.forEach(locale => {
    const filePath = path.join(process.cwd(), 'messages', locale, legalFile);
    
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${locale}/${legalFile} 存在`);
      
      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        // 检查必要的键
        const requiredKeys = ['terms', 'privacy'];
        requiredKeys.forEach(key => {
          if (content[key]) {
            console.log(`  ✅ ${key} 部分存在`);
            
            // 检查子键
            const subKeys = ['title', 'sections'];
            subKeys.forEach(subKey => {
              if (content[key][subKey]) {
                console.log(`    ✅ ${key}.${subKey} 存在`);
              } else {
                console.log(`    ❌ ${key}.${subKey} 缺失`);
              }
            });
          } else {
            console.log(`  ❌ ${key} 部分缺失`);
          }
        });
        
      } catch (error) {
        console.log(`  ❌ JSON 格式错误: ${error.message}`);
      }
    } else {
      console.log(`❌ ${locale}/${legalFile} 不存在`);
    }
    
    console.log(''); // 空行分隔
  });
}

/**
 * 测试翻译键访问
 */
function testTranslationKeys() {
  console.log('🧪 测试翻译键访问...\n');
  
  const testCases = [
    {
      namespace: 'legal.terms',
      keys: [
        'title',
        'lastUpdated',
        'needHelp',
        'helpText',
        'sections.overview.title',
        'sections.overview.content',
        'sections.contact.title'
      ]
    },
    {
      namespace: 'legal.privacy',
      keys: [
        'title',
        'lastUpdated',
        'needHelp',
        'helpText',
        'sections.overview.title',
        'sections.overview.content',
        'sections.contact.title'
      ]
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`📋 测试命名空间: ${testCase.namespace}`);
    
    testCase.keys.forEach(key => {
      console.log(`  🔑 ${testCase.namespace}.${key}`);
    });
    
    console.log(''); // 空行分隔
  });
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 修复 Legal 命名空间问题\n');
  
  // 1. 验证翻译文件
  validateTranslationFiles();
  
  // 2. 修复文件
  const filesToFix = [
    {
      path: 'src/app/[locale]/(main)/terms/page.tsx',
      old: 'Legal.terms',
      new: 'legal.terms'
    },
    {
      path: 'src/app/[locale]/(main)/privacy/page.tsx',
      old: 'Legal.privacy',
      new: 'legal.privacy'
    }
  ];
  
  let fixedFiles = 0;
  
  filesToFix.forEach(file => {
    const fullPath = path.join(process.cwd(), file.path);
    if (fixFile(fullPath, file.old, file.new)) {
      fixedFiles++;
    }
  });
  
  console.log(`\n📊 修复结果:`);
  console.log(`  修复文件数: ${fixedFiles}/${filesToFix.length}`);
  
  // 3. 测试翻译键
  testTranslationKeys();
  
  console.log('✅ 修复完成！\n');
  console.log('📝 下一步：');
  console.log('1. 重启 VSCode');
  console.log('2. 打开 terms 或 privacy 页面');
  console.log('3. 检查 i18n Ally 是否正确识别翻译');
  console.log('4. 测试自动完成功能');
  console.log('5. 验证页面显示是否正常');
}

main();
