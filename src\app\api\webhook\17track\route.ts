import { NextRequest, NextResponse } from 'next/server';

import { Track17API, WebhookNotification } from '@/lib/17track';
import prisma from '@/lib/db';

// 处理17TRACK WebHook推送通知
export async function POST(request: NextRequest) {
  try {
    // 获取请求体和签名
    const body = await request.text();
    const signature = request.headers.get('sign');

    if (!signature) {
      return NextResponse.json({ error: '缺少签名' }, { status: 401 });
    }

    // 验证签名
    const apiKey = process.env.TRACK_17_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ error: '配置错误' }, { status: 500 });
    }

    const isValidSignature = Track17API.verifyWebhookSignature(
      body,
      signature,
      apiKey,
    );
    if (!isValidSignature) {
      return NextResponse.json({ error: '签名验证失败' }, { status: 401 });
    }

    // 解析推送数据
    const notification: WebhookNotification = JSON.parse(body);

    console.log(
      '收到17TRACK推送:',
      notification.event,
      notification.data.number,
    );

    // 根据事件类型处理
    switch (notification.event) {
      case 'TRACKING_UPDATED':
        await handleTrackingUpdated(notification.data);
        break;
      case 'TRACKING_STOPPED':
        await handleTrackingStopped(notification.data);
        break;
      default:
        console.warn('未知事件类型:', notification.event);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('WebHook处理错误:', error);
    return NextResponse.json({ error: '处理失败' }, { status: 500 });
  }
}

// 处理物流更新通知
async function handleTrackingUpdated(data: any) {
  const { number, carrier, tag, track_info } = data;

  try {
    // 根据物流单号查找委托
    const task = await prisma.task.findFirst({
      where: {
        trackingNumber: number,
      },
    });

    if (!task) {
      console.warn('找不到对应的委托:', number);
      return;
    }

    console.log('物流跟踪更新 - 委托ID:', task.id);
    console.log('物流状态:', track_info.latest_status?.status);
    console.log('最新事件:', track_info.latest_event?.description);

    // 如果已签收，更新委托状态
    if (track_info.latest_status?.status === 'Delivered') {
      await prisma.task.update({
        where: { id: task.id },
        data: {
          status: 'PENDING_REVIEW',
          completedAt: new Date(),
        },
      });
      console.log('委托状态已更新为待审核:', task.id);
    }

    // TODO: 将来这里将物流数据存储到LogisticsTracking表中
    // 记录日志
  } catch (error) {
    console.error('处理物流更新失败:', error);
  }
}

// 处理物流停止跟踪通知
async function handleTrackingStopped(data: any) {
  const { number, carrier } = data;

  try {
    // 根据物流单号查找委托
    const task = await prisma.task.findFirst({
      where: {
        trackingNumber: number,
      },
    });

    if (!task) {
      console.warn('找不到对应的委托:', number);
      return;
    }

    console.log('物流跟踪已停止 - 委托ID:', task.id, '物流单号:', number);

    // TODO: 将来这里更新LogisticsTracking表的状态
    // 记录日志
  } catch (error) {
    console.error('处理物流停止失败:', error);
  }
}

// 处理GET请求（用于测试）
export async function GET() {
  return NextResponse.json({ message: '17TRACK WebHook endpoint' });
}
