import { NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import { currencyConverter } from '@/lib/payment/currency-converter';

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const cacheInfo = currencyConverter.getCacheInfo();

    return NextResponse.json({
      success: true,
      data: {
        cacheCount: cacheInfo.length,
        cacheEntries: cacheInfo.map(item => ({
          currencyPair: item.key,
          rate: item.rate.rate,
          source: item.rate.source,
          timestamp: item.rate.timestamp,
          expiresAt: item.rate.expiresAt,
          isExpired: Date.now() > item.rate.expiresAt,
          remainingTime: Math.max(0, item.rate.expiresAt - Date.now()),
        })),
      },
    });
  } catch (error) {
    console.error('Get currency cache error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function DELETE() {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    currencyConverter.clearCache();

    return NextResponse.json({
      success: true,
      message: 'Currency cache cleared successfully',
    });
  } catch (error) {
    console.error('Clear currency cache error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
