import { NextRequest, NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';
import { sendEvidenceReviewPublisherEmail } from '@/lib/email';

// 证据审核API
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    const body = await request.json();
    const { taskId, action, reason } = body;

    // 验证请求参数
    if (!taskId) {
      return NextResponse.json({ error: '委托ID不能为空' }, { status: 400 });
    }

    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json({ error: '操作类型无效' }, { status: 400 });
    }

    if (action === 'reject' && !reason) {
      return NextResponse.json(
        { error: '拒绝操作必须提供拒绝理由' },
        { status: 400 },
      );
    }

    // 查找委托
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            name: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 执行审核操作
    let newEvidenceStatus: string;
    let updateData: any = {};

    if (action === 'approve') {
      newEvidenceStatus = 'REVIEWED';
      updateData = {
        evidenceStatus: newEvidenceStatus,
      };
    } else {
      // 拒绝操作 - 设置为拒绝状态，并存储拒绝理由
      newEvidenceStatus = 'REJECTED';
      updateData = {
        evidenceStatus: newEvidenceStatus,
        evidenceRejectReason: reason, // 存储拒绝理由
        evidenceFiles: [], // 清空证据文件，让用户重新上传
      };
    }

    // 开始数据库事务
    const result = await prisma.$transaction(async tx => {
      // 更新委托状态
      const updatedTask = await tx.task.update({
        where: { id: taskId },
        data: updateData,
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // 处理证据费的冻结金额操作
      if (action === 'approve') {
        const systemRate = await tx.systemRate.findFirst();
        if (systemRate) {
          // 计算证据费用
          const evidenceFee =
            (task.totalAmount * systemRate.noEvidenceExtraRate) / 100;

          if (evidenceFee > 0) {
            // 审核通过：从冻结金额释放并返还到可用余额
            await tx.user.update({
              where: { id: task.publisher.id },
              data: {
                frozenAmount: {
                  decrement: evidenceFee,
                },
                balance: {
                  increment: evidenceFee,
                },
              },
            });

            // 记录证据费退还交易日志
            await tx.walletTransaction.create({
              data: {
                userId: task.publisher.id,
                type: 'REFUND',
                amount: evidenceFee,
                status: 'COMPLETED',
                description: `证据审核通过，退还证据费用 - 委托 ${taskId}`,
                reference: taskId,
                completedAt: new Date(),
              },
            });
          }
        }
      }
      // 审核拒绝时：冻结金额保持不变，因为用户可以重新提交证据进行重审

      return updatedTask;
    });

    // 发送邮件通知发布者
    try {
      const publisherEmail = task.publisher.email;
      if (publisherEmail) {
        const evidenceFeeRefund =
          action === 'approve' ? (task.totalAmount * 2) / 100 : 0; // 2%证据费

        await sendEvidenceReviewPublisherEmail(publisherEmail, {
          publisherName: task.publisher.name || '用户',
          publisherEmail,
          taskId: task.id,
          platform: task.platform?.name || '未知平台',
          category: task.category?.name || '未知分类',
          quantity: task.quantity,
          unitPrice: task.unitPrice,
          totalAmount: task.totalAmount,
          reviewedAt: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
          }),
          approved: action === 'approve',
          rejectReason: action === 'reject' ? reason : undefined,
          evidenceFeeRefund,
        });
      }
    } catch (emailError) {
      console.error('发送证据审核邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: action === 'approve' ? '证据审核通过' : '证据审核拒绝',
      data: {
        taskId: result.id,
        evidenceStatus: newEvidenceStatus,
        action,
        reason: action === 'reject' ? reason : undefined,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
