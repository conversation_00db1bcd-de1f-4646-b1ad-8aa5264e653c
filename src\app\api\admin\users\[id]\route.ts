import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { hashPassword } from '@/lib/password';

// 更新用户信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户身份和管理员权限
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { id: userId } = await params;
    const body = await request.json();
    const {
      nickname,
      email,
      memberPlan,
      memberPlanExpiry,
      status,
      password, // 可选，如果提供则更新密码
    } = body;

    // 验证必填字段
    if (!nickname || !email) {
      return NextResponse.json(
        { error: '昵称和邮箱为必填项' },
        { status: 400 },
      );
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    // 检查邮箱是否被其他用户使用
    const emailConflict = await prisma.user.findFirst({
      where: {
        email,
        id: { not: userId },
      },
    });

    if (emailConflict) {
      return NextResponse.json(
        { error: '该邮箱已被其他用户使用' },
        { status: 400 },
      );
    }

    // 处理会员到期时间
    let expiryDate = null;
    if (memberPlan !== 'FREE' && memberPlanExpiry) {
      expiryDate = new Date(memberPlanExpiry);
    }

    // 准备更新数据
    const updateData: any = {
      name: nickname,
      email,
      memberPlan: memberPlan as any,
      memberPlanExpiry: expiryDate,
      status: status as any,
      isActive: status === 'ACTIVE',
    };

    // 如果提供了新密码，则更新密码
    if (password && password.trim()) {
      const hashedPassword = await hashPassword(password);
      updateData.password = hashedPassword;
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
    });

    // 返回格式化的用户数据
    const formattedUser = {
      id: updatedUser.id,
      nickname: updatedUser.name || '未设置',
      email: updatedUser.email || '',
      avatar: updatedUser.image || '',
      memberPlan: updatedUser.memberPlan,
      memberPlanExpiry:
        updatedUser.memberPlan === 'FREE'
          ? '2099-12-31T23:59:59.000Z'
          : updatedUser.memberPlanExpiry?.toISOString() ||
            '2099-12-31T23:59:59.000Z',
      balance: updatedUser.balance,
      completedTasks: updatedUser.completedTasks,
      publishedTasks: updatedUser.publishedTasks,
      status: updatedUser.status,
      registerDate: updatedUser.createdAt.toISOString().split('T')[0],
    };

    return NextResponse.json({
      user: formattedUser,
      message: '用户信息更新成功',
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
