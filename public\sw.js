// RefundGo Service Worker
const CACHE_NAME = 'refundgo-v1.0.0';
const STATIC_CACHE = 'refundgo-static-v1.0.0';
const DYNAMIC_CACHE = 'refundgo-dynamic-v1.0.0';
const API_CACHE = 'refundgo-api-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  // 添加其他关键静态资源
];

// 需要缓存的页面路由
const CACHED_ROUTES = [
  '/',
  '/dashboard',
  '/tasks',
  '/profile',
  '/wallet',
  '/membership',
];

// API 缓存策略配置
const API_CACHE_CONFIG = {
  // 用户数据 - 网络优先，缓存备用
  '/api/user': { strategy: 'networkFirst', maxAge: 5 * 60 * 1000 },
  // 委托数据 - 网络优先，缓存备用
  '/api/tasks': { strategy: 'networkFirst', maxAge: 2 * 60 * 1000 },
  // 静态数据 - 缓存优先
  '/api/static': { strategy: 'cacheFirst', maxAge: 60 * 60 * 1000 },
};

// Service Worker 安装事件
self.addEventListener('install', event => {
  console.log('Service Worker installing...');

  event.waitUntil(
    Promise.all([
      // 缓存静态资源
      caches.open(STATIC_CACHE).then(cache => {
        return cache.addAll(STATIC_ASSETS);
      }),
      // 缓存页面路由
      caches.open(DYNAMIC_CACHE).then(cache => {
        return cache.addAll(CACHED_ROUTES);
      }),
    ]).then(() => {
      console.log('Service Worker installed successfully');
      // 强制激活新的 Service Worker
      return self.skipWaiting();
    })
  );
});

// Service Worker 激活事件
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');

  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (
              cacheName !== STATIC_CACHE &&
              cacheName !== DYNAMIC_CACHE &&
              cacheName !== API_CACHE &&
              cacheName.startsWith('refundgo-')
            ) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // 立即控制所有客户端
      self.clients.claim(),
    ]).then(() => {
      console.log('Service Worker activated successfully');
    })
  );
});

// 网络请求拦截
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // 只处理同源请求
  if (url.origin !== location.origin) {
    return;
  }

  // API 请求处理
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleAPIRequest(request));
    return;
  }

  // 静态资源处理
  if (isStaticAsset(url.pathname)) {
    event.respondWith(handleStaticAsset(request));
    return;
  }

  // 页面请求处理
  if (request.mode === 'navigate') {
    event.respondWith(handlePageRequest(request));
    return;
  }

  // 其他请求使用网络优先策略
  event.respondWith(networkFirst(request, DYNAMIC_CACHE));
});

// 处理 API 请求
async function handleAPIRequest(request) {
  const url = new URL(request.url);
  const config = getAPIConfig(url.pathname);

  switch (config.strategy) {
    case 'networkFirst':
      return networkFirst(request, API_CACHE, config.maxAge);
    case 'cacheFirst':
      return cacheFirst(request, API_CACHE, config.maxAge);
    case 'staleWhileRevalidate':
      return staleWhileRevalidate(request, API_CACHE, config.maxAge);
    default:
      return networkFirst(request, API_CACHE, config.maxAge);
  }
}

// 处理静态资源
async function handleStaticAsset(request) {
  return cacheFirst(request, STATIC_CACHE);
}

// 处理页面请求
async function handlePageRequest(request) {
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);

    // 缓存成功的响应
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // 网络失败，尝试缓存
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // 返回离线页面
    return (
      caches.match('/offline.html') ||
      new Response('页面暂时无法访问', {
        status: 503,
        statusText: 'Service Unavailable',
      })
    );
  }
}

// 网络优先策略
async function networkFirst(request, cacheName, maxAge) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseToCache = networkResponse.clone();

      // 添加时间戳
      if (maxAge) {
        const headers = new Headers(responseToCache.headers);
        headers.set('sw-cache-timestamp', Date.now().toString());
        const modifiedResponse = new Response(responseToCache.body, {
          status: responseToCache.status,
          statusText: responseToCache.statusText,
          headers,
        });
        cache.put(request, modifiedResponse);
      } else {
        cache.put(request, responseToCache);
      }
    }

    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);

    if (cachedResponse && isResponseFresh(cachedResponse, maxAge)) {
      return cachedResponse;
    }

    throw error;
  }
}

// 缓存优先策略
async function cacheFirst(request, cacheName, maxAge) {
  const cachedResponse = await caches.match(request);

  if (cachedResponse && isResponseFresh(cachedResponse, maxAge)) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseToCache = networkResponse.clone();

      if (maxAge) {
        const headers = new Headers(responseToCache.headers);
        headers.set('sw-cache-timestamp', Date.now().toString());
        const modifiedResponse = new Response(responseToCache.body, {
          status: responseToCache.status,
          statusText: responseToCache.statusText,
          headers,
        });
        cache.put(request, modifiedResponse);
      } else {
        cache.put(request, responseToCache);
      }
    }

    return networkResponse;
  } catch (error) {
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// 过期重新验证策略
async function staleWhileRevalidate(request, cacheName, maxAge) {
  const cachedResponse = await caches.match(request);

  const networkPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      const cache = caches.open(cacheName);
      cache.then(c => {
        const responseToCache = networkResponse.clone();

        if (maxAge) {
          const headers = new Headers(responseToCache.headers);
          headers.set('sw-cache-timestamp', Date.now().toString());
          const modifiedResponse = new Response(responseToCache.body, {
            status: responseToCache.status,
            statusText: responseToCache.statusText,
            headers,
          });
          c.put(request, modifiedResponse);
        } else {
          c.put(request, responseToCache);
        }
      });
    }
    return networkResponse;
  });

  return cachedResponse || networkPromise;
}

// 检查响应是否新鲜
function isResponseFresh(response, maxAge) {
  if (!maxAge) return true;

  const timestamp = response.headers.get('sw-cache-timestamp');
  if (!timestamp) return true;

  const age = Date.now() - parseInt(timestamp);
  return age < maxAge;
}

// 获取 API 配置
function getAPIConfig(pathname) {
  for (const [pattern, config] of Object.entries(API_CACHE_CONFIG)) {
    if (pathname.startsWith(pattern)) {
      return config;
    }
  }

  // 默认配置
  return { strategy: 'networkFirst', maxAge: 5 * 60 * 1000 };
}

// 判断是否为静态资源
function isStaticAsset(pathname) {
  const staticExtensions = [
    '.js',
    '.css',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.svg',
    '.ico',
    '.woff',
    '.woff2',
  ];
  return staticExtensions.some(ext => pathname.endsWith(ext));
}

// 消息处理
self.addEventListener('message', event => {
  const { type, payload } = event.data;

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;

    case 'CACHE_URLS':
      if (payload && payload.urls) {
        cacheUrls(payload.urls);
      }
      break;

    case 'CLEAR_CACHE':
      clearCache(payload?.cacheName);
      break;

    case 'GET_CACHE_INFO':
      getCacheInfo().then(info => {
        event.ports[0].postMessage(info);
      });
      break;
  }
});

// 缓存指定 URL
async function cacheUrls(urls) {
  const cache = await caches.open(DYNAMIC_CACHE);
  return cache.addAll(urls);
}

// 清理缓存
async function clearCache(cacheName) {
  if (cacheName) {
    return caches.delete(cacheName);
  } else {
    const cacheNames = await caches.keys();
    return Promise.all(cacheNames.map(name => caches.delete(name)));
  }
}

// 获取缓存信息
async function getCacheInfo() {
  const cacheNames = await caches.keys();
  const info = {};

  for (const name of cacheNames) {
    const cache = await caches.open(name);
    const keys = await cache.keys();
    info[name] = keys.length;
  }

  return info;
}

// 后台同步
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

// 执行后台同步
async function doBackgroundSync() {
  // 这里可以实现离线数据同步逻辑
  console.log('Background sync triggered');
}

// 推送通知
self.addEventListener('push', event => {
  if (!event.data) return;

  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    data: data.data,
    actions: data.actions,
    requireInteraction: data.requireInteraction || false,
  };

  event.waitUntil(self.registration.showNotification(data.title, options));
});

// 通知点击处理
self.addEventListener('notificationclick', event => {
  event.notification.close();

  const { action, data } = event;
  let url = '/';

  if (action) {
    url = data?.actionUrls?.[action] || '/';
  } else if (data?.url) {
    url = data.url;
  }

  event.waitUntil(clients.openWindow(url));
});

console.log('Service Worker loaded');
