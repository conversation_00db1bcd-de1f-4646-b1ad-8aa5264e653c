import { NextResponse } from 'next/server';

import prisma from '@/lib/db';

export async function GET() {
  try {
    // 从数据库获取启用的支付配置
    const configs = await prisma.paymentConfig.findMany({
      where: {
        isEnabled: true,
      },
    });

    const methods = [];

    // 处理聚合易支付的支付方式
    const yunpayConfig = configs.find(config => config.provider === 'yunpay');
    if (yunpayConfig && yunpayConfig.settings) {
      const settings = yunpayConfig.settings as any;
      const paymentMethods = settings.paymentMethods || {};

      // Alipay
      if (paymentMethods.alipay?.enabled) {
        methods.push({
          id: 'alipay',
          name: 'Alipay',
          provider: 'yunpay',
          icon: 'wallet',
          color: 'blue',
          feeRate: paymentMethods.alipay.feeRate,
          minAmount: paymentMethods.alipay.minAmount || 0,
        });
      }

      // WeChat Pay
      if (paymentMethods.wxpay?.enabled) {
        methods.push({
          id: 'wxpay',
          name: 'WeChat Pay',
          provider: 'yunpay',
          icon: 'smartphone',
          color: 'green',
          feeRate: paymentMethods.wxpay.feeRate,
          minAmount: paymentMethods.wxpay.minAmount || 0,
        });
      }

      // PayPal
      if (paymentMethods.paypal?.enabled) {
        methods.push({
          id: 'paypal',
          name: 'PayPal',
          provider: 'yunpay',
          icon: 'creditCard',
          color: 'indigo',
          feeRate: paymentMethods.paypal.feeRate,
          minAmount: paymentMethods.paypal.minAmount || 0,
        });
      }
    }

    // 处理NOWPayments加密货币支付
    const nowpaymentsConfig = configs.find(
      config => config.provider === 'nowpayments',
    );
    if (nowpaymentsConfig && nowpaymentsConfig.settings) {
      const settings = nowpaymentsConfig.settings as any;
      methods.push({
        id: 'crypto',
        name: 'NOWPayments (Cryptocurrency)',
        provider: 'nowpayments',
        icon: 'bitcoin',
        color: 'orange',
        feeRate: settings.feeRate,
        minAmount: settings.minAmount || 0,
      });
    }

    return NextResponse.json({ methods });
  } catch (error) {
    return NextResponse.json({ error: '获取支付方式失败' }, { status: 500 });
  }
}
