import { TaskStatus } from '@prisma/client';
import { NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';

// 获取委托统计数据
export async function GET() {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    // 获取待审核委托统计
    const [totalReviewTasks, taskCounts, recentTasks] = await Promise.all([
      // 总待审核委托数
      prisma.task.count({
        where: { status: TaskStatus.PENDING },
      }),
      // 按状态分组统计
      prisma.task.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      // 获取最近的待审核委托以计算证据状态
      prisma.task.findMany({
        where: { status: TaskStatus.PENDING },
        select: {
          id: true,
          evidenceFiles: true,
          evidenceUploadType: true,
          finalTotal: true,
        },
      }),
    ]);

    // 计算证据状态统计
    let uploadedEvidence = 0;
    let pendingUpload = 0;
    let noEvidence = 0;
    let pendingReview = 0;
    let totalValue = 0;

    recentTasks.forEach(task => {
      totalValue += task.finalTotal;

      if (task.evidenceFiles && task.evidenceFiles.length > 0) {
        uploadedEvidence++;
      } else if (task.evidenceUploadType === 'IMMEDIATE') {
        pendingUpload++;
      } else if (task.evidenceUploadType === 'NONE') {
        noEvidence++;
      } else {
        pendingReview++;
      }
    });

    // 转换委托状态统计格式
    const statusStats = taskCounts.reduce(
      (acc, item) => {
        acc[item.status] = item._count.id;
        return acc;
      },
      {} as Record<string, number>,
    );

    return NextResponse.json({
      success: true,
      data: {
        totalReviewTasks,
        uploadedEvidence,
        pendingUpload,
        noEvidence,
        pendingReview,
        totalValue,
        statusStats: {
          PENDING: statusStats.PENDING || 0,
          RECRUITING: statusStats.RECRUITING || 0,
          IN_PROGRESS: statusStats.IN_PROGRESS || 0,
          COMPLETED: statusStats.COMPLETED || 0,
          REJECTED: statusStats.REJECTED || 0,
          EXPIRED: statusStats.EXPIRED || 0,
          CANCELLED: statusStats.CANCELLED || 0,
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
