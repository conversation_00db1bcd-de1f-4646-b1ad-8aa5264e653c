'use client';

import { Wallet, Bitcoin, AlertCircle } from 'lucide-react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useUserSystemRate } from '@/hooks/use-rates-api';
import { useCreateWithdrawalRequest } from '@/hooks/use-withdrawal-api';

interface CryptoWithdrawalModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialAmount: string;
  userBalance: number;
  selectedNetwork: 'USDT_ERC20' | 'USDT_TRC20';
  withdrawalToken?: string; // 新增：验证token参数
}

export function CryptoWithdrawalModal({
  isOpen,
  onClose,
  initialAmount,
  userBalance,
  selectedNetwork,
  withdrawalToken, // 新增参数
}: CryptoWithdrawalModalProps) {
  const [formData, setFormData] = useState<CryptoWithdrawalFormData>({
    amount: parseFloat(initialAmount || '0') || 0,
    walletAddress: '',
    network: selectedNetwork,
  });

  const [errors, setErrors] = useState<
    Partial<Record<keyof CryptoWithdrawalFormData, string>>
  >({});

  const { data: systemRate } = useUserSystemRate();
  const createWithdrawalMutation = useCreateWithdrawalRequest();

  // 当 initialAmount 变化时更新金额
  useEffect(() => {
    const amount = parseFloat(initialAmount || '0') || 0;
    setFormData(prev => ({ ...prev, amount }));
  }, [initialAmount]);

  // 当模态框打开时重新初始化金额
  useEffect(() => {
    if (isOpen) {
      const amount = parseFloat(initialAmount || '0') || 0;
      setFormData(prev => ({ ...prev, amount }));
    }
  }, [isOpen, initialAmount]);

  // 表单验证
  const validateForm = (): boolean => {
    try {
      // 验证表单字段
      cryptoWithdrawalSchema.parse({
        walletAddress: formData.walletAddress,
        network: formData.network,
      });

      // 验证提现金额
      if (
        typeof systemRate?.minimumWithdrawalAmount === 'number' &&
        formData.amount < systemRate.minimumWithdrawalAmount
      ) {
        setErrors({
          amount: `最低提现金额为$${systemRate.minimumWithdrawalAmount.toFixed(2)}`,
        });
        return false;
      }

      if (formData.amount > 10000) {
        setErrors({ amount: '单次提现金额不能超过$10000' });
        return false;
      }

      // 检查余额
      if (formData.amount > userBalance) {
        setErrors({ amount: '提现金额不能超过可用余额' });
        return false;
      }

      // 验证钱包地址格式
      if (
        formData.network === 'USDT_ERC20' &&
        !formData.walletAddress.startsWith('0x')
      ) {
        setErrors({ walletAddress: 'ERC20钱包地址必须以0x开头' });
        return false;
      }

      if (
        formData.network === 'USDT_TRC20' &&
        !formData.walletAddress.startsWith('T')
      ) {
        setErrors({ walletAddress: 'TRC20钱包地址必须以T开头' });
        return false;
      }

      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Partial<
          Record<keyof CryptoWithdrawalFormData, string>
        > = {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof CryptoWithdrawalFormData] =
              err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // 检查是否有有效的验证token
    if (!withdrawalToken) {
      toast.error('请先完成邮箱验证');
      onClose(); // 关闭当前模态框，让用户重新进行验证
      return;
    }

    try {
      await createWithdrawalMutation.mutateAsync({
        amount: formData.amount,
        withdrawMethod: formData.network,
        walletAddress: formData.walletAddress,
        cryptoCurrency: 'USDT',
        blockchain: formData.network === 'USDT_ERC20' ? 'ERC20' : 'TRC20',
        withdrawalToken, // 传递验证token
      });

      onClose();
      // 重置表单（保持金额和网络不变）
      setFormData(prev => ({
        ...prev,
        walletAddress: '',
      }));
      setErrors({});
    } catch (error) {
      console.error('提现申请失败:', error);
      // 如果是验证相关的错误，关闭模态框让用户重新验证
      if (error instanceof Error && error.message.includes('验证')) {
        onClose();
      }
    }
  };

  const networkName = formData.network === 'USDT_ERC20' ? 'ERC20' : 'TRC20';
  const networkDescription =
    formData.network === 'USDT_ERC20'
      ? '以太坊网络，转账速度稳定'
      : '波场网络，转账速度较快';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Wallet className='h-5 w-5 text-purple-600' />
            USDT 提现
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* 网络选择 */}
          <div className='space-y-2'>
            <Label htmlFor='network'>区块链网络</Label>
            <div className='grid grid-cols-2 gap-4'>
              <div
                className={`cursor-pointer rounded-lg border p-4 text-center transition-all ${formData.network === 'USDT_ERC20' ? 'border-primary ring-2 ring-primary' : 'border-border'}`}
                onClick={() =>
                  setFormData({ ...formData, network: 'USDT_ERC20' })
                }
              >
                <div className='flex items-center justify-center gap-2'>
                  <Bitcoin className='h-5 w-5' />
                  <span className='font-semibold'>ERC20</span>
                </div>
                <p className='text-xs text-muted-foreground mt-1'>以太坊</p>
              </div>
              <div
                className={`cursor-pointer rounded-lg border p-4 text-center transition-all ${formData.network === 'USDT_TRC20' ? 'border-primary ring-2 ring-primary' : 'border-border'}`}
                onClick={() =>
                  setFormData({ ...formData, network: 'USDT_TRC20' })
                }
              >
                <div className='flex items-center justify-center gap-2'>
                  <Bitcoin className='h-5 w-5' />
                  <span className='font-semibold'>TRC20</span>
                </div>
                <p className='text-xs text-muted-foreground mt-1'>波场</p>
              </div>
            </div>
            <p className='text-xs text-muted-foreground'>
              {networkDescription}
            </p>
          </div>

          {/* 提现金额显示 */}
          <div className='space-y-2'>
            <Label>提现金额</Label>
            <div className='flex items-center justify-between p-3 bg-muted rounded-md'>
              <span className='text-sm text-muted-foreground'>提现金额：</span>
              <span className='text-lg font-semibold'>
                {formData.amount && formData.amount > 0
                  ? `$${formData.amount.toFixed(2)}`
                  : '请先在钱包页面输入金额'}
              </span>
            </div>
          </div>

          {/* 钱包地址 */}
          <div className='space-y-2'>
            <Label htmlFor='walletAddress'>钱包地址</Label>
            <Input
              id='walletAddress'
              placeholder={formData.network === 'USDT_ERC20' ? '0x...' : 'T...'}
              value={formData.walletAddress}
              onChange={e =>
                setFormData({
                  ...formData,
                  walletAddress: e.target.value.trim(),
                })
              }
              className='font-mono text-sm'
            />
            {errors.walletAddress && (
              <p className='text-sm text-red-600'>{errors.walletAddress}</p>
            )}
            <p className='text-xs text-muted-foreground'>
              请确保钱包地址正确，错误的地址可能导致资金丢失
            </p>
          </div>

          {/* 重要提示 */}
          <Alert className='border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800'>
            <AlertCircle className='h-4 w-4 text-yellow-600 dark:text-yellow-400' />
            <AlertDescription className='text-yellow-800 dark:text-yellow-100'>
              <h4 className='font-medium text-yellow-800 dark:text-yellow-100'>
                重要提示
              </h4>
              <div className='mt-2 text-sm space-y-1'>
                <p>• 请确保提供的钱包地址准确无误</p>
                <p>• 虚拟货币交易不可逆转，请谨慎操作</p>
                <p>• 提现后将发送确认邮件至您的注册邮箱</p>
                <p>• 如遇问题请及时联系客服</p>
              </div>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={onClose}>
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              createWithdrawalMutation.isPending ||
              !formData.amount ||
              formData.amount <= 0 ||
              !formData.walletAddress ||
              !withdrawalToken
            }
          >
            {createWithdrawalMutation.isPending
              ? '提交中...'
              : !withdrawalToken
                ? '请先完成邮箱验证'
                : '确认提现'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

const cryptoWithdrawalSchema = z.object({
  walletAddress: z
    .string()
    .min(26, '钱包地址格式不正确')
    .max(100, '钱包地址过长'),
  network: z.enum(['USDT_ERC20', 'USDT_TRC20']),
});

type CryptoWithdrawalFormData = z.infer<typeof cryptoWithdrawalSchema> & {
  amount: number;
};
