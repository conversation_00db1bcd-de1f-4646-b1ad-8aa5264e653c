import crypto from 'crypto';

import {
  PaymentProvider,
  CreatePaymentParams,
  CreatePaymentResult,
  QueryPaymentResult,
  NOWPaymentsConfig,
} from '../types';

export class NOWPaymentsProvider implements PaymentProvider {
  name = 'nowpayments';
  private apiUrl: string;

  constructor(private config: NOWPaymentsConfig) {
    // 根据沙盒模式选择API URL
    if (config.sandbox) {
      this.apiUrl = 'https://api-sandbox.nowpayments.io';
    } else {
      this.apiUrl = config.apiUrl || 'https://api.nowpayments.io';
    }
  }

  async createPayment(
    params: CreatePaymentParams,
  ): Promise<CreatePaymentResult> {
    // 使用Invoice API创建支付发票，用户跳转到NOWPayments托管页面
    const invoiceData = {
      price_amount: params.amount,
      price_currency: params.currency.toLowerCase(), // 法币种类 (usd, eur等)
      // pay_currency留空，让用户在页面上选择加密货币
      order_id: params.orderNo,
      order_description: params.description || `钱包充值 - $${params.amount}`,
      ipn_callback_url: params.notifyUrl,
      success_url:
        params.returnUrl || `${process.env.NEXTAUTH_URL}/payment/success`,
      cancel_url: `${process.env.NEXTAUTH_URL}/payment/cancel`,
      is_fixed_rate: false, // 使用浮动汇率
      is_fee_paid_by_user: false, // 商户支付手续费
    };

    try {
      const response = await fetch(`${this.apiUrl}/v1/invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.config.apiKey,
        },
        body: JSON.stringify(invoiceData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `NOWPayments API错误: ${response.status} - ${errorData.message || '创建发票失败'}`,
        );
      }

      const result = await response.json();

      if (!result.id || !result.invoice_url) {
        throw new Error('NOWPayments返回的数据格式不正确');
      }

      return {
        success: true,
        paymentUrl: result.invoice_url, // 跳转到NOWPayments托管页面
        qrCode: undefined, // NOWPayments托管页面不需要二维码
        thirdOrderNo: result.id, // 保存invoice ID
        message: 'NOWPayments发票创建成功',
      };
    } catch (error) {
      return {
        success: false,
        message:
          error instanceof Error ? error.message : 'NOWPayments支付创建失败',
      };
    }
  }

  async queryPayment(orderNo: string): Promise<QueryPaymentResult> {
    // 注意：这里的orderNo实际上是invoice_id，需要先获取相关的payment
    try {
      // 获取与该invoice相关的payments
      const response = await fetch(
        `${this.apiUrl}/v1/payment/?invoiceId=${orderNo}&limit=1`,
        {
          method: 'GET',
          headers: {
            'x-api-key': this.config.apiKey,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`NOWPayments查询API错误: ${response.status}`);
      }

      const result = await response.json();

      if (!result.data || result.data.length === 0) {
        return {
          success: false,
          message: '未找到相关支付记录',
        };
      }

      const payment = result.data[0];

      return {
        success: true,
        status: this.mapPaymentStatus(payment.payment_status),
        amount: payment.price_amount,
        currency: payment.price_currency,
        thirdOrderNo: payment.payment_id.toString(),
        paidAmount: payment.actually_paid,
        paymentMethod: payment.pay_currency,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'NOWPayments查询支付状态失败',
      };
    }
  }

  verifyNotify(data: any): boolean {
    // NOWPayments IPN验证逻辑
    if (this.config.ipnSecret) {
      // 实现HMAC-SHA512验证
      const receivedHmac = data.x_nowpayments_sig;
      if (!receivedHmac) {
        return false;
      }

      // 创建验证payload（移除签名字段）
      const { x_nowpayments_sig, ...payloadData } = data;
      const payload = JSON.stringify(payloadData);

      const computedHmac = crypto
        .createHmac('sha512', this.config.ipnSecret)
        .update(payload)
        .digest('hex');

      return receivedHmac === computedHmac;
    }

    // 如果没有设置IPN密钥，接受所有回调（不推荐生产环境）
    return true;
  }

  private mapPaymentStatus(paymentStatus: string): string {
    // 根据NOWPayments官方文档映射支付状态
    const statusMap: Record<string, string> = {
      waiting: 'PENDING', // 等待客户支付
      confirming: 'PENDING', // 区块链处理中
      confirmed: 'PENDING', // 区块链确认
      sending: 'PENDING', // 资金发送中
      partially_paid: 'PENDING', // 部分支付
      finished: 'PAID', // 完成
      failed: 'FAILED', // 失败
      refunded: 'REFUNDED', // 退款
      expired: 'EXPIRED', // 过期
    };

    return statusMap[paymentStatus] || 'PENDING';
  }
}
