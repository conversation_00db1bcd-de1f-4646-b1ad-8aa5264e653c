import {
  Task,
  Platform,
  ChargebackType,
  PaymentMethod,
  TaskStatus,
  EvidenceStatus,
} from '@/lib/types/task';

export const mockTasks: Task[] = [
  {
    id: '1',
    platform: Platform.AMAZON,
    category: '电子产品',
    chargebackTypes: [ChargebackType.FRAUD, ChargebackType.AUTHORIZATION],
    paymentMethods: [PaymentMethod.CREDIT_CARD, PaymentMethod.PAYPAL],
    commission: 150.0,
    deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2天后
    status: TaskStatus.PENDING,
    evidenceStatus: EvidenceStatus.PENDING_SUBMISSION,
    description:
      '处理Amazon平台电子产品类目的欺诈拒付申诉，需要提供完整的交易记录和物流信息。',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    unitPrice: 299.99,
    quantity: 2,
    totalAmount: 599.98,
    deposit: 300.0,
    timeLimit: 24,
  },
  {
    id: '2',
    platform: Platform.EBAY,
    category: '服装配饰',
    chargebackTypes: [
      ChargebackType.AUTHORIZATION,
      ChargebackType.CONSUMER_DISPUTE,
    ],
    paymentMethods: [PaymentMethod.PAYPAL, PaymentMethod.APPLE_PAY],
    commission: 89.5,
    deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5天后
    status: TaskStatus.IN_PROGRESS,
    evidenceStatus: EvidenceStatus.UNDER_REVIEW,
    description:
      '协助处理eBay平台服装类商品的授权争议，需要验证商品真实性和授权文件。',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
    unitPrice: 89.99,
    quantity: 1,
    totalAmount: 89.99,
    deposit: 90.0,
    timeLimit: 24,
  },
  {
    id: '3',
    platform: Platform.SHOPIFY,
    category: '家居用品',
    chargebackTypes: [ChargebackType.DUPLICATE_PROCESSING],
    paymentMethods: [PaymentMethod.CREDIT_CARD, PaymentMethod.DEBIT_CARD],
    commission: 200.0,
    deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1天后
    status: TaskStatus.PENDING,
    evidenceStatus: EvidenceStatus.PENDING_SUBMISSION,
    description:
      '处理Shopify平台家居用品的重复扣款申诉，需要核实交易记录和退款状态。',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    unitPrice: 159.99,
    quantity: 1,
    totalAmount: 159.99,
    deposit: 160.0,
    timeLimit: 24,
  },
  {
    id: '4',
    platform: Platform.WALMART,
    category: '食品饮料',
    chargebackTypes: [
      ChargebackType.CONSUMER_DISPUTE,
      ChargebackType.NON_RECEIPT,
    ],
    paymentMethods: [PaymentMethod.DEBIT_CARD, PaymentMethod.BANK_TRANSFER],
    commission: 75.0,
    deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
    status: TaskStatus.COMPLETED,
    evidenceStatus: EvidenceStatus.REVIEWED,
    description: '已成功处理Walmart平台食品类商品的消费者争议案例。',
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    unitPrice: 29.99,
    quantity: 3,
    totalAmount: 89.97,
    deposit: 90.0,
    timeLimit: 24,
  },
  {
    id: '5',
    platform: Platform.ETSY,
    category: '手工艺品',
    chargebackTypes: [ChargebackType.NON_RECEIPT, ChargebackType.FRAUD],
    paymentMethods: [PaymentMethod.APPLE_PAY, PaymentMethod.GOOGLE_PAY],
    commission: 120.0,
    deadline: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1天前（已过期）
    status: TaskStatus.EXPIRED,
    evidenceStatus: EvidenceStatus.UNDER_REVIEW,
    description: 'Etsy平台手工艺品订单的未收到商品申诉，已超过处理期限。',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    unitPrice: 79.99,
    quantity: 1,
    totalAmount: 79.99,
    deposit: 80.0,
    timeLimit: 24,
  },
  {
    id: '6',
    platform: Platform.ALIEXPRESS,
    category: '数码配件',
    chargebackTypes: [
      ChargebackType.PROCESSING_ERROR,
      ChargebackType.AUTHORIZATION,
    ],
    paymentMethods: [PaymentMethod.GOOGLE_PAY, PaymentMethod.CRYPTOCURRENCY],
    commission: 45.0,
    deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3天后
    status: TaskStatus.IN_PROGRESS,
    evidenceStatus: EvidenceStatus.REVIEWED,
    description: '处理AliExpress平台数码配件订单的系统处理错误问题。',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    unitPrice: 25.99,
    quantity: 2,
    totalAmount: 51.98,
    deposit: 52.0,
    timeLimit: 24,
  },
  {
    id: '7',
    platform: Platform.AMAZON,
    category: '图书音像',
    chargebackTypes: [
      ChargebackType.CREDIT_NOT_PROCESSED,
      ChargebackType.PROCESSING_ERROR,
    ],
    paymentMethods: [PaymentMethod.BANK_TRANSFER, PaymentMethod.CREDIT_CARD],
    commission: 35.0,
    deadline: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000), // 4天后
    status: TaskStatus.PENDING,
    evidenceStatus: EvidenceStatus.PENDING_SUBMISSION,
    description: 'Amazon平台图书订单的退款未正确处理，需要跟进退款状态。',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    unitPrice: 19.99,
    quantity: 1,
    totalAmount: 19.99,
    deposit: 20.0,
    timeLimit: 24,
  },
  {
    id: '8',
    platform: Platform.EBAY,
    category: '运动户外',
    chargebackTypes: [
      ChargebackType.FRAUD,
      ChargebackType.CONSUMER_DISPUTE,
      ChargebackType.NON_RECEIPT,
    ],
    paymentMethods: [PaymentMethod.CRYPTOCURRENCY, PaymentMethod.PAYPAL],
    commission: 180.0,
    deadline: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000), // 6天后
    status: TaskStatus.CANCELLED,
    evidenceStatus: EvidenceStatus.PENDING_SUBMISSION,
    description: 'eBay平台运动用品的欺诈申诉案例，客户已取消申请。',
    createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    unitPrice: 199.99,
    quantity: 1,
    totalAmount: 199.99,
    deposit: 200.0,
    timeLimit: 24,
  },
];

// 筛选和排序函数
export function filterAndSortTasks(
  tasks: Task[],
  filters: {
    search?: string;
    platform?: Platform[];
    chargebackType?: ChargebackType[];
    paymentMethod?: PaymentMethod[];
    status?: TaskStatus[];
    evidenceStatus?: EvidenceStatus[];
    sortBy?: 'deadline' | 'commission' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  },
): Task[] {
  let filteredTasks = [...tasks];

  // 搜索筛选
  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filteredTasks = filteredTasks.filter(task => {
      // 处理 category 可能是字符串或对象的情况
      const categoryText =
        typeof task.category === 'string'
          ? task.category
          : task.category?.name || '';
      return (
        categoryText.toLowerCase().includes(searchLower) ||
        task.description?.toLowerCase().includes(searchLower)
      );
    });
  }

  // 平台筛选
  if (filters.platform?.length) {
    filteredTasks = filteredTasks.filter(task => {
      // 处理 platform 可能是枚举、字符串或对象的情况
      const platformValue =
        typeof task.platform === 'string'
          ? task.platform
          : typeof task.platform === 'object'
            ? task.platform.name
            : task.platform;
      return filters.platform!.includes(platformValue as Platform);
    });
  }

  // 拒付类型筛选 - 更新为数组匹配
  if (filters.chargebackType?.length) {
    filteredTasks = filteredTasks.filter(task => {
      if (!task.chargebackTypes || !Array.isArray(task.chargebackTypes))
        return false;
      return task.chargebackTypes.some(type => {
        const typeValue = typeof type === 'string' ? type : type;
        return filters.chargebackType!.includes(typeValue as ChargebackType);
      });
    });
  }

  // 支付方式筛选 - 更新为数组匹配
  if (filters.paymentMethod?.length) {
    filteredTasks = filteredTasks.filter(task => {
      if (!task.paymentMethods || !Array.isArray(task.paymentMethods))
        return false;
      return task.paymentMethods.some(method => {
        const methodValue = typeof method === 'string' ? method : method;
        return filters.paymentMethod!.includes(methodValue as PaymentMethod);
      });
    });
  }

  // 状态筛选
  if (filters.status?.length) {
    filteredTasks = filteredTasks.filter(task =>
      filters.status!.includes(task.status),
    );
  }

  // 证据状态筛选
  if (filters.evidenceStatus?.length) {
    filteredTasks = filteredTasks.filter(task => {
      if (!task.evidenceStatus) return false;
      const statusValue =
        typeof task.evidenceStatus === 'string'
          ? task.evidenceStatus
          : task.evidenceStatus;
      return filters.evidenceStatus!.includes(statusValue as EvidenceStatus);
    });
  }

  // 排序
  const sortBy = filters.sortBy || 'createdAt';
  const sortOrder = filters.sortOrder || 'desc';

  filteredTasks.sort((a, b) => {
    let aValue: number | Date;
    let bValue: number | Date;

    switch (sortBy) {
      case 'commission':
        aValue = a.commission || 0;
        bValue = b.commission || 0;
        break;
      case 'createdAt':
        aValue = a.createdAt;
        bValue = b.createdAt;
        break;
      case 'deadline':
      default:
        aValue = a.deadline || new Date(0);
        bValue = b.deadline || new Date(0);
        break;
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  return filteredTasks;
}
