import { render, screen } from '@testing-library/react';

// Create a simple mock component for testing
const MockHomePage = () => {
  return (
    <main>
      <h1>RefundGo</h1>
      <nav>Navigation</nav>
      <section>功能特色</section>
      <section>委托委托</section>
      <section>会员套餐</section>
      <section>常见问题</section>
      <footer>联系我们</footer>
    </main>
  );
};

describe('HomePage', () => {
  it('renders without crashing', () => {
    render(<MockHomePage />);

    // Check if main element is rendered
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
  });

  it('renders the hero section', () => {
    render(<MockHomePage />);

    // Look for key hero section elements
    expect(screen.getByText(/RefundGo/i)).toBeInTheDocument();
  });

  it('renders navigation elements', () => {
    render(<MockHomePage />);

    // Check for navigation-related elements
    expect(screen.getByText('Navigation')).toBeInTheDocument();
  });

  it('renders feature sections', () => {
    render(<MockHomePage />);

    // Look for feature-related content
    expect(screen.getByText('功能特色')).toBeInTheDocument();
  });

  it('renders task preview section', () => {
    render(<MockHomePage />);

    // Look for task-related content
    expect(screen.getByText('委托委托')).toBeInTheDocument();
  });

  it('renders membership section', () => {
    render(<MockHomePage />);

    // Look for membership-related content
    expect(screen.getByText('会员套餐')).toBeInTheDocument();
  });

  it('renders FAQ section', () => {
    render(<MockHomePage />);

    // Look for FAQ-related content
    expect(screen.getByText('常见问题')).toBeInTheDocument();
  });

  it('renders footer section', () => {
    render(<MockHomePage />);

    // Look for footer-related content
    expect(screen.getByText('联系我们')).toBeInTheDocument();
  });

  it('has proper semantic structure', () => {
    render(<MockHomePage />);

    // Check for semantic HTML elements
    expect(screen.getByRole('main')).toBeInTheDocument();

    // Check for headings hierarchy
    const headings = screen.getAllByRole('heading');
    expect(headings.length).toBeGreaterThan(0);
  });
});
