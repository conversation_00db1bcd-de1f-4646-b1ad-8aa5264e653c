{"success": {"create": "Created successfully", "update": "Updated successfully", "delete": "Deleted successfully", "save": "Saved successfully", "submit": "Submitted successfully", "upload": "Uploaded successfully", "download": "Downloaded successfully", "login": "Logged in successfully", "logout": "Logged out safely", "operation": "Operation successful", "confirm": "Confirmed successfully", "accept": "Accepted successfully", "publish": "Published successfully", "complete": "Completed successfully", "cancel": "Cancelled successfully", "approve": "Approved", "reject": "Rejected", "send": "<PERSON><PERSON> successfully", "copy": "<PERSON><PERSON>d successfully", "share": "Shared successfully", "export": "Exported successfully", "import": "Imported successfully", "refresh": "Refreshed successfully", "reset": "Reset successfully", "verify": "Verified successfully", "activate": "Activated successfully", "deactivate": "Deactivated successfully"}, "error": {"create": "Creation failed", "update": "Update failed", "delete": "Deletion failed", "save": "Save failed", "submit": "Submission failed", "upload": "Upload failed", "download": "Download failed", "login": "<PERSON><PERSON> failed", "logout": "Logout failed", "operation": "Operation failed", "confirm": "Confirmation failed", "accept": "Accept failed", "publish": "Publish failed", "complete": "Completion failed", "cancel": "Cancellation failed", "approve": "Approval failed", "reject": "Rejection failed", "send": "Send failed", "copy": "Co<PERSON> failed", "share": "Share failed", "export": "Export failed", "import": "Import failed", "refresh": "Refresh failed", "reset": "Reset failed", "verify": "Verification failed", "activate": "Activation failed", "deactivate": "Deactivation failed", "network": "Network error, please try again", "server": "Server error, please try again later", "timeout": "Request timeout, please try again", "unauthorized": "Unauthorized access", "forbidden": "Insufficient permissions", "notFound": "Resource not found", "conflict": "Data conflict", "validation": "Data validation failed", "unknown": "Unknown error"}, "loading": {"create": "Creating...", "update": "Updating...", "delete": "Deleting...", "save": "Saving...", "submit": "Submitting...", "upload": "Uploading...", "download": "Downloading...", "login": "Logging in...", "logout": "Logging out...", "operation": "Processing...", "confirm": "Confirming...", "accept": "Accepting...", "publish": "Publishing...", "complete": "Completing...", "cancel": "Cancelling...", "approve": "Approving...", "reject": "Rejecting...", "send": "Sending...", "copy": "Copying...", "share": "Sharing...", "export": "Exporting...", "import": "Importing...", "refresh": "Refreshing...", "reset": "Resetting...", "verify": "Verifying...", "activate": "Activating...", "deactivate": "Deactivating...", "loading": "Loading...", "processing": "Processing...", "connecting": "Connecting...", "syncing": "Syncing..."}, "warning": {"unsavedChanges": "You have unsaved changes", "confirmLeave": "Are you sure you want to leave?", "dataLoss": "Leaving the page will lose unsaved data", "irreversible": "This action cannot be undone", "confirmDelete": "Are you sure you want to delete?", "confirmCancel": "Are you sure you want to cancel?", "lowBalance": "Insufficient balance", "expiringSoon": "Expiring soon", "limitExceeded": "Limit exceeded", "duplicateData": "Duplicate data", "invalidOperation": "Invalid operation", "sessionExpiring": "Session expiring soon", "maintenanceMode": "System under maintenance"}, "info": {"noData": "No data available", "emptyList": "List is empty", "searchNoResults": "No matching results found", "filterNoResults": "No results for filter", "loadingMore": "Loading more...", "endOfList": "End of list", "refreshToUpdate": "Pull to refresh", "autoSave": "Auto save", "lastSaved": "Last saved", "draft": "Draft", "preview": "Preview", "beta": "Beta", "new": "New", "updated": "Updated", "recommended": "Recommended", "popular": "Popular", "trending": "Trending"}, "confirm": {"title": "Confirm Action", "delete": {"title": "Confirm Delete", "message": "Are you sure you want to delete this item? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel"}, "cancel": {"title": "Confirm Cancel", "message": "Are you sure you want to cancel the current operation?", "confirm": "Cancel Operation", "cancel": "Continue"}, "leave": {"title": "Confirm Leave", "message": "You have unsaved changes, are you sure you want to leave?", "confirm": "Leave", "cancel": "Stay"}, "submit": {"title": "Confirm Submit", "message": "Are you sure you want to submit this form?", "confirm": "Submit", "cancel": "Cancel"}, "reset": {"title": "Confirm Reset", "message": "Are you sure you want to reset all settings?", "confirm": "Reset", "cancel": "Cancel"}}, "upload": {"selectFile": "Select File", "selectFiles": "Select Files", "dragAndDrop": "Drag files here", "clickToSelect": "Click to select files", "dragActive": "Drop files here to upload", "uploading": "Uploading...", "uploadComplete": "Upload Complete", "uploadSuccess": "Upload successful", "uploadFailed": "Upload failed", "uploadProgress": "Upload Progress", "filesUploaded": "files uploaded", "uploadedFiles": "Uploaded Files", "clearAll": "Clear All", "removeFile": "Remove File", "preview": "Preview", "download": "Download", "retry": "Retry", "cancel": "Cancel", "replace": "Replace", "fileCount": "files", "errors": {"fileRequired": "Please select at least one file", "invalidFileType": "Invalid file type. Please select supported formats.", "fileTooLarge": "File size exceeds the maximum limit", "tooManyFiles": "Too many files selected. Maximum allowed: {max}", "uploadTimeout": "Upload timeout. Please try again.", "uploadInterrupted": "Upload was interrupted. Please try again.", "insufficientStorage": "Insufficient storage space", "browserNotSupported": "Your browser does not support this feature", "permissionDenied": "File access permission denied", "corruptedFile": "File appears to be corrupted", "networkError": "Network error, please try again", "serverError": "Server error, please try again later", "fileTypeError": "Unsupported file type", "fileSizeError": "File size exceeds limit", "fileCountError": "Too many files selected"}, "formats": {"image": "Supports PNG, JPG, JPEG, GIF formats", "video": "Supports MP4, MOV, AVI formats", "document": "Supports PDF, DOC, DOCX formats", "archive": "Supports ZIP, RAR, 7Z formats", "all": "Supports images, videos, documents and more"}, "limits": {"maxSize": "Max {size}MB per file", "maxFiles": "Maximum {count} files", "totalSize": "Total size not exceeding {size}MB"}, "browser": {"unsupportedBrowser": "Your browser version is too old, please update", "enableJavaScript": "Please enable JavaScript to use file upload", "cookiesRequired": "File upload requires cookies to be enabled", "pasteFromClipboard": "Paste from clipboard"}}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 6 characters", "confirmPassword": "Passwords do not match", "minLength": "Must be at least {min} characters", "maxLength": "Cannot exceed {max} characters", "numeric": "Please enter a number", "positive": "Please enter a positive number", "url": "Please enter a valid URL", "phone": "Please enter a valid phone number", "date": "Please select a date", "time": "Please select a time", "range": "Please select a value between {min} and {max}", "format": "Invalid format", "unique": "This value already exists", "match": "Input does not match", "custom": "Validation failed"}, "whitelist": {"shopApplied": "This shop has applied for whitelist", "shopNotInWhitelist": "This shop is not in the whitelist and can publish tasks", "checkFailed": "Whitelist check failed, please try again"}}