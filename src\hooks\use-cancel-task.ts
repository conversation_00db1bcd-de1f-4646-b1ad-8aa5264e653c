import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface CancelTaskResponse {
  success: boolean;
  message: string;
  data: {
    taskId: string;
    status: string;
    cancelledAt: string;
  };
}

export function useCancelTask() {
  const queryClient = useQueryClient();

  return useMutation<CancelTaskResponse, Error, string>({
    mutationFn: async (taskId: string) => {
      const response = await fetch(
        `/api/user/published-tasks/${taskId}/cancel`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error('取消委托失败');
      }

      return response.json();
    },
    onSuccess: data => {
      toast.success('委托已取消', {
        description: '委托已从委托大厅移除，相关费用将原路退回',
      });

      // 刷新委托列表
      queryClient.invalidateQueries({ queryKey: ['published-tasks'] });
    },
    onError: error => {
      toast.error('取消失败', {
        description: error.message || '网络错误，请稍后重试',
      });
    },
  });
}
