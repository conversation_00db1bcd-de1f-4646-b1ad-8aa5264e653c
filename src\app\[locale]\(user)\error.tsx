'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>f<PERSON><PERSON><PERSON>,
  User,
  Home,
  Bug,
  Wifi,
  Shield,
  Clock,
  ArrowLeft,
} from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

interface UserErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

// Error type classification
enum ErrorType {
  NETWORK = 'network',
  AUTH = 'auth',
  PERMISSION = 'permission',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown',
}

// Error analysis function
function analyzeError(error: Error): {
  type: ErrorType;
  title: string;
  description: string;
  suggestions: string[];
  icon: React.ComponentType<any>;
  severity: 'low' | 'medium' | 'high';
} {
  const message = error.message.toLowerCase();

  // Network errors
  if (
    message.includes('fetch') ||
    message.includes('network') ||
    message.includes('connection')
  ) {
    return {
      type: ErrorType.NETWORK,
      title: 'Network Connection Issue',
      description: 'Unable to connect to the server, please check your network connection.',
      suggestions: ['Check if network connection is normal', 'Try refreshing the page', 'Try again later'],
      icon: Wifi,
      severity: 'medium',
    };
  }

  // Authentication errors
  if (
    message.includes('auth') ||
    message.includes('unauthorized') ||
    message.includes('login')
  ) {
    return {
      type: ErrorType.AUTH,
      title: 'Authentication Failed',
      description: 'Your login status has expired or is invalid, please log in again.',
      suggestions: ['Log in to your account again', 'Clear browser cache', 'Check account status'],
      icon: Shield,
      severity: 'high',
    };
  }

  // Permission errors
  if (
    message.includes('permission') ||
    message.includes('forbidden') ||
    message.includes('access')
  ) {
    return {
      type: ErrorType.PERMISSION,
      title: 'Insufficient Permissions',
      description: 'You do not have permission to access this page or perform this operation.',
      suggestions: ['Contact administrator for permissions', 'Check account level', 'Return to accessible pages'],
      icon: Shield,
      severity: 'medium',
    };
  }

  // Default unknown error
  return {
    type: ErrorType.UNKNOWN,
    title: 'User Panel Error',
    description: 'The user panel encountered an error. This may be a network issue or data loading failure.',
    suggestions: ['Refresh page and retry', 'Check network connection', 'Return to homepage'],
    icon: Bug,
    severity: 'medium',
  };
}

/**
 * User area error boundary component
 * Handles errors in user pages
 */
export default function UserError({ error, reset }: UserErrorProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const errorAnalysis = analyzeError(error);
  const {
    title,
    description,
    suggestions,
    icon: Icon,
    severity,
  } = errorAnalysis;
  React.useEffect(() => {
    // Log user area errors
    console.error('User area error:', error);
  }, [error]);

  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center'>
        <div className='mb-6'>
          <div className='mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20'>
            <AlertTriangle className='h-8 w-8 text-blue-600' />
          </div>
        </div>

        <h1 className='mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100'>
          User Panel Error
        </h1>

        <p className='mb-6 text-gray-600 dark:text-gray-400'>
          The user panel encountered an error. This may be a network issue or data loading failure. Please try reloading the page.
        </p>

        {/* Show error details in development environment */}
        {process.env.NODE_ENV === 'development' && (
          <div className='mb-6 rounded-lg bg-blue-50 p-4 text-left dark:bg-blue-900/20'>
            <h3 className='mb-2 font-semibold text-blue-800 dark:text-blue-200'>
              User Error Details:
            </h3>
            <pre className='text-xs text-blue-700 dark:text-blue-300 overflow-auto'>
              {error.message}
            </pre>
            {error.digest && (
              <p className='mt-2 text-xs text-blue-600 dark:text-blue-400'>
                Error ID: {error.digest}
              </p>
            )}
          </div>
        )}

        <div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
          <Button
            onClick={reset}
            variant='default'
            className='flex items-center gap-2'
          >
            <RefreshCw className='h-4 w-4' />
            Retry
          </Button>

          <Button
            onClick={() => (window.location.href = '/dashboard')}
            variant='outline'
            className='flex items-center gap-2'
          >
            <User className='h-4 w-4' />
            User Dashboard
          </Button>

          <Button
            onClick={() => (window.location.href = '/')}
            variant='ghost'
            className='flex items-center gap-2'
          >
            <Home className='h-4 w-4' />
            Return to Home
          </Button>
        </div>

        {/* Common solution suggestions */}
        <div className='mt-6 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20'>
          <h3 className='mb-2 font-semibold text-blue-900 dark:text-blue-100'>
            Common Solutions:
          </h3>
          <ul className='text-sm text-blue-800 dark:text-blue-200 space-y-1'>
            <li>• Check network connection</li>
            <li>• Refresh page and retry</li>
            <li>• Clear browser cache</li>
            <li>• Log in to account again</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
