# RefundGo Internationalization (i18n)

## Overview

RefundGo provides comprehensive internationalization support for Chinese (zh) and English (en) locales using next-intl. The system includes automatic language detection, user preference tracking, and multilingual email templates.

## Architecture

### Core Technologies

- **Framework**: next-intl for React/Next.js internationalization
- **Locales**: Chinese (zh) and English (en)
- **Routing**: Locale-based routing (`/zh/dashboard`, `/en/dashboard`)
- **Storage**: User language preferences in database
- **Email**: Multilingual email templates with language detection

### Directory Structure

```
messages/
├── en.json                 # Main English translations
├── zh.json                 # Main Chinese translations
├── en/                     # English modules
│   ├── common.json         # Shared UI text
│   ├── homepage.json       # Homepage content
│   ├── dashboard.json      # Dashboard content
│   └── [other modules]
└── zh/                     # Chinese modules
    ├── common.json         # 共享界面文本
    ├── homepage.json       # 首页内容
    └── [other modules]
```

### Route Structure

```
src/app/
├── [locale]/               # Internationalized routes
│   ├── (main)/            # Public pages
│   ├── (user)/            # User dashboard
│   └── payment/           # Payment flows
└── (admin)/               # Admin panel (no i18n)
```

## Language Detection System

### Detection Hierarchy

The system uses a prioritized approach for determining user language:

1. **User Registration Language** (highest priority)
   - Stored in database `User.registrationLanguage`
   - Set during account creation
   - Persistent across sessions

2. **URL Locale Parameter** 
   - Current route locale (`/zh/` or `/en/`)
   - Represents user's active language choice
   - Takes priority over browser settings

3. **Accept-Language Header**
   - Browser language preference
   - Fallback when no explicit choice made
   - Parsed from HTTP headers

4. **Default Fallback**
   - English (en) as system default
   - Used when all other methods fail

### Implementation

```typescript
// Language detection function
export async function getUserRegistrationLanguage(
  email: string,
  request?: NextRequest
): Promise<'zh' | 'en'> {
  // 1. Check user's registration language
  const user = await db.user.findUnique({
    where: { email },
    select: { registrationLanguage: true }
  });
  
  if (user?.registrationLanguage) {
    return user.registrationLanguage as 'zh' | 'en';
  }

  // 2. Check URL locale
  if (request) {
    const urlLocale = extractLocaleFromUrl(request.url);
    if (urlLocale === 'zh' || urlLocale === 'en') {
      return urlLocale;
    }
  }

  // 3. Check Accept-Language header
  if (request) {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage?.includes('zh')) return 'zh';
  }

  // 4. Default fallback
  return 'en';
}
```

## Translation Management

### Translation Files

#### Modular Structure

```json
// messages/en/homepage.json
{
  "hero": {
    "title": "Global Cross-Border E-commerce Platform",
    "subtitle": "Secure and efficient customer service solutions"
  },
  "features": {
    "security": {
      "title": "Enterprise Security",
      "description": "Bank-level security protection"
    }
  }
}
```

#### Usage in Components

```tsx
import { useTranslations } from 'next-intl';

export default function HomePage() {
  const t = useTranslations('homepage');
  
  return (
    <div>
      <h1>{t('hero.title')}</h1>
      <p>{t('hero.subtitle')}</p>
    </div>
  );
}
```

### Translation Validation

```bash
# Validate translation completeness
node scripts/validate-translations.js

# Migrate translation files
node scripts/migrate-translations.js

# Check for missing keys
npm run i18n:check
```

## Email Internationalization

### Language Detection for Emails

Email language is determined using the same hierarchy as the web interface:

```typescript
// Email language detection
const userLanguage = await getUserRegistrationLanguage(
  user.email,
  request
);

const emailContent = verificationCodeTemplateI18n(
  code,
  userLanguage
);
```

### Multilingual Email Templates

```typescript
// Email template with i18n
export function verificationCodeTemplateI18n(
  code: string,
  language: 'zh' | 'en' = 'en'
) {
  const translations = {
    zh: {
      subject: '验证码确认',
      title: '邮箱验证',
      message: '您的验证码是：'
    },
    en: {
      subject: 'Verification Code',
      title: 'Email Verification',
      message: 'Your verification code is:'
    }
  };

  const t = translations[language];
  
  return {
    subject: t.subject,
    html: generateEmailHTML(t, code),
    text: generateEmailText(t, code)
  };
}
```

## Configuration

### Next.js Configuration

```typescript
// next.config.js
const withNextIntl = require('next-intl/plugin')('./src/i18n.ts');

module.exports = withNextIntl({
  // Other Next.js config
});
```

### i18n Configuration

```typescript
// src/i18n.ts
import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async ({ locale }) => ({
  messages: (await import(`../messages/${locale}.json`)).default
}));
```

### Middleware Configuration

```typescript
// src/middleware.ts
import createMiddleware from 'next-intl/middleware';

export default createMiddleware({
  locales: ['en', 'zh'],
  defaultLocale: 'en',
  localePrefix: 'always'
});

export const config = {
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)']
};
```

## Development Tools

### i18n Ally Setup

VSCode extension for translation management:

```json
// .vscode/settings.json
{
  "i18n-ally.localesPaths": ["messages"],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.defaultNamespace": "common",
  "i18n-ally.enabledFrameworks": ["next-intl"]
}
```

### Development Commands

```bash
# Start development with i18n
npm run dev

# Extract translation keys
npm run i18n:extract

# Validate translations
npm run i18n:validate

# Generate missing translations
npm run i18n:generate
```

## Best Practices

### Translation Keys

1. **Hierarchical Structure**: Use nested keys for organization
   ```json
   {
     "dashboard": {
       "navigation": {
         "tasks": "Tasks",
         "wallet": "Wallet"
       }
     }
   }
   ```

2. **Descriptive Naming**: Use clear, descriptive key names
   ```json
   {
     "buttons": {
       "submit": "Submit",
       "cancel": "Cancel",
       "saveChanges": "Save Changes"
     }
   }
   ```

3. **Consistent Patterns**: Follow established naming conventions
   ```json
   {
     "forms": {
       "validation": {
         "required": "This field is required",
         "email": "Please enter a valid email"
       }
     }
   }
   ```

### Component Implementation

1. **Use Specific Namespaces**: Import specific translation namespaces
   ```tsx
   const t = useTranslations('dashboard.tasks');
   ```

2. **Handle Missing Keys**: Provide fallbacks for missing translations
   ```tsx
   const title = t('title', { fallback: 'Default Title' });
   ```

3. **Dynamic Content**: Use interpolation for dynamic values
   ```tsx
   const message = t('welcome', { name: user.name });
   ```

## Migration Guide

### From Legacy System

1. **Extract Hardcoded Strings**: Identify all hardcoded text
2. **Create Translation Keys**: Organize into logical namespaces
3. **Update Components**: Replace strings with translation calls
4. **Test All Locales**: Verify functionality in both languages

### User Language Migration

```typescript
// Migrate existing users to new language system
npx tsx scripts/migrate-user-language.ts
```

## Troubleshooting

### Common Issues

1. **Missing Translations**: Check translation files for missing keys
2. **Locale Routing**: Verify middleware configuration
3. **Email Language**: Check language detection hierarchy
4. **Build Errors**: Ensure all translation files are valid JSON

### Debug Tools

```bash
# Check translation coverage
npm run i18n:coverage

# Validate JSON syntax
npm run i18n:validate-json

# Test language detection
curl -H "Accept-Language: zh-CN" http://localhost:3000/api/test-language
```

---

**Supported Locales**: Chinese (zh), English (en)  
**Last Updated**: 2025-01-29  
**Framework**: next-intl v3.x
