{"title": "Task Center", "breadcrumb": "Task List", "description": "View and manage your task list", "navigation": {"taskHall": "Task Hall", "browseTasks": "Browse Tasks"}, "filters": {"title": "Filters", "platform": "Platform", "category": "Category", "chargebackType": "Chargeback Type", "paymentMethod": "Payment Method", "evidenceStatus": "Evidence Status", "rewardRange": "Reward Range", "minReward": "<PERSON>", "maxReward": "<PERSON>", "reset": "Reset Filters", "apply": "Apply Filters", "clear": "Clear", "all": "All", "any": "Any"}, "sorting": {"title": "Sort By", "latest": "Latest Published", "rewardHighToLow": "Reward: High to Low", "rewardLowToHigh": "Reward: Low to High", "expiringSoon": "Expiring Soon"}, "search": {"placeholder": "Search task titles, categories or descriptions...", "hint": "Enter keywords to search", "noResults": "No tasks found", "resultsCount": "Found {count} tasks"}, "taskCard": {"platform": "Platform", "category": "Category", "reward": "<PERSON><PERSON>", "evidence": "Evidence", "chargebackType": "Chargeback Type", "paymentMethod": "Payment Method", "viewDetails": "View Details", "none": "None", "expired": "Expired", "expiresAt": "Expires At", "timeLeft": "Time Left", "days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "dateFormat": {"month": "/", "day": " "}, "evidenceStatusLabels": {"PENDING_SUBMISSION": "Pending Upload", "UNDER_REVIEW": "Under Review", "NO_EVIDENCE": "No Evidence", "REVIEWED": "Reviewed", "REJECTED": "Rejected"}}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page {page}", "totalPages": "of {total}", "showing": "Showing {start} - {end} of {total} items", "itemsPerPage": "Items per page", "items": "items"}, "emptyState": {"noTasks": "No Tasks", "noTasksDescription": "No tasks match the current filters", "noTasksHint": "Try adjusting your filters or check back later", "loading": "Loading...", "loadingTasks": "Loading tasks..."}, "actions": {"refresh": "Refresh", "filter": "Filter", "search": "Search", "view": "View", "accept": "Accept Task", "bookmark": "Bookmark", "share": "Share", "publish": "Publish Task"}, "status": {"active": "Active", "completed": "Completed", "expired": "Expired", "pending": "Pending", "cancelled": "Cancelled"}, "evidenceStatus": {"required": "Evidence Required", "optional": "Evidence Optional", "notRequired": "No Evidence Required", "pending": "Pending Review", "approved": "Approved", "rejected": "Rejected"}, "messages": {"loadError": "Failed to load, please try again", "loadFailed": "Failed to load task data, please refresh and try again", "filterError": "Filter failed, please try again", "searchError": "Search failed, please try again", "noConnection": "Network connection failed", "tryAgain": "Try Again", "loading": "Loading..."}, "taskDetail": {"title": "Task Details", "basicInfo": {"title": "Basic Information", "platform": "Platform", "category": "Category", "timeLeft": "Time Left", "deadline": "Deadline", "calculating": "Calculating...", "expired": "Expired", "noData": "No Data", "unknownPlatform": "Unknown Platform", "unknownCategory": "Unknown Category", "days": " days ", "hours": " hours ", "minutes": " minutes"}, "productInfo": {"title": "Product Information", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "deposit": "Required <PERSON><PERSON><PERSON><PERSON>", "commission": "Task Reward", "pieces": "pcs"}, "paymentInfo": {"chargebackType": "Chargeback Type", "paymentMethods": "Supported Payment Methods", "noData": "No Data"}, "evidenceStatus": {"title": "Evidence Status", "descriptions": {"PENDING_SUBMISSION": "The publisher has not yet submitted chargeback evidence. Please confirm if you can bear the related risks before accepting. If the publisher still cannot provide valid chargeback evidence after the task ends, the task reward will be increased by an additional 5% of the total product price.", "UNDER_REVIEW": "The publisher has submitted chargeback evidence and the platform is reviewing it. Please wait patiently for the review results. If valid evidence still cannot be provided after the task ends, the task reward will be increased by 5%.", "NO_EVIDENCE": "This task does not provide chargeback evidence. Please confirm if you can bear the related risks before accepting.", "REVIEWED": "The chargeback evidence has passed platform review. The evidence materials are authentic and valid, and it is safe to accept the order.", "REJECTED": "The chargeback evidence did not pass review. The publisher needs to provide new evidence. It is recommended to accept orders with caution. If valid evidence still cannot be provided after the task ends, the task reward will be increased by 5%.", "default": "Evidence status is abnormal. It is recommended to contact customer service to understand the detailed situation before deciding whether to accept the order."}}, "evidenceStatuses": {"PENDING_SUBMISSION": "Pending Submission", "UNDER_REVIEW": "Under Review", "NO_EVIDENCE": "No Evidence", "REVIEWED": "Reviewed", "REJECTED": "Rejected"}, "requirements": {"title": "Task Requirements", "timeFrame": {"title": "Time Frame", "description": "Complete the order within {timeLimit} hours after accepting the task."}, "documentation": {"title": "Documentation", "description": "Submit order number and order screenshot after completion."}, "completion": {"title": "Information Completion", "description": "Upload tracking number and logistics screenshot after shipping."}}, "actions": {"close": "Close", "acceptTask": "Accept Task"}}, "confirmDialog": {"title": "Confirm Accept Task", "description": "Please carefully read the task details and evidence fee instructions. The corresponding deposit will be frozen after confirmation.", "cannotAcceptTitle": "Cannot Accept Task", "cannotAcceptDescription": "You are the publisher of this task and cannot accept your own published task", "basicInfo": {"title": "Basic Task Information", "platform": "Platform:", "platformCategory": "Platform Category:", "quantity": "Quantity:", "unitPrice": "Unit Price:", "deposit": "Required Deposit:", "pieces": "pcs", "unknownPlatform": "Unknown Platform", "unknownCategory": "Unknown Category"}, "evidenceInfo": {"titles": {"PENDING_SUBMISSION": "Awaiting Evidence Submission", "UNDER_REVIEW": "Evidence Under Review", "NO_EVIDENCE": "No Evidence Task", "REVIEWED": "Evidence Reviewed", "REJECTED": "Evidence Rejected"}, "descriptions": {"PENDING_SUBMISSION": "Publisher has not yet submitted chargeback evidence", "UNDER_REVIEW": "Publisher has submitted chargeback evidence, platform is reviewing", "NO_EVIDENCE": "This task does not provide chargeback evidence", "REVIEWED": "Chargeback evidence has passed platform review, materials are authentic and valid", "REJECTED": "Chargeback evidence did not pass review, publisher needs to provide new evidence"}, "evidenceFeeNote": "Publisher has paid evidence fee", "evidenceApproved": "Evidence has been approved", "evidenceRejected": "Evidence review failed, this fee will be given to you as additional reward", "evidencePending": "If evidence fails review, this fee will be given to you as additional reward"}, "rewardCalculation": {"title": "Reward Calculation", "finalReward": "Your final reward:", "withEvidenceFee": "(including evidence fee)", "baseReward": "Base reward:", "ifEvidenceFails": "If evidence fails: additional"}, "riskWarning": "Publisher has not yet submitted chargeback evidence, please carefully evaluate your capability", "actions": {"close": "Close", "cancel": "Cancel", "confirm": "Confirm Accept Task", "processing": "Processing..."}, "messages": {"acceptSuccess": "Task accepted successfully!", "acceptSuccessDesc": "Your deposit has been frozen, please complete the task within the specified time", "acceptError": "Failed to accept task", "networkError": "Network error, please try again later"}}, "hooks": {"acceptTask": {"success": {"title": "Task accepted successfully!", "description": "Task ID: {taskId}\nDeposit: {deposit}\nBalance: {balance}"}, "errors": {"insufficientBalance": {"title": "Insufficient Balance", "description": "Account balance is insufficient to pay the deposit. Please top up first before accepting the task"}, "ownTask": {"title": "Invalid Operation", "description": "Cannot accept your own published task"}, "taskTaken": {"title": "Task Already Taken", "description": "Sorry, this task has already been accepted by another user"}, "taskExpired": {"title": "Task Expired", "description": "The recruitment period for this task has ended"}, "general": {"title": "Accept Failed", "description": "Failed to accept task"}, "networkError": "Network error, please try again later"}}, "abandonTask": {"success": {"title": "Task Abandoned", "description": "Task has been successfully abandoned"}, "error": {"title": "Abandon Failed", "description": "Failed to abandon task"}}, "cancelTask": {"success": {"title": "Task Cancelled", "description": "Task has been removed from the task hall, related fees will be refunded"}, "error": {"title": "Cancel Failed", "description": "Failed to cancel task"}}, "submitOrder": {"success": {"title": "Order Submitted Successfully!", "description": "Order information has been submitted and is awaiting review"}, "error": {"title": "Order Submission Failed", "description": "Failed to submit order"}}, "confirmDelivery": {"success": {"title": "Delivery Confirmed Successfully!", "description": "Delivery confirmation successful"}, "error": {"title": "Delivery Confirmation Failed", "description": "Failed to confirm delivery"}}}, "logistics": {"dialog": {"title": "Logistics Information", "taskId": "Task ID", "orderNumber": "Order Number", "trackingNumber": "Tracking Number", "submitted": "Submitted", "approved": "Approved", "underReview": "Under Review", "copySuccess": "<PERSON>pied", "orderCopySuccess": "Order number copied", "trackingCopySuccess": "Tracking number copied", "loading": "Loading logistics information...", "noData": "Click to view logistics information", "company": "Logistics Company", "currentStatus": "Current Status", "estimatedDelivery": "Estimated Delivery", "lastUpdate": "Last Update", "noInfo": "No Information", "unknown": "Unknown", "trackingHistory": "Tracking History", "timeMetrics": "Time Metrics", "transitDays": "Transit Days", "daysAfterOrder": "Days After Order", "daysNoUpdate": "Days No Update", "serviceType": "Service Type", "standard": "Standard", "days": "days"}, "status": {"NotFound": "Not Found", "InfoReceived": "Info Received", "InTransit": "In Transit", "Expired": "Expired", "AvailableForPickup": "Available for Pickup", "OutForDelivery": "Out for Delivery", "DeliveryFailure": "Delivery Failure", "Delivered": "Delivered", "Exception": "Exception", "unknown": "Unknown Status"}, "errors": {"noTrackingInfo": "No tracking information available", "networkError": "Network error, please try again later", "registerFailed": "Registration failed", "fetchFailed": "Failed to fetch logistics information", "reasons": {"title": "This usually occurs in the following situations:", "justCreated": "Tracking number was just created and hasn't entered the logistics system yet", "syncDelay": "17TRACK needs time to sync logistics information", "notUpdated": "Logistics company hasn't updated package status yet", "suggestion": "Suggestion: Please try again later or contact the sender to confirm the tracking number"}}}, "submitLogistics": {"dialog": {"title": "Submit Tracking Number", "description": "Please fill in the logistics tracking number so customers can track the package status", "trackingNumberLabel": "Tracking Number *", "trackingNumberPlaceholder": "Please enter the complete tracking number", "screenshotsLabel": "Logistics Screenshots * (Proof that this tracking number belongs to this order)", "cancel": "Cancel", "submit": "Submit Tracking Number", "submitting": "Submitting..."}, "validation": {"trackingNumberRequired": "Please enter tracking number", "trackingNumberMinLength": "Tracking number must be at least 6 characters", "screenshotsRequired": "Please upload at least one logistics screenshot", "screenshotsMaxCount": "Maximum 5 logistics screenshots allowed"}, "messages": {"submitSuccess": "Tracking number submitted successfully!", "submitFailed": "Failed to submit tracking number", "networkError": "Network error, please try again later"}}, "api": {"errors": {"taskAlreadyAccepted": "This task has already been accepted by another user", "cannotAcceptOwnTask": "Cannot accept your own published task", "taskExpired": "Task has expired", "userNotFound": "User not found", "taskNotFound": "Task not found", "insufficientBalance": "Insufficient balance", "unauthorized": "Unauthorized access", "serverError": "Server error", "validationError": "Data validation failed"}}}