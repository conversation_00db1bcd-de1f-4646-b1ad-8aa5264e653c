import { PrismaClient } from '@prisma/client';

declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

const prismaClientSingleton = (): PrismaClient => {
  // 数据库连接配置
  const baseUrl = process.env.DATABASE_URL || '';
  const separator = baseUrl.includes('?') ? '&' : '?';
  const utcUrl = `${baseUrl}${separator}timezone=UTC&application_name=nextjs-app`;

  return new PrismaClient({
    datasources: {
      db: {
        url: utcUrl,
      },
    },
  });
};

let prisma: PrismaClient;
if (!global.prisma) {
  prisma = prismaClientSingleton();
  if (process.env.NODE_ENV !== 'production') global.prisma = prisma;
} else {
  prisma = global.prisma;
}

export default prisma;
