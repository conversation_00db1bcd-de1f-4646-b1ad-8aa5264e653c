import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { toast } from 'sonner';

// 邮件发送请求的类型定义
export interface SendEmailRequest {
  type: 'custom' | 'notification' | 'verification';
  to: string | string[];
  data?: {
    userName?: string;
    userEmail?: string;
    signInTime?: string;
    action?: 'login' | 'register' | 'reset-password';
    expiresIn?: number;
    verificationCode?: string;
  };
  subject?: string;
  html?: string;
  text?: string;
  from?: string;
}

export interface SendEmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  details?: any;
}

// 邮件发送 API 调用函数
const sendEmailApi = async (
  emailData: SendEmailRequest,
): Promise<SendEmailResponse> => {
  const response = await axios.post('/api/send-email', emailData);
  return response.data;
};

// 检查邮件服务状态 API 调用函数
const checkEmailServiceApi = async () => {
  const response = await axios.get('/api/send-email');
  return response.data;
};

// 邮件发送 Hook
export const useSendEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: sendEmailApi,
    onSuccess: (data, variables) => {
      if (data.success) {
        toast.success('邮件发送成功！', {
          description: `邮件已成功发送到 ${Array.isArray(variables.to) ? variables.to.join(', ') : variables.to}`,
        });
      } else {
        toast.error('邮件发送失败', {
          description: data.error || '未知错误',
        });
      }
    },
    onError: (error: any) => {
      toast.error('邮件发送失败', {
        description: error.response?.data?.error || error.message || '网络错误',
      });
    },
  });
};

// 欢迎邮件功能已移除

// 发送通知邮件的便捷 Hook
export const useSendNotificationEmail = () => {
  const sendEmailMutation = useSendEmail();

  const sendNotificationEmail = (
    to: string,
    notificationData: {
      userName: string;
      userEmail: string;
      signInTime: string;
    },
  ) => {
    return sendEmailMutation.mutate({
      type: 'notification',
      to,
      data: notificationData,
    });
  };

  return {
    sendNotificationEmail,
    isLoading: sendEmailMutation.isPending,
    error: sendEmailMutation.error,
    isSuccess: sendEmailMutation.isSuccess,
    reset: sendEmailMutation.reset,
  };
};

// 发送自定义邮件的便捷 Hook
export const useSendCustomEmail = () => {
  const sendEmailMutation = useSendEmail();

  const sendCustomEmail = (emailData: {
    to: string | string[];
    subject: string;
    html?: string;
    text?: string;
    from?: string;
  }) => {
    return sendEmailMutation.mutate({
      type: 'custom',
      ...emailData,
    });
  };

  return {
    sendCustomEmail,
    isLoading: sendEmailMutation.isPending,
    error: sendEmailMutation.error,
    isSuccess: sendEmailMutation.isSuccess,
    reset: sendEmailMutation.reset,
  };
};

// 批量发送邮件的 Hook
export const useBulkSendEmail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (emails: SendEmailRequest[]) => {
      const results = await Promise.allSettled(
        emails.map(email => sendEmailApi(email)),
      );

      const successful = results.filter(
        (result): result is PromiseFulfilledResult<SendEmailResponse> =>
          result.status === 'fulfilled' && result.value.success,
      ).length;

      const failed = results.length - successful;

      return {
        total: results.length,
        successful,
        failed,
        results: results.map(result =>
          result.status === 'fulfilled'
            ? result.value
            : { success: false, error: result.reason },
        ),
      };
    },
    onSuccess: data => {
      if (data.successful > 0) {
        toast.success(`批量邮件发送完成`, {
          description: `成功发送 ${data.successful} 封，失败 ${data.failed} 封`,
        });
      } else {
        toast.error('批量邮件发送失败', {
          description: `所有 ${data.total} 封邮件发送失败`,
        });
      }
    },
    onError: (error: any) => {
      toast.error('批量邮件发送失败', {
        description: error.message || '网络错误',
      });
    },
  });
};

// 检查邮件服务状态的 Hook
export const useEmailServiceStatus = () => {
  return useMutation({
    mutationFn: checkEmailServiceApi,
    onSuccess: data => {
      if (data.success) {
        toast.success('邮件服务正常', {
          description: `配置状态：API密钥 ${data.config.hasApiKey ? '✓' : '✗'}, 发件人邮箱 ${data.config.hasFromEmail ? '✓' : '✗'}`,
        });
      } else {
        toast.error('邮件服务异常', {
          description: data.error || '服务检查失败',
        });
      }
    },
    onError: (error: any) => {
      toast.error('邮件服务检查失败', {
        description: error.response?.data?.error || error.message || '网络错误',
      });
    },
  });
};
