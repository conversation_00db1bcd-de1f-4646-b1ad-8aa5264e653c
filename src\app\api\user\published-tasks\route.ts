import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

// 获取用户发布的委托列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // 构建查询条件
    const where: any = {
      publisherId: session.user.id,
    };

    // 根据状态筛选
    if (status && status !== 'all') {
      const statusMap: Record<string, TaskStatus> = {
        available: TaskStatus.RECRUITING,
        in_progress: TaskStatus.IN_PROGRESS,
        completed: TaskStatus.COMPLETED,
        expired: TaskStatus.EXPIRED,
        cancelled: TaskStatus.CANCELLED,
      };

      if (statusMap[status]) {
        where.status = statusMap[status];
      }
    }

    // 查询委托数据
    const [tasks, totalCount] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          accepter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.task.count({ where: { publisherId: session.user.id } }),
    ]);

    // 获取时区修正的扩展字段（与接单者API保持一致的时区处理）
    const taskIds = tasks.map(task => task.id);
    const extendedData = await prisma.$queryRaw<
      Array<{
        id: string;
        logisticsDeadline: string | null;
        orderReviewDeadline: string | null;
        logisticsReviewDeadline: string | null;
        deliveryDeadline: string | null;
        reviewedAt: string | null;
      }>
    >`
      SELECT 
        id, 
        CASE 
          WHEN "logisticsDeadline" IS NOT NULL 
          THEN to_char("logisticsDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "logisticsDeadline",
        CASE 
          WHEN "orderReviewDeadline" IS NOT NULL 
          THEN to_char("orderReviewDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "orderReviewDeadline",
        CASE 
          WHEN "logisticsReviewDeadline" IS NOT NULL 
          THEN to_char("logisticsReviewDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "logisticsReviewDeadline",
        CASE 
          WHEN "deliveryDeadline" IS NOT NULL 
          THEN to_char("deliveryDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "deliveryDeadline",
        CASE 
          WHEN "reviewedAt" IS NOT NULL 
          THEN to_char("reviewedAt" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "reviewedAt"
      FROM tasks 
      WHERE id = ANY(${taskIds}::text[])
    `;

    // 创建扩展数据映射
    const extendedMap = extendedData.reduce(
      (map, item) => {
        map[item.id] = {
          logisticsDeadline: item.logisticsDeadline,
          orderReviewDeadline: item.orderReviewDeadline,
          logisticsReviewDeadline: item.logisticsReviewDeadline,
          deliveryDeadline: item.deliveryDeadline,
          reviewedAt: item.reviewedAt,
        };
        return map;
      },
      {} as Record<
        string,
        {
          logisticsDeadline: string | null;
          orderReviewDeadline: string | null;
          logisticsReviewDeadline: string | null;
          deliveryDeadline: string | null;
          reviewedAt: string | null;
        }
      >,
    );

    // 获取各状态的统计数据
    const [
      availableCount,
      inProgressCount,
      completedCount,
      expiredCount,
      cancelledCount,
    ] = await Promise.all([
      prisma.task.count({
        where: { publisherId: session.user.id, status: TaskStatus.RECRUITING },
      }),
      prisma.task.count({
        where: { publisherId: session.user.id, status: TaskStatus.IN_PROGRESS },
      }),
      prisma.task.count({
        where: { publisherId: session.user.id, status: TaskStatus.COMPLETED },
      }),
      prisma.task.count({
        where: { publisherId: session.user.id, status: TaskStatus.EXPIRED },
      }),
      prisma.task.count({
        where: { publisherId: session.user.id, status: TaskStatus.CANCELLED },
      }),
    ]);

    // 获取拒付类型和支付方式名称
    const [chargebackTypes, paymentMethods] = await Promise.all([
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    // 创建ID到名称的映射
    const chargebackTypeMap = chargebackTypes.reduce(
      (map, type) => {
        map[type.id] = type.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const paymentMethodMap = paymentMethods.reduce(
      (map, method) => {
        map[method.id] = method.name;
        return map;
      },
      {} as Record<string, string>,
    );

    // 转换数据格式以匹配前端期望的结构（使用时区修正后的数据）
    const formattedTasks = tasks.map(task => {
      // 从映射中获取时区修正后的扩展数据
      const taskExtendedData = extendedMap[task.id] || {
        logisticsDeadline: null,
        orderReviewDeadline: null,
        logisticsReviewDeadline: null,
        deliveryDeadline: null,
        reviewedAt: null,
      };

      return {
        id: task.id,
        platform: task.platform.name,
        category: task.category.name,
        chargebackTypes: task.chargebackTypeIds.map(
          id => chargebackTypeMap[id] || id,
        ),
        paymentMethods: task.paymentMethodIds.map(
          id => paymentMethodMap[id] || id,
        ),
        quantity: task.quantity,
        unitPrice: task.unitPrice,
        totalAmount: task.totalAmount,
        finalTotal: task.finalTotal,
        productUrl: task.productUrl,
        productDescription: task.productDescription,
        recipientName: task.recipientName,
        recipientPhone: task.recipientPhone,
        shippingAddress: task.shippingAddress,
        listingTime: task.listingTime,
        status: task.status,
        evidenceStatus: (task as any).evidenceStatus,
        evidenceRejectReason: (task as any).evidenceRejectReason,
        evidenceUploadType: task.evidenceUploadType,
        evidenceFiles: task.evidenceFiles,
        cartScreenshots: task.cartScreenshots,

        // 订单信息
        orderNumber: (task as any).orderNumber,
        orderScreenshot: (task as any).orderScreenshot,

        // 物流信息
        trackingNumber: (task as any).trackingNumber,
        logisticsScreenshots: (task as any).logisticsScreenshots || [],
        logisticsDeadline: taskExtendedData.logisticsDeadline, // 使用时区修正后的数据

        // 审核时间相关（使用时区修正后的数据）
        orderReviewDeadline: taskExtendedData.orderReviewDeadline,
        logisticsReviewDeadline: taskExtendedData.logisticsReviewDeadline,
        deliveryDeadline: taskExtendedData.deliveryDeadline,

        // 审核信息
        reviewedAt: taskExtendedData.reviewedAt, // 使用时区修正后的数据
        reviewRejectReason: (task as any).reviewRejectReason,

        accepter: task.accepter
          ? {
              id: task.accepter.id,
              nickname: task.accepter.name || '未知用户',
              email: task.accepter.email || '',
            }
          : null,
        createdAt: task.createdAt.toISOString(),
        publishedAt: task.publishedAt?.toISOString(),
        acceptedAt: task.acceptedAt?.toISOString(),
        expiresAt: task.expiresAt?.toISOString(),
        completedAt: task.completedAt?.toISOString(),
        cancelledAt:
          task.status === TaskStatus.CANCELLED
            ? task.updatedAt.toISOString()
            : undefined,
        deadline:
          task.expiresAt?.toISOString() ||
          new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      };
    });

    // 统计数据
    const stats = {
      total: totalCount,
      available: availableCount,
      inProgress: inProgressCount,
      completed: completedCount,
      expired: expiredCount,
      cancelled: cancelledCount,
    };

    return NextResponse.json({
      success: true,
      data: {
        tasks: formattedTasks,
        stats,
        pagination: {
          page,
          limit,
          total: tasks.length,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
