'use client';

import {
  Clock,
  Calendar,
  Building2,
  FileText,
  CheckCircle,
  AlertCircle,
  XCircle,
  Upload,
  Eye,
  X,
  Loader2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { useState, useMemo, useEffect } from 'react';

import { AbandonTaskDialog } from '@/components/abandon-task-dialog';
import { AcceptedTaskDetailSheet } from '@/components/accepted-task-detail-sheet';
import { AppSidebar } from '@/components/app-sidebar';
import { CountdownTimer } from '@/components/countdown-timer';
import { ModeToggle } from '@/components/mode-toggle';
import { SubmitLogisticsDialog } from '@/components/submit-logistics-dialog';
import { SubmitOrderDialog } from '@/components/submit-order-dialog';
import { Badge } from '@/components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ViewOrderDialog } from '@/components/ui/view-order-dialog';
import { ViewOrderDialog as PublishedViewOrderDialog } from '@/components/view-order-dialog';
import { ViewReviewStatusDialog } from '@/components/view-review-status-dialog';
import {
  useAcceptedTasks,
  type AcceptedTask,
} from '@/hooks/use-accepted-tasks';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import { useResponsive } from '@/hooks/use-responsive';
import { Task, TaskStatus, EvidenceStatus } from '@/lib/types/task';
import {
  getTaskCommission,
  getTaskBaseCommission,
} from '@/lib/utils/commission';

interface MyTaskCardProps {
  task: AcceptedTask;
  onAbandonTask?: (taskId: string) => void;
}

function MyTaskCard({ task, onAbandonTask }: MyTaskCardProps) {
  const t = useTranslations('my-accepted-tasks');
  const tPublish = useTranslations('publish');
  const locale = useLocale();
  const router = useRouter();

  // 获取平台名称的翻译
  const getPlatformName = (platform: any) => {
    if (typeof platform === 'object' && platform?.name) {
      // 如果是对象，尝试翻译name字段
      return (
        tPublish(`platformSelection.platformLabels.${platform.name}` as any) ||
        platform.name
      );
    }
    if (typeof platform === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.platformLabels.${platform}` as any) ||
        platform
      );
    }
    return platform;
  };

  // 获取分类名称的翻译
  const getCategoryName = (category: any) => {
    if (typeof category === 'object' && category?.name) {
      // 如果是对象，尝试翻译name字段
      return (
        tPublish(`platformSelection.categoryLabels.${category.name}` as any) ||
        category.name
      );
    }
    if (typeof category === 'string') {
      // 如果是字符串，尝试翻译
      return (
        tPublish(`platformSelection.categoryLabels.${category}` as any) ||
        category
      );
    }
    return category;
  };

  const [mounted, setMounted] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    setMounted(true);

    // 立即更新当前时间
    setCurrentTime(new Date());

    // 每秒更新一次当前时间，确保倒计时准确
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000); // 改为每秒更新

    return () => clearInterval(timer);
  }, []);

  // 获取费率数据
  const { chargebackTypes, paymentMethods, systemRate } = useCommissionRates();

  // 计算酬金（使用真实的费率数据，包含证据状态）
  const taskWithEvidenceStatus = {
    ...task,
    evidenceStatus: task.evidenceStatus?.toString(),
  };
  const commission = getTaskCommission(
    taskWithEvidenceStatus,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算基础酬金（不包含证据费用）
  const baseCommission = getTaskBaseCommission(
    taskWithEvidenceStatus,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  // 计算产品总价：单价 × 数量
  const calculateTotalPrice = () => {
    const unitPrice = task.unitPrice || 0;
    const quantity = task.quantity || 1;
    return unitPrice * quantity;
  };

  // 计算所需押金：产品总价 × 押金比例
  const calculateDeposit = () => {
    const totalPrice = calculateTotalPrice();
    const depositRatio = systemRate?.depositRatio || 0;
    return totalPrice * (depositRatio / 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-100';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-100';
      case 'PENDING_LOGISTICS':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-100';
      case 'PENDING_REVIEW':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-100';
      case 'PENDING_DELIVERY':
        return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-100';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-100';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-100';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-100';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className='h-4 w-4' />;
      case 'IN_PROGRESS':
        return <AlertCircle className='h-4 w-4' />;
      case 'PENDING_LOGISTICS':
        return <Upload className='h-4 w-4' />;
      case 'PENDING_REVIEW':
        return <Eye className='h-4 w-4' />;
      case 'PENDING_DELIVERY':
        return <CheckCircle className='h-4 w-4' />;
      case 'COMPLETED':
        return <CheckCircle className='h-4 w-4' />;
      case 'EXPIRED':
        return <XCircle className='h-4 w-4' />;
      case 'CANCELLED':
        return <XCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return t('status.PENDING');
      case 'IN_PROGRESS':
        return t('status.IN_PROGRESS');
      case 'PENDING_LOGISTICS':
        return t('status.PENDING_LOGISTICS');
      case 'PENDING_REVIEW':
        return t('status.PENDING_REVIEW');
      case 'PENDING_DELIVERY':
        return t('status.PENDING_DELIVERY');
      case 'COMPLETED':
        return t('status.COMPLETED');
      case 'EXPIRED':
        return t('status.EXPIRED');
      case 'CANCELLED':
        return t('status.CANCELLED');
      default:
        return status;
    }
  };

  const getEvidenceStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING_SUBMISSION':
        return 'bg-gray-100 text-gray-800';
      case 'UNDER_REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'NO_EVIDENCE':
        return 'bg-orange-100 text-orange-800';
      case 'REVIEWED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-gray-100 text-gray-800'; // 拒绝后显示为灰色（待上传样式）
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEvidenceStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING_SUBMISSION':
        return <FileText className='h-4 w-4' />;
      case 'UNDER_REVIEW':
        return <Clock className='h-4 w-4' />;
      case 'NO_EVIDENCE':
        return <XCircle className='h-4 w-4' />;
      case 'REVIEWED':
        return <CheckCircle className='h-4 w-4' />;
      case 'REJECTED':
        return <FileText className='h-4 w-4' />; // 拒绝后显示为文件图标（待上传样式）
      default:
        return <FileText className='h-4 w-4' />;
    }
  };

  const getEvidenceStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING_SUBMISSION':
        return t('evidence.PENDING_SUBMISSION');
      case 'UNDER_REVIEW':
        return t('evidence.UNDER_REVIEW');
      case 'NO_EVIDENCE':
        return t('evidence.NO_EVIDENCE');
      case 'REVIEWED':
        return t('evidence.REVIEWED');
      case 'REJECTED':
        return t('evidence.PENDING_SUBMISSION'); // 拒绝后显示为待上传，让用户重新上传
      default:
        return status;
    }
  };

  // 计算24小时订单提交倒计时
  const getOrderCountdown = () => {
    if (!mounted) {
      return {
        text: t('time.calculating'),
        isExpired: false,
        isUrgent: false,
        type: 'order',
      };
    }

    const now = currentTime;

    // 如果委托被驳回，给予24小时重新提交时间
    let deadline: Date;
    if (task.reviewRejectReason && task.reviewedAt) {
      // 被驳回委托：从审核时间开始计算24小时
      deadline = new Date(
        new Date(task.reviewedAt).getTime() + 24 * 60 * 60 * 1000,
      );
    } else {
      // 正常委托：从接单时间开始计算24小时
      deadline = new Date(task.deadline);
    }

    const diffMs = deadline.getTime() - now.getTime();

    if (diffMs <= 0) {
      return {
        text: t('time.expired'),
        isExpired: true,
        isUrgent: false,
        type: 'order',
      };
    }

    const totalHours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (totalHours >= 24) {
      const days = Math.floor(totalHours / 24);
      const remainingHours = totalHours % 24;
      if (remainingHours > 0) {
        return {
          text: `${days}${t('time.days')}${remainingHours}${t('time.hours')}`,
          isExpired: false,
          isUrgent: false,
          type: 'order',
        };
      } else {
        return {
          text: `${days}${t('time.days')}`,
          isExpired: false,
          isUrgent: false,
          type: 'order',
        };
      }
    } else if (totalHours > 0) {
      if (minutes > 0) {
        return {
          text: `${totalHours}${t('time.hours')}${minutes}${t('time.minutes')}`,
          isExpired: false,
          isUrgent: totalHours < 2,
          type: 'order',
        };
      } else {
        return {
          text: `${totalHours}${t('time.hours')}`,
          isExpired: false,
          isUrgent: totalHours < 2,
          type: 'order',
        };
      }
    } else {
      return {
        text: `${minutes}${t('time.minutes')}`,
        isExpired: false,
        isUrgent: true,
        type: 'order',
      };
    }
  };

  // 计算5天物流号提交倒计时
  const getLogisticsCountdown = () => {
    if (!mounted) {
      return {
        text: t('time.loading'),
        isExpired: false,
        isUrgent: false,
        type: 'logistics',
      };
    }

    if (!task.logisticsDeadline) {
      return {
        text: t('time.noDeadline'),
        isExpired: false,
        isUrgent: false,
        type: 'logistics',
      };
    }

    const now = currentTime;
    const deadline = new Date(task.logisticsDeadline);
    const diffMs = deadline.getTime() - now.getTime();

    if (diffMs <= 0) {
      return {
        text: t('time.expired'),
        isExpired: true,
        isUrgent: false,
        type: 'logistics',
      };
    }

    // 倒计时显示逻辑
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
    );
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    let timeText = '';

    // 详细显示时间倒计时
    if (days > 0) {
      // 有天数时，显示天时分
      timeText = `${days}${t('time.days')}${hours}${t('time.hours')}${minutes}${t('time.minutes')}`;
    } else if (hours > 0) {
      // 不足1天时，显示小时分
      timeText = `${hours}${t('time.hours')}${minutes}${t('time.minutes')}`;
    } else {
      // 不足1小时时，显示分钟
      timeText = `${minutes}${t('time.minutes')}`;
    }

    return {
      text: timeText,
      isExpired: false,
      isUrgent: days < 1,
      type: 'logistics',
    };
  };

  // 根据委托状态选择合适的倒计时
  const getCountdown = () => {
    if (task.status === 'PENDING_LOGISTICS') {
      return getLogisticsCountdown();
    } else if (task.status === 'PENDING_REVIEW') {
      // 审核状态：区分订单审核和物流审核倒计时
      if (task.orderReviewDeadline) {
        return getOrderReviewCountdown();
      } else if (task.logisticsReviewDeadline) {
        return getLogisticsReviewCountdown();
      } else {
        return {
          text: t('status.PENDING_REVIEW'),
          isExpired: false,
          isUrgent: false,
          type: 'review',
        };
      }
    } else if (task.status === 'PENDING_DELIVERY') {
      return getDeliveryCountdown();
    } else {
      return getOrderCountdown();
    }
  };

  // 计算5小时订单审核倒计时
  const getOrderReviewCountdown = () => {
    if (!mounted || !task.orderReviewDeadline) {
      return {
        text: t('time.calculating'),
        isExpired: false,
        isUrgent: false,
        type: 'order_review',
      };
    }

    const now = currentTime;
    const deadline = new Date(task.orderReviewDeadline);
    const diffMs = deadline.getTime() - now.getTime();

    if (diffMs <= 0) {
      return {
        text: t('time.reviewTimeout'),
        isExpired: true,
        isUrgent: false,
        type: 'order_review',
      };
    }

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    let timeText = '';
    if (hours > 0) {
      timeText = `${hours}${t('time.hours')}${minutes}${t('time.minutes')}`;
    } else {
      timeText = `${minutes}${t('time.minutes')}`;
    }

    return {
      text: `${t('labels.orderReview')}：${timeText}`,
      isExpired: false,
      isUrgent: hours < 1,
      type: 'order_review',
    };
  };

  // 计算24小时物流审核倒计时
  const getLogisticsReviewCountdown = () => {
    if (!mounted || !task.logisticsReviewDeadline) {
      return {
        text: t('time.calculating'),
        isExpired: false,
        isUrgent: false,
        type: 'logistics_review',
      };
    }

    const now = currentTime;
    const deadline = new Date(task.logisticsReviewDeadline);
    const diffMs = deadline.getTime() - now.getTime();

    if (diffMs <= 0) {
      return {
        text: t('time.reviewTimeout'),
        isExpired: true,
        isUrgent: false,
        type: 'logistics_review',
      };
    }

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    let timeText = '';
    if (hours > 0) {
      timeText = `${hours}${t('time.hours')}${minutes}${t('time.minutes')}`;
    } else {
      timeText = `${minutes}${t('time.minutes')}`;
    }

    return {
      text: `${t('labels.logisticsReview')}：${timeText}`,
      isExpired: false,
      isUrgent: hours < 2,
      type: 'logistics_review',
    };
  };

  // 计算30天自动收货倒计时
  const getDeliveryCountdown = () => {
    if (!mounted || !task.deliveryDeadline) {
      return {
        text: t('time.calculating'),
        isExpired: false,
        isUrgent: false,
        type: 'delivery',
      };
    }

    const now = currentTime;
    const deadline = new Date(task.deliveryDeadline);
    const diffMs = deadline.getTime() - now.getTime();

    if (diffMs <= 0) {
      return {
        text: t('time.autoConfirmDelivery'),
        isExpired: true,
        isUrgent: false,
        type: 'delivery',
      };
    }

    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
    );

    let timeText = '';
    if (days > 0) {
      timeText = `${days}${t('time.days')}${hours}${t('time.hours')}`;
    } else if (hours > 0) {
      timeText = `${hours}${t('time.hours')}`;
    } else {
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      timeText = `${minutes}${t('time.minutes')}`;
    }

    return {
      text: `${timeText}`,
      isExpired: false,
      isUrgent: days < 3,
      type: 'delivery',
    };
  };

  const countdown = getCountdown();

  return (
    <Card className='hover:shadow-md transition-shadow'>
      <CardContent className='space-y-4 pt-6'>
        {/* 平台信息和委托状态 */}
        <div className='flex items-start justify-between'>
          <div className='flex items-center gap-2'>
            <Building2 className='h-4 w-4 text-gray-600 dark:text-gray-300' />
            <span className='font-medium'>
              {getPlatformName(task.platform)} -{' '}
              {getCategoryName(task.category)}
            </span>
          </div>
          <div className='text-right'>
            {task.status === 'IN_PROGRESS' && (
              <Badge
                className={`${task.reviewRejectReason ? 'bg-red-100 text-red-800 border-red-300' : 'bg-green-100 text-green-800 border-green-300'}`}
              >
                {task.reviewRejectReason
                  ? t('status.needResubmit')
                  : t('status.IN_PROGRESS')}
              </Badge>
            )}
            {task.status === 'PENDING_LOGISTICS' && (
              <Badge className='bg-blue-100 text-blue-800 border-blue-300'>
                {t('status.orderSubmitted')}
              </Badge>
            )}
            {task.status === 'PENDING_REVIEW' && (
              <Badge className='bg-orange-100 text-orange-800 border-orange-300'>
                {t('status.PENDING_REVIEW')}
              </Badge>
            )}
            {task.status === 'PENDING_DELIVERY' && (
              <Badge className='bg-purple-100 text-purple-800 border-purple-300'>
                {t('status.PENDING_DELIVERY')}
              </Badge>
            )}
            {task.status === 'COMPLETED' && (
              <Badge className='bg-emerald-100 text-emerald-800 border-emerald-300'>
                {t('status.COMPLETED')}
              </Badge>
            )}
            {task.status === 'EXPIRED' && (
              <Badge className='bg-gray-100 text-gray-800 border-gray-300'>
                {t('status.EXPIRED')}
              </Badge>
            )}
            {task.status === 'CANCELLED' && (
              <Badge className='bg-gray-100 text-gray-800 border-gray-300'>
                {t('status.CANCELLED')}
              </Badge>
            )}
          </div>
        </div>

        {/* 商品信息和酬金 */}
        <div className='grid grid-cols-2 md:grid-cols-3 gap-3 md:gap-4 py-3 bg-muted/30 rounded-lg px-3'>
          <div className='space-y-1'>
            <span className='text-xs text-gray-600 dark:text-gray-300'>
              {t('card.totalPrice')}
            </span>
            <div className='font-semibold text-sm'>
              ${calculateTotalPrice().toFixed(2)}
            </div>
          </div>
          <div className='space-y-1'>
            <span className='text-xs text-gray-600 dark:text-gray-300'>
              {t('card.deposit')}
            </span>
            <div className='font-semibold text-orange-600 text-sm'>
              ${calculateDeposit().toFixed(2)}
            </div>
          </div>
          <div className='space-y-1 col-span-2 md:col-span-1'>
            <span className='text-xs text-gray-600 dark:text-gray-300'>
              {t('card.reward')}
            </span>
            <div className='font-semibold text-green-600 text-sm'>
              ${baseCommission.toFixed(2)}
            </div>
          </div>
        </div>

        {/* 证据状态和提交时限 */}
        <div className='space-y-3'>
          <div
            className={`flex items-center ${task.status === 'EXPIRED' || task.status === 'COMPLETED' ? 'justify-start' : 'justify-between'} gap-4`}
          >
            {/* 证据状态 */}
            <div className='flex items-center gap-2'>
              {getEvidenceStatusIcon(task.evidenceStatus)}
              <span className='text-sm text-gray-600 dark:text-gray-300'>
                {t('labels.evidenceStatus')}
              </span>
              <Badge
                className={`${getEvidenceStatusColor(task.evidenceStatus)} transition-colors duration-200 text-xs`}
              >
                {getEvidenceStatusLabel(task.evidenceStatus)}
              </Badge>
            </div>

            {/* 倒计时 - 已过期、已完成、等待审核委托不显示 */}
            {task.status !== 'EXPIRED' &&
              task.status !== 'COMPLETED' &&
              task.status !== 'CANCELLED' &&
              task.status !== 'PENDING_REVIEW' && (
                <div className='flex items-center gap-2'>
                  <Clock className='h-4 w-4 text-gray-600 dark:text-gray-300' />
                  <span className='text-sm text-gray-600 dark:text-gray-300'>
                    {countdown.type === 'logistics'
                      ? t('labels.logisticsSubmission')
                      : countdown.type === 'delivery'
                        ? t('labels.autoDeliveryLimit')
                        : t('labels.orderSubmissionLimit')}
                  </span>
                  <span
                    className={`text-sm font-medium ${
                      countdown.isExpired
                        ? 'text-red-600'
                        : countdown.isUrgent
                          ? 'text-orange-600'
                          : countdown.type === 'delivery'
                            ? 'text-green-600'
                            : 'text-blue-600'
                    }`}
                  >
                    {countdown.isExpired
                      ? countdown.text
                      : ` ${countdown.text}`}
                  </span>
                </div>
              )}
          </div>

          {/* 证据拒绝理由显示 */}
          {task.evidenceStatus === 'REJECTED' && task.evidenceRejectReason && (
            <div className='p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950 dark:border-red-800'>
              <div className='flex items-start gap-2'>
                <AlertCircle className='h-4 w-4 text-red-600 mt-0.5 flex-shrink-0' />
                <div>
                  <div className='text-sm font-medium text-red-800 dark:text-red-300'>
                    {t('messages.evidenceRejected')}
                  </div>
                  <div className='text-sm text-red-700 dark:text-red-400 mt-1'>
                    {task.evidenceRejectReason}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className='pt-2'>
          {task.status === 'IN_PROGRESS' ? (
            // 进行中委托显示三个按钮：查看详情、提交订单、放弃委托
            <div className='flex gap-2'>
              <AcceptedTaskDetailSheet task={task as any}>
                <Button size='sm' variant='outline' className='flex-1'>
                  <Eye className='h-4 w-4 mr-1' />
                  {t('actions.viewDetails')}
                </Button>
              </AcceptedTaskDetailSheet>
              <SubmitOrderDialog
                taskId={task.id}
                reviewRejectReason={task.reviewRejectReason}
              >
                <Button
                  size='sm'
                  variant='outline'
                  className='flex-1 text-blue-600 hover:text-blue-700'
                >
                  <Upload className='h-4 w-4 mr-1' />
                  {t('actions.submitOrder')}
                </Button>
              </SubmitOrderDialog>
              <AbandonTaskDialog
                taskId={task.id}
                deposit={calculateDeposit()}
                onConfirm={() => onAbandonTask?.(task.id)}
              >
                <Button
                  size='sm'
                  variant='outline'
                  className='flex-1 text-red-600 hover:text-red-700'
                >
                  <XCircle className='h-4 w-4 mr-1' />
                  {t('actions.abandonTask')}
                </Button>
              </AbandonTaskDialog>
            </div>
          ) : task.status === 'PENDING_LOGISTICS' ? (
            // 等待物流单号委托显示三个按钮：查看详情、查看订单号、提交物流单号
            <div className='flex gap-2'>
              <AcceptedTaskDetailSheet task={task as any}>
                <Button size='sm' variant='outline' className='flex-1'>
                  <Eye className='h-4 w-4 mr-1' />
                  {t('actions.viewDetails')}
                </Button>
              </AcceptedTaskDetailSheet>
              <ViewOrderDialog
                orderNumber={task.orderNumber}
                orderScreenshot={task.orderScreenshot}
                submittedAt={task.updatedAt}
              >
                <Button
                  size='sm'
                  variant='outline'
                  className='flex-1 text-green-600 hover:text-green-700'
                >
                  <FileText className='h-4 w-4 mr-1' />
                  {t('actions.viewOrderNumber')}
                </Button>
              </ViewOrderDialog>
              <SubmitLogisticsDialog taskId={task.id}>
                <Button
                  size='sm'
                  variant='outline'
                  className='flex-1 text-purple-600 hover:text-purple-700'
                >
                  <Upload className='h-4 w-4 mr-1' />
                  {t('actions.submitLogistics')}
                </Button>
              </SubmitLogisticsDialog>
            </div>
          ) : task.status === 'PENDING_REVIEW' ? (
            // 等待审核委托显示查看详情和查看审核状态
            <div className='flex gap-2'>
              <AcceptedTaskDetailSheet task={task as any}>
                <Button size='sm' variant='outline' className='flex-1'>
                  <Eye className='h-4 w-4 mr-1' />
                  {t('actions.viewDetails')}
                </Button>
              </AcceptedTaskDetailSheet>
              <ViewReviewStatusDialog
                taskId={task.id}
                taskStatus={task.status}
                orderNumber={task.orderNumber}
                orderScreenshot={task.orderScreenshot}
                trackingNumber={task.trackingNumber}
                logisticsScreenshots={task.logisticsScreenshots}
                reviewRejectReason={task.reviewRejectReason}
                reviewedAt={task.reviewedAt}
                logisticsReviewDeadline={task.logisticsReviewDeadline}
                onResubmit={() => {
                  // 重新提交逻辑，可以跳转到提交物流页面或打开对话框
                  window.location.reload();
                }}
              >
                <Button
                  size='sm'
                  className='flex-1 bg-indigo-600 hover:bg-indigo-700'
                >
                  <AlertCircle className='h-4 w-4 mr-1' />
                  {t('actions.viewReviewStatus')}
                </Button>
              </ViewReviewStatusDialog>
            </div>
          ) : task.status === 'PENDING_DELIVERY' ? (
            // 等待确认收货委托显示三个按钮：查看详情、查看订单、查看物流状态
            <div className='flex gap-2'>
              <AcceptedTaskDetailSheet task={task as any}>
                <Button size='sm' variant='outline' className='flex-1'>
                  <Eye className='h-4 w-4 mr-1' />
                  {t('actions.viewDetails')}
                </Button>
              </AcceptedTaskDetailSheet>
              <PublishedViewOrderDialog
                taskId={task.id}
                orderNumber={task.orderNumber}
                orderScreenshot={task.orderScreenshot}
                trackingNumber={task.trackingNumber}
                logisticsScreenshots={task.logisticsScreenshots}
                orderReviewDeadline={task.orderReviewDeadline}
                logisticsReviewDeadline={task.logisticsReviewDeadline}
                taskStatus={task.status}
              >
                <Button
                  size='sm'
                  variant='outline'
                  className='flex-1 text-green-600 hover:text-green-700'
                >
                  <FileText className='h-4 w-4 mr-1' />
                  {t('actions.viewOrder')}
                </Button>
              </PublishedViewOrderDialog>
              <ViewReviewStatusDialog
                taskId={task.id}
                taskStatus={task.status}
                orderNumber={task.orderNumber}
                orderScreenshot={task.orderScreenshot}
                trackingNumber={task.trackingNumber}
                logisticsScreenshots={task.logisticsScreenshots}
                reviewRejectReason={task.reviewRejectReason}
                reviewedAt={task.reviewedAt}
                logisticsReviewDeadline={task.logisticsReviewDeadline}
              >
                <Button
                  size='sm'
                  className='flex-1 bg-cyan-600 hover:bg-cyan-700'
                >
                  <CheckCircle className='h-4 w-4 mr-1' />
                  {t('actions.viewLogistics')}
                </Button>
              </ViewReviewStatusDialog>
            </div>
          ) : (
            // 其他状态委托只显示查看详情
            <AcceptedTaskDetailSheet task={task as any}>
              <Button size='sm' variant='outline' className='w-full'>
                <Eye className='h-4 w-4 mr-1' />
                {t('actions.viewDetails')}
              </Button>
            </AcceptedTaskDetailSheet>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// 响应式接受任务筛选标签组件
interface ResponsiveAcceptedTaskTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  stats: {
    total: number;
    inProgress: number;
    pendingLogistics: number;
    pendingReview: number;
    pendingDelivery: number;
    completed: number;
    expired: number;
  };
  t: (key: string) => string;
}

function ResponsiveAcceptedTaskTabs({
  activeTab,
  onTabChange,
  stats,
  t,
}: ResponsiveAcceptedTaskTabsProps) {
  const { isMobile, isTablet, breakpoint } = useResponsive();

  const tabItems = [
    {
      value: 'all',
      label: t('tabs.all'),
      shortLabel: 'All',
      count: stats.total,
    },
    {
      value: 'in_progress',
      label: t('tabs.inProgress'),
      shortLabel: 'Active',
      count: stats.inProgress,
    },
    {
      value: 'pending_logistics',
      label: t('tabs.pendingLogistics'),
      shortLabel: 'Logistics',
      count: stats.pendingLogistics,
    },
    {
      value: 'pending_review',
      label: t('tabs.pendingReview'),
      shortLabel: 'Review',
      count: stats.pendingReview,
    },
    {
      value: 'pending_delivery',
      label: t('tabs.pendingDelivery'),
      shortLabel: 'Delivery',
      count: stats.pendingDelivery,
    },
    {
      value: 'completed',
      label: t('tabs.completed'),
      shortLabel: 'Done',
      count: stats.completed,
    },
    {
      value: 'expired',
      label: t('tabs.expired'),
      shortLabel: 'Expired',
      count: stats.expired,
    },
  ];

  // 移动端：使用水平滚动
  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block sm:hidden'>{item.label}</span>
                <span className='hidden sm:block'>
                  {item.label} ({item.count})
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  // 平板端：使用2行布局
  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.slice(0, 6).map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label} ({item.count})
            </button>
          ))}
        </div>
        {/* 第二行显示剩余的标签 */}
        {tabItems.length > 6 && (
          <div className='grid grid-cols-3 gap-2 mt-2'>
            {tabItems.slice(6).map(item => (
              <button
                key={item.value}
                type='button'
                onClick={() => onTabChange(item.value)}
                className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                  activeTab === item.value
                    ? 'bg-primary text-primary-foreground border-primary'
                    : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
                }`}
              >
                {item.label} ({item.count})
              </button>
            ))}
          </div>
        )}
      </Tabs>
    );
  }

  // 桌面端：保持原有的7列网格布局
  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className='grid w-full grid-cols-7'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label} ({item.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

export function MyAcceptedTasksContent() {
  const t = useTranslations('my-accepted-tasks');

  const [activeTab, setActiveTab] = useState('all');

  // 获取接受的委托数据
  const {
    data: taskData,
    isLoading,
    error,
  } = useAcceptedTasks({
    status: activeTab === 'all' ? undefined : activeTab,
  });

  const stats = taskData?.data?.stats || {
    total: 0,
    inProgress: 0,
    pendingLogistics: 0,
    pendingReview: 0,
    pendingDelivery: 0,
    completed: 0,
    expired: 0,
    cancelled: 0,
  };

  // 处理放弃委托
  const handleAbandonTask = (taskId: string) => {
    // 功能已集成到 AbandonTaskDialog 中，这里可以作为额外的回调处理
  };

  const filteredTasks = useMemo(() => {
    const tasks = taskData?.data?.tasks || [];
    switch (activeTab) {
      case 'in_progress':
        return tasks.filter(task => task.status === 'IN_PROGRESS');
      case 'pending_logistics':
        return tasks.filter(task => task.status === 'PENDING_LOGISTICS');
      case 'pending_review':
        return tasks.filter(task => task.status === 'PENDING_REVIEW');
      case 'pending_delivery':
        return tasks.filter(task => task.status === 'PENDING_DELIVERY');
      case 'completed':
        return tasks.filter(task => task.status === 'COMPLETED');
      case 'expired':
        return tasks.filter(task => task.status === 'EXPIRED');
      default:
        return tasks;
    }
  }, [taskData?.data?.tasks, activeTab]);

  if (error) {
    return (
      <div className='flex items-center justify-center py-12'>
        <p className='text-sm text-red-600'>{t('messages.loadFailed')}</p>
      </div>
    );
  }

  return (
    <>
      {/* 页面标题 */}
      <div>
        <h1 className='text-2xl font-bold tracking-tight'>
          {t('navigation.title')}
        </h1>
        <p className='text-gray-600 dark:text-gray-300'>
          {t('navigation.description')}
        </p>
      </div>

      {/* 统计卡片 */}
      <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold'>{stats.total}</div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.all')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {stats.inProgress}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.inProgress')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {stats.pendingLogistics}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.pendingLogistics')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-indigo-600'>
                {stats.pendingReview || 0}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.pendingReview')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-cyan-600'>
                {stats.pendingDelivery || 0}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.pendingDelivery')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                {stats.completed}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.completed')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-red-600'>
                {stats.expired}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.expired')}
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-gray-600'>
                {stats.cancelled}
              </div>
              <div className='text-xs text-gray-600 dark:text-gray-300'>
                {t('tabs.cancelled')}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 委托筛选标签 - 响应式设计 */}
      <ResponsiveAcceptedTaskTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        stats={stats}
        t={t}
      />

      {/* 标签内容 */}
      <div className='mt-6'>
        {isLoading ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Loader2 className='h-8 w-8 animate-spin mb-4' />
              <p className='text-gray-600 dark:text-gray-300'>
                {t('loading.tasks')}
              </p>
            </CardContent>
          </Card>
        ) : filteredTasks.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <div className='w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4'>
                <CheckCircle className='h-8 w-8 text-gray-600 dark:text-gray-300' />
              </div>
              <h3 className='text-lg font-medium mb-2'>{t('empty.title')}</h3>
              <p className='text-gray-600 dark:text-gray-300 text-center'>
                {activeTab === 'all' ? t('empty.all') : t('empty.filtered')}
              </p>
            </CardContent>
          </Card>
        ) : (
          <>
            <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
              {filteredTasks.map(task => (
                <MyTaskCard
                  key={task.id}
                  task={task}
                  onAbandonTask={handleAbandonTask}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </>
  );
}
