/**
 * Email Formatting Utilities
 * Provides locale-specific formatting for currencies, dates, and numbers in emails
 */

import type { SupportedLanguage } from './email-translations';

/**
 * Format currency amount for email display
 */
export function formatEmailAmount(
  amount: number,
  currency: string = 'USD',
  language: SupportedLanguage = 'zh',
): string {
  try {
    const locale = language === 'zh' ? 'zh-CN' : 'en-US';

    // Special handling for common currencies
    const currencySymbols: Record<string, string> = {
      USD: '$',
      CNY: '¥',
      EUR: '€',
      GBP: '£',
    };

    const symbol = currencySymbols[currency] || currency;

    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    // Try to use Intl.NumberFormat first
    try {
      return formatter.format(amount);
    } catch {
      // Fallback to manual formatting if currency is not supported
      const formattedNumber = new Intl.NumberFormat(locale, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);

      return language === 'zh'
        ? `${symbol}${formattedNumber}`
        : `${symbol}${formattedNumber}`;
    }
  } catch (error) {
    // Ultimate fallback
    return `${currency} ${amount.toFixed(2)}`;
  }
}

/**
 * Format date and time for email display
 */
export function formatEmailDateTime(
  date: Date | string,
  language: SupportedLanguage = 'zh',
  options: {
    includeTime?: boolean;
    includeSeconds?: boolean;
    timeZone?: string;
  } = {},
): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const locale = language === 'zh' ? 'zh-CN' : 'en-US';

    const formatOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: language === 'zh' ? 'long' : 'short',
      day: 'numeric',
      timeZone: options.timeZone || 'Asia/Shanghai',
    };

    if (options.includeTime) {
      formatOptions.hour = '2-digit';
      formatOptions.minute = '2-digit';
      formatOptions.hour12 = language === 'en';

      if (options.includeSeconds) {
        formatOptions.second = '2-digit';
      }
    }

    return new Intl.DateTimeFormat(locale, formatOptions).format(dateObj);
  } catch (error) {
    // Fallback formatting
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString(language === 'zh' ? 'zh-CN' : 'en-US');
  }
}

/**
 * Format numbers with proper separators
 */
export function formatEmailNumber(
  number: number,
  language: SupportedLanguage = 'zh',
  options: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {},
): string {
  try {
    const locale = language === 'zh' ? 'zh-CN' : 'en-US';

    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: options.minimumFractionDigits || 0,
      maximumFractionDigits: options.maximumFractionDigits || 2,
    }).format(number);
  } catch (error) {
    return number.toString();
  }
}

/**
 * Format transaction ID with masking for security
 */
export function formatTransactionId(
  transactionId: string,
  maskLength: number = 4,
): string {
  if (transactionId.length <= maskLength * 2) {
    return transactionId;
  }

  const start = transactionId.substring(0, maskLength);
  const end = transactionId.substring(transactionId.length - maskLength);
  const middle = '*'.repeat(Math.min(8, transactionId.length - maskLength * 2));

  return `${start}${middle}${end}`;
}

/**
 * Format duration in minutes to human-readable format
 */
export function formatEmailDuration(
  minutes: number,
  language: SupportedLanguage = 'zh',
): string {
  const translations = {
    zh: {
      minutes: '分钟',
      hours: '小时',
      days: '天',
    },
    en: {
      minutes: 'minutes',
      hours: 'hours',
      days: 'days',
    },
  };

  const t = translations[language];

  if (minutes < 60) {
    return `${minutes} ${t.minutes}`;
  } else if (minutes < 1440) { // Less than 24 hours
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours} ${t.hours}`;
    } else {
      return `${hours} ${t.hours} ${remainingMinutes} ${t.minutes}`;
    }
  } else {
    const days = Math.floor(minutes / 1440);
    const remainingHours = Math.floor((minutes % 1440) / 60);

    if (remainingHours === 0) {
      return `${days} ${t.days}`;
    } else {
      return `${days} ${t.days} ${remainingHours} ${t.hours}`;
    }
  }
}

/**
 * Format email address for display (with optional masking)
 */
export function formatEmailAddress(
  email: string,
  maskDomain: boolean = false,
): string {
  if (!maskDomain) {
    return email;
  }

  const [localPart, domain] = email.split('@');
  if (!domain) {
    return email;
  }

  const maskedLocal = localPart.length > 2
    ? `${localPart[0]}***${localPart[localPart.length - 1]}`
    : localPart;

  return `${maskedLocal}@${domain}`;
}

/**
 * Format percentage for display
 */
export function formatEmailPercentage(
  value: number,
  language: SupportedLanguage = 'zh',
  decimals: number = 1,
): string {
  try {
    const locale = language === 'zh' ? 'zh-CN' : 'en-US';

    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value / 100);
  } catch (error) {
    return `${value.toFixed(decimals)}%`;
  }
}

/**
 * Format file size for display
 */
export function formatEmailFileSize(
  bytes: number,
  language: SupportedLanguage = 'zh',
): string {
  const units = language === 'zh'
    ? ['字节', 'KB', 'MB', 'GB', 'TB']
    : ['bytes', 'KB', 'MB', 'GB', 'TB'];

  if (bytes === 0) return `0 ${units[0]}`;

  const k = 1024;
  const dm = 2;
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${units[i]}`;
}

/**
 * Utility function to get locale string for language
 */
export function getEmailLocale(language: SupportedLanguage): string {
  return language === 'zh' ? 'zh-CN' : 'en-US';
}

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 */
export function formatEmailRelativeTime(
  date: Date | string,
  language: SupportedLanguage = 'zh',
): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffMs = dateObj.getTime() - now.getTime();
    const diffMinutes = Math.round(diffMs / (1000 * 60));

    const rtf = new Intl.RelativeTimeFormat(getEmailLocale(language), {
      numeric: 'auto',
    });

    if (Math.abs(diffMinutes) < 60) {
      return rtf.format(diffMinutes, 'minute');
    } else if (Math.abs(diffMinutes) < 1440) {
      return rtf.format(Math.round(diffMinutes / 60), 'hour');
    } else {
      return rtf.format(Math.round(diffMinutes / 1440), 'day');
    }
  } catch (error) {
    // Fallback to absolute time
    return formatEmailDateTime(date, language, { includeTime: true });
  }
}
