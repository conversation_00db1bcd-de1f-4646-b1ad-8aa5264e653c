/* DHgate 主题样式 - 白色背景主题 (已整合到 homepage.css) */
/* 此文件中的CSS变量定义已移动到 homepage.css 以避免重复 */

/* 简洁的背景网格 - 白色背景适配 */
.homepage-theme .tech-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.03; /* 微妙的灰色网格纹理 */
  background-image:
    linear-gradient(rgba(148, 163, 184, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
  background-size: 60px 60px;
  z-index: -1;
  /* 静态设计，无动画 */
}

/* 移除网格动画，保持静态设计 */

/* 简洁的品牌背景元素 - 白色背景适配 */
.homepage-theme .brand-bg {
  position: fixed;
  top: 30%;
  right: -5%;
  width: 400px;
  height: 400px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 51, 234, 0.03) 50%,
    transparent 70%
  );
  border-radius: 50%;
  z-index: -1;
  /* 静态设计，无动画 */
}

/* 移除浮动动画，保持静态设计 */

/* 白色背景的卡片效果 */
.homepage-theme .glass-linear {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(8px);
  /* 增强阴影效果以在白色背景上提供层次感 */
}

/* 白色背景的悬浮效果 */
.homepage-theme .hover-lift {
  transition:
    box-shadow 0.2s ease,
    transform 0.2s ease;
}

.homepage-theme .hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 移除粒子效果 - 保持简洁设计 */

/* 优化渐变文字效果 - 修复文字底部被遮挡问题 */
.homepage-theme .gradient-text-tech {
  background: linear-gradient(
    135deg,
    hsl(var(--tech-gradient-1)) 0%,
    hsl(var(--tech-gradient-2)) 50%,
    hsl(var(--tech-gradient-3)) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 修复文字底部被遮挡问题 */
  line-height: 1.3 !important;
  padding-bottom: 0.15em !important;
  /* 确保下降字符有足够空间 */
  overflow: visible !important;
  /* 防止文字被裁剪 */
  display: inline-block;
}

/* 白色背景的自定义滚动条 */
.homepage-theme ::-webkit-scrollbar {
  width: 6px;
}

.homepage-theme ::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
}

.homepage-theme ::-webkit-scrollbar-thumb {
  background: linear-gradient(
    180deg,
    hsl(var(--primary)) 0%,
    hsl(var(--tech-gradient-2)) 100%
  );
  border-radius: 3px;
}

.homepage-theme ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    180deg,
    hsl(var(--tech-gradient-1)) 0%,
    hsl(var(--tech-gradient-3)) 100%
  );
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  .homepage-theme .text-responsive-xl {
    font-size: 1.5rem;
  }
  .homepage-theme .gradient-text-tech {
    font-size: clamp(1.2rem, 3.5vw, 2rem) !important;
    white-space: nowrap !important;
  }
}

@media (min-width: 641px) {
  .homepage-theme .text-responsive-xl {
    font-size: 2rem;
  }
}

@media (min-width: 1024px) {
  .homepage-theme .text-responsive-xl {
    font-size: 2.5rem;
  }
}

@media (max-width: 1200px) {
  .homepage-theme .gradient-text-tech {
    font-size: clamp(2rem, 5vw, 4rem) !important;
  }
}

@media (max-width: 768px) {
  .homepage-theme .gradient-text-tech {
    font-size: clamp(1.5rem, 4vw, 3rem) !important;
    line-height: 1.2 !important;
    padding-bottom: 0.1em !important;
  }
}

/* 确保标题在一行显示的响应式样式 */
.homepage-theme .hero-title-single-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
