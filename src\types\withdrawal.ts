import { z } from 'zod';

// 提现状态枚举
export enum WithdrawalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
}

// 提现方式枚举
export enum WithdrawMethod {
  ALIPAY = 'ALIPAY',
  WECHAT = 'WECHAT',
  BANK_CARD = 'BANK_CARD',
  USDT_ERC20 = 'USDT_ERC20',
  USDT_TRC20 = 'USDT_TRC20',
}

// 用户信息接口
export interface WithdrawalUser {
  id: string;
  name: string | null;
  email: string | null;
  username: string | null;
}

// 审核人信息接口
export interface WithdrawalReviewer {
  id: string;
  name: string | null;
  email: string | null;
}

// 提现申请接口
export interface WithdrawalRequest {
  id: string;
  userId: string;
  amount: number;
  fee: number;
  actualAmount: number;
  currency: string;
  withdrawMethod: WithdrawMethod;
  reference: string | null; // 关联的交易记录ID

  // 美元提现字段
  accountHolder: string | null;
  bankCard: string | null;
  bankName: string | null;
  bankSwift: string | null; // 新增
  branchAddress: string | null; // 新增
  bankCountry: string | null; // 新增

  // 加密货币提现字段
  walletAddress: string | null;
  cryptoCurrency: string | null;
  blockchain: string | null;

  status: WithdrawalStatus;
  rejectionReason: string | null;
  reviewedBy: string | null;
  reviewedAt: string | null;
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;

  user: WithdrawalUser;
  reviewer: WithdrawalReviewer | null;
}

// 创建提现申请的验证模式
// 重命名为 createWithdrawalSchema 避免重复声明
export const createWithdrawalSchema = z
  .object({
    amount: z.number().min(0.01, '提现金额必须大于0'),
    withdrawMethod: z.nativeEnum(WithdrawMethod),

    // 美元提现字段
    accountHolder: z.string().optional(),
    bankCard: z.string().optional(),
    bankName: z.string().optional(),
    bankSwift: z.string().optional(), // 新增
    branchAddress: z.string().optional(), // 新增
    bankCountry: z.string().optional(), // 新增

    // 加密货币提现字段
    walletAddress: z.string().optional(),
    cryptoCurrency: z.string().optional(),
    blockchain: z.string().optional(),
  })
  .refine(
    data => {
      // 美元提现时必须提供完整的银行信息
      if (['BANK_CARD', 'ALIPAY', 'WECHAT'].includes(data.withdrawMethod)) {
        return (
          data.accountHolder &&
          data.bankCard &&
          data.bankName &&
          data.bankSwift &&
          data.branchAddress &&
          data.bankCountry
        );
      }

      // 加密货币提现时必须提供钱包地址
      if (['USDT_ERC20', 'USDT_TRC20'].includes(data.withdrawMethod)) {
        return data.walletAddress && data.cryptoCurrency && data.blockchain;
      }

      return true;
    },
    {
      message: '请完善提现信息',
      path: ['withdrawMethod'],
    },
  );

// 提现申请列表响应接口
export interface WithdrawalRequestsResponse {
  withdrawalRequests: WithdrawalRequest[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  stats: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    completed: number;
    bankCard: number;
    crypto: number;
  };
}

// 提现申请查询参数
export interface WithdrawalRequestsParams {
  search?: string;
  withdrawMethod?: WithdrawMethod;
  status?: WithdrawalStatus;
  page?: number;
  pageSize?: number;
}

// 更新提现申请状态的请求
export interface UpdateWithdrawalStatusRequest {
  status: 'APPROVED' | 'REJECTED';
  rejectionReason?: string;
}

// API 响应接口
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
}

// Zod 验证模式
export const updateWithdrawalStatusSchema = z.object({
  status: z.enum(['APPROVED', 'REJECTED']),
  rejectionReason: z.string().optional(),
});

// 创建提现申请的验证模式
export const createWithdrawalRequestSchema = z
  .object({
    amount: z.number().min(0.01, '提现金额必须大于0'),
    withdrawMethod: z.nativeEnum(WithdrawMethod),

    // 美元提现字段
    accountHolder: z.string().optional(),
    bankCard: z.string().optional(),
    bankName: z.string().optional(),
    bankSwift: z.string().optional(), // 新增
    branchAddress: z.string().optional(), // 新增
    bankCountry: z.string().optional(), // 新增

    // 加密货币提现字段
    walletAddress: z.string().optional(),
    cryptoCurrency: z.string().optional(),
    blockchain: z.string().optional(),
  })
  .refine(
    data => {
      // 美元提现时必须提供美元账户信息
      if (['BANK_CARD', 'ALIPAY', 'WECHAT'].includes(data.withdrawMethod)) {
        return data.accountHolder && data.bankCard && data.bankName;
      }

      // 加密货币提现时必须提供钱包地址
      if (['USDT_ERC20', 'USDT_TRC20'].includes(data.withdrawMethod)) {
        return data.walletAddress && data.cryptoCurrency && data.blockchain;
      }

      return true;
    },
    {
      message: '请完善提现信息',
      path: ['withdrawMethod'],
    },
  );

// 提现申请创建类型
export type CreateWithdrawalRequest = z.infer<
  typeof createWithdrawalRequestSchema
>;

// 提现申请统计信息
export interface WithdrawalStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  completed: number;
  bankCard: number;
  crypto: number;
}

// 提现方式配置
export interface WithdrawMethodConfig {
  key: WithdrawMethod;
  label: string;
  icon: string;
  description: string;
  isBank: boolean;
  isCrypto: boolean;
}

// 提现方式配置数组
export const withdrawMethodConfigs: WithdrawMethodConfig[] = [
  {
    key: WithdrawMethod.BANK_CARD,
    label: '美元账户',
    icon: 'CreditCard',
    description: '提现到美元账户',
    isBank: true,
    isCrypto: false,
  },
  {
    key: WithdrawMethod.ALIPAY,
    label: 'Alipay',
    icon: 'Smartphone',
    description: '提现到Alipay',
    isBank: true,
    isCrypto: false,
  },
  {
    key: WithdrawMethod.WECHAT,
    label: 'WeChat Pay',
    icon: 'MessageCircle',
    description: '提现到WeChat Pay',
    isBank: true,
    isCrypto: false,
  },
  {
    key: WithdrawMethod.USDT_ERC20,
    label: 'USDT (ERC20)',
    icon: 'Wallet',
    description: '提现到 USDT ERC20 钱包',
    isBank: false,
    isCrypto: true,
  },
  {
    key: WithdrawMethod.USDT_TRC20,
    label: 'USDT (TRC20)',
    icon: 'Wallet',
    description: '提现到 USDT TRC20 钱包',
    isBank: false,
    isCrypto: true,
  },
];

// 提现状态配置
export interface WithdrawalStatusConfig {
  key: WithdrawalStatus;
  label: string;
  color: string;
  description: string;
}

// 提现状态配置数组
export const withdrawalStatusConfigs: WithdrawalStatusConfig[] = [
  {
    key: WithdrawalStatus.PENDING,
    label: '待审核',
    color: 'orange',
    description: '等待管理员审核',
  },
  {
    key: WithdrawalStatus.APPROVED,
    label: '已通过',
    color: 'green',
    description: '审核通过，等待转账',
  },
  {
    key: WithdrawalStatus.REJECTED,
    label: '已拒绝',
    color: 'red',
    description: '审核被拒绝',
  },
  {
    key: WithdrawalStatus.COMPLETED,
    label: '已完成',
    color: 'blue',
    description: '转账完成',
  },
];
