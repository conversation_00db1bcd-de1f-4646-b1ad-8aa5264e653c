export interface TaskAcceptedPublisherEmailData {
  publisherName: string;
  publisherEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  acceptedAt: string;
  accepterName: string;
  accepterEmail: string;
  orderDeadline: string;
}

export const taskAcceptedPublisherTemplate = (
  data: TaskAcceptedPublisherEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>委托被接受通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #28a745; margin-bottom: 20px; text-align: center;">🎉 委托已被接受</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.publisherName}！您发布的委托已被接受。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">接受时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.acceptedAt}</span>
        </div>
      </div>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">接单者信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">用户名：</span>
          <span style="color: #333; margin-left: 10px;">${data.accepterName}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">邮箱：</span>
          <span style="color: #333; margin-left: 10px;">${data.accepterEmail}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">订单提交截止时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.orderDeadline}</span>
        </div>
      </div>
      
      <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #155724; margin: 0; font-size: 14px;">
          ✅ <strong>委托进行中：</strong><br>
          • 接单者已接受委托，委托正式开始<br>
          • 接单者需要在24小时内提交订单信息<br>
          • 随后会提交物流单号供您审核<br>
          • 请保持关注委托进度<br>
          • 如有问题，可以直接联系接单者
        </p>
      </div>
      
      <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
          ⚠️ <strong>温馨提醒：</strong><br>
          • 请确保收货地址和联系方式正确<br>
          • 收到商品后请及时确认收货<br>
          • 如发现异常情况，请及时联系客服<br>
          • 委托完成后，系统会自动处理酬金结算
        </p>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
