'use client';

import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  FileImage,
  FileText,
  Upload,
  CheckCircle,
  XCircle,
  Eye,
  AlertTriangle,
  Clock,
  Info,
  ExternalLink,
  Copy,
} from 'lucide-react';
// import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { EvidenceFileViewer } from '@/components/evidence-file-viewer';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { convertFileUrlsToApiUrls } from '@/lib/file-utils';

interface AdminViewEvidenceSheetProps {
  task: any;
  children: React.ReactNode;
}

export function AdminViewEvidenceSheet({
  task,
  children,
}: AdminViewEvidenceSheetProps) {
  const [isOpen, setIsOpen] = useState(false);
  // 管理员面板固定文本（不使用国际化）
  const t = (key: string) => {
    const texts: Record<string, string> = {
      'evidence.status.pendingSubmission': '待提交',
      'evidence.status.underReview': '审核中',
      'evidence.status.reviewed': '已审核',
      'evidence.status.rejected': '已拒绝',
      'evidence.status.noEvidence': '无证据',
      'evidence.status.unknown': '未知状态',
      'evidence.viewEvidence': '查看证据',
      'evidence.viewEvidenceDescription': '查看任务证据和相关文件',
      'evidence.basicInfo': '基础信息',
      'evidence.taskId': '任务ID',
      'evidence.platform': '平台',
      'evidence.productUrl': '商品链接',
      'evidence.evidenceStatus': '证据状态',
      'evidence.uploadType': '上传类型',
      'evidence.uploadTypes.immediate': '立即上传',
      'evidence.uploadTypes.delayed': '延迟上传',
      'evidence.uploadTypes.none': '无需上传',
      'evidence.submissionInfo': '提交信息',
      'evidence.submitter': '提交者',
      'evidence.submitTime': '提交时间',
      'evidence.reviewTime': '审核时间',
      'evidence.reviewer': '审核者',
      'evidence.rejectReason': '拒绝原因',
      'evidence.evidenceFiles': '证据文件',
      'evidence.noFiles': '暂无证据文件',
      'evidence.fileInfo': '文件信息',
      'evidence.fileName': '文件名',
      'evidence.fileSize': '文件大小',
      'evidence.uploadTime': '上传时间',
      'evidence.preview': '预览',
      'evidence.download': '下载',
      'evidence.viewOriginal': '查看原图',
      'evidence.copyLink': '复制链接',
      'messages.linkCopied': '链接已复制',
      'messages.copyFailed': '复制失败',
    };
    return texts[key] || key;
  };

  // 获取证据状态显示
  const getEvidenceStatusDisplay = (evidenceStatus: string) => {
    switch (evidenceStatus) {
      case 'PENDING_SUBMISSION':
        return {
          color: 'bg-yellow-100 text-yellow-800',
          icon: <Upload className='h-4 w-4' />,
          label: t('evidence.status.pendingSubmission'),
        };
      case 'UNDER_REVIEW':
        return {
          color: 'bg-blue-100 text-blue-800',
          icon: <Eye className='h-4 w-4' />,
          label: t('evidence.status.underReview'),
        };
      case 'REVIEWED':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className='h-4 w-4' />,
          label: t('evidence.status.reviewed'),
        };
      case 'REJECTED':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <XCircle className='h-4 w-4' />,
          label: t('evidence.status.rejected'),
        };
      case 'NO_EVIDENCE':
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <FileText className='h-4 w-4' />,
          label: t('evidence.status.noEvidence'),
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <AlertTriangle className='h-4 w-4' />,
          label: t('evidence.status.unknown'),
        };
    }
  };

  // 复制链接到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(t('messages.linkCopied'));
    } catch (error) {
      toast.error(t('messages.copyFailed'));
    }
  };

  // 打开链接
  const openLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const evidenceStatusDisplay = getEvidenceStatusDisplay(task.evidenceStatus);

  // 处理证据文件URL
  const evidenceFileUrls =
    task.evidenceFiles
      ?.map((file: any) => {
        if (typeof file === 'string') {
          return file;
        }
        return file.fileUrl || file.url;
      })
      .filter(Boolean) || [];

  const processedEvidenceFiles = convertFileUrlsToApiUrls(evidenceFileUrls);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='w-[600px] sm:w-[800px] overflow-y-auto'>
        <SheetHeader>
          <SheetTitle className='flex items-center gap-2'>
            <FileImage className='h-5 w-5' />
            {t('evidence.viewEvidence')}
          </SheetTitle>
          <SheetDescription>
            {t('evidence.viewEvidenceDescription')}
          </SheetDescription>
        </SheetHeader>

        <div className='mt-6 space-y-6'>
          {/* 基本信息 */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold flex items-center gap-2'>
              <Info className='h-5 w-5' />
              {t('evidence.basicInfo')}
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <label className='text-sm font-medium text-muted-foreground'>
                  {t('evidence.taskId')}
                </label>
                <p className='font-mono text-sm'>{task.id}</p>
              </div>
              <div className='space-y-2'>
                <label className='text-sm font-medium text-muted-foreground'>
                  {t('evidence.platform')}
                </label>
                <Badge variant='outline'>{task.platform}</Badge>
              </div>
            </div>

            {/* 商品链接 */}
            {task.productUrl && (
              <div className='space-y-2'>
                <label className='text-sm font-medium text-muted-foreground'>
                  {t('evidence.productUrl')}
                </label>
                <div className='flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border'>
                  <div className='flex-1 min-w-0'>
                    <p
                      className='text-sm font-mono text-gray-700 dark:text-gray-300 truncate'
                      title={task.productUrl}
                    >
                      {task.productUrl}
                    </p>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => copyToClipboard(task.productUrl)}
                      className='h-8 w-8 p-0'
                    >
                      <Copy className='h-4 w-4' />
                    </Button>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => openLink(task.productUrl)}
                      className='h-8 w-8 p-0'
                    >
                      <ExternalLink className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* 证据状态 */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold flex items-center gap-2'>
              <FileImage className='h-5 w-5' />
              {t('evidence.evidenceStatus')}
            </h3>
            <div className='flex items-center gap-4'>
              <Badge
                className={`${evidenceStatusDisplay.color} flex items-center gap-1 w-fit`}
              >
                {evidenceStatusDisplay.icon}
                {evidenceStatusDisplay.label}
              </Badge>
              <span className='text-sm text-muted-foreground'>
                {t('evidence.uploadType')}:{' '}
                {task.evidenceUploadType === 'IMMEDIATE'
                  ? t('evidence.uploadTypes.immediate')
                  : task.evidenceUploadType === 'DELAYED'
                    ? t('evidence.uploadTypes.delayed')
                    : task.evidenceUploadType === 'NONE'
                      ? t('evidence.uploadTypes.none')
                      : t('evidence.uploadTypes.unknown')}
              </span>
            </div>

            {/* 拒绝理由显示 */}
            {task.evidenceStatus === 'REJECTED' &&
              task.evidenceRejectReason && (
                <div className='p-3 bg-red-50 border border-red-200 rounded-lg'>
                  <div className='flex items-start gap-2'>
                    <AlertTriangle className='h-4 w-4 text-red-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <div className='text-sm font-medium text-red-800'>
                        {t('evidence.rejectReason')}
                      </div>
                      <div className='text-sm text-red-700 mt-1'>
                        {task.evidenceRejectReason}
                      </div>
                    </div>
                  </div>
                </div>
              )}

            {/* 上传时间 */}
            {task.evidenceUploadedAt && (
              <div className='space-y-2'>
                <label className='text-sm font-medium text-muted-foreground'>
                  {t('evidence.uploadTime')}
                </label>
                <p className='text-sm'>
                  {format(
                    new Date(task.evidenceUploadedAt),
                    'yyyy-MM-dd HH:mm:ss',
                    { locale: zhCN },
                  )}
                </p>
              </div>
            )}
          </div>

          <Separator />

          {/* 拒付类型 */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold'>
              {t('evidence.chargebackTypes')}
            </h3>
            <div className='flex flex-wrap gap-2'>
              {task.chargebackTypes?.map((type: string, index: number) => (
                <Badge key={index} className='bg-orange-100 text-orange-800'>
                  {type}
                </Badge>
              ))}
            </div>
          </div>

          <Separator />

          {/* 证据文件列表 */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              {t('evidence.evidenceFiles')}
              {processedEvidenceFiles.length > 0 && (
                <Badge variant='secondary' className='ml-2'>
                  {processedEvidenceFiles.length} {t('evidence.filesCount')}
                </Badge>
              )}
            </h3>

            {processedEvidenceFiles.length > 0 ? (
              <EvidenceFileViewer files={processedEvidenceFiles} />
            ) : (
              <div className='text-center py-8'>
                <Upload className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                <p className='text-muted-foreground'>
                  {task.evidenceUploadType === 'NONE'
                    ? t('evidence.noEvidenceForTask')
                    : t('evidence.noEvidenceFiles')}
                </p>
                {task.evidenceUploadType !== 'NONE' && (
                  <p className='text-sm text-muted-foreground mt-1'>
                    {t('evidence.publisherNotUploaded')}
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
