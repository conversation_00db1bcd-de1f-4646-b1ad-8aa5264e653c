import { NextRequest, NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';
import {
  ticketQuerySchema,
  type TicketListResponse,
  type TicketStatsResponse,
} from '@/lib/types/ticket';

// 强制动态渲染
export const dynamic = 'force-dynamic';

// 获取工单统计
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    await requireAdmin();

    const { searchParams } = new URL(request.url);

    // 如果请求统计数据
    if (searchParams.get('stats') === 'true') {
      const [
        totalTickets,
        pendingTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
        cancelledTickets,
        unassignedTickets,
      ] = await Promise.all([
        prisma.ticket.count(),
        prisma.ticket.count({ where: { status: 'PENDING' } }),
        prisma.ticket.count({ where: { status: 'IN_PROGRESS' } }),
        prisma.ticket.count({ where: { status: 'RESOLVED' } }),
        prisma.ticket.count({ where: { status: 'CLOSED' } }),
        prisma.ticket.count({ where: { status: 'CANCELLED' } }),
        prisma.ticket.count({ where: { assignedTo: null } }),
      ]);

      const stats: TicketStatsResponse = {
        totalTickets,
        pendingTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
        cancelledTickets,
        unassignedTickets,
      };

      return NextResponse.json(stats);
    }

    // 验证查询参数
    const queryValidation = ticketQuerySchema.safeParse({
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      search: searchParams.get('search'),
      type: searchParams.get('type'),
      status: searchParams.get('status'),
      priority: searchParams.get('priority'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder'),
    });

    if (!queryValidation.success) {
      return NextResponse.json(
        { error: '查询参数无效', details: queryValidation.error.errors },
        { status: 400 },
      );
    }

    const { page, limit, search, type, status, priority, sortBy, sortOrder } =
      queryValidation.data;

    // 构建查询条件
    const where: any = {};

    // 搜索筛选
    if (search) {
      where.OR = [
        {
          title: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          user: {
            OR: [
              {
                name: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
              {
                email: {
                  contains: search,
                  mode: 'insensitive',
                },
              },
            ],
          },
        },
      ];
    }

    // 类型筛选
    if (type) {
      where.type = type;
    }

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 优先级筛选
    if (priority) {
      where.priority = priority;
    }

    // 分配状态筛选
    const assigned = searchParams.get('assigned');
    if (assigned === 'true') {
      where.assignedTo = { not: null };
    } else if (assigned === 'false') {
      where.assignedTo = null;
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询工单列表和总数
    const [tickets, total] = await Promise.all([
      prisma.ticket.findMany({
        where,
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          replies: {
            take: 3, // 只取最新的3条回复用于预览
            orderBy: {
              createdAt: 'desc',
            },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          _count: {
            select: {
              replies: true,
            },
          },
        },
      }),
      prisma.ticket.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    const response: TicketListResponse = {
      tickets,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
