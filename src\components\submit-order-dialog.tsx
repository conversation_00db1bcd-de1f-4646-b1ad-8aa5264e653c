'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, CheckCircle, Upload } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { FileUpload } from '@/components/ui/file-upload';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UploadedFile } from '@/hooks/use-file-upload';
import { useSubmitOrder } from '@/hooks/use-submit-order';

// 创建验证模式的函数，接受翻译函数作为参数
const createSubmitOrderSchema = (t: any) =>
  z.object({
    orderNumber: z
      .string()
      .min(1, t('validation.orderNumberRequired'))
      .min(6, t('validation.orderNumberMinLength')),
    orderScreenshot: z
      .array(z.any())
      .min(1, t('validation.orderScreenshotRequired'))
      .max(1, t('validation.orderScreenshotMaxCount')),
  });

type SubmitOrderForm = z.infer<ReturnType<typeof createSubmitOrderSchema>>;

interface SubmitOrderDialogProps {
  children?: React.ReactNode;
  taskId: string;
  reviewRejectReason?: string | null;
  onSubmit?: (data: SubmitOrderForm) => void;
}

export function SubmitOrderDialog({
  children,
  taskId,
  reviewRejectReason,
  onSubmit,
}: SubmitOrderDialogProps) {
  const t = useTranslations('my-accepted-tasks');
  const [isOpen, setIsOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const submitOrderMutation = useSubmitOrder();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<SubmitOrderForm>({
    resolver: zodResolver(createSubmitOrderSchema(t)),
    defaultValues: {
      orderScreenshot: [],
    },
  });

  const handleFileChange = (files: UploadedFile[]) => {
    setUploadedFiles(files);
    setValue('orderScreenshot', files);
  };

  const onFormSubmit = async (data: SubmitOrderForm) => {
    try {
      await submitOrderMutation.mutateAsync({
        taskId,
        orderNumber: data.orderNumber,
        orderScreenshot: uploadedFiles[0]?.fileUrl || undefined,
      });

      // 调用父组件的提交处理函数
      onSubmit?.(data);

      // 重置表单和状态
      reset();
      setUploadedFiles([]);
      setIsOpen(false);
    } catch (error) {
      // 错误处理已在hook中完成
    }
  };

  const handleDialogClose = (open: boolean) => {
    setIsOpen(open);
    if (!open && !submitOrderMutation.isPending) {
      reset();
      setUploadedFiles([]);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Upload className='h-5 w-5' />
            {t('submitOrder.title')}
          </DialogTitle>
          <DialogDescription>{t('submitOrder.description')}</DialogDescription>
        </DialogHeader>

        {/* 驳回理由显示 */}
        {reviewRejectReason && (
          <div className='p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950 dark:border-red-800'>
            <div className='flex items-start gap-2'>
              <AlertCircle className='h-4 w-4 text-red-600 mt-0.5 flex-shrink-0' />
              <div>
                <div className='text-sm font-medium text-red-800 dark:text-red-300'>
                  {t('submitOrder.rejectionReason')}
                </div>
                <div className='text-sm text-red-700 dark:text-red-400 mt-1'>
                  {reviewRejectReason}
                </div>
                <div className='text-xs text-red-600 dark:text-red-500 mt-1'>
                  {t('submitOrder.resubmitHint')}
                </div>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-4'>
          {/* 订单号输入 */}
          <div className='space-y-2'>
            <Label htmlFor='orderNumber'>{t('submitOrder.orderNumber')}</Label>
            <Input
              id='orderNumber'
              placeholder={t('submitOrder.orderNumberPlaceholder')}
              {...register('orderNumber')}
              disabled={submitOrderMutation.isPending}
            />
            {errors.orderNumber && (
              <p className='text-sm text-red-600'>
                {errors.orderNumber.message}
              </p>
            )}
          </div>

          {/* 订单截图上传 */}
          <div className='space-y-2'>
            <Label>{t('submitOrder.orderScreenshot')}</Label>
            <FileUpload
              value={uploadedFiles}
              onChange={handleFileChange}
              accept='image/*'
              multiple={false}
              maxFiles={1}
              maxSize={5}
              placeholder={t('fileUpload.dragAndDrop')}
              description={t('fileUpload.description')}
              disabled={submitOrderMutation.isPending}
            />
            {errors.orderScreenshot && (
              <p className='text-sm text-red-600'>
                {errors.orderScreenshot.message}
              </p>
            )}
          </div>

          {/* 提交按钮 */}
          <div className='flex gap-2 pt-2'>
            <Button
              type='button'
              variant='outline'
              onClick={() => handleDialogClose(false)}
              disabled={submitOrderMutation.isPending}
              className='flex-1'
            >
              {t('browser.cancel')}
            </Button>
            <Button
              type='submit'
              disabled={
                submitOrderMutation.isPending || uploadedFiles.length === 0
              }
              className='flex-1'
            >
              {submitOrderMutation.isPending ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                  {t('submitOrder.submitting')}
                </>
              ) : (
                <>
                  <CheckCircle className='h-4 w-4 mr-1' />
                  {t('submitOrder.submitButton')}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
