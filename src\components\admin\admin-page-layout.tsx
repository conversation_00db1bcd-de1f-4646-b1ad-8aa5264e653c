import { Home } from 'lucide-react';
import Link from 'next/link';
import { ReactNode } from 'react';

import { AdminModeToggle } from '@/components/admin/admin-mode-toggle';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';

interface AdminPageLayoutProps {
  title: string;
  breadcrumbPage: string;
  href: string;
  children: ReactNode;
  showBackButton?: boolean;
}

export function AdminPageLayout({
  title,
  breadcrumbPage,
  href,
  children,
  showBackButton = true,
}: AdminPageLayoutProps) {
  return (
    <>
      <header className='flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12'>
        <div className='flex items-center gap-2 px-4'>
          <SidebarTrigger className='-ml-1' />
          <Separator orientation='vertical' className='mr-2 h-4' />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className='hidden md:block'>
                <BreadcrumbLink href={href}>{title}</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className='hidden md:block' />
              <BreadcrumbItem>
                <BreadcrumbPage>{breadcrumbPage}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className='ml-auto flex items-center gap-2 px-4'>
          {showBackButton && (
            <Button variant='outline' size='sm' asChild>
              <Link href='/dashboard'>
                <Home className='mr-2 h-4 w-4' />
                返回前台
              </Link>
            </Button>
          )}
          <AdminModeToggle />
        </div>
      </header>
      <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>{children}</div>
    </>
  );
}
