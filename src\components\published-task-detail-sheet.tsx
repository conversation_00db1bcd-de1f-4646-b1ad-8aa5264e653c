'use client';

import {
  Clock,
  DollarSign,
  Calendar,
  Tag,
  CreditCard,
  Building2,
  FileText,
  Package,
  AlertCircle,
  CheckCircle,
  Info,
  User,
  Users,
  X,
  Upload,
  Eye,
  Truck,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

import { EvidenceFileViewer } from '@/components/evidence-file-viewer';
import { ResubmitEvidenceDialog } from '@/components/resubmit-evidence-dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useCommissionRates } from '@/hooks/use-commission-rates';
import { UploadedFile } from '@/hooks/use-file-upload';
import { type PublishedTask } from '@/hooks/use-published-tasks';
import { useResubmitEvidence } from '@/hooks/use-resubmit-evidence';
import { getTaskBaseCommission } from '@/lib/utils/commission';

interface PublishedTaskDetailSheetProps {
  task: PublishedTask;
  children: React.ReactNode;
}

export function PublishedTaskDetailSheet({
  task,
  children,
}: PublishedTaskDetailSheetProps) {
  const t = useTranslations('my-published-tasks');
  const tPublish = useTranslations('publish');
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    setMounted(true);

    // 设置定时器，每秒更新一次时间
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // 清理定时器
    return () => clearInterval(timer);
  }, []);

  // 获取费率数据
  const { chargebackTypes, paymentMethods, systemRate } = useCommissionRates();

  // 重新提交证据的mutation
  const resubmitEvidenceMutation = useResubmitEvidence();

  // 处理重新提交证据
  const handleResubmitEvidence = (data: {
    taskId: string;
    files: UploadedFile[];
  }) => {
    resubmitEvidenceMutation.mutate(data);
  };

  // 获取平台显示名称
  const getPlatformName = (platform: string) => {
    if (typeof platform === 'string') {
      return (
        tPublish(`platformSelection.platformLabels.${platform}`) || platform
      );
    }
    return platform;
  };

  // 获取分类显示名称
  const getCategoryName = (category: string) => {
    if (typeof category === 'string') {
      return (
        tPublish(`platformSelection.categoryLabels.${category}`) || category
      );
    }
    return category;
  };

  // 计算产品总价：单价 × 数量
  const calculateTotalPrice = () => {
    const unitPrice = task.unitPrice || 0;
    const quantity = task.quantity || 1;
    return unitPrice * quantity;
  };

  // 计算所需押金：产品总价 × 押金比例
  const calculateDeposit = () => {
    const totalPrice = calculateTotalPrice();
    const depositRatio = systemRate?.depositRatio || 0;
    // 将百分比转换为小数（例如：10.0% -> 0.10）
    return totalPrice * (depositRatio / 100);
  };

  // 计算酬金（使用真实的费率数据）
  // 需要将PublishedTask的数据格式转换为酬金计算函数期望的格式
  const taskForCommission = {
    totalAmount: task.totalAmount,
    unitPrice: task.unitPrice,
    chargebackTypeIds: task.chargebackTypes, // PublishedTask中是字符串数组
    paymentMethodIds: task.paymentMethods, // PublishedTask中是字符串数组
    evidenceUploadType: task.evidenceUploadType,
  };
  // 计算基础酬金（不包含证据费用）
  const baseCommission = getTaskBaseCommission(
    taskForCommission,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'RECRUITING':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'PENDING_LOGISTICS':
        return 'bg-orange-100 text-orange-800';
      case 'PENDING_REVIEW':
        return 'bg-indigo-100 text-indigo-800';
      case 'PENDING_DELIVERY':
        return 'bg-cyan-100 text-cyan-800';
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className='h-4 w-4' />;
      case 'REJECTED':
        return <AlertCircle className='h-4 w-4' />;
      case 'RECRUITING':
        return <Users className='h-4 w-4' />;
      case 'IN_PROGRESS':
        return <Clock className='h-4 w-4' />;
      case 'PENDING_LOGISTICS':
        return <Upload className='h-4 w-4' />;
      case 'PENDING_REVIEW':
        return <Eye className='h-4 w-4' />;
      case 'PENDING_DELIVERY':
        return <Truck className='h-4 w-4' />;
      case 'COMPLETED':
        return <CheckCircle className='h-4 w-4' />;
      case 'EXPIRED':
      case 'CANCELLED':
        return <AlertCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  const getPublishedStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return t('status.PENDING');
      case 'REJECTED':
        return t('status.REJECTED');
      case 'RECRUITING':
        return t('status.RECRUITING');
      case 'IN_PROGRESS':
        return t('status.IN_PROGRESS');
      case 'PENDING_LOGISTICS':
        return t('status.PENDING_LOGISTICS');
      case 'PENDING_REVIEW':
        return t('status.PENDING_REVIEW');
      case 'PENDING_DELIVERY':
        return t('status.PENDING_DELIVERY');
      case 'COMPLETED':
        return t('status.COMPLETED');
      case 'EXPIRED':
        return t('status.EXPIRED');
      case 'CANCELLED':
        return t('status.CANCELLED');
      default:
        return status;
    }
  };

  const getEvidenceStatusColor = (status: string | null) => {
    if (!status) return 'bg-gray-100 text-gray-800';

    switch (status) {
      case 'PENDING_SUBMISSION':
      case 'pending_submission':
        return 'bg-gray-100 text-gray-800';
      case 'UNDER_REVIEW':
      case 'under_review':
        return 'bg-blue-100 text-blue-800';
      case 'NO_EVIDENCE':
      case 'no_evidence':
        return 'bg-orange-100 text-orange-800';
      case 'REVIEWED':
      case 'reviewed':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEvidenceStatusIcon = (status: string | null) => {
    if (!status) return <AlertCircle className='h-4 w-4' />;

    switch (status) {
      case 'PENDING_SUBMISSION':
      case 'pending_submission':
        return <AlertCircle className='h-4 w-4' />;
      case 'UNDER_REVIEW':
      case 'under_review':
        return <Clock className='h-4 w-4' />;
      case 'NO_EVIDENCE':
      case 'no_evidence':
        return <X className='h-4 w-4' />;
      case 'REVIEWED':
      case 'reviewed':
        return <CheckCircle className='h-4 w-4' />;
      case 'REJECTED':
      case 'rejected':
        return <X className='h-4 w-4' />;
      default:
        return <AlertCircle className='h-4 w-4' />;
    }
  };

  const getEvidenceStatusLabel = (status: string | null) => {
    if (!status) return t('evidenceStatus.PENDING_SUBMISSION');

    switch (status) {
      case 'PENDING_SUBMISSION':
      case 'pending_submission':
        return t('evidenceStatus.PENDING_SUBMISSION');
      case 'UNDER_REVIEW':
      case 'under_review':
        return t('evidenceStatus.UNDER_REVIEW');
      case 'NO_EVIDENCE':
      case 'no_evidence':
        return t('evidenceStatus.NO_EVIDENCE');
      case 'REVIEWED':
      case 'reviewed':
        return t('evidenceStatus.REVIEWED');
      case 'REJECTED':
      case 'rejected':
        return t('evidenceStatus.REJECTED');
      default:
        return t('evidenceStatus.PENDING_SUBMISSION');
    }
  };

  const calculateTimeLeft = () => {
    if (!mounted || !task.expiresAt) {
      return t('time.calculating');
    }

    const now = currentTime.getTime();
    const deadlineTime = new Date(task.expiresAt).getTime();
    const difference = deadlineTime - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      if (days > 0) {
        return `${days}${t('time.days')}${hours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
      } else if (hours > 0) {
        return `${hours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
      } else if (minutes > 0) {
        return `${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
      } else {
        return `${seconds}${t('time.seconds')}`;
      }
    } else {
      return t('time.expired');
    }
  };

  // 计算提交截止时间倒计时（如果委托进行中）
  const getSubmissionCountdown = () => {
    if (!mounted || task.status !== 'IN_PROGRESS' || !task.acceptedAt) {
      return null;
    }

    const acceptedTime = new Date(task.acceptedAt);
    const deadline = new Date(acceptedTime.getTime() + 24 * 60 * 60 * 1000);
    const diffMs = deadline.getTime() - currentTime.getTime();

    if (diffMs <= 0) {
      return { text: t('time.overtime'), isExpired: true, isUrgent: false };
    }

    const totalHours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

    if (totalHours > 0) {
      return {
        text: `${totalHours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`,
        isExpired: false,
        isUrgent: totalHours < 2,
      };
    } else if (minutes > 0) {
      return {
        text: `${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`,
        isExpired: false,
        isUrgent: true,
      };
    } else {
      return {
        text: `${seconds}${t('time.seconds')}`,
        isExpired: false,
        isUrgent: true,
      };
    }
  };

  // 统一的倒计时函数，处理不同状态
  const getTaskCountdown = () => {
    if (!mounted) {
      return null;
    }

    switch (task.status) {
      case 'RECRUITING':
        if (task.expiresAt) {
          const deadline = new Date(task.expiresAt);
          const diffMs = deadline.getTime() - currentTime.getTime();

          if (diffMs <= 0) {
            return {
              text: t('time.expired'),
              isExpired: true,
              isUrgent: false,
              label: t('time.recruitmentTime'),
            };
          }

          const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

          let timeText = '';
          if (days > 0) {
            timeText = `${days}${t('time.days')}${hours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
          } else if (hours > 0) {
            timeText = `${hours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
          } else if (minutes > 0) {
            timeText = `${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
          } else {
            timeText = `${seconds}${t('time.seconds')}`;
          }

          return {
            text: timeText,
            isExpired: false,
            isUrgent: days < 1,
            label: t('time.recruitmentTimeRemaining'),
          };
        }
        break;

      case 'PENDING_LOGISTICS':
        if (task.logisticsDeadline) {
          const deadline = new Date(task.logisticsDeadline);
          const diffMs = deadline.getTime() - currentTime.getTime();

          if (diffMs <= 0) {
            return {
              text: t('time.logisticsSubmissionOvertime'),
              isExpired: true,
              isUrgent: false,
              label: t('time.logisticsSubmissionTime'),
            };
          }

          const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
          );
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

          let timeText = '';
          if (days > 0) {
            timeText = `${days}${t('time.days')}${hours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
          } else if (hours > 0) {
            timeText = `${hours}${t('time.hours')}${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
          } else if (minutes > 0) {
            timeText = `${minutes}${t('time.minutes')}${seconds}${t('time.seconds')}`;
          } else {
            timeText = `${seconds}${t('time.seconds')}`;
          }

          return {
            text: timeText,
            isExpired: false,
            isUrgent: days < 1,
            label: t('time.logisticsSubmissionTimeRemaining'),
          };
        }
        break;
    }

    return null;
  };

  // 格式化日期时间的安全函数
  const formatDateTime = (dateString: string | undefined) => {
    if (!mounted || !dateString) {
      return t('time.loading');
    }

    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return t('time.invalidDate');
    }
  };

  const submissionCountdown = getSubmissionCountdown();
  const taskCountdown = getTaskCountdown();

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className='w-full sm:max-w-2xl overflow-y-auto'>
        <SheetHeader className='space-y-3'>
          <div className='flex items-center justify-between'>
            <SheetTitle className='text-xl font-bold'>
              {t('taskDetail.title')}
            </SheetTitle>
            <Badge className={getStatusColor(task.status)}>
              {getStatusIcon(task.status)}
              <span className='ml-1'>
                {getPublishedStatusLabel(task.status)}
              </span>
            </Badge>
          </div>
          <SheetDescription className='text-base'>
            {getPlatformName(task.platform)} - {getCategoryName(task.category)}
          </SheetDescription>
        </SheetHeader>

        <div className='space-y-6 py-6'>
          {/* 委托状态提醒 */}
          {task.status === 'IN_PROGRESS' && submissionCountdown && (
            <Alert
              className={
                submissionCountdown.isExpired
                  ? 'border-red-200 bg-red-50'
                  : submissionCountdown.isUrgent
                    ? 'border-orange-200 bg-orange-50'
                    : 'border-blue-200 bg-blue-50'
              }
            >
              <Clock className='h-4 w-4' />
              <AlertDescription>
                {submissionCountdown.isExpired ? (
                  <span className='text-red-600 font-medium'>
                    {t('taskDetail.submissionOvertime')}
                  </span>
                ) : (
                  <span
                    className={
                      submissionCountdown.isUrgent
                        ? 'text-orange-600'
                        : 'text-blue-600'
                    }
                  >
                    {t('taskDetail.submissionDeadlinePrefix')}{' '}
                    <span className='font-medium'>
                      {submissionCountdown.text}
                    </span>{' '}
                    {t('taskDetail.submissionDeadlineSuffix')}
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}

          {task.status === 'RECRUITING' && (
            <Alert className='border-green-200 bg-green-50'>
              <Users className='h-4 w-4' />
              <AlertDescription>
                <span className='text-green-600'>
                  {t('taskDetail.recruiting')}
                </span>
              </AlertDescription>
            </Alert>
          )}

          {task.status === 'PENDING_LOGISTICS' && (
            <Alert className='border-purple-200 bg-purple-50'>
              <Clock className='h-4 w-4' />
              <AlertDescription>
                <span className='text-purple-600'>
                  {t('taskDetail.waitingLogistics')}
                </span>
              </AlertDescription>
            </Alert>
          )}

          {/* 接单者信息 */}
          {task.accepter && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2 text-lg'>
                  <User className='h-5 w-5' />
                  {t('taskDetail.accepterInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center gap-2'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.nickname')}:
                  </span>
                  <span className='font-medium'>{task.accepter.nickname}</span>
                </div>
                <div className='flex items-center gap-2'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.email')}:
                  </span>
                  <span className='font-medium'>{task.accepter.email}</span>
                </div>
                {task.acceptedAt && (
                  <div className='flex items-center gap-2'>
                    <span className='text-sm text-muted-foreground'>
                      {t('taskDetail.acceptedTime')}:
                    </span>
                    <span className='font-medium'>
                      {formatDateTime(task.acceptedAt)}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 基本信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <Info className='h-5 w-5' />
                {t('taskDetail.basicInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <Building2 className='h-4 w-4 text-muted-foreground' />
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.platform')}:
                  </span>
                  <span className='font-medium'>
                    {getPlatformName(task.platform)}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Tag className='h-4 w-4 text-muted-foreground' />
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.category')}:
                  </span>
                  <span className='font-medium'>
                    {getCategoryName(task.category)}
                  </span>
                </div>
              </div>

              <div className='flex items-center gap-2'>
                <Calendar className='h-4 w-4 text-muted-foreground' />
                <span className='text-sm text-muted-foreground'>
                  {t('taskDetail.publishTime')}:
                </span>
                <span className='font-medium'>
                  {formatDateTime(task.createdAt)}
                </span>
              </div>

              {taskCountdown && (
                <div className='flex items-center gap-2'>
                  <Clock className='h-4 w-4 text-muted-foreground' />
                  <span className='text-sm text-muted-foreground'>
                    {taskCountdown.label}:
                  </span>
                  <span
                    className={`font-medium ${
                      taskCountdown.isExpired
                        ? 'text-red-600'
                        : taskCountdown.isUrgent
                          ? 'text-orange-600'
                          : 'text-green-600'
                    }`}
                  >
                    {taskCountdown.text}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 商品信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <Package className='h-5 w-5' />
                {t('taskDetail.productInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.unitPrice')}
                  </span>
                  <div className='text-lg font-semibold text-green-600'>
                    ${task.unitPrice?.toFixed(2) || '0.00'}
                  </div>
                </div>
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.quantity')}
                  </span>
                  <div className='text-lg font-semibold'>
                    {task.quantity || 1} {t('taskDetail.unit')}
                  </div>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.totalPrice')}
                  </span>
                  <div className='text-lg font-semibold text-blue-600'>
                    ${calculateTotalPrice().toFixed(2)}
                  </div>
                </div>
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.deposit')}
                  </span>
                  <div className='text-lg font-semibold text-orange-600'>
                    ${calculateDeposit().toFixed(2)}
                  </div>
                </div>
              </div>

              {task.productUrl && (
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.productUrl')}
                  </span>
                  <div className='text-sm break-all text-blue-600'>
                    <a
                      href={task.productUrl}
                      target='_blank'
                      rel='noopener noreferrer'
                      className='hover:underline'
                    >
                      {task.productUrl}
                    </a>
                  </div>
                </div>
              )}

              {task.productDescription && (
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.productDescription')}
                  </span>
                  <div className='text-sm text-gray-700'>
                    {task.productDescription}
                  </div>
                </div>
              )}

              {/* 购物车示范截图 */}
              {task.cartScreenshots && task.cartScreenshots.length > 0 && (
                <div className='space-y-2'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.cartScreenshots')}
                  </span>
                  <EvidenceFileViewer files={task.cartScreenshots} />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 拒付类型和支付方式 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <CreditCard className='h-5 w-5' />
                {t('taskDetail.chargebackInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <span className='text-sm text-muted-foreground'>
                  {t('taskDetail.chargebackTypes')}
                </span>
                <div className='flex flex-wrap gap-2'>
                  {task.chargebackTypes?.map((type, index) => (
                    <Badge key={index} variant='outline'>
                      {tPublish(
                        `platformSelection.chargebackTypeLabels.${type}` as any,
                      ) || type}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className='space-y-2'>
                <span className='text-sm text-muted-foreground'>
                  {t('taskDetail.paymentMethods')}
                </span>
                <div className='flex flex-wrap gap-2'>
                  {task.paymentMethods?.map((method, index) => (
                    <Badge key={index} variant='outline'>
                      {tPublish(
                        `platformSelection.paymentMethodLabels.${method}` as any,
                      ) || method}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 收货信息 */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <Package className='h-5 w-5' />
                {t('taskDetail.shippingInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.recipientName')}
                  </span>
                  <div className='font-medium'>{task.recipientName}</div>
                </div>
                <div className='space-y-1'>
                  <span className='text-sm text-muted-foreground'>
                    {t('taskDetail.recipientPhone')}
                  </span>
                  <div className='font-medium'>{task.recipientPhone}</div>
                </div>
              </div>
              <div className='space-y-1'>
                <span className='text-sm text-muted-foreground'>
                  {t('taskDetail.shippingAddress')}
                </span>
                <div className='font-medium'>{task.shippingAddress}</div>
              </div>
            </CardContent>
          </Card>

          {/* 证据状态 */}
          {task.evidenceStatus && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center justify-between text-lg'>
                  <div className='flex items-center gap-2'>
                    <FileText className='h-5 w-5' />
                    {t('taskDetail.evidenceStatus')}
                  </div>
                  {/* 重审证据按钮 */}
                  {(task.evidenceStatus === 'REJECTED' ||
                    task.evidenceStatus === 'rejected') && (
                    <ResubmitEvidenceDialog
                      taskId={task.id}
                      rejectionReason={task.evidenceRejectReason}
                      onSubmit={handleResubmitEvidence}
                    >
                      <Button
                        size='sm'
                        className='flex items-center gap-1'
                        disabled={resubmitEvidenceMutation.isPending}
                      >
                        <Upload className='h-4 w-4' />
                        {t('taskDetail.resubmitEvidence')}
                      </Button>
                    </ResubmitEvidenceDialog>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center gap-2'>
                  <Badge
                    className={getEvidenceStatusColor(task.evidenceStatus)}
                  >
                    {getEvidenceStatusIcon(task.evidenceStatus)}
                    <span className='ml-1'>
                      {getEvidenceStatusLabel(task.evidenceStatus)}
                    </span>
                  </Badge>
                </div>

                {/* 拒绝理由显示 */}
                {(task.evidenceStatus === 'REJECTED' ||
                  task.evidenceStatus === 'rejected') &&
                  task.evidenceRejectReason && (
                    <div className='p-3 bg-red-50 border border-red-200 rounded-lg'>
                      <div className='flex items-start gap-2'>
                        <AlertCircle className='h-4 w-4 text-red-600 mt-0.5 flex-shrink-0' />
                        <div>
                          <div className='text-sm font-medium text-red-800'>
                            {t('taskDetail.rejectionReason')}
                          </div>
                          <div className='text-sm text-red-700 mt-1'>
                            {task.evidenceRejectReason}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                {task.evidenceFiles && task.evidenceFiles.length > 0 && (
                  <div className='space-y-2'>
                    <span className='text-sm text-muted-foreground'>
                      {t('taskDetail.evidenceFiles')}
                    </span>
                    <EvidenceFileViewer files={task.evidenceFiles} />
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 订单物流信息 - 在已完成状态时显示 */}
          {task.status === 'COMPLETED' &&
            (task.orderNumber ||
              task.trackingNumber ||
              task.orderScreenshot ||
              (task.logisticsScreenshots &&
                task.logisticsScreenshots.length > 0)) && (
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2 text-lg'>
                    <Truck className='h-5 w-5' />
                    {t('taskDetail.orderLogisticsInfo')}
                  </CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  {/* 订单号 */}
                  {task.orderNumber && (
                    <div className='space-y-1'>
                      <span className='text-sm text-muted-foreground'>
                        {t('taskDetail.orderNumber')}
                      </span>
                      <div className='p-3 bg-gray-50 rounded-lg font-mono text-sm'>
                        {task.orderNumber}
                      </div>
                    </div>
                  )}

                  {/* 物流单号 */}
                  {task.trackingNumber && (
                    <div className='space-y-1'>
                      <span className='text-sm text-muted-foreground'>
                        {t('taskDetail.trackingNumber')}
                      </span>
                      <div className='p-3 bg-gray-50 rounded-lg font-mono text-sm'>
                        {task.trackingNumber}
                      </div>
                    </div>
                  )}

                  {/* 订单截图 */}
                  {task.orderScreenshot && (
                    <div className='space-y-2'>
                      <span className='text-sm text-muted-foreground'>
                        {t('taskDetail.orderScreenshot')}
                      </span>
                      <EvidenceFileViewer files={[task.orderScreenshot]} />
                    </div>
                  )}

                  {/* 物流截图 */}
                  {task.logisticsScreenshots &&
                    task.logisticsScreenshots.length > 0 && (
                      <div className='space-y-2'>
                        <span className='text-sm text-muted-foreground'>
                          {t('taskDetail.logisticsScreenshots')}
                        </span>
                        <EvidenceFileViewer files={task.logisticsScreenshots} />
                      </div>
                    )}
                </CardContent>
              </Card>
            )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
