import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

// 接受委托响应类型
interface AcceptTaskResponse {
  success: boolean;
  message: string;
  data: {
    task: {
      id: string;
      status: string;
      acceptedAt: string;
      platform: string;
      category: string;
    };
    payment: {
      deposit: number;
      newBalance: number;
      transactionId: string;
    };
  };
}

// 接受委托API调用函数
async function acceptTask(taskId: string): Promise<AcceptTaskResponse> {
  const response = await fetch(`/api/tasks/${taskId}/accept`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || '接受委托失败');
  }

  return response.json();
}

// 自定义hook
export function useAcceptTask() {
  const t = useTranslations('Messages');
  const tTasks = useTranslations('Tasks');
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: acceptTask,
    onSuccess: data => {
      // 显示成功提示（包含支付信息）
      toast.success(tTasks('hooks.acceptTask.success.title'), {
        description: tTasks('hooks.acceptTask.success.description', {
          taskId: data.data.task.id,
          deposit: `$${data.data.payment.deposit.toFixed(2)}`,
          balance: `$${data.data.payment.newBalance.toFixed(2)}`,
        }),
        duration: 6000,
      });

      // 清除相关缓存
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['accepted-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['published-tasks'] });
      // 刷新钱包相关缓存
      queryClient.invalidateQueries({ queryKey: ['user-wallet'] });
      queryClient.invalidateQueries({ queryKey: ['wallet-transactions'] });

      // 跳转到我的已接受委托页面
      router.push('/my-accepted-tasks');
    },
    onError: (error: Error) => {
      // 根据错误类型显示不同的提示
      if (
        error.message.includes('余额不足') ||
        error.message.includes('Insufficient balance')
      ) {
        toast.error(
          tTasks('hooks.acceptTask.errors.insufficientBalance.title'),
          {
            description: tTasks(
              'hooks.acceptTask.errors.insufficientBalance.description',
            ),
            duration: 5000,
          },
        );
      } else if (
        error.message.includes('不能接受自己发布的委托') ||
        error.message.includes('Cannot accept your own')
      ) {
        toast.error(tTasks('hooks.acceptTask.errors.ownTask.title'), {
          description: tTasks('hooks.acceptTask.errors.ownTask.description'),
          duration: 5000,
        });
      } else if (
        error.message.includes('委托已被其他用户接受') ||
        error.message.includes('already been accepted')
      ) {
        toast.error(tTasks('hooks.acceptTask.errors.taskTaken.title'), {
          description: tTasks('hooks.acceptTask.errors.taskTaken.description'),
          duration: 5000,
        });
      } else if (
        error.message.includes('委托已过期') ||
        error.message.includes('expired')
      ) {
        toast.error(tTasks('hooks.acceptTask.errors.taskExpired.title'), {
          description: tTasks(
            'hooks.acceptTask.errors.taskExpired.description',
          ),
          duration: 5000,
        });
      } else {
        toast.error(tTasks('hooks.acceptTask.errors.general.title'), {
          description: tTasks('hooks.acceptTask.errors.general.description'),
          duration: 5000,
        });
      }
    },
  });
}
