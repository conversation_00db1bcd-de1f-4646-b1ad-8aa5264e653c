'use client';
import { X } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function CloseModalButton() {
  const router = useRouter();

  const handleClose = () => {
    router.back();
  };

  return (
    <button
      type='button'
      onClick={handleClose}
      className='p-1 hover:bg-gray-100 rounded-full'
      aria-label='Close modal'
      title='Close'
    >
      <X className='h-5 w-5' />
    </button>
  );
}
