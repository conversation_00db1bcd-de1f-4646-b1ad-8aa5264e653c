"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";

import Marquee from "@/components/ui/marquee";
import { cn } from "@/lib/utils";

interface ReviewData {
  name: string;
  role: string;
  company: string;
  quote: string;
  img: string;
}

const ReviewCard = ({
  name,
  role,
  company,
  quote,
  img,
}: ReviewData) => {
  return (
    <figure
      className={cn(
        "relative w-64 cursor-pointer overflow-hidden rounded-xl border p-4",
        // light styles
        "border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]",
        // dark styles
        "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]",
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <Image className="rounded-full" width={32} height={32} alt={`${name} avatar`} src={img} />
        <div className="flex flex-col">
          <figcaption className="text-sm font-medium dark:text-white">
            {name}
          </figcaption>
          <p className="text-xs font-medium dark:text-white/40">{role}</p>
          <p className="text-xs text-gray-500 dark:text-white/30">{company}</p>
        </div>
      </div>
      <blockquote className="mt-2 text-sm">{quote}</blockquote>
    </figure>
  );
};

const Comments = () => {
  const t = useTranslations("HomePage");

  // Get testimonials data from internationalization
  const quotes = t.raw("testimonials.quotes") as string[];
  const users = t.raw("testimonials.users") as Array<{
    name: string;
    role: string;
    company: string;
  }>;

  // Create reviews array by combining quotes and users
  const reviews: ReviewData[] = quotes.map((quote, index) => ({
    name: users[index]?.name || `User ${index + 1}`,
    role: users[index]?.role || "User",
    company: users[index]?.company || "Company",
    quote,
    img: `https://avatar.vercel.sh/${users[index]?.name.toLowerCase().replace(/\s+/g, '') || `user${index + 1}`}`,
  }));

  const firstRow = reviews.slice(0, Math.ceil(reviews.length / 2));
  const secondRow = reviews.slice(Math.ceil(reviews.length / 2));

  return (
    <div className="relative flex h-full w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-background py-4 sm:py-20 md:py-20 xl:py-20">
      <Marquee pauseOnHover className="[--duration:20s]">
        {firstRow.map((review, index) => (
          <ReviewCard key={`first-${index}`} {...review} />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover className="[--duration:20s]">
        {secondRow.map((review, index) => (
          <ReviewCard key={`second-${index}`} {...review} />
        ))}
      </Marquee>
      <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white dark:from-background"></div>
      <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white dark:from-background"></div>
    </div>
  );
};

export { Comments };
