'use client';

import { Building2 } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useResponsive } from '@/hooks/use-responsive';

// Mock translation function for demo
const mockT = (key: string) => {
  const translations: Record<string, string> = {
    'tabs.all': 'All Tasks',
    'tabs.available': 'Available',
    'tabs.inProgress': 'In Progress',
    'tabs.completed': 'Completed',
    'tabs.expired': 'Expired',
    'tabs.cancelled': 'Cancelled',
  };
  return translations[key] || key;
};

// Mock stats for demo
const mockStats = {
  total: 25,
  available: 8,
  inProgress: 5,
  completed: 10,
  expired: 1,
  cancelled: 1,
};

// Responsive Task Tabs Component (same as in the main file)
interface ResponsiveTaskTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  stats: {
    total: number;
    available: number;
    inProgress: number;
    completed: number;
    expired: number;
    cancelled: number;
  };
  t: (key: string) => string;
}

function ResponsiveTaskTabs({
  activeTab,
  onTabChange,
  stats,
  t,
}: ResponsiveTaskTabsProps) {
  const { isMobile, isTablet, breakpoint } = useResponsive();

  const tabItems = [
    {
      value: 'all',
      label: t('tabs.all'),
      shortLabel: 'All',
      count: stats.total,
    },
    {
      value: 'available',
      label: t('tabs.available'),
      shortLabel: 'Open',
      count: stats.available,
    },
    {
      value: 'in_progress',
      label: t('tabs.inProgress'),
      shortLabel: 'Active',
      count: stats.inProgress,
    },
    {
      value: 'completed',
      label: t('tabs.completed'),
      shortLabel: 'Done',
      count: stats.completed,
    },
    {
      value: 'expired',
      label: t('tabs.expired'),
      shortLabel: 'Expired',
      count: stats.expired,
    },
    {
      value: 'cancelled',
      label: t('tabs.cancelled'),
      shortLabel: 'Cancel',
      count: stats.cancelled,
    },
  ];

  // 移动端：使用水平滚动
  if (isMobile) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='w-full overflow-x-auto scrollbar-hide responsive-tabs-mobile pb-1'>
          <TabsList className='inline-flex h-10 items-center justify-start rounded-lg bg-muted p-1 text-muted-foreground min-w-max gap-1'>
            {tabItems.map(item => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                className='whitespace-nowrap px-2 py-1.5 text-xs font-medium min-w-[60px] flex-shrink-0 touch-manipulation responsive-tab-trigger'
              >
                <span className='block xs:hidden'>{item.shortLabel}</span>
                <span className='hidden xs:block sm:hidden'>{item.label}</span>
                <span className='hidden sm:block'>
                  {item.label} ({item.count})
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
    );
  }

  // 平板端：使用2行3列布局
  if (isTablet) {
    return (
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <div className='grid grid-cols-3 gap-2'>
          {tabItems.map(item => (
            <button
              key={item.value}
              type='button'
              onClick={() => onTabChange(item.value)}
              className={`h-10 px-3 py-2 text-sm font-medium rounded-md border transition-colors touch-manipulation responsive-tab-trigger ${
                activeTab === item.value
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'border-border bg-background hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {item.label} ({item.count})
            </button>
          ))}
        </div>
      </Tabs>
    );
  }

  // 桌面端：保持原有的6列网格布局
  return (
    <Tabs value={activeTab} onValueChange={onTabChange}>
      <TabsList className='grid w-full grid-cols-6'>
        {tabItems.map(item => (
          <TabsTrigger key={item.value} value={item.value}>
            {item.label} ({item.count})
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}

// Demo Component
export function ResponsiveTaskTabsDemo() {
  const [activeTab, setActiveTab] = useState('all');
  const { breakpoint, isMobile, isTablet, width } = useResponsive();

  return (
    <div className='space-y-6 p-6'>
      {/* Debug Info */}
      <Card>
        <CardContent className='p-4'>
          <h3 className='font-semibold mb-2'>Responsive Debug Info</h3>
          <div className='text-sm space-y-1'>
            <p>
              Current breakpoint:{' '}
              <span className='font-mono'>{breakpoint}</span>
            </p>
            <p>
              Screen width: <span className='font-mono'>{width}px</span>
            </p>
            <p>
              Is Mobile:{' '}
              <span className='font-mono'>{isMobile ? 'true' : 'false'}</span>
            </p>
            <p>
              Is Tablet:{' '}
              <span className='font-mono'>{isTablet ? 'true' : 'false'}</span>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Responsive Tabs Demo */}
      <Card>
        <CardContent className='p-6'>
          <h2 className='text-xl font-bold mb-4'>
            Responsive Task Filter Tabs
          </h2>
          <p className='text-gray-600 mb-6'>
            Resize your browser window to see how the tabs adapt to different
            screen sizes:
          </p>

          <ResponsiveTaskTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            stats={mockStats}
            t={mockT}
          />

          <div className='mt-6 p-4 bg-muted rounded-lg'>
            <p className='text-sm'>
              Active tab: <span className='font-semibold'>{activeTab}</span>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Layout Explanation */}
      <Card>
        <CardContent className='p-6'>
          <h3 className='font-semibold mb-3'>Responsive Behavior:</h3>
          <div className='space-y-2 text-sm'>
            <div className='flex items-center gap-2'>
              <span className='w-3 h-3 bg-red-500 rounded-full'></span>
              <span>
                <strong>Mobile (320px-768px):</strong> Horizontal scrolling with
                abbreviated labels
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='w-3 h-3 bg-yellow-500 rounded-full'></span>
              <span>
                <strong>Tablet (768px-1024px):</strong> 2-row, 3-column grid
                layout
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='w-3 h-3 bg-green-500 rounded-full'></span>
              <span>
                <strong>Desktop (1024px+):</strong> Single-row, 6-column grid
                layout
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
