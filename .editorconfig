# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, TSX
[*.{js,jsx,ts,tsx}]
indent_size = 2
max_line_length = 80

# JSON files
[*.json]
indent_size = 2
max_line_length = 120

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = 100

# HTML files
[*.html]
indent_size = 2

# CSS, SCSS, SASS files
[*.{css,scss,sass}]
indent_size = 2

# Configuration files
[*.{config.js,config.ts}]
indent_size = 2

# Package files
[package.json]
indent_size = 2

# Environment files
[.env*]
trim_trailing_whitespace = false

# Git files
[.gitignore]
trim_trailing_whitespace = true

# Docker files
[Dockerfile*]
indent_style = tab
indent_size = 4

# Makefile
[Makefile]
indent_style = tab
indent_size = 4

# Shell scripts
[*.sh]
indent_size = 2
end_of_line = lf

# Windows batch files
[*.bat]
end_of_line = crlf

# PowerShell files
[*.ps1]
end_of_line = crlf
