{"React Functional Component": {"prefix": "rfc", "body": ["import type { FC } from 'react';", "", "interface ${1:ComponentName}Props {", "  $2", "}", "", "const ${1:ComponentName}: FC<${1:ComponentName}Props> = ({ $3 }) => {", "  return (", "    <div>", "      $4", "    </div>", "  );", "};", "", "export default ${1:ComponentName};"], "description": "Create a React functional component with TypeScript"}, "React Component with Props": {"prefix": "rfcp", "body": ["import type { FC, ReactNode } from 'react';", "", "interface ${1:ComponentName}Props {", "  children?: ReactNode;", "  className?: string;", "  $2", "}", "", "const ${1:ComponentName}: FC<${1:ComponentName}Props> = ({", "  children,", "  className,", "  $3", "}) => {", "  return (", "    <div className={className}>", "      {children}", "      $4", "    </div>", "  );", "};", "", "export default ${1:ComponentName};"], "description": "Create a React component with common props"}, "Next.js Page Component": {"prefix": "npage", "body": ["import type { NextPage } from 'next';", "import Head from 'next/head';", "", "const ${1:PageName}: NextPage = () => {", "  return (", "    <>", "      <Head>", "        <title>${2:Page Title}</title>", "        <meta name=\"description\" content=\"${3:Page description}\" />", "      </Head>", "      <main>", "        <h1>${2:Page Title}</h1>", "        $4", "      </main>", "    </>", "  );", "};", "", "export default ${1:PageName};"], "description": "Create a Next.js page component"}, "Next.js API Route": {"prefix": "napi", "body": ["import type { NextApiRequest, NextApiResponse } from 'next';", "", "type Data = {", "  $1", "};", "", "export default function handler(", "  req: NextApiRequest,", "  res: NextApiResponse<Data>", ") {", "  if (req.method === '${2:GET}') {", "    // Handle ${2:GET} request", "    $3", "    res.status(200).json({ $4 });", "  } else {", "    res.setHeader('Allow', ['${2:GET}']);", "    res.status(405).end(`Method ${req.method} Not Allowed`);", "  }", "}"], "description": "Create a Next.js API route"}, "React Hook": {"prefix": "rhook", "body": ["import { useState, useEffect } from 'react';", "", "interface Use${1:HookName}Return {", "  $2", "}", "", "export const use${1:HookName} = (): Use${1:HookName}Return => {", "  const [${3:state}, set${3/(.*)/${3:/capitalize}/}] = useState$4();", "", "  useEffect(() => {", "    $5", "  }, []);", "", "  return {", "    ${3:state},", "    set${3/(.*)/${3:/capitalize}/},", "    $6", "  };", "};"], "description": "Create a custom React hook"}, "Jest Test Suite": {"prefix": "jtest", "body": ["import { render, screen } from '@testing-library/react';", "import userEvent from '@testing-library/user-event';", "import ${1:ComponentName} from './${1:ComponentName}';", "", "describe('${1:ComponentName}', () => {", "  it('renders correctly', () => {", "    render(<${1:ComponentName} $2 />);", "    ", "    expect(screen.getByText('$3')).toBeInTheDocument();", "  });", "", "  it('handles user interaction', async () => {", "    const user = userEvent.setup();", "    render(<${1:ComponentName} $2 />);", "    ", "    $4", "  });", "});"], "description": "Create a Jest test suite for React component"}, "TypeScript Interface": {"prefix": "tsi", "body": ["interface ${1:InterfaceName} {", "  ${2:property}: ${3:type};", "  $4", "}"], "description": "Create a TypeScript interface"}, "TypeScript Type": {"prefix": "tst", "body": ["type ${1:TypeName} = {", "  ${2:property}: ${3:type};", "  $4", "};"], "description": "Create a TypeScript type"}, "Async Function": {"prefix": "afn", "body": ["const ${1:functionName} = async (${2:params}): Promise<${3:ReturnType}> => {", "  try {", "    $4", "    return $5;", "  } catch (error) {", "    console.error('Error in ${1:functionName}:', error);", "    throw error;", "  }", "};"], "description": "Create an async function with error handling"}, "Try-Catch Block": {"prefix": "tryc", "body": ["try {", "  $1", "} catch (error) {", "  console.error('${2:Error message}:', error);", "  $3", "}"], "description": "Create a try-catch block"}, "Console Log": {"prefix": "cl", "body": ["console.log('${1:message}:', $2);"], "description": "Console log with message"}, "Console Error": {"prefix": "ce", "body": ["console.error('${1:error message}:', $2);"], "description": "Console error with message"}, "Import Statement": {"prefix": "imp", "body": ["import ${2:{ $3 \\}} from '${1:module}';"], "description": "Import statement"}, "Import Type": {"prefix": "impt", "body": ["import type { ${2:Type} } from '${1:module}';"], "description": "Import type statement"}, "Export Default": {"prefix": "exp", "body": ["export default ${1:name};"], "description": "Export default statement"}, "Export Named": {"prefix": "expn", "body": ["export { ${1:name} };"], "description": "Export named statement"}}