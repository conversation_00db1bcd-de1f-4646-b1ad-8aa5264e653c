// 导出所有模板 (现在使用统一的i18n版本)
export { notificationTemplate } from './notification';
export { verificationCodeTemplate } from './verification-code';

// 直接导出i18n版本
export { notificationTemplateI18n, type NotificationEmailData } from './notification-i18n';
export { verificationCodeTemplateI18n, type VerificationCodeData } from './verification-code-i18n';
export { withdrawalApprovedTemplate } from './withdrawal-approved';
export { taskCompletedPublisherTemplate } from './task-completed-publisher';
export { taskCompletedAccepterTemplate } from './task-completed-accepter';
export { taskCancelledAccepterTemplate } from './task-cancelled-accepter';
export { taskReviewApprovedAccepterTemplate } from './task-review-approved-accepter';
export { taskReviewRejectedAccepterTemplate } from './task-review-rejected-accepter';
export { withdrawalRejectedTemplate } from './withdrawal-rejected';
export { taskCancelledPublisherTemplate } from './task-cancelled-publisher';

export {
  taskReviewPublisherTemplate,
  type TaskReviewPublisherEmailData,
} from './task-review-publisher';

export {
  evidenceReviewPublisherTemplate,
  type EvidenceReviewPublisherEmailData,
} from './evidence-review-publisher';

export { taskAcceptedPublisherTemplate } from './task-accepted-publisher';

export {
  logisticsReviewPublisherTemplate,
  type LogisticsReviewPublisherEmailData,
} from './logistics-review-publisher';

// 财务相关邮件模板
export {
  depositSuccessTemplateI18n,
  type DepositSuccessEmailData,
} from './deposit-success-i18n';

export {
  depositFailedTemplateI18n,
  type DepositFailedEmailData,
} from './deposit-failed-i18n';

export {
  withdrawalApprovedTemplateI18n,
  type WithdrawalApprovedEmailData,
} from './withdrawal-approved-i18n';

export {
  withdrawalRejectedTemplateI18n,
  type WithdrawalRejectedEmailData,
} from './withdrawal-rejected-i18n';

// 任务管理相关邮件模板
export {
  taskCancelledPublisherTemplateI18n,
  type TaskCancelledPublisherEmailData,
} from './task-cancelled-publisher-i18n';

export {
  taskCancelledAccepterTemplateI18n,
  type TaskCancelledAccepterEmailData,
} from './task-cancelled-accepter-i18n';

export {
  taskAcceptedPublisherTemplateI18n,
  type TaskAcceptedPublisherEmailData,
} from './task-accepted-publisher-i18n';

// 重新导出类型（仅导出未在上面导出的类型）
export type { TaskCompletedPublisherEmailData } from './task-completed-publisher';
export type { TaskCompletedAccepterEmailData } from './task-completed-accepter';
export type { TaskReviewApprovedAccepterEmailData } from './task-review-approved-accepter';
export type { TaskReviewRejectedAccepterEmailData } from './task-review-rejected-accepter';

// 基础邮件数据接口
export interface BaseEmailData {
  userName?: string;
  userEmail?: string;
}
