import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

// 获取用户接受的委托列表
export async function GET(request: NextRequest) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // 2. 构建查询条件
    const where: any = {
      accepterId: userId, // 只查询当前用户接受的委托
    };

    // 根据状态筛选
    if (status && status !== 'all') {
      const statusMap: Record<string, string> = {
        in_progress: 'IN_PROGRESS',
        pending_logistics: 'PENDING_LOGISTICS',
        pending_review: 'PENDING_REVIEW',
        pending_delivery: 'PENDING_DELIVERY',
        completed: 'COMPLETED',
        expired: 'EXPIRED',
      };

      if (statusMap[status]) {
        where.status = statusMap[status] as any;
      }
    }

    // 3. 查询委托数据
    const [tasks, totalCount] = await Promise.all([
      prisma.task.findMany({
        where,
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          acceptedAt: 'desc', // 按接受时间降序排列
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.task.count({ where }),
    ]);

    // 4. 获取统计数据
    const stats = await prisma.task.groupBy({
      by: ['status'],
      where: {
        accepterId: userId,
      },
      _count: {
        status: true,
      },
    });

    // 5. 处理统计数据
    const statsMap = stats.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      },
      {} as Record<string, number>,
    );

    const processedStats = {
      total: stats.reduce((sum, stat) => sum + stat._count.status, 0),
      inProgress: statsMap['IN_PROGRESS'] || 0,
      pendingLogistics: statsMap['PENDING_LOGISTICS'] || 0,
      pendingReview: statsMap['PENDING_REVIEW'] || 0,
      pendingDelivery: statsMap['PENDING_DELIVERY'] || 0,
      completed: statsMap['COMPLETED'] || 0,
      expired: statsMap['EXPIRED'] || 0,
      cancelled: statsMap['CANCELLED'] || 0,
    };

    // 6. 获取拒付类型和支付方式数据
    const [chargebackTypes, paymentMethods] = await Promise.all([
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    // 创建ID到名称的映射
    const chargebackTypeMap = chargebackTypes.reduce(
      (map, type) => {
        map[type.id] = type.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const paymentMethodMap = paymentMethods.reduce(
      (map, method) => {
        map[method.id] = method.name;
        return map;
      },
      {} as Record<string, string>,
    );

    // 7. 获取扩展字段（使用原生查询，强制UTC时区避免转换问题）
    const taskIds = tasks.map(task => task.id);
    const extendedData = await prisma.$queryRaw<
      Array<{
        id: string;
        logisticsDeadline: string | null;
        logisticsReviewDeadline: string | null;
        deliveryDeadline: string | null;
        trackingNumber: string | null;
        logisticsScreenshots: string[];
        reviewedAt: string | null;
        reviewRejectReason: string | null;
        orderNumber: string | null;
        orderScreenshot: string | null;
      }>
    >`
      SELECT 
        id, 
        CASE 
          WHEN "logisticsDeadline" IS NOT NULL 
          THEN to_char("logisticsDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "logisticsDeadline",
        CASE 
          WHEN "logisticsReviewDeadline" IS NOT NULL 
          THEN to_char("logisticsReviewDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "logisticsReviewDeadline",
        CASE 
          WHEN "deliveryDeadline" IS NOT NULL 
          THEN to_char("deliveryDeadline" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "deliveryDeadline",
        "trackingNumber", 
        "logisticsScreenshots", 
        CASE 
          WHEN "reviewedAt" IS NOT NULL 
          THEN to_char("reviewedAt" AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
          ELSE NULL 
        END as "reviewedAt", 
        "reviewRejectReason", 
        "orderNumber", 
        "orderScreenshot"
      FROM tasks 
      WHERE id = ANY(${taskIds}::text[])
    `;

    // 创建扩展数据映射
    const extendedMap = extendedData.reduce(
      (map, item) => {
        map[item.id] = {
          logisticsDeadline: item.logisticsDeadline,
          logisticsReviewDeadline: item.logisticsReviewDeadline,
          deliveryDeadline: item.deliveryDeadline,
          trackingNumber: item.trackingNumber,
          logisticsScreenshots: item.logisticsScreenshots || [],
          reviewedAt: item.reviewedAt,
          reviewRejectReason: item.reviewRejectReason,
          orderNumber: item.orderNumber,
          orderScreenshot: item.orderScreenshot,
        };
        return map;
      },
      {} as Record<
        string,
        {
          logisticsDeadline: string | null;
          logisticsReviewDeadline: string | null;
          deliveryDeadline: string | null;
          trackingNumber: string | null;
          logisticsScreenshots: string[];
          reviewedAt: string | null;
          reviewRejectReason: string | null;
          orderNumber: string | null;
          orderScreenshot: string | null;
        }
      >,
    );

    // 8. 处理委托数据
    const processedTasks = tasks.map(task => {
      // 从映射中获取扩展数据
      const taskExtendedData = extendedMap[task.id] || {
        logisticsDeadline: null,
        logisticsReviewDeadline: null,
        deliveryDeadline: null,
        trackingNumber: null,
        logisticsScreenshots: [],
        reviewedAt: null,
        reviewRejectReason: null,
        orderNumber: null,
        orderScreenshot: null,
      };

      // 计算委托截止时间
      let deadline: Date;
      if (taskExtendedData.reviewRejectReason && taskExtendedData.reviewedAt) {
        // 如果委托被驳回，从审核时间开始计算24小时
        deadline = new Date(
          new Date(taskExtendedData.reviewedAt).getTime() + 24 * 60 * 60 * 1000,
        );
      } else {
        // 正常委托：从接受时间开始计算24小时
        deadline = task.acceptedAt
          ? new Date(task.acceptedAt.getTime() + 24 * 60 * 60 * 1000)
          : new Date(Date.now() + 24 * 60 * 60 * 1000);
      }

      return {
        id: task.id,
        platform: task.platform.name,
        category: task.category.name,
        chargebackTypes: task.chargebackTypeIds.map(
          id => chargebackTypeMap[id] || id,
        ),
        paymentMethods: task.paymentMethodIds.map(
          id => paymentMethodMap[id] || id,
        ),
        // 保留原始ID字段用于酬金计算
        chargebackTypeIds: task.chargebackTypeIds,
        paymentMethodIds: task.paymentMethodIds,
        quantity: task.quantity,
        unitPrice: task.unitPrice,
        totalAmount: task.totalAmount,
        finalTotal: task.finalTotal,
        productUrl: task.productUrl,
        productDescription: task.productDescription,
        recipientName: task.recipientName,
        recipientPhone: task.recipientPhone,
        shippingAddress: task.shippingAddress,
        listingTime: task.listingTime,
        status: task.status,
        evidenceStatus: task.evidenceStatus || 'PENDING_SUBMISSION',
        evidenceRejectReason: task.evidenceRejectReason,
        evidenceUploadType: task.evidenceUploadType,
        evidenceFiles: task.evidenceFiles,
        cartScreenshots: task.cartScreenshots,
        publisher: {
          id: task.publisher.id,
          nickname: task.publisher.name || '未知用户',
          email: task.publisher.email || '',
        },
        createdAt: task.createdAt.toISOString(),
        updatedAt: task.updatedAt.toISOString(),
        publishedAt: task.publishedAt?.toISOString(),
        acceptedAt: task.acceptedAt?.toISOString(),
        expiresAt: task.expiresAt?.toISOString(),
        completedAt: task.completedAt?.toISOString(),
        cancelledAt:
          task.status === 'CANCELLED'
            ? task.updatedAt.toISOString()
            : undefined,
        deadline: deadline.toISOString(),
        logisticsDeadline: taskExtendedData.logisticsDeadline,
        logisticsReviewDeadline: taskExtendedData.logisticsReviewDeadline,
        deliveryDeadline: taskExtendedData.deliveryDeadline,
        trackingNumber: taskExtendedData.trackingNumber,
        logisticsScreenshots: taskExtendedData.logisticsScreenshots,
        reviewedAt: taskExtendedData.reviewedAt,
        reviewRejectReason: taskExtendedData.reviewRejectReason,
        orderNumber: taskExtendedData.orderNumber,
        orderScreenshot: taskExtendedData.orderScreenshot,
      };
    });

    // 9. 返回响应
    return NextResponse.json({
      success: true,
      data: {
        tasks: processedTasks,
        stats: processedStats,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
