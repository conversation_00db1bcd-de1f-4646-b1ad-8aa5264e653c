#!/usr/bin/env pwsh
# 同步 GitLab 和 GitHub 仓库的所有分支

Write-Host "🔄 开始同步 GitLab 和 GitHub 仓库..." -ForegroundColor Green

# 获取所有远程分支
Write-Host "📡 获取最新的远程分支信息..." -ForegroundColor Yellow
git fetch --all

# 获取 GitLab (origin) 的所有分支
$originBranches = git branch -r | Where-Object { $_ -match "origin/" -and $_ -notmatch "origin/HEAD" } | ForEach-Object { $_.Trim().Replace("origin/", "") }

# 获取 GitHub (github) 的所有分支
$githubBranches = git branch -r | Where-Object { $_ -match "github/" -and $_ -notmatch "github/HEAD" } | ForEach-Object { $_.Trim().Replace("github/", "") }

Write-Host "📋 GitLab 分支: $($originBranches -join ', ')" -ForegroundColor Cyan
Write-Host "📋 GitHub 分支: $($githubBranches -join ', ')" -ForegroundColor Cyan

# 找出需要同步到 GitHub 的分支
$branchesToSync = $originBranches | Where-Object { $_ -notin $githubBranches }

if ($branchesToSync.Count -eq 0) {
    Write-Host "✅ 所有分支已同步！" -ForegroundColor Green
} else {
    Write-Host "🔄 需要同步的分支: $($branchesToSync -join ', ')" -ForegroundColor Yellow
    
    foreach ($branch in $branchesToSync) {
        Write-Host "📤 正在推送分支: $branch" -ForegroundColor Blue
        try {
            # 直接推送远程分支到 GitHub
            git push github "refs/remotes/origin/${branch}:refs/heads/${branch}"
            Write-Host "✅ 成功推送分支: $branch" -ForegroundColor Green
        } catch {
            Write-Host "❌ 推送分支失败: $branch - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 同步现有分支的最新提交
Write-Host "🔄 同步现有分支的最新提交..." -ForegroundColor Yellow
foreach ($branch in $githubBranches) {
    if ($branch -in $originBranches) {
        Write-Host "📤 更新分支: $branch" -ForegroundColor Blue
        try {
            git push github "refs/remotes/origin/${branch}:refs/heads/${branch}"
            Write-Host "✅ 成功更新分支: $branch" -ForegroundColor Green
        } catch {
            Write-Host "❌ 更新分支失败: $branch - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "🎉 仓库同步完成！" -ForegroundColor Green
Write-Host "💡 提示: 现在你可以使用 'git push origin' 来同时推送到两个仓库" -ForegroundColor Cyan
