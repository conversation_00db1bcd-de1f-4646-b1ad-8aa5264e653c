import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/db';
import { logDepositEmailDelivery } from '@/lib/email-logging';
import { notifyDepositSuccess, notifyDepositFailure } from '@/lib/financial-email-integration';
import { PaymentManager } from '@/lib/payment/payment-manager';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ provider: string }> },
) {
  try {
    const { provider } = await params;
    const body = await request.text();
    const headers = Object.fromEntries(request.headers);

    let orderNo, tradeNo, money, tradeStatus, sign;
    let notifyData: any;

    if (provider === 'nowpayments') {
      // NOWPayments使用JSON格式回调
      try {
        notifyData = JSON.parse(body);
        orderNo = notifyData.order_id;
        tradeNo = notifyData.payment_id?.toString();
        money = notifyData.price_amount;
        tradeStatus = notifyData.payment_status;
        sign = headers['x-nowpayments-sig'];
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid JSON format' },
          { status: 400 },
        );
      }
    } else {
      // 聚合易支付使用URL编码数据
      const formData = new URLSearchParams(body);
      orderNo = formData.get('out_trade_no');
      tradeNo = formData.get('trade_no');
      money = formData.get('money');
      tradeStatus = formData.get('trade_status');
      sign = formData.get('sign');
      notifyData = {
        out_trade_no: orderNo,
        trade_no: tradeNo,
        money,
        trade_status: tradeStatus,
        sign,
      };
    }

    return await processPaymentNotify(
      orderNo,
      tradeNo,
      money,
      tradeStatus,
      sign,
      notifyData,
      provider,
    );
  } catch (error) {
    return await handleError(error);
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ provider: string }> },
) {
  try {
    const { provider } = await params;
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    let orderNo, tradeNo, money, tradeStatus, sign;
    let notifyData: any;

    if (provider === 'yunpay') {
      // 聚合易支付使用GET方法，参数通过URL传递
      orderNo = searchParams.get('out_trade_no');
      tradeNo = searchParams.get('trade_no');
      money = searchParams.get('money');
      tradeStatus = searchParams.get('trade_status');
      sign = searchParams.get('sign');

      notifyData = {
        pid: searchParams.get('pid'),
        out_trade_no: orderNo,
        trade_no: tradeNo,
        type: searchParams.get('type'),
        name: searchParams.get('name'),
        money,
        trade_status: tradeStatus,
        sign,
        sign_type: searchParams.get('sign_type'),
      };
    } else {
      return NextResponse.json(
        { error: 'GET method not supported for this provider' },
        { status: 405 },
      );
    }

    return await processPaymentNotify(
      orderNo,
      tradeNo,
      money,
      tradeStatus,
      sign,
      notifyData,
      provider,
    );
  } catch (error) {
    return await handleError(error);
  }
}

async function processPaymentNotify(
  orderNo: string | null,
  tradeNo: string | null,
  money: string | null,
  tradeStatus: string | null,
  sign: string | null,
  notifyData: any,
  provider: string,
) {
  if (!orderNo) {
    return NextResponse.json(
      { error: 'Order number required' },
      { status: 400 },
    );
  }

  // 确保 orderNo 不是 null
  const validOrderNo = orderNo as string;

  // 查找订单
  const order = await prisma.paymentOrder.findUnique({
    where: { orderNo: validOrderNo },
  });

  if (!order) {
    await prisma.paymentLog.create({
      data: {
        orderNo: validOrderNo,
        action: 'NOTIFY',
        request: { notifyData, provider },
        status: 'FAILED',
        message: 'Order not found',
      },
    });
    return NextResponse.json({ error: 'Order not found' }, { status: 404 });
  }

  // 检查是否是模拟支付
  const isMockPayment =
    tradeNo?.startsWith('MOCK_') || sign === 'mock_signature';

  let paymentStatus: string;

  if (isMockPayment) {
    // 模拟支付处理
    if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'finished') {
      paymentStatus = 'PAID';
    } else {
      paymentStatus = 'FAILED';
    }
  } else {
    // 真实支付需要验证签名等
    // 获取支付配置
    const config = await prisma.paymentConfig.findUnique({
      where: {
        provider,
        isEnabled: true,
      },
    });

    if (!config) {
      return NextResponse.json(
        { error: 'Provider not found' },
        { status: 404 },
      );
    }

    // 初始化支付管理器进行验证
    const paymentManager = new PaymentManager();
    const providerInstance = await paymentManager.getProvider(provider);

    if (!providerInstance) {
      return NextResponse.json(
        { error: 'Provider unavailable' },
        { status: 500 },
      );
    }

    // 验证回调签名（真实支付）
    const isValid = providerInstance.verifyNotify(notifyData);

    if (!isValid) {
      await prisma.paymentLog.create({
        data: {
          orderNo: validOrderNo,
          action: 'NOTIFY',
          request: { notifyData, provider },
          status: 'FAILED',
          message: 'Invalid signature',
        },
      });
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // 映射真实支付状态
    if (provider === 'nowpayments') {
      // NOWPayments状态映射
      if (tradeStatus === 'finished') {
        paymentStatus = 'PAID';
      } else if (
        tradeStatus &&
        ['failed', 'expired', 'refunded'].includes(tradeStatus)
      ) {
        paymentStatus = 'FAILED';
      } else {
        paymentStatus = 'PENDING';
      }
    } else {
      // 聚合易支付状态映射
      if (tradeStatus === 'TRADE_SUCCESS') {
        paymentStatus = 'PAID';
      } else {
        paymentStatus = 'FAILED';
      }
    }
  }

  // 检查金额是否匹配（如果提供了金额）
  if (money) {
    let amountMatches = false;
    const callbackAmount = parseFloat(money);
    const orderAmount = parseFloat(order.amount.toString());

    if (provider === 'yunpay') {
      // YunPay 特殊处理：回调金额是 CNY，订单金额是 USD
      // 需要进行货币转换比较
      try {
        console.log(`🔍 YunPay 金额验证: 回调金额=${callbackAmount} CNY, 订单金额=${orderAmount} USD`);

        // 导入货币转换器
        const { currencyConverter } = await import('@/lib/payment/currency-converter');

        // 将 CNY 转换为 USD 进行比较
        const conversion = await currencyConverter.convertCurrency(
          callbackAmount,
          'CNY',
          'USD',
        );

        const convertedAmount = conversion.convertedAmount;
        console.log(`💱 货币转换: ${callbackAmount} CNY -> ${convertedAmount} USD (汇率: ${conversion.exchangeRate})`);

        // 允许 2% 的汇率波动误差（考虑汇率变化和精度问题）
        const tolerance = orderAmount * 0.02;
        const difference = Math.abs(convertedAmount - orderAmount);

        console.log(`📊 金额比较: |${convertedAmount} - ${orderAmount}| = ${difference}, 容忍度: ${tolerance}`);

        amountMatches = difference <= tolerance;

        if (amountMatches) {
          console.log('✅ YunPay 金额验证通过');
        } else {
          console.log('❌ YunPay 金额验证失败');
        }

      } catch (error) {
        console.error('YunPay 货币转换失败:', error);
        // 转换失败时，使用更宽松的验证（5% 容忍度）
        const tolerance = orderAmount * 0.05;
        const difference = Math.abs(callbackAmount - orderAmount);
        amountMatches = difference <= tolerance;

        console.log(`⚠️ 使用备用验证: 差异=${difference}, 容忍度=${tolerance}, 结果=${amountMatches}`);
      }
    } else {
      // 其他支付提供商的标准金额比较
      amountMatches = Math.abs(callbackAmount - orderAmount) <= 0.01;
    }

    if (!amountMatches) {
      await prisma.paymentLog.create({
        data: {
          orderNo: validOrderNo,
          action: 'NOTIFY',
          request: { notifyData, provider },
          status: 'FAILED',
          message: `Amount mismatch: callback=${callbackAmount}, order=${orderAmount}, provider=${provider}`,
        },
      });
      return NextResponse.json({ error: 'Amount mismatch' }, { status: 400 });
    }
  }

  // 更新订单状态
  const updateData: any = {
    status: paymentStatus as any,
    updatedAt: new Date(),
  };

  if (paymentStatus === 'PAID') {
    updateData.paidAt = new Date();

    // 处理不同类型的订单
    if (validOrderNo.startsWith('DEPOSIT_') && order.userId) {
      // 充值订单处理
      const metadata = order.metadata as any;
      const depositAmount = metadata?.depositAmount || order.amount;

      // 更新用户余额（使用实际充值金额，不包含手续费）
      await prisma.user.update({
        where: { id: order.userId },
        data: {
          balance: {
            increment: depositAmount,
          },
        },
      });

      // 更新钱包交易状态
      await prisma.walletTransaction.updateMany({
        where: { reference: validOrderNo },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
        },
      });

      // 发送充值成功邮件通知
      try {
        const user = await prisma.user.findUnique({
          where: { id: order.userId },
          select: {
            id: true,
            name: true,
            email: true,
            registrationLanguage: true,
            balance: true,
          },
        });

        if (user?.email) {
          const depositResult = {
            success: true,
            transactionId: tradeNo || validOrderNo,
            amount: depositAmount,
            currency: order.currency || 'USD',
            paymentMethod: order.paymentMethod || 'Unknown',
            processedAt: new Date().toISOString(),
            newBalance: user.balance,
          };

          const emailResult = await notifyDepositSuccess(
            {
              id: user.id,
              name: user.name || 'User',
              email: user.email,
              registrationLanguage: user.registrationLanguage as 'zh' | 'en',
            },
            depositResult,
          );

          // Log email delivery status
          await logDepositEmailDelivery(
            validOrderNo,
            user.email,
            'deposit-success',
            emailResult,
          );

          console.log(`✅ Deposit success email processed for order ${validOrderNo}, user: ${user.email}, success: ${emailResult.success}`);
        } else {
          console.warn(`⚠️ No user email found for deposit order ${validOrderNo}, user ID: ${order.userId}`);
        }
      } catch (emailError) {
        console.error(`❌ Failed to send deposit success email for order ${validOrderNo}:`, emailError);
        // Continue webhook processing - don't fail due to email issues
      }
    } else if (
      (validOrderNo.startsWith('MEMBERSHIP_UPGRADE_') ||
        validOrderNo.startsWith('MEMBERSHIP_RENEW_')) &&
      order.userId
    ) {
      // 会员升级/续费订单处理
      const metadata = order.metadata as any;
      const action = metadata?.action; // 'upgrade' 或 'renew'
      const planId = metadata?.planId;
      const planName = metadata?.planName;

      if (action && planId && planName) {
        // 获取会员套餐信息
        const plan = await prisma.membershipPlan.findFirst({
          where: { id: planId, isActive: true },
        });

        if (plan) {
          // 确定新的会员等级
          const newMemberPlan =
            plan.name === '专业版'
              ? 'PRO'
              : plan.name === '商业版'
                ? 'BUSINESS'
                : 'FREE';

          let newExpiry: Date;

          if (action === 'upgrade') {
            // 升级：从当前时间开始计算
            newExpiry = new Date();
            newExpiry.setMonth(newExpiry.getMonth() + 1);
          } else {
            // 续费：从当前过期时间开始计算
            const user = await prisma.user.findUnique({
              where: { id: order.userId },
              select: { memberPlanExpiry: true },
            });

            const currentExpiry = user?.memberPlanExpiry || new Date();
            newExpiry = new Date(currentExpiry);

            // 如果已过期，从今天开始；否则从当前有效期开始
            if (currentExpiry < new Date()) {
              newExpiry.setTime(Date.now());
            }

            newExpiry.setMonth(newExpiry.getMonth() + 1);
          }

          // 更新用户会员信息
          await prisma.user.update({
            where: { id: order.userId },
            data: {
              memberPlan: newMemberPlan as any,
              memberPlanExpiry: newExpiry,
            },
          });

          // 更新钱包交易状态
          await prisma.walletTransaction.updateMany({
            where: { reference: validOrderNo },
            data: {
              status: 'COMPLETED',
              completedAt: new Date(),
            },
          });

          console.log(
            `[会员支付成功] 用户 ${order.userId} ${action === 'upgrade' ? '升级到' : '续费'} ${plan.name}，有效期至 ${newExpiry.toISOString()}`,
          );
        }
      }
    }
  } else if (paymentStatus === 'FAILED' && validOrderNo.startsWith('DEPOSIT_') && order.userId) {
    // 处理充值失败的邮件通知
    try {
      const user = await prisma.user.findUnique({
        where: { id: order.userId },
        select: {
          id: true,
          name: true,
          email: true,
          registrationLanguage: true,
        },
      });

      if (user?.email) {
        const metadata = order.metadata as any;
        const depositAmount = metadata?.depositAmount || order.amount;

        const depositResult = {
          success: false,
          transactionId: tradeNo || validOrderNo,
          amount: depositAmount,
          currency: order.currency || 'USD',
          paymentMethod: order.paymentMethod || 'Unknown',
          processedAt: new Date().toISOString(),
          failureReason: 'Payment processing failed',
        };

        const emailResult = await notifyDepositFailure(
          {
            id: user.id,
            name: user.name || 'User',
            email: user.email,
            registrationLanguage: user.registrationLanguage as 'zh' | 'en',
          },
          depositResult,
        );

        // Log email delivery status
        await logDepositEmailDelivery(
          validOrderNo,
          user.email,
          'deposit-failed',
          emailResult,
        );

        console.log(`✅ Deposit failure email processed for order ${validOrderNo}, user: ${user.email}, success: ${emailResult.success}`);
      } else {
        console.warn(`⚠️ No user email found for failed deposit order ${validOrderNo}, user ID: ${order.userId}`);
      }
    } catch (emailError) {
      console.error(`❌ Failed to send deposit failure email for order ${validOrderNo}:`, emailError);
      // Continue webhook processing - don't fail due to email issues
    }
  }

  if (tradeNo) {
    updateData.thirdOrderNo = tradeNo;
  }

  await prisma.paymentOrder.update({
    where: { orderNo: validOrderNo },
    data: updateData,
  });

  // 记录成功日志
  await prisma.paymentLog.create({
    data: {
      orderNo: validOrderNo,
      action: 'NOTIFY',
      request: { notifyData, provider },
      response: {
        orderNo: validOrderNo,
        tradeNo,
        money,
        tradeStatus,
        paymentStatus,
      },
      status: 'SUCCESS',
      message: `Payment status updated to ${paymentStatus}`,
    },
  });

  // 根据不同提供商返回相应格式
  if (provider === 'yunpay') {
    return new NextResponse('success', { status: 200 });
  } else if (provider === 'nowpayments') {
    return NextResponse.json({ success: true });
  }

  return NextResponse.json({ success: true });
}

async function handleError(error: unknown) {
  console.error(
    '支付回调处理异常:',
    error instanceof Error ? error.message : 'Unknown error',
  );

  // 记录错误日志
  try {
    await prisma.paymentLog.create({
      data: {
        orderNo: 'ERROR',
        action: 'NOTIFY',
        request: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        status: 'FAILED',
        message: 'Notification processing failed',
      },
    });
  } catch (logError) {
    console.error('记录错误日志失败:', logError);
  }

  return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
}
