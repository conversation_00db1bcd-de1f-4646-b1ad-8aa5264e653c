import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { hashPassword, verifyPassword } from '@/lib/password';

// 修改密码验证Schema - 与注册时相同的规则
const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, '请输入当前密码'),
    newPassword: z
      .string()
      .min(6, '密码长度至少6位')
      .max(32, '密码长度不能超过32位')
      .regex(/^[a-zA-Z0-9]+$/, '密码只能包含字母和数字'),
    confirmPassword: z.string().min(1, '请确认新密码'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  });

export async function POST(request: NextRequest) {
  try {
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const body = await request.json();
    const validation = changePasswordSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: '数据验证失败',
          details: validation.error.issues.map(issue => issue.message),
        },
        { status: 400 },
      );
    }

    const { currentPassword, newPassword } = validation.data;

    // 获取用户当前密码
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        password: true,
      },
    });

    if (!user || !user.password) {
      return NextResponse.json(
        { success: false, message: '用户不存在或密码未设置' },
        { status: 404 },
      );
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyPassword(
      currentPassword,
      user.password,
    );
    if (!isCurrentPasswordValid) {
      return NextResponse.json(
        { success: false, message: '当前密码不正确' },
        { status: 400 },
      );
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await verifyPassword(newPassword, user.password);
    if (isSamePassword) {
      return NextResponse.json(
        { success: false, message: '新密码不能与当前密码相同' },
        { status: 400 },
      );
    }

    // 加密新密码
    const hashedNewPassword = await hashPassword(newPassword);

    // 更新密码
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: '密码修改成功',
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
