'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

interface CacheEntry {
  currencyPair: string;
  rate: number;
  source: string;
  timestamp: number;
  expiresAt: number;
  isExpired: boolean;
  remainingTime: number;
}

interface ConversionResult {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  source: string;
}

export default function CurrencyManagementPage() {
  const [amount, setAmount] = useState('100');
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('CNY');
  const queryClient = useQueryClient();

  // 获取缓存信息
  const { data: cacheData, isLoading: isLoadingCache } = useQuery({
    queryKey: ['currency-cache'],
    queryFn: async () => {
      const response = await fetch('/api/admin/currency/cache');
      if (!response.ok) throw new Error('Failed to fetch cache');
      return response.json();
    },
    refetchInterval: 30000, // 每30秒刷新
  });

  // 清除缓存
  const clearCacheMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/admin/currency/cache', {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to clear cache');
      return response.json();
    },
    onSuccess: () => {
      toast.success('缓存已清除');
      queryClient.invalidateQueries({ queryKey: ['currency-cache'] });
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : '清除缓存失败');
    },
  });

  // 测试货币转换
  const convertMutation = useMutation({
    mutationFn: async (data: { amount: number; from: string; to: string }) => {
      const response = await fetch('/api/admin/currency/convert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to convert currency');
      return response.json();
    },
    onSuccess: () => {
      toast.success('货币转换成功');
      queryClient.invalidateQueries({ queryKey: ['currency-cache'] });
    },
    onError: error => {
      toast.error(error instanceof Error ? error.message : '货币转换失败');
    },
  });

  const handleConvert = () => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      toast.error('请输入有效的金额');
      return;
    }

    convertMutation.mutate({
      amount: numAmount,
      from: fromCurrency.toUpperCase(),
      to: toCurrency.toUpperCase(),
    });
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}分${seconds}秒`;
  };

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <div className='flex items-center justify-between'>
        <h1 className='text-3xl font-bold'>货币转换管理</h1>
      </div>

      {/* 货币转换测试 */}
      <Card>
        <CardHeader>
          <CardTitle>测试货币转换</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
              <Label htmlFor='amount'>金额</Label>
              <Input
                id='amount'
                type='number'
                value={amount}
                onChange={e => setAmount(e.target.value)}
                placeholder='100'
              />
            </div>
            <div>
              <Label htmlFor='from'>从</Label>
              <Input
                id='from'
                value={fromCurrency}
                onChange={e => setFromCurrency(e.target.value)}
                placeholder='USD'
                maxLength={3}
              />
            </div>
            <div>
              <Label htmlFor='to'>到</Label>
              <Input
                id='to'
                value={toCurrency}
                onChange={e => setToCurrency(e.target.value)}
                placeholder='CNY'
                maxLength={3}
              />
            </div>
            <div className='flex items-end'>
              <Button
                onClick={handleConvert}
                disabled={convertMutation.isPending}
                className='w-full'
              >
                {convertMutation.isPending ? '转换中...' : '转换'}
              </Button>
            </div>
          </div>

          {convertMutation.data?.data && (
            <div className='mt-4 p-4 bg-muted rounded-lg'>
              <h4 className='font-semibold mb-2'>转换结果:</h4>
              <div className='space-y-1 text-sm'>
                <p>
                  {convertMutation.data.data.originalAmount}{' '}
                  {convertMutation.data.data.originalCurrency} ={' '}
                  {convertMutation.data.data.convertedAmount}{' '}
                  {convertMutation.data.data.convertedCurrency}
                </p>
                <p>汇率: {convertMutation.data.data.exchangeRate}</p>
                <p>来源: {convertMutation.data.data.source}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 缓存管理 */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            汇率缓存
            <Button
              variant='outline'
              onClick={() => clearCacheMutation.mutate()}
              disabled={clearCacheMutation.isPending}
            >
              {clearCacheMutation.isPending ? '清除中...' : '清除缓存'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingCache ? (
            <p>加载中...</p>
          ) : cacheData?.data?.cacheEntries?.length > 0 ? (
            <div className='space-y-4'>
              <p className='text-sm text-muted-foreground'>
                缓存条目数: {cacheData.data.cacheCount}
              </p>
              <Separator />
              <div className='space-y-3'>
                {cacheData.data.cacheEntries.map((entry: CacheEntry) => (
                  <div
                    key={entry.currencyPair}
                    className='flex items-center justify-between p-3 border rounded-lg'
                  >
                    <div>
                      <div className='font-medium'>{entry.currencyPair}</div>
                      <div className='text-sm text-muted-foreground'>
                        汇率: {entry.rate.toFixed(6)}
                      </div>
                    </div>
                    <div className='text-right space-y-1'>
                      <Badge
                        variant={entry.isExpired ? 'destructive' : 'default'}
                      >
                        {entry.source}
                      </Badge>
                      <div className='text-xs text-muted-foreground'>
                        {entry.isExpired
                          ? '已过期'
                          : `剩余: ${formatDuration(entry.remainingTime)}`}
                      </div>
                      <div className='text-xs text-muted-foreground'>
                        更新: {formatTime(entry.timestamp)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p className='text-muted-foreground'>暂无缓存数据</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
