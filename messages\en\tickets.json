{"title": "Ticket Management", "breadcrumb": "Ticket List", "description": "Submit feedback and track ticket processing status", "navigation": {"title": "Ticket Management", "description": "Submit feedback and track ticket processing status", "breadcrumb": "Ticket List"}, "types": {"TECHNICAL": "Technical Support", "BILLING": "Billing Issues", "ACCOUNT": "Account Issues", "FEATURE_REQUEST": "Feature Request", "BUG_REPORT": "Bug Report", "OTHER": "Other"}, "status": {"PENDING": "Pending", "IN_PROGRESS": "In Progress", "RESOLVED": "Resolved", "CLOSED": "Closed", "CANCELLED": "Cancelled"}, "priority": {"LOW": "Low", "MEDIUM": "Medium", "HIGH": "High", "URGENT": "<PERSON><PERSON>"}, "actions": {"submit": "Submit Ticket", "cancel": "Cancel", "reply": "Reply", "close": "Close", "reopen": "Reopen", "view": "View Details", "edit": "Edit", "delete": "Delete"}, "form": {"dialogTitle": "Submit New Ticket", "dialogDescription": "Please describe your issue in detail, and we will process it as soon as possible", "title": "Ticket Title", "titlePlaceholder": "Please briefly describe the issue", "type": "Issue Type", "typePlaceholder": "Select issue type", "priority": "Priority", "priorityPlaceholder": "Select priority", "description": "Detailed Description", "descriptionPlaceholder": "Please describe your issue in detail, including steps, error messages, etc.", "attachments": "Attachments", "attachmentsHint": "Support uploading images, documents, etc., max 10MB", "submit": "Submit Ticket", "submitting": "Submitting...", "cancel": "Cancel", "reset": "Reset"}, "search": {"placeholder": "Search ticket title or content...", "noResults": "No matching tickets found", "resultsCount": "Found {count} tickets"}, "filters": {"all": "All", "byStatus": "Filter by Status", "byType": "Filter by Type", "byPriority": "Filter by Priority", "dateRange": "Date Range", "apply": "Apply Filters", "reset": "Reset Filters"}, "list": {"empty": "No Tickets", "emptySearch": "No matching tickets found", "emptyDescription": "You haven't submitted any tickets yet", "ticketId": "Ticket ID", "createdAt": "Created At", "updatedAt": "Last Updated", "assignedTo": "Assigned To"}, "details": {"title": "Ticket Details", "basicInfo": "Basic Information", "ticketId": "Ticket ID", "status": "Status", "type": "Type", "priority": "Priority", "creator": "Creator", "assignee": "Assignee", "createdAt": "Created At", "updatedAt": "Last Updated", "description": "Issue Description", "attachments": "Attachments", "replies": "Reply History", "noReplies": "No replies yet", "replyPlaceholder": "Please enter your reply...", "addReply": "Add Reply", "statusHistory": "Status Change History", "staff": "Staff", "sendReply": "Send Reply", "closeTicket": "Close Ticket", "loadingDetails": "Loading ticket details...", "createdLabel": "Created: ", "updatedLabel": "Updated: ", "repliesCount": " replies", "createdAtLabel": "Created At: ", "lastUpdatedLabel": "Last Updated: "}, "messages": {"submitSuccess": "Ticket submitted successfully", "submitError": "Failed to submit ticket", "replySuccess": "Reply sent successfully", "replyError": "Failed to send reply", "statusUpdateSuccess": "Ticket status updated successfully", "statusUpdateError": "Failed to update ticket status", "closeSuccess": "Ticket closed successfully", "closeError": "Failed to close ticket", "deleteSuccess": "Ticket deleted successfully", "deleteError": "Failed to delete ticket", "loading": "Loading...", "loadError": "Failed to load, please try again", "networkError": "Network error, please try again", "completeInfoRequired": "Please fill in complete information", "titleDescriptionRequired": "Title and description cannot be empty", "replyContentRequired": "Please enter reply content", "invalidTransition": "Invalid status transition"}, "validation": {"titleRequired": "Title is required", "titleMaxLength": "Title cannot exceed 200 characters", "descriptionRequired": "Description is required", "descriptionMinLength": "Description must be at least 10 characters", "descriptionMaxLength": "Description cannot exceed 5000 characters", "typeRequired": "Please select an issue type", "priorityRequired": "Please select a priority", "replyRequired": "Reply content is required", "replyMaxLength": "Reply content cannot exceed 5000 characters"}, "tabs": {"all": "All Tickets", "pending": "Pending", "inProgress": "In Progress", "resolved": "Resolved", "closed": "Closed"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "pages": "pages", "showing": "Showing", "to": "to", "entries": "entries of", "total": "total"}, "hooks": {"createTicket": {"success": {"title": "Tick<PERSON> submitted"}, "error": {"description": "Failed to create ticket"}}}}