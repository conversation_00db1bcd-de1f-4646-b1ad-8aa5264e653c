import React from 'react';

/**
 * 全局加载组件
 * 当页面正在加载时显示的 UI
 */
export default function Loading() {
  return (
    <div className='flex min-h-screen items-center justify-center'>
      <div className='flex flex-col items-center space-y-4'>
        {/* 加载动画 */}
        <div className='relative'>
          <div className='h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600'></div>
        </div>

        {/* 加载文本 */}
        <div className='text-center'>
          <h2 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>
            加载中...
          </h2>
          <p className='text-sm text-gray-500 dark:text-gray-400'>
            请稍候，正在为您准备内容
          </p>
        </div>
      </div>
    </div>
  );
}
