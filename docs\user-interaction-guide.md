# RefundGo 用户交互流程指南

## 概述

本文档详细描述了 RefundGo 平台的主要用户交互流程，包括用户注册、委托管理、支付流程、会员服务等核心业务场景的操作步骤和注意事项。

## 目录

1. [用户注册和认证流程](#用户注册和认证流程)
2. [委托发布流程](#委托发布流程)
3. [委托接单流程](#委托接单流程)
4. [委托执行流程](#委托执行流程)
5. [支付和充值流程](#支付和充值流程)
6. [提现流程](#提现流程)
7. [会员升级流程](#会员升级流程)
8. [工单系统流程](#工单系统流程)
9. [管理员审核流程](#管理员审核流程)

---

## 用户注册和认证流程

### 1.1 新用户注册

**流程步骤**：

1. 访问平台主页，点击"注册"按钮
2. 选择注册方式：
   - 邮箱注册（填写邮箱、密码、确认密码）
   - OAuth登录（Google、GitHub等）
3. 填写基本信息：
   - 用户名（唯一标识）
   - 显示名称
   - 联系方式
4. 阅读并同意服务条款和隐私政策
5. 提交注册申请
6. 邮箱验证（如适用）
7. 注册完成，自动登录

**系统行为**：

- 自动检测用户浏览器语言设置
- 记录用户注册语言偏好
- 创建用户钱包账户（初始余额为0）
- 分配默认用户角色（USER）
- 发送欢迎邮件

**注意事项**：

- 用户名和邮箱必须唯一
- 密码需符合安全要求（8位以上，包含字母和数字）
- OAuth登录会自动创建账户

### 1.2 用户登录

**流程步骤**：

1. 访问登录页面
2. 选择登录方式：
   - 邮箱/用户名 + 密码
   - OAuth快速登录
3. 输入凭据信息
4. 可选：记住登录状态
5. 点击登录按钮
6. 系统验证并跳转到仪表盘

**系统行为**：

- 验证用户凭据
- 创建会话令牌
- 记录登录时间和设备信息
- 根据用户角色跳转到相应界面

---

## 委托发布流程

### 2.1 委托发布向导

**前置条件**：

- 用户已登录
- 钱包余额充足（覆盖委托费用和酬金）

**流程步骤**：

**第一步：基本信息**

1. 进入"发布委托"页面
2. 填写委托基本信息：
   - 委托标题（可选，系统可自动生成）
   - 商品链接
   - 商品描述
   - 购买数量
   - 单价金额

**第二步：分类设置** 3. 选择委托分类：

- 电商平台（Amazon、eBay、DHgate等）
- 商品类别（电子产品、服装、家居等）
- 拒付类型（未收到货、商品不符等）
- 支付方式（信用卡、PayPal等）

**第三步：收货信息** 4. 填写收货详情：

- 收件人姓名
- 联系电话
- 详细地址

**第四步：证据要求** 5. 设置证据上传要求：

- 立即上传证据
- 稍后上传证据
- 无需证据

**第五步：费用确认** 6. 系统计算总费用：

- 商品总价 = 单价 × 数量
- 平台酬金（根据分类和支付方式计算）
- 证据费（如适用）
- 最终总额

7. 确认费用并提交

**系统行为**：

- 验证用户余额是否充足
- 冻结相应金额（证据费进入冻结余额）
- 创建委托记录，状态为"PENDING"（待审核）
- 扣除非证据费用
- 发送委托创建确认邮件

### 2.2 委托状态跟踪

**委托状态流转**：

```
PENDING (待审核)
    ↓ [管理员审核通过]
RECRUITING (招募中)
    ↓ [用户接单]
IN_PROGRESS (进行中)
    ↓ [接单者提交订单]
PENDING_LOGISTICS (等待物流)
    ↓ [接单者提交物流]
PENDING_REVIEW (等待审核)
    ↓ [发布者审核通过]
PENDING_DELIVERY (等待收货)
    ↓ [发布者确认收货]
COMPLETED (已完成)
```

**用户可执行操作**：

- 查看委托详情和进度
- 与接单者沟通
- 审核订单和物流信息
- 确认收货完成委托
- 申请客服介入

---

## 委托接单流程

### 3.1 浏览和筛选委托

**流程步骤**：

1. 进入"委托大厅"
2. 使用筛选条件：
   - 平台类型
   - 商品类别
   - 价格范围
   - 地区要求
   - 证据要求
3. 查看委托列表和详情
4. 评估委托可行性

**系统功能**：

- 实时委托状态更新
- 智能推荐匹配委托
- 委托收藏和关注
- 历史浏览记录

### 3.2 接受委托

**前置条件**：

- 用户钱包余额充足（支付押金）
- 委托状态为"RECRUITING"

**流程步骤**：

1. 点击"接受委托"按钮
2. 系统显示押金要求和委托详情
3. 确认接受条件
4. 系统冻结押金金额
5. 委托状态变更为"IN_PROGRESS"
6. 开始委托执行倒计时

**系统行为**：

- 验证用户余额
- 冻结押金到用户账户
- 更新委托状态和接单者信息
- 发送接单确认邮件给双方
- 创建钱包交易记录

---

## 委托执行流程

### 4.1 订单提交阶段

**时间限制**：接单后24小时内

**流程步骤**：

1. 根据委托要求下单购买
2. 截图保存订单信息
3. 在平台提交订单截图和订单号
4. 等待系统确认

**系统行为**：

- 委托状态更新为"PENDING_LOGISTICS"
- 设置物流提交截止时间（5天）
- 通知发布者订单已提交

### 4.2 物流提交阶段

**时间限制**：订单提交后5天内

**流程步骤**：

1. 获取物流跟踪号
2. 截图物流信息
3. 在平台提交物流截图和跟踪号
4. 等待发布者审核

**系统行为**：

- 委托状态更新为"PENDING_REVIEW"
- 设置发布者审核截止时间
- 发送审核通知邮件

### 4.3 发布者审核阶段

**发布者操作**：

1. 查看订单和物流信息
2. 验证信息真实性和准确性
3. 选择审核结果：
   - 通过：委托进入等待收货阶段
   - 不通过：返回进行中状态，接单者需重新提交

**系统行为**：

- 根据审核结果更新委托状态
- 发送审核结果通知
- 记录审核意见和时间

### 4.4 确认收货阶段

**流程步骤**：

1. 发布者等待商品到货
2. 确认收到商品并验证
3. 在平台确认收货
4. 委托完成，系统结算

**系统行为**：

- 委托状态更新为"COMPLETED"
- 释放接单者押金
- 计算并分配委托酬金
- 处理证据费归属
- 发送完成通知邮件

---

## 支付和充值流程

### 5.1 钱包充值

**支持的支付方式**：

- 支付宝（Alipay）
- 微信支付（WeChat Pay）
- PayPal
- 加密货币（通过NOWPayments）

**流程步骤**：

1. 进入"钱包管理"页面
2. 点击"充值"按钮
3. 选择充值金额（最低$10）
4. 选择支付方式
5. 系统显示手续费和总支付金额
6. 确认充值信息
7. 跳转到支付页面完成支付
8. 支付成功后自动返回平台

**费用计算**：

- 充值金额：用户指定的金额
- 手续费：根据支付方式计算（2-5%）
- 总支付金额：充值金额 + 手续费
- 到账金额：充值金额（手续费不计入余额）

**系统行为**：

- 创建支付订单记录
- 生成唯一订单号
- 调用第三方支付接口
- 监听支付回调通知
- 更新用户余额和交易记录

### 5.2 支付状态处理

**支付状态**：

- PENDING：等待支付
- PROCESSING：支付处理中
- COMPLETED：支付成功
- FAILED：支付失败
- EXPIRED：支付过期

**异常处理**：

- 支付超时：订单自动过期
- 支付失败：显示失败原因，允许重试
- 重复支付：系统自动识别并处理
- 金额不符：人工审核处理

---

## 提现流程

### 6.1 提现申请

**支持的提现方式**：

- 银行卡转账
- USDT (ERC20)
- USDT (TRC20)

**前置条件**：

- 账户余额充足
- 完成身份验证
- 满足最低提现金额要求

**流程步骤**：

1. 进入"钱包管理" → "提现"
2. 选择提现方式
3. 填写提现信息：
   - 提现金额
   - 收款账户信息
   - 备注说明
4. 邮箱验证码确认
5. 提交提现申请
6. 等待管理员审核

**系统行为**：

- 验证提现条件
- 冻结提现金额
- 创建提现申请记录
- 发送申请确认邮件
- 通知管理员审核

### 6.2 提现审核和处理

**管理员审核**：

1. 查看提现申请详情
2. 验证用户身份和账户状态
3. 检查提现信息准确性
4. 决定审核结果：
   - 批准：进入财务处理流程
   - 拒绝：返还冻结金额，通知用户

**处理时效**：

- 银行卡：1-3个工作日
- USDT：24小时内
- 节假日可能延迟

---

## 会员升级流程

### 7.1 会员套餐对比

**套餐类型**：

**免费版**：

- 月委托限额：10个
- 标准酬金费率
- 基础客服支持

**专业版**：

- 月委托限额：100个
- 酬金优惠：85折
- 白名单功能：50个槽位
- 优先客服支持

**商业版**：

- 月委托限额：500个
- 酬金优惠：75折
- 白名单功能：200个槽位
- 专属客服支持

### 7.2 升级流程

**流程步骤**：

1. 进入"会员中心"
2. 查看当前会员状态和套餐对比
3. 选择目标套餐
4. 选择付费周期（月付/年付）
5. 选择支付方式
6. 确认订单信息和费用
7. 完成支付流程
8. 会员权限自动升级

**系统行为**：

- 计算升级费用和优惠
- 创建会员订单
- 处理支付流程
- 更新用户会员等级
- 激活高级功能权限
- 发送升级成功通知

---

## 工单系统流程

### 8.1 创建工单

**工单类型**：

- 技术支持
- 账户问题
- 支付问题
- 委托纠纷
- 功能建议
- 其他问题

**流程步骤**：

1. 进入"工单系统"
2. 点击"创建工单"
3. 选择工单类型和优先级
4. 填写工单标题和详细描述
5. 上传相关截图或文件（可选）
6. 提交工单申请

**系统行为**：

- 生成唯一工单编号
- 分配给相应的客服团队
- 发送创建确认邮件
- 设置响应时间目标

### 8.2 工单处理流程

**状态流转**：

```
PENDING (待处理)
    ↓
IN_PROGRESS (处理中)
    ↓
WAITING_USER (等待用户回复)
    ↓
RESOLVED (已解决)
    ↓
CLOSED (已关闭)
```

**用户操作**：

- 查看工单状态和回复
- 补充信息和材料
- 确认问题解决
- 评价服务质量

**客服操作**：

- 接收和分配工单
- 调查和分析问题
- 回复和解决方案
- 跟进处理结果

---

## 管理员审核流程

### 9.1 委托审核

**审核内容**：

- 委托信息完整性
- 商品链接有效性
- 价格合理性
- 分类准确性
- 风险评估

**审核流程**：

1. 查看待审核委托队列
2. 逐项检查委托信息
3. 验证商品和平台信息
4. 评估委托风险等级
5. 做出审核决定：
   - 通过：委托上架招募
   - 拒绝：返还费用，通知原因

**系统功能**：

- 批量审核操作
- 审核历史记录
- 风险标记和分类
- 审核统计报表

### 9.2 证据审核

**审核标准**：

- 证据真实性
- 证据完整性
- 证据时效性
- 证据相关性

**审核流程**：

1. 查看证据材料
2. 验证证据有效性
3. 对比委托要求
4. 做出审核结论
5. 记录审核意见

### 9.3 用户管理

**管理功能**：

- 用户账户状态管理
- 权限调整和角色变更
- 违规行为处理
- 账户冻结和解冻

**处理流程**：

1. 接收用户相关报告
2. 调查用户行为记录
3. 评估违规程度
4. 执行相应处理措施
5. 记录处理结果

---

## 注意事项和最佳实践

### 安全提醒

- 定期更新密码
- 启用两步验证
- 谨慎处理个人信息
- 及时报告异常情况

### 操作建议

- 仔细阅读委托要求
- 保存重要截图和记录
- 及时响应系统通知
- 主动与对方沟通

### 客服支持

- 工作时间：9:00-18:00 (UTC+8)
- 响应时间：普通用户24小时，高级用户4小时
- 联系方式：工单系统、在线客服

---

## 界面导航指南

### 10.1 主页导航

**主要区域**：

- 顶部导航栏：登录/注册、语言切换、主题切换
- 英雄区域：平台介绍和主要CTA按钮
- 功能特色：核心功能展示
- 流程说明：使用步骤介绍
- 会员套餐：价格和功能对比
- FAQ区域：常见问题解答
- 页脚：联系信息和法律条款

**交互元素**：

- 3D背景动画（React Three Fiber）
- 响应式设计适配
- 平滑滚动和动画效果
- 多语言切换（中/英文）

### 10.2 用户仪表盘

**左侧导航菜单**：

- 仪表盘：数据概览和快捷操作
- 委托大厅：浏览可接受的委托
- 发布委托：创建新的委托委托
- 委托管理：
  - 已接受委托：跟踪进行中的委托
  - 已发布委托：管理自己发布的委托
- 工单管理：客服支持和问题反馈
- 钱包管理：余额、充值、提现
- 会员中心：套餐管理和升级
- 个人资料：账户设置和安全

**主要功能区域**：

- 数据统计卡片：余额、委托数、收益等
- 快捷操作按钮：常用功能入口
- 最近活动：委托动态和系统通知
- 推荐委托：智能匹配的委托建议

### 10.3 管理后台

**管理员专用界面**：

- 系统仪表盘：平台运营数据
- 用户管理：用户账户和权限
- 白名单审核：商户验证管理
- 委托管理：
  - 委托审核：新委托审批
  - 委托列表：所有委托管理
  - 证据管理：证据审核处理
- 工单管理：客服工单处理
- 提现审核：财务提现处理
- 系统设置：平台配置管理

---

## 移动端适配

### 11.1 响应式设计

**断点设置**：

- 手机：< 768px
- 平板：768px - 1024px
- 桌面：> 1024px

**适配特性**：

- 触摸友好的按钮尺寸
- 简化的导航菜单
- 优化的表单布局
- 适配的图片和媒体

### 11.2 移动端特殊功能

**手机端优化**：

- 侧边栏折叠菜单
- 底部导航栏（可选）
- 手势操作支持
- 相机拍照上传
- 地理位置服务

---

## 国际化和本地化

### 12.1 多语言支持

**支持语言**：

- 中文（简体）：zh
- 英文：en

**语言切换**：

- 自动检测浏览器语言
- 手动切换语言选项
- 记住用户语言偏好
- URL路径包含语言标识

**本地化内容**：

- 界面文本翻译
- 日期时间格式
- 数字和货币格式
- 邮件模板多语言

### 12.2 用户语言偏好

**语言记录**：

- 注册时记录首选语言
- 登录时应用语言设置
- 邮件通知使用用户语言
- 客服支持语言匹配

---

## 性能优化和用户体验

### 13.1 页面加载优化

**技术实现**：

- Next.js App Router
- 服务端渲染（SSR）
- 静态生成（SSG）
- 图片懒加载
- 代码分割和预加载

**用户体验**：

- 页面加载进度条
- 骨架屏占位符
- 错误边界处理
- 离线状态提示

### 13.2 交互反馈

**即时反馈**：

- 按钮点击状态
- 表单验证提示
- 操作成功/失败通知
- 加载状态指示器

**通知系统**：

- Toast消息提示
- 系统通知中心
- 邮件通知
- 浏览器推送通知

---

## 安全和隐私保护

### 14.1 数据安全

**安全措施**：

- HTTPS加密传输
- 密码哈希存储
- 会话令牌管理
- API访问控制
- 输入数据验证

**隐私保护**：

- 个人信息加密
- 访问日志记录
- 数据备份和恢复
- GDPR合规处理

### 14.2 用户安全

**账户安全**：

- 强密码要求
- 登录设备管理
- 异常登录检测
- 账户锁定机制

**交易安全**：

- 支付信息加密
- 交易验证机制
- 风险评估系统
- 反欺诈检测

---

## 错误处理和故障排除

### 15.1 常见错误处理

**网络错误**：

- 连接超时重试
- 网络中断提示
- 离线模式支持
- 数据同步恢复

**业务错误**：

- 余额不足提示
- 权限不够说明
- 操作失败原因
- 解决方案建议

### 15.2 用户支持

**自助服务**：

- 帮助文档中心
- 常见问题FAQ
- 视频教程指南
- 操作步骤说明

**人工支持**：

- 在线客服聊天
- 工单系统
- 邮件支持
- 电话客服（高级用户）

---

## 更新日志和版本说明

### 版本历史

- v2.0.0：全新架构，支持国际化
- v1.5.0：添加会员系统和白名单功能
- v1.0.0：基础功能发布

### 即将推出的功能

- 移动端APP
- API开放平台
- 高级数据分析
- 自动化工具集成
