import {
  emailStyles,
  emailStylesLegacy,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface TaskCompletedPublisherEmailData {
  taskId: string;
  accepterName: string;
  accepterEmail: string;
  completedAt: string;
  language?: 'zh' | 'en';
}

export interface TaskCompletedAccepterEmailData {
  taskId: string;
  publisherName: string;
  publisherEmail: string;
  completedAt: string;
  reward: number;
  language?: 'zh' | 'en';
}

export const taskCompletedPublisherTemplateI18n = (
  data: TaskCompletedPublisherEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.completedAt, language);

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
        },
        buttons: {
          viewDetails: '查看详情',
        },
      },
      notifications: {
        taskCompleted: {
          publisher: {
            title: '委托已完成',
            greeting: '您发布的委托已经成功完成！',
            taskInfo: '委托信息',
            taskId: '委托ID：',
            accepter: '完成者：',
            completedAt: '完成时间：',
            action: '您可以点击下方按钮查看委托详情和进行后续操作。',
          },
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
        },
        buttons: {
          viewDetails: 'View Details',
        },
      },
      notifications: {
        taskCompleted: {
          publisher: {
            title: 'Task Completed',
            greeting: 'Your published task has been successfully completed!',
            taskInfo: 'Task Information',
            taskId: 'Task ID:',
            accepter: 'Completed by:',
            completedAt: 'Completed at:',
            action:
              'You can click the button below to view task details and perform follow-up actions.',
          },
        },
      },
    },
  };

  const t = translations[language];

  return `
<!DOCTYPE html>
<html lang="${langAttr}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${t.notifications.taskCompleted.publisher.title} - ${t.common.brandName}</title>
  <style>
    ${emailStyles}
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="email-header">
      <h1>${t.common.brandName}</h1>
    </div>

    <!-- Content -->
    <div class="email-content">
      <h2 style="color: #1e293b; margin-top: 0; text-align: center;">${t.notifications.taskCompleted.publisher.title}</h2>

      <p style="margin-bottom: 20px; text-align: center;">
        ${t.common.greeting}，
      </p>

      <p style="margin-bottom: 30px; text-align: center; color: #64748b;">
        ${t.notifications.taskCompleted.publisher.greeting}
      </p>

      <div class="success-box">
        <h3 style="margin-top: 0; color: #16a34a;">${t.notifications.taskCompleted.publisher.taskInfo}</h3>
        <p><strong>${t.notifications.taskCompleted.publisher.taskId}</strong> ${data.taskId}</p>
        <p><strong>${t.notifications.taskCompleted.publisher.accepter}</strong> ${data.accepterName}</p>
        <p><strong>${t.notifications.taskCompleted.publisher.completedAt}</strong> ${formattedDate}</p>
      </div>

      <p style="margin-top: 30px; text-align: center; color: #64748b;">
        ${t.notifications.taskCompleted.publisher.action}
      </p>

      <div class="email-buttons">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/my-published-tasks" class="email-button email-button-primary">
          ${t.common.buttons.viewDetails}
        </a>
      </div>
    </div>

    <!-- Footer -->
    <div class="email-footer">
      <p>${t.common.regards},<br>${t.common.team}</p>
      <div class="email-footer-links">
        <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
          ${t.common.footer.contact}
        </p>
        <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
          ${t.common.footer.copyright}
        </p>
      </div>
    </div>
  </div>
</body>
</html>
  `.trim();
};

export const taskCompletedAccepterTemplateI18n = (
  data: TaskCompletedAccepterEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.completedAt, language);
  const formattedReward = formatEmailAmount(data.reward, 'USD', language);

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
        },
        buttons: {
          viewDetails: '查看详情',
        },
      },
      notifications: {
        taskCompleted: {
          accepter: {
            title: '委托完成确认',
            greeting: '恭喜您成功完成委托！',
            taskInfo: '委托信息',
            taskId: '委托ID：',
            publisher: '发布者：',
            completedAt: '完成时间：',
            reward: '酬金金额：',
            action: '您的酬金将很快到账，您可以点击下方按钮查看详情。',
          },
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
        },
        buttons: {
          viewDetails: 'View Details',
        },
      },
      notifications: {
        taskCompleted: {
          accepter: {
            title: 'Task Completion Confirmation',
            greeting: 'Congratulations on successfully completing the task!',
            taskInfo: 'Task Information',
            taskId: 'Task ID:',
            publisher: 'Publisher:',
            completedAt: 'Completed at:',
            reward: 'Reward Amount:',
            action:
              'Your reward will be credited soon. You can click the button below to view details.',
          },
        },
      },
    },
  };

  const t = translations[language];

  return `
<!DOCTYPE html>
<html lang="${langAttr}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${t.notifications.taskCompleted.accepter.title} - ${t.common.brandName}</title>
  <style>
    ${emailStyles}
  </style>
</head>
<body>
  <div class="email-container">
    <!-- Header -->
    <div class="email-header">
      <h1>${t.common.brandName}</h1>
    </div>

    <!-- Content -->
    <div class="email-content">
      <h2 style="color: #1e293b; margin-top: 0; text-align: center;">${t.notifications.taskCompleted.accepter.title}</h2>

      <p style="margin-bottom: 20px; text-align: center;">
        ${t.common.greeting}，
      </p>

      <p style="margin-bottom: 30px; text-align: center; color: #64748b;">
        ${t.notifications.taskCompleted.accepter.greeting}
      </p>

      <div class="success-box">
        <h3 style="margin-top: 0; color: #16a34a;">${t.notifications.taskCompleted.accepter.taskInfo}</h3>
        <p><strong>${t.notifications.taskCompleted.accepter.taskId}</strong> ${data.taskId}</p>
        <p><strong>${t.notifications.taskCompleted.accepter.publisher}</strong> ${data.publisherName}</p>
        <p><strong>${t.notifications.taskCompleted.accepter.completedAt}</strong> ${formattedDate}</p>
        <p><strong>${t.notifications.taskCompleted.accepter.reward}</strong> ${formattedReward}</p>
      </div>

      <p style="margin-top: 30px; text-align: center; color: #64748b;">
        ${t.notifications.taskCompleted.accepter.action}
      </p>

      <div class="email-buttons">
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/my-accepted-tasks" class="email-button email-button-primary">
          ${t.common.buttons.viewDetails}
        </a>
      </div>
    </div>

    <!-- Footer -->
    <div class="email-footer">
      <p>${t.common.regards},<br>${t.common.team}</p>
      <div class="email-footer-links">
        <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
          ${t.common.footer.contact}
        </p>
        <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
          ${t.common.footer.copyright}
        </p>
      </div>
    </div>
  </div>
</body>
</html>
  `.trim();
};
