'use client';

import {
  Ticket,
  Search,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  Calendar,
  User,
  Tag,
  MoreHorizontal,
  Eye,
  MessageCircle,
  CheckCheck,
  X,
  RefreshCw,
  Loader2,
  <PERSON>rendingUp,
  Users,
  AlertTriangle,
} from 'lucide-react';
import { useState, useMemo } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import {
  useAdminTickets,
  useTicketStats,
  useAdminTicket,
  useAdminReplyTicket,
  useAdminUpdateTicketStatus,
  useAssignTicket,
  useAdminUsers,
} from '@/hooks/use-admin-tickets';
import {
  TICKET_TYPE_LABELS,
  TICKET_STATUS_LABELS,
  TICKET_PRIORITY_LABELS,
  TicketType,
  TicketStatus,
  TicketPriority,
} from '@/lib/types/ticket';

export function AdminTicketsContent() {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [selectedAssignee, setSelectedAssignee] = useState<string>('');

  // API调用
  const {
    data: ticketsData,
    isLoading,
    error,
  } = useAdminTickets({
    page: 1,
    limit: 100,
    search: searchQuery || undefined,
    status: activeTab !== 'all' ? (activeTab as TicketStatus) : undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { data: stats } = useTicketStats();
  const { data: selectedTicket } = useAdminTicket(selectedTicketId || '');
  const { data: adminUsers } = useAdminUsers();

  const replyTicketMutation = useAdminReplyTicket(selectedTicketId || '');
  const updateStatusMutation = useAdminUpdateTicketStatus(
    selectedTicketId || '',
  );
  const assignTicketMutation = useAssignTicket(selectedTicketId || '');

  // 筛选工单
  const filteredTickets = useMemo(() => {
    const tickets = ticketsData?.tickets || [];
    let filtered = tickets;

    // 按状态筛选
    if (activeTab !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === activeTab);
    }

    return filtered.sort(
      (a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    );
  }, [ticketsData?.tickets, activeTab]);

  // 统计数据
  const statsData = useMemo(() => {
    if (!stats) return { total: 0, pending: 0, inProgress: 0, resolved: 0 };

    return {
      total: stats.totalTickets,
      pending: stats.pendingTickets,
      inProgress: stats.inProgressTickets,
      resolved: stats.resolvedTickets,
    };
  }, [stats]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case TicketStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300';
      case TicketStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300';
      case TicketStatus.RESOLVED:
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300';
      case TicketStatus.CLOSED:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
      case TicketStatus.CANCELLED:
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case TicketPriority.LOW:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
      case TicketPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300';
      case TicketPriority.HIGH:
        return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300';
      case TicketPriority.URGENT:
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case TicketStatus.PENDING:
        return <Clock className='h-4 w-4' />;
      case TicketStatus.IN_PROGRESS:
        return <AlertCircle className='h-4 w-4' />;
      case TicketStatus.RESOLVED:
        return <CheckCircle className='h-4 w-4' />;
      case TicketStatus.CLOSED:
        return <XCircle className='h-4 w-4' />;
      case TicketStatus.CANCELLED:
        return <XCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  // 格式化日期时间
  const formatDateTime = (date: string | Date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 查看工单详情
  const handleViewTicket = (ticketId: string) => {
    setSelectedTicketId(ticketId);
    setIsDetailDialogOpen(true);
  };

  // 更新工单状态
  const handleUpdateStatus = (status: TicketStatus) => {
    if (!selectedTicketId) return;

    updateStatusMutation.mutate({ status });
  };

  // 发送回复
  const handleSendReply = () => {
    if (!replyContent.trim()) {
      toast.error('请输入回复内容');
      return;
    }

    replyTicketMutation.mutate(
      { content: replyContent },
      {
        onSuccess: () => {
          setReplyContent('');
        },
      },
    );
  };

  // 分配工单
  const handleAssignTicket = () => {
    if (!selectedAssignee) {
      toast.error('请选择分配的管理员');
      return;
    }

    assignTicketMutation.mutate(
      {
        action: 'assign',
        assignedTo: selectedAssignee === 'unassign' ? null : selectedAssignee,
      },
      {
        onSuccess: () => {
          setIsAssignDialogOpen(false);
          setSelectedAssignee('');
        },
      },
    );
  };

  return (
    <div className='space-y-6'>
      {/* 页面标题 */}
      <div className='flex items-start justify-between'>
        <div>
          <h1 className='text-2xl font-bold tracking-tight'>工单管理</h1>
          <p className='text-muted-foreground'>管理和处理所有用户提交的工单</p>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>总工单数</CardTitle>
            <Ticket className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{statsData.total}</div>
            <p className='text-xs text-muted-foreground'>所有提交的工单</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>待处理</CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{statsData.pending}</div>
            <p className='text-xs text-muted-foreground'>需要立即关注</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>处理中</CardTitle>
            <AlertTriangle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{statsData.inProgress}</div>
            <p className='text-xs text-muted-foreground'>正在处理的工单</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>已解决</CardTitle>
            <TrendingUp className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{statsData.resolved}</div>
            <p className='text-xs text-muted-foreground'>成功解决的工单</p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className='pt-6'>
          <div className='flex flex-col lg:flex-row gap-4'>
            <div className='relative flex-1'>
              <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='搜索工单ID、标题、用户姓名或邮箱...'
                className='pl-10'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant='outline' onClick={() => setSearchQuery('')}>
              重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 工单列表 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-2 lg:grid-cols-6'>
          <TabsTrigger value='all'>全部</TabsTrigger>
          <TabsTrigger value='PENDING'>待处理</TabsTrigger>
          <TabsTrigger value='IN_PROGRESS'>处理中</TabsTrigger>
          <TabsTrigger value='RESOLVED'>已解决</TabsTrigger>
          <TabsTrigger value='CLOSED'>已关闭</TabsTrigger>
          <TabsTrigger value='CANCELLED'>已取消</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className='mt-6'>
          <Card>
            <CardHeader>
              <CardTitle>工单列表</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className='flex items-center justify-center py-12'>
                  <Loader2 className='h-8 w-8 animate-spin text-muted-foreground' />
                  <span className='ml-2 text-sm text-muted-foreground'>
                    加载工单数据...
                  </span>
                </div>
              ) : error ? (
                <div className='text-center py-12'>
                  <XCircle className='h-12 w-12 text-red-500 mx-auto mb-2' />
                  <p className='text-red-600 font-medium'>加载失败</p>
                  <p className='text-sm text-muted-foreground'>请稍后重试</p>
                </div>
              ) : filteredTickets.length === 0 ? (
                <div className='text-center py-12'>
                  <Ticket className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                  <p className='text-muted-foreground'>暂无工单数据</p>
                </div>
              ) : (
                <div className='overflow-x-auto'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>工单ID</TableHead>
                        <TableHead>用户信息</TableHead>
                        <TableHead>标题</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>优先级</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>分配给</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>更新时间</TableHead>
                        <TableHead className='text-right'>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTickets.map(ticket => (
                        <TableRow key={ticket.id} className='hover:bg-muted/50'>
                          <TableCell>
                            <span className='font-mono text-sm font-medium text-blue-600'>
                              #{ticket.id}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className='space-y-1'>
                              <p className='text-sm font-medium'>
                                {ticket.user.name || '未知用户'}
                              </p>
                              <p className='text-xs text-muted-foreground'>
                                {ticket.user.email}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell className='max-w-xs'>
                            <div className='truncate' title={ticket.title}>
                              {ticket.title}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant='outline' className='text-xs'>
                              <Tag className='h-3 w-3 mr-1' />
                              {TICKET_TYPE_LABELS[ticket.type as TicketType] ||
                                ticket.type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant='outline'
                              className={getPriorityColor(ticket.priority)}
                            >
                              {TICKET_PRIORITY_LABELS[
                                ticket.priority as TicketPriority
                              ] || ticket.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(ticket.status)}>
                              {getStatusIcon(ticket.status)}
                              <span className='ml-1'>
                                {TICKET_STATUS_LABELS[
                                  ticket.status as TicketStatus
                                ] || ticket.status}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className='text-sm'>
                              {ticket.assignee ? (
                                <span className='text-blue-600'>
                                  {ticket.assignee.name ||
                                    ticket.assignee.email}
                                </span>
                              ) : (
                                <span className='text-muted-foreground'>
                                  未分配
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className='text-sm text-muted-foreground'>
                            {formatDateTime(ticket.createdAt)}
                          </TableCell>
                          <TableCell className='text-sm text-muted-foreground'>
                            {formatDateTime(ticket.updatedAt)}
                          </TableCell>
                          <TableCell className='text-right'>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuItem
                                  onClick={() => handleViewTicket(ticket.id)}
                                >
                                  <Eye className='mr-2 h-4 w-4' />
                                  查看详情
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedTicketId(ticket.id);
                                    setIsAssignDialogOpen(true);
                                  }}
                                >
                                  <Users className='mr-2 h-4 w-4' />
                                  分配工单
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 工单详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className='sm:max-w-[800px] max-h-[80vh] overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>
              {selectedTicket ? selectedTicket.title : '工单详情'}
            </DialogTitle>
          </DialogHeader>

          {selectedTicket && (
            <>
              <div className='flex items-start justify-between gap-4 mb-4'>
                <div className='flex-1 min-w-0'>
                  <div className='flex items-center gap-2 mb-2'>
                    <span className='text-sm text-muted-foreground'>
                      #{selectedTicket.id}
                    </span>
                    <Badge
                      variant='outline'
                      className={getPriorityColor(selectedTicket.priority)}
                    >
                      {TICKET_PRIORITY_LABELS[
                        selectedTicket.priority as TicketPriority
                      ] || selectedTicket.priority}
                    </Badge>
                    <Badge variant='outline' className='text-xs'>
                      <Tag className='h-3 w-3 mr-1' />
                      {TICKET_TYPE_LABELS[selectedTicket.type as TicketType] ||
                        selectedTicket.type}
                    </Badge>
                  </div>
                </div>
                <Badge className={getStatusColor(selectedTicket.status)}>
                  {getStatusIcon(selectedTicket.status)}
                  <span className='ml-1'>
                    {TICKET_STATUS_LABELS[
                      selectedTicket.status as TicketStatus
                    ] || selectedTicket.status}
                  </span>
                </Badge>
              </div>

              <div className='grid grid-cols-2 gap-4 text-sm text-muted-foreground mb-6'>
                <div className='flex items-center gap-1'>
                  <User className='h-4 w-4' />
                  <span>
                    提交者：
                    {selectedTicket.user.name || selectedTicket.user.email}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Users className='h-4 w-4' />
                  <span>
                    分配给：{selectedTicket.assignee?.name || '未分配'}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>
                    创建时间：{formatDateTime(selectedTicket.createdAt)}
                  </span>
                </div>
                <div className='flex items-center gap-1'>
                  <Clock className='h-4 w-4' />
                  <span>
                    最后更新：{formatDateTime(selectedTicket.updatedAt)}
                  </span>
                </div>
              </div>

              <div className='space-y-6'>
                {/* 工单描述 */}
                <div>
                  <h4 className='font-medium mb-2'>问题描述</h4>
                  <div className='bg-muted/50 p-4 rounded-lg'>
                    <p className='text-sm whitespace-pre-wrap'>
                      {selectedTicket.description}
                    </p>
                  </div>
                </div>

                {/* 快捷操作 */}
                <div>
                  <h4 className='font-medium mb-3'>快捷操作</h4>
                  <div className='flex flex-wrap gap-2'>
                    {selectedTicket.status === TicketStatus.PENDING && (
                      <Button
                        size='sm'
                        onClick={() =>
                          handleUpdateStatus(TicketStatus.IN_PROGRESS)
                        }
                        disabled={updateStatusMutation.isPending}
                      >
                        {updateStatusMutation.isPending && (
                          <Loader2 className='h-3 w-3 mr-1 animate-spin' />
                        )}
                        <RefreshCw className='h-3 w-3 mr-1' />
                        开始处理
                      </Button>
                    )}
                    {selectedTicket.status === TicketStatus.IN_PROGRESS && (
                      <Button
                        size='sm'
                        onClick={() =>
                          handleUpdateStatus(TicketStatus.RESOLVED)
                        }
                        disabled={updateStatusMutation.isPending}
                      >
                        {updateStatusMutation.isPending && (
                          <Loader2 className='h-3 w-3 mr-1 animate-spin' />
                        )}
                        <CheckCheck className='h-3 w-3 mr-1' />
                        标记已解决
                      </Button>
                    )}
                    {selectedTicket.status !== TicketStatus.CLOSED &&
                      selectedTicket.status !== TicketStatus.CANCELLED && (
                        <Button
                          size='sm'
                          variant='outline'
                          onClick={() =>
                            handleUpdateStatus(TicketStatus.CLOSED)
                          }
                          disabled={updateStatusMutation.isPending}
                        >
                          {updateStatusMutation.isPending && (
                            <Loader2 className='h-3 w-3 mr-1 animate-spin' />
                          )}
                          <X className='h-3 w-3 mr-1' />
                          关闭工单
                        </Button>
                      )}
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={() => setIsAssignDialogOpen(true)}
                    >
                      <Users className='h-3 w-3 mr-1' />
                      重新分配
                    </Button>
                  </div>
                </div>

                {/* 回复列表 */}
                {selectedTicket.replies &&
                  selectedTicket.replies.length > 0 && (
                    <div>
                      <h4 className='font-medium mb-4'>回复记录</h4>
                      <div className='space-y-4'>
                        {selectedTicket.replies.map(reply => (
                          <div
                            key={reply.id}
                            className={`flex gap-3 ${reply.isStaff ? 'flex-row' : 'flex-row-reverse'}`}
                          >
                            <div
                              className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                                reply.isStaff
                                  ? 'bg-blue-100 text-blue-600'
                                  : 'bg-green-100 text-green-600'
                              }`}
                            >
                              <User className='h-4 w-4' />
                            </div>
                            <div
                              className={`flex-1 ${reply.isStaff ? 'mr-12' : 'ml-12'}`}
                            >
                              <div className='flex items-center gap-2 mb-1'>
                                <span className='text-sm font-medium'>
                                  {reply.authorName}
                                </span>
                                {reply.isStaff && (
                                  <Badge
                                    variant='secondary'
                                    className='text-xs'
                                  >
                                    管理员
                                  </Badge>
                                )}
                                <span className='text-xs text-muted-foreground'>
                                  {formatDateTime(reply.createdAt)}
                                </span>
                              </div>
                              <div
                                className={`p-3 rounded-lg text-sm ${
                                  reply.isStaff
                                    ? 'bg-blue-50 text-blue-900 dark:bg-blue-900/20 dark:text-blue-100'
                                    : 'bg-green-50 text-green-900 dark:bg-green-900/20 dark:text-green-100'
                                }`}
                              >
                                <p className='whitespace-pre-wrap'>
                                  {reply.content}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* 管理员回复区域 */}
                {selectedTicket.status !== TicketStatus.CANCELLED && (
                  <div>
                    <h4 className='font-medium mb-2'>管理员回复</h4>
                    <div className='space-y-4'>
                      <Textarea
                        placeholder='请输入回复内容...'
                        value={replyContent}
                        onChange={e => setReplyContent(e.target.value)}
                        className='min-h-[100px]'
                      />
                      <div className='flex justify-end'>
                        <Button
                          onClick={handleSendReply}
                          disabled={replyTicketMutation.isPending}
                        >
                          {replyTicketMutation.isPending && (
                            <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                          )}
                          <MessageCircle className='h-4 w-4 mr-2' />
                          发送回复
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}

          {!selectedTicket && (
            <div className='text-center py-8'>
              <Loader2 className='h-8 w-8 animate-spin mx-auto mb-2' />
              <p className='text-sm text-muted-foreground'>加载工单详情...</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 分配工单对话框 */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>分配工单</DialogTitle>
            <DialogDescription>
              选择要分配的管理员，或者取消分配
            </DialogDescription>
          </DialogHeader>
          <div className='space-y-4 py-4'>
            <div className='space-y-2'>
              <Label htmlFor='assignee'>分配给</Label>
              <Select
                value={selectedAssignee}
                onValueChange={setSelectedAssignee}
              >
                <SelectTrigger>
                  <SelectValue placeholder='选择管理员' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='unassign'>取消分配</SelectItem>
                  {Array.isArray(adminUsers) &&
                    adminUsers.map((admin: any) => (
                      <SelectItem key={admin.id} value={admin.id}>
                        {admin.name || admin.email}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsAssignDialogOpen(false)}
            >
              取消
            </Button>
            <Button
              onClick={handleAssignTicket}
              disabled={assignTicketMutation.isPending}
            >
              {assignTicketMutation.isPending && (
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
              )}
              确认分配
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
