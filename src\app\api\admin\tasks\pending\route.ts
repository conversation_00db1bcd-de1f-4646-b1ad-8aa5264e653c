import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';

// 获取待审核委托列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const search = searchParams.get('search');
    const platform = searchParams.get('platform');
    const evidenceStatus = searchParams.get('evidenceStatus');
    const timeRange = searchParams.get('timeRange');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // 构建where条件
    const where: any = {
      // 只获取待审核的委托
      status: TaskStatus.PENDING,
    };

    // 搜索筛选（委托ID或发布者信息）
    if (search) {
      where.OR = [
        {
          id: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          publisher: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          publisher: {
            email: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // 平台筛选
    if (platform && platform !== 'all') {
      where.platformId = platform;
    }

    // 时间范围筛选
    if (timeRange && timeRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (timeRange) {
        case 'today':
          startDate = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
          );
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        default:
          startDate = new Date(0);
      }

      where.createdAt = {
        gte: startDate,
      };
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询委托列表
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.task.count({ where }),
    ]);

    // 获取拒付类型和支付方式名称
    const [chargebackTypes, paymentMethods] = await Promise.all([
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    // 创建ID到名称的映射
    const chargebackTypeMap = chargebackTypes.reduce(
      (map, type) => {
        map[type.id] = type.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const paymentMethodMap = paymentMethods.reduce(
      (map, method) => {
        map[method.id] = method.name;
        return map;
      },
      {} as Record<string, string>,
    );

    // 处理委托数据，添加名称映射
    const processedTasks = tasks.map(task => ({
      ...task,
      chargebackTypeNames: task.chargebackTypeIds.map(
        id => chargebackTypeMap[id] || id,
      ),
      paymentMethodNames: task.paymentMethodIds.map(
        id => paymentMethodMap[id] || id,
      ),
      evidenceStatus: getEvidenceStatus(task),
    }));

    return NextResponse.json({
      success: true,
      data: {
        tasks: processedTasks,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('获取待审核委托失败:', error);
    return NextResponse.json({ error: '获取待审核委托失败' }, { status: 500 });
  }
}

// 获取证据状态的辅助函数
function getEvidenceStatus(task: any) {
  if (task.evidenceUploadType === 'NONE') {
    return '无证据';
  } else if (task.evidenceFiles && task.evidenceFiles.length > 0) {
    return task.evidenceStatus || '待审核';
  } else {
    return '待上传';
  }
}
