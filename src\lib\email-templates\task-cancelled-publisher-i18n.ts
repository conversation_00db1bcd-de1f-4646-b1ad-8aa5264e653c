import {
  emailStyles,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface TaskCancelledPublisherEmailData {
  userName: string;
  userEmail: string;
  taskId: string;
  taskTitle: string;
  cancelledAt: string;
  refundAmount?: number;
  currency?: string;
  cancellationReason?: string;
  language?: 'zh' | 'en';
}

export const taskCancelledPublisherTemplateI18n = (
  data: TaskCancelledPublisherEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.cancelledAt, language);
  const formattedRefund =
    data.refundAmount && data.currency
      ? formatEmailAmount(data.refundAmount, data.currency, language)
      : null;

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
          security: '为了您的账户安全，请勿向任何人透露您的账户信息。',
        },
        buttons: {
          viewTasks: '查看我的委托',
          publishNew: '发布新委托',
          contactSupport: '联系客服',
        },
      },
      notifications: {
        taskCancelled: {
          title: '委托取消通知',
          greeting: '您的委托已成功取消',
          description: '根据您的要求，我们已成功取消以下委托。',
          taskDetails: '委托详情',
          taskId: '委托编号：',
          taskTitle: '委托标题：',
          cancelledAt: '取消时间：',
          cancellationReason: '取消原因：',
          refundInfo: '退款信息',
          refundAmount: '退款金额：',
          refundNotice: '退款已原路返回到您的账户，请查收。',
          nextSteps: '接下来您可以：',
          stepsList: {
            publish: '发布新的委托任务',
            browse: '浏览其他委托机会',
            manage: '管理您的账户设置',
          },
          tips: '温馨提示',
          tipsList: {
            refund: '退款已处理完成，请查收',
            publish: '您可以随时发布新的委托任务',
            help: '如有疑问，请联系客服支持',
          },
          thankYou: '感谢您使用RefundGo！',
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
          security:
            'For your account security, please do not share your account information with anyone.',
        },
        buttons: {
          viewTasks: 'View My Tasks',
          publishNew: 'Publish New Task',
          contactSupport: 'Contact Support',
        },
      },
      notifications: {
        taskCancelled: {
          title: 'Task Cancellation Notice',
          greeting: 'Your task has been successfully cancelled',
          description:
            'As per your request, we have successfully cancelled the following task.',
          taskDetails: 'Task Details',
          taskId: 'Task ID:',
          taskTitle: 'Task Title:',
          cancelledAt: 'Cancelled At:',
          cancellationReason: 'Cancellation Reason:',
          refundInfo: 'Refund Information',
          refundAmount: 'Refund Amount:',
          refundNotice:
            'The refund has been processed back to your account. Please check your balance.',
          nextSteps: 'What you can do next:',
          stepsList: {
            publish: 'Publish new tasks',
            browse: 'Browse other task opportunities',
            manage: 'Manage your account settings',
          },
          tips: 'Important Notes',
          tipsList: {
            refund:
              'Refund has been processed successfully, please check your account',
            publish: 'You can publish new tasks at any time',
            help: 'If you have any questions, please contact customer support',
          },
          thankYou: 'Thank you for using RefundGo!',
        },
      },
    },
  };

  const t = translations[language];

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.taskCancelled.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .cancel-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #f59e0b, #d97706);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .task-card {
          background: #fefbf3;
          border: 1px solid #fed7aa;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 8px 0;
          border-bottom: 1px solid #fed7aa;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #92400e;
          font-weight: 500;
          min-width: 120px;
        }
        .info-value {
          color: #a16207;
          font-weight: 600;
          flex: 1;
          text-align: right;
        }
        .refund-card {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
          text-align: center;
        }
        .refund-amount {
          font-size: 24px;
          font-weight: bold;
          color: #16a34a;
          margin: 10px 0;
        }
        .steps-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .steps-list li {
          padding: 8px 0;
          padding-left: 24px;
          position: relative;
          color: #1e40af;
        }
        .steps-list li:before {
          content: "→";
          position: absolute;
          left: 0;
          color: #3b82f6;
          font-weight: bold;
        }
        .tips-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .tips-list li {
          padding: 6px 0;
          padding-left: 20px;
          position: relative;
          color: #f59e0b;
          font-size: 14px;
        }
        .tips-list li:before {
          content: "•";
          position: absolute;
          left: 0;
          color: #f59e0b;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Cancel Icon -->
          <div class="cancel-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}
          </h2>
          
          <h3 style="text-align: center; color: #f59e0b; margin-bottom: 16px;">
            ${t.notifications.taskCancelled.greeting}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.taskCancelled.description}
          </p>

          <!-- Task Details -->
          <div class="task-card">
            <h4 style="margin-top: 0; color: #92400e;">
              ${t.notifications.taskCancelled.taskDetails}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.taskId}</span>
              <span class="info-value" style="font-family: monospace;">${data.taskId}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.taskTitle}</span>
              <span class="info-value">${data.taskTitle}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.cancelledAt}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
            
            ${
              data.cancellationReason
                ? `
            <div class="info-row">
              <span class="info-label">${t.notifications.taskCancelled.cancellationReason}</span>
              <span class="info-value">${data.cancellationReason}</span>
            </div>
            `
                : ''
            }
          </div>

          <!-- Refund Information -->
          ${
            formattedRefund
              ? `
          <div class="refund-card">
            <h4 style="margin-top: 0; color: #16a34a;">
              💰 ${t.notifications.taskCancelled.refundInfo}
            </h4>
            <div class="refund-amount">
              ${t.notifications.taskCancelled.refundAmount} ${formattedRefund}
            </div>
            <p style="margin-bottom: 0; color: #16a34a;">
              ${t.notifications.taskCancelled.refundNotice}
            </p>
          </div>
          `
              : ''
          }

          <!-- Next Steps -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              ${t.notifications.taskCancelled.nextSteps}
            </h4>
            <ul class="steps-list">
              <li>${t.notifications.taskCancelled.stepsList.publish}</li>
              <li>${t.notifications.taskCancelled.stepsList.browse}</li>
              <li>${t.notifications.taskCancelled.stepsList.manage}</li>
            </ul>
          </div>

          <!-- Tips -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              💡 ${t.notifications.taskCancelled.tips}
            </h4>
            <ul class="tips-list">
              ${formattedRefund ? `<li>${t.notifications.taskCancelled.tipsList.refund}</li>` : ''}
              <li>${t.notifications.taskCancelled.tipsList.publish}</li>
              <li>${t.notifications.taskCancelled.tipsList.help}</li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/publish" class="email-button email-button-primary">
              ${t.common.buttons.publishNew}
            </a>
            <a href="${process.env.DOMAIN}/my-published-tasks" class="email-button email-button-secondary">
              ${t.common.buttons.viewTasks}
            </a>
          </div>

          <!-- Thank You -->
          <p style="text-align: center; color: #64748b; margin-top: 32px; font-style: italic;">
            ${t.notifications.taskCancelled.thankYou}
          </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.contact}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.security}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `.trim();
};
