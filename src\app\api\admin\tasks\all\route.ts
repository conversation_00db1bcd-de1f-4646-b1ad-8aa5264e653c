import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';
import { getTaskCommission } from '@/lib/utils/commission';

// 获取所有委托列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json(
        { error: 'Admin permission required' },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const search = searchParams.get('search');
    const platform = searchParams.get('platform');
    const taskStatus = searchParams.get('taskStatus');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // 构建where条件
    const where: any = {};

    // 搜索筛选（委托ID或发布者信息）
    if (search) {
      where.OR = [
        {
          id: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          publisher: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          publisher: {
            email: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // 平台筛选
    if (platform && platform !== 'all') {
      where.platformId = platform;
    }

    // 委托状态筛选
    if (taskStatus && taskStatus !== 'all') {
      const statusMap: Record<string, TaskStatus> = {
        pending: TaskStatus.PENDING,
        recruiting: TaskStatus.RECRUITING,
        in_progress: TaskStatus.IN_PROGRESS,
        completed: TaskStatus.COMPLETED,
        rejected: TaskStatus.REJECTED,
        expired: TaskStatus.EXPIRED,
        cancelled: TaskStatus.CANCELLED,
      };

      if (statusMap[taskStatus]) {
        where.status = statusMap[taskStatus];
      }
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询委托列表
    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          accepter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.task.count({ where }),
    ]);

    // 获取拒付类型和支付方式名称
    const [chargebackTypes, paymentMethods] = await Promise.all([
      prisma.chargebackType.findMany({
        where: { status: 'ACTIVE' },
      }),
      prisma.paymentMethod.findMany({
        where: { status: 'ACTIVE' },
      }),
    ]);

    // 创建ID到名称的映射
    const chargebackTypeMap = chargebackTypes.reduce(
      (map, type) => {
        map[type.id] = type.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const paymentMethodMap = paymentMethods.reduce(
      (map, method) => {
        map[method.id] = method.name;
        return map;
      },
      {} as Record<string, string>,
    );

    // 获取系统费率配置
    const systemRate = await prisma.systemRate.findFirst();

    // 处理委托数据
    const processedTasks = tasks.map(task => {
      // 使用与委托列表相同的酬金计算方式
      const commission = getTaskCommission(
        {
          totalAmount: task.totalAmount,
          unitPrice: task.unitPrice,
          chargebackTypeIds: task.chargebackTypeIds,
          paymentMethodIds: task.paymentMethodIds,
          evidenceUploadType: task.evidenceUploadType,
          evidenceStatus: (task as any).evidenceStatus,
        },
        chargebackTypes,
        paymentMethods,
        systemRate
          ? { noEvidenceExtraRate: systemRate.noEvidenceExtraRate }
          : { noEvidenceExtraRate: 2.0 },
      );

      return {
        id: task.id,
        title: task.title,
        platform: task.platform.name,
        category: task.category.name,
        productUrl: task.productUrl,
        productDescription: task.productDescription,
        chargebackTypes: task.chargebackTypeIds.map(
          id => chargebackTypeMap[id] || id,
        ),
        paymentMethods: task.paymentMethodIds.map(
          id => paymentMethodMap[id] || id,
        ),
        quantity: task.quantity,
        unitPrice: task.unitPrice,
        totalAmount: task.totalAmount,
        finalTotal: task.finalTotal,
        commission,
        listingTime: task.listingTime,
        recipientName: task.recipientName,
        recipientPhone: task.recipientPhone,
        shippingAddress: task.shippingAddress,
        publisher: {
          id: task.publisher.id,
          nickname: task.publisher.name || 'Unknown User',
          email: task.publisher.email,
        },
        accepter: task.accepter
          ? {
              id: task.accepter.id,
              nickname: task.accepter.name || 'Unknown User',
              email: task.accepter.email,
            }
          : null,
        status: task.status,
        evidenceStatus: (task as any).evidenceStatus,
        evidenceUploadType: task.evidenceUploadType || 'LATER',
        evidenceFiles: task.evidenceFiles || [],
        evidenceRejectReason: task.evidenceRejectReason,
        createdAt: task.createdAt.toISOString(),
        updatedAt: task.updatedAt.toISOString(),
        expiresAt: task.expiresAt?.toISOString(),
        completedAt: task.completedAt?.toISOString(),
      };
    });

    // 计算统计数据
    const allTasks = await prisma.task.findMany({
      select: {
        status: true,
        finalTotal: true,
      },
    });

    const stats = {
      totalTasks: allTasks.length,
      pendingTasks: allTasks.filter(t => t.status === TaskStatus.PENDING)
        .length,
      recruitingTasks: allTasks.filter(t => t.status === TaskStatus.RECRUITING)
        .length,
      activeTasks: allTasks.filter(t => t.status === TaskStatus.IN_PROGRESS)
        .length,
      completedTasks: allTasks.filter(t => t.status === TaskStatus.COMPLETED)
        .length,
      rejectedTasks: allTasks.filter(t => t.status === TaskStatus.REJECTED)
        .length,
      expiredTasks: allTasks.filter(t => t.status === TaskStatus.EXPIRED)
        .length,
      totalValue: allTasks.reduce((sum, t) => sum + t.finalTotal, 0),
      pendingSubmissionEvidence: Math.floor(allTasks.length * 0.4), // 模拟数据
      underReviewEvidence: Math.floor(allTasks.length * 0.3),
      reviewedEvidence: Math.floor(allTasks.length * 0.2),
      noEvidence: Math.floor(allTasks.length * 0.1),
    };

    return NextResponse.json({
      success: true,
      data: {
        tasks: processedTasks,
        stats,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
