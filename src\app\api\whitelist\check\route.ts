import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 获取用户语言偏好
function getUserLocale(request: NextRequest): string {
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage?.includes('zh')) {
    return 'zh';
  }
  return 'en';
}

// 翻译消息
const messages = {
  zh: {
    loginRequired: '请先登录',
    invalidData: '请求数据格式错误',
    whitelisted: '该店铺已申请白名单',
    notWhitelisted: '该店铺不在白名单中，可以发布委托',
    checkFailed: '白名单检测失败，请重试',
    invalidUrl: '请输入有效的商品链接',
  },
  en: {
    loginRequired: 'Please login first',
    invalidData: 'Invalid request data format',
    whitelisted: 'This shop has applied for whitelist',
    notWhitelisted: 'This shop is not on the whitelist, you can publish tasks',
    checkFailed: 'Whitelist check failed, please try again',
    invalidUrl: 'Please enter a valid product URL',
  },
};

export async function POST(request: NextRequest) {
  try {
    const locale = getUserLocale(request);
    const t = messages[locale as keyof typeof messages];

    // 白名单检测请求验证schema
    const checkWhitelistSchema = z.object({
      productUrl: z.string().url(t.invalidUrl),
    });

    // 验证用户认证
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: t.loginRequired }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const validation = checkWhitelistSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: t.invalidData,
          details: validation.error.errors,
        },
        { status: 400 },
      );
    }

    const { productUrl } = validation.data;

    // 获取所有已通过审核的白名单
    const whitelistItems = await prisma.shopWhitelist.findMany({
      where: {
        status: 'APPROVED',
        isActive: true,
      },
      select: {
        id: true,
        shopName: true,
        shopUrl: true,
        platform: true,
      },
    });

    // 检测DHgate平台
    if (productUrl.includes('dhgate.com')) {
      const dhgateWhitelist = whitelistItems.filter(
        item => item.platform === 'DHgate',
      );

      if (dhgateWhitelist.length > 0) {
        // 检测DHgate店铺信息
        const shopInfo = await extractDhgateShopInfo(productUrl);

        if (shopInfo) {
          // 检查是否在白名单中
          const matchedWhitelist = dhgateWhitelist.find(item => {
            const normalizedWhitelistUrl = normalizeUrl(item.shopUrl);
            const normalizedShopUrl = normalizeUrl(shopInfo.shopUrl);

            return (
              normalizedWhitelistUrl === normalizedShopUrl ||
              item.shopName.toLowerCase() === shopInfo.shopName.toLowerCase()
            );
          });

          if (matchedWhitelist) {
            return NextResponse.json({
              isWhitelisted: true,
              platform: 'DHgate',
              matchedShop: {
                id: matchedWhitelist.id,
                name: matchedWhitelist.shopName,
                url: matchedWhitelist.shopUrl,
              },
              extractedShop: shopInfo,
              message: 'whitelist.shopApplied',
            });
          }
        }
      }
    } else {
      // 检测其他平台
      const otherPlatformWhitelist = whitelistItems.filter(
        item => item.platform === '其他',
      );

      if (otherPlatformWhitelist.length > 0) {
        const matchedWhitelist = otherPlatformWhitelist.find(item => {
          return isDomainMatch(productUrl, item.shopUrl);
        });

        if (matchedWhitelist) {
          return NextResponse.json({
            isWhitelisted: true,
            platform: '其他',
            matchedShop: {
              id: matchedWhitelist.id,
              name: matchedWhitelist.shopName,
              url: matchedWhitelist.shopUrl,
            },
            message: 'whitelist.shopApplied',
          });
        }
      }
    }

    return NextResponse.json({
      isWhitelisted: false,
      message: 'whitelist.shopNotInWhitelist',
    });
  } catch (error) {
    const locale = getUserLocale(request);
    const t = messages[locale as keyof typeof messages];
    return NextResponse.json(
      { error: 'whitelist.checkFailed' },
      { status: 500 },
    );
  }
}

// 提取DHgate店铺信息
async function extractDhgateShopInfo(
  url: string,
): Promise<{ shopName: string; shopUrl: string } | null> {
  try {
    // 将dhgate.com域名替换为dhgate-proxy.moneycat.life
    const proxyUrl = url.replace(
      /(?:www\.)?dhgate\.com/g,
      'dhgate-proxy.moneycat.life',
    );

    const response = await fetch(proxyUrl, {
      method: 'GET',
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
        DNT: '1',
        Pragma: 'no-cache',
        'Sec-Fetch-User': '?1',
        'Sec-CH-UA':
          '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': '"Windows"',
      },
      // 添加超时控制
      signal: AbortSignal.timeout(10000),
    });

    if (!response.ok) {
      return null;
    }

    const html = await response.text();

    // 正则模式匹配店铺链接
    const patterns = [
      /<a[^>]*rel="external"[^>]*spm-index="store"[^>]*event-type="click"[^>]*title="([^"]*)"[^>]*class="storeNavagation_storeName__[^"]*"[^>]*href="([^"]*)"[^>]*>/,
      /<a[^>]*title="([^"]*)"[^>]*class="storeNavagation_storeName__[^"]*"[^>]*href="([^"]*)"[^>]*>/,
      /<a[^>]*class="storeNavagation_storeName__[^"]*"[^>]*title="([^"]*)"[^>]*href="([^"]*)"[^>]*>/,
    ];

    for (const pattern of patterns) {
      const match = html.match(pattern);
      if (match) {
        return {
          shopName: match[1],
          shopUrl: match[2],
        };
      }
    }

    return null;
  } catch (error) {
    return null;
  }
}

// 标准化URL格式
function normalizeUrl(url: string): string {
  if (url.startsWith('//')) {
    url = `https:${url}`;
  }
  return url.replace(/^https?:\/\//, '').replace(/\/$/, '');
}

// 提取域名
function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    return urlObj.hostname.replace(/^www\./, '');
  } catch {
    // 如果无法解析URL，尝试简单的域名提取
    const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^/]+)/i);
    return match ? match[1].replace(/^www\./, '') : url;
  }
}

// 域名匹配逻辑（支持子域名）
function isDomainMatch(inputUrl: string, whitelistUrl: string): boolean {
  const inputDomain = extractDomain(inputUrl);
  const whitelistDomain = extractDomain(whitelistUrl);

  const normalizedInput = inputDomain.toLowerCase();
  const normalizedWhitelist = whitelistDomain.toLowerCase();

  // 完全匹配
  if (normalizedInput === normalizedWhitelist) {
    return true;
  }

  // 检查输入域名是否是白名单域名的子域名
  // 例如：输入 www.baidu.com，白名单 baidu.com
  if (normalizedInput.endsWith(`.${normalizedWhitelist}`)) {
    return true;
  }

  return false;
}
