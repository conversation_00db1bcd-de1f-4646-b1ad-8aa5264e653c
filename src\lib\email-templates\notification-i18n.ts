import { emailStyles } from '@/hooks/useEmailTranslation';

export interface NotificationEmailData {
  userName: string;
  userEmail: string;
  signInTime: string;
  language?: 'zh' | 'en';
}

const translations = {
  zh: {
    common: {
      brandName: 'RefundGo',
      greeting: '您好',
      regards: '此致敬礼',
      team: 'RefundGo 团队',
      autoMessage: '此邮件由 RefundGo 系统自动发送，请勿回复。',
      copyright: '© 2024 RefundGo. 保留所有权利。',
    },
    notifications: {
      newUser: {
        title: '新用户注册通知',
        subject: '新用户注册通知 - RefundGo',
        header: '新用户注册通知',
        description: '有新用户注册了账户',
        userLabel: '用户：',
        emailLabel: '邮箱：',
        timeLabel: '时间：',
      },
    },
  },
  en: {
    common: {
      brandName: 'RefundGo',
      greeting: 'Hello',
      regards: 'Best regards',
      team: 'RefundGo Team',
      autoMessage: 'This email was sent automatically by RefundGo system. Please do not reply.',
      copyright: '© 2024 RefundGo. All rights reserved.',
    },
    notifications: {
      newUser: {
        title: 'New User Registration Notification',
        subject: 'New User Registration Notification - RefundGo',
        header: 'New User Registration',
        description: 'A new user has registered an account',
        userLabel: 'User:',
        emailLabel: 'Email:',
        timeLabel: 'Time:',
      },
    },
  },
};

export const notificationTemplateI18n = (data: NotificationEmailData): string => {
  const language = data.language || 'zh';
  const t = translations[language];
  const langAttr = language === 'zh' ? 'zh-CN' : 'en';

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.newUser.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .notification-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
          color: white;
          font-size: 24px;
          font-weight: bold;
        }
        .user-info-card {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          margin-bottom: 15px;
          align-items: center;
        }
        .info-row:last-child {
          margin-bottom: 0;
        }
        .info-label {
          color: #64748b;
          font-size: 14px;
          min-width: 60px;
          font-weight: 500;
        }
        .info-value {
          color: #1e293b;
          font-weight: 600;
          margin-left: 10px;
        }
        .email-value {
          color: #3b82f6;
          text-decoration: none;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>
        
        <div class="email-content">
          <div class="notification-icon">
            👤
          </div>
          
          <h2 style="color: #1e293b; margin-bottom: 20px; text-align: center; font-size: 24px;">
            ${t.notifications.newUser.header}
          </h2>
          
          <p style="color: #64748b; margin-bottom: 30px; text-align: center; font-size: 16px;">
            ${t.notifications.newUser.description}
          </p>
          
          <div class="user-info-card">
            <div class="info-row">
              <span class="info-label">${t.notifications.newUser.userLabel}</span>
              <span class="info-value">${data.userName}</span>
            </div>
            <div class="info-row">
              <span class="info-label">${t.notifications.newUser.emailLabel}</span>
              <a href="mailto:${data.userEmail}" class="info-value email-value">${data.userEmail}</a>
            </div>
            <div class="info-row">
              <span class="info-label">${t.notifications.newUser.timeLabel}</span>
              <span class="info-value">${data.signInTime}</span>
            </div>
          </div>
          
          <div class="info-box">
            <p style="margin: 0; color: #1e293b;">
              <strong>📧 ${t.common.brandName}</strong><br>
              ${t.common.autoMessage}
            </p>
          </div>
        </div>
        
        <div class="email-footer">
          <p>${t.common.copyright}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b;">
              ${t.common.brandName} - 专业的退款服务平台
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

// Export both the new i18n template and maintain backward compatibility
export const notificationTemplate = notificationTemplateI18n;
