import { AuthHeader } from '@/components/auth-header';
import { LoginForm } from '@/components/login-form';
import { constructMetadata } from '@/lib/utils';

export const metadata = constructMetadata({
  title: '注册',
  description: '创建您的RefundGo账户',
});

export default function SignUpPage() {
  return (
    <div className='flex min-h-svh flex-col bg-muted'>
      {/* 桌面端头部导航 */}
      <div className='hidden md:block'>
        <AuthHeader />
      </div>

      {/* 主要内容区域 */}
      <div className='flex flex-1 items-center justify-center p-6 md:p-10'>
        <div className='w-full max-w-sm md:max-w-3xl'>
          <LoginForm mode='signup' />
        </div>
      </div>
    </div>
  );
}
