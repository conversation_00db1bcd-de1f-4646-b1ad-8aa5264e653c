// Role-Based Access Control (RBAC) System

export const ROLES = {
  USER: 'USER',
  PREMIUM: 'PREMIUM',
  ADMIN: 'ADMIN',
} as const;

export const PERMISSIONS = {
  // 基础权限
  VIEW_DASHBOARD: 'view:dashboard',
  VIEW_PROFILE: 'view:profile',

  // 委托相关权限
  PUBLISH_TASK: 'publish:task',
  ACCEPT_TASK: 'accept:task',
  VIEW_TASKS: 'view:tasks',
  MANAGE_OWN_TASKS: 'manage:own_tasks',

  // 钱包相关权限
  VIEW_WALLET: 'view:wallet',
  WITHDRAW_FUNDS: 'withdraw:funds',

  // 高级功能权限
  MANAGE_WHITELIST: 'manage:whitelist',
  ACCESS_PREMIUM: 'access:premium',
  BULK_OPERATIONS: 'bulk:operations',

  // 管理员权限
  ADMIN_DASHBOARD: 'admin:dashboard',
  MANAGE_USERS: 'admin:users',
  MANAGE_TASKS: 'admin:tasks',
  MANAGE_PAYMENTS: 'admin:payments',
  MANAGE_SETTINGS: 'admin:settings',
  VIEW_ANALYTICS: 'admin:analytics',
} as const;

// 先定义基础用户权限
const USER_PERMISSIONS = [
  PERMISSIONS.VIEW_DASHBOARD,
  PERMISSIONS.VIEW_PROFILE,
  PERMISSIONS.PUBLISH_TASK,
  PERMISSIONS.ACCEPT_TASK,
  PERMISSIONS.VIEW_TASKS,
  PERMISSIONS.MANAGE_OWN_TASKS,
  PERMISSIONS.VIEW_WALLET,
  PERMISSIONS.WITHDRAW_FUNDS,
] as const;

export const ROLE_PERMISSIONS = {
  [ROLES.USER]: USER_PERMISSIONS,
  [ROLES.PREMIUM]: [
    // 包含所有用户权限
    ...USER_PERMISSIONS,
    // 高级功能权限
    PERMISSIONS.MANAGE_WHITELIST,
    PERMISSIONS.ACCESS_PREMIUM,
    PERMISSIONS.BULK_OPERATIONS,
  ],
  [ROLES.ADMIN]: [
    // 包含所有权限
    ...Object.values(PERMISSIONS),
  ],
} as const;

// 权限检查函数
export function hasPermission(userRole: string, permission: string): boolean {
  const rolePermissions =
    ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS];
  return rolePermissions?.includes(permission as any) || false;
}

// 检查用户是否有多个权限中的任意一个
export function hasAnyPermission(
  userRole: string,
  permissions: readonly string[],
): boolean {
  return permissions.some(permission => hasPermission(userRole, permission));
}

// 检查用户是否拥有所有指定权限
export function hasAllPermissions(
  userRole: string,
  permissions: string[],
): boolean {
  return permissions.every(permission => hasPermission(userRole, permission));
}

// 获取角色的所有权限
export function getRolePermissions(role: string): readonly string[] {
  return ROLE_PERMISSIONS[role as keyof typeof ROLE_PERMISSIONS] || [];
}

// 检查角色层级（用于权限继承）
export function isRoleHigherOrEqual(
  userRole: string,
  requiredRole: string,
): boolean {
  const roleHierarchy = {
    [ROLES.USER]: 1,
    [ROLES.PREMIUM]: 2,
    [ROLES.ADMIN]: 3,
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel =
    roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

// 页面权限映射
export const PAGE_PERMISSIONS = {
  '/dashboard': [PERMISSIONS.VIEW_DASHBOARD],
  '/profile': [PERMISSIONS.VIEW_PROFILE],
  '/tasks': [PERMISSIONS.VIEW_TASKS],
  '/publish': [PERMISSIONS.PUBLISH_TASK],
  '/wallet': [PERMISSIONS.VIEW_WALLET],
  '/whitelist': [PERMISSIONS.MANAGE_WHITELIST],
  '/membership': [PERMISSIONS.ACCESS_PREMIUM],
  '/admin': [PERMISSIONS.ADMIN_DASHBOARD],
  '/admin/users': [PERMISSIONS.MANAGE_USERS],
  '/admin/tasks': [PERMISSIONS.MANAGE_TASKS],
  '/admin/payments': [PERMISSIONS.MANAGE_PAYMENTS],
  '/admin/settings': [PERMISSIONS.MANAGE_SETTINGS],
} as const;

// 检查用户是否可以访问特定页面
export function canAccessPage(userRole: string, pagePath: string): boolean {
  const requiredPermissions =
    PAGE_PERMISSIONS[pagePath as keyof typeof PAGE_PERMISSIONS];

  if (!requiredPermissions) {
    // 如果页面没有定义权限要求，默认允许访问
    return true;
  }

  return hasAnyPermission(userRole, requiredPermissions);
}

// 会员计划相关
export const MEMBER_PLANS = {
  FREE: 'FREE',
  PREMIUM: 'PREMIUM',
  ENTERPRISE: 'ENTERPRISE',
} as const;

export const PLAN_FEATURES = {
  [MEMBER_PLANS.FREE]: {
    maxTasksPerMonth: 10,
    maxWithdrawPerMonth: 1000,
    whitelistAccess: false,
    prioritySupport: false,
    bulkOperations: false,
  },
  [MEMBER_PLANS.PREMIUM]: {
    maxTasksPerMonth: 100,
    maxWithdrawPerMonth: 10000,
    whitelistAccess: true,
    prioritySupport: true,
    bulkOperations: true,
  },
  [MEMBER_PLANS.ENTERPRISE]: {
    maxTasksPerMonth: -1, // 无限制
    maxWithdrawPerMonth: -1, // 无限制
    whitelistAccess: true,
    prioritySupport: true,
    bulkOperations: true,
  },
} as const;

// 检查用户是否可以执行特定操作
export function canPerformAction(
  userRole: string,
  memberPlan: string,
  action: string,
): boolean {
  // 首先检查角色权限
  if (!hasPermission(userRole, action)) {
    return false;
  }

  // 然后检查会员计划限制
  const planFeatures = PLAN_FEATURES[memberPlan as keyof typeof PLAN_FEATURES];
  if (!planFeatures) {
    return false;
  }

  // 根据操作类型检查具体限制
  switch (action) {
    case PERMISSIONS.MANAGE_WHITELIST:
      return planFeatures.whitelistAccess;
    case PERMISSIONS.BULK_OPERATIONS:
      return planFeatures.bulkOperations;
    default:
      return true;
  }
}

// 错误类型
export class PermissionError extends Error {
  constructor(
    message: string,
    public requiredPermission?: string,
  ) {
    super(message);
    this.name = 'PermissionError';
  }
}

export class RoleError extends Error {
  constructor(
    message: string,
    public requiredRole?: string,
  ) {
    super(message);
    this.name = 'RoleError';
  }
}

// 权限装饰器工具函数
export function requirePermission(permission: string) {
  return function (userRole: string) {
    if (!hasPermission(userRole, permission)) {
      throw new PermissionError(
        `Permission denied. Required: ${permission}`,
        permission,
      );
    }
  };
}

export function requireRole(role: string) {
  return function (userRole: string) {
    if (!isRoleHigherOrEqual(userRole, role)) {
      throw new RoleError(
        `Role insufficient. Required: ${role} or higher`,
        role,
      );
    }
  };
}

// 类型定义
export type Role = (typeof ROLES)[keyof typeof ROLES];
export type Permission = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];
export type MemberPlan = (typeof MEMBER_PLANS)[keyof typeof MEMBER_PLANS];

// 导出类型
export type {
  Role as UserRole,
  Permission as UserPermission,
  MemberPlan as UserMemberPlan,
};
