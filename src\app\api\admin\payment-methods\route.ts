import { UserRole } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { paymentMethodSchema } from '@/types/rates';

// 翻译消息
const messages = {
  zh: {
    unauthorized: '未授权访问',
    permissionDenied: '权限不足',
    serverError: '服务器内部错误',
  },
  en: {
    unauthorized: 'Unauthorized access',
    permissionDenied: 'Permission denied',
    serverError: 'Internal server error',
  },
};

// GET /api/admin/payment-methods - 获取所有支付方式
export async function GET(request: NextRequest) {
  try {
    // 获取语言设置
    const acceptLanguage = request.headers.get('accept-language') || 'zh';
    const lang = acceptLanguage.includes('en') ? 'en' : 'zh';
    const t = messages[lang];

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: t.unauthorized },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: t.permissionDenied },
        { status: 403 },
      );
    }

    const paymentMethods = await prisma.paymentMethod.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({
      success: true,
      data: paymentMethods,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: messages.zh.serverError },
      { status: 500 },
    );
  }
}

// POST /api/admin/payment-methods - 创建新支付方式
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validatedData = paymentMethodSchema.parse(body);

    // 检查支付方式名称是否已存在
    const existingPaymentMethod = await prisma.paymentMethod.findFirst({
      where: { name: validatedData.name },
    });

    if (existingPaymentMethod) {
      return NextResponse.json(
        { success: false, message: '支付方式名称已存在' },
        { status: 400 },
      );
    }

    const paymentMethod = await prisma.paymentMethod.create({
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: paymentMethod,
      message: '支付方式创建成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
