export interface LogisticsReviewPublisherEmailData {
  publisherName: string;
  publisherEmail: string;
  taskId: string;
  platform: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  submittedAt: string;
  accepterName: string;
  accepterEmail: string;
  orderNumber: string;
  trackingNumber: string;
  reviewDeadline: string;
  logisticsScreenshots: string[];
}

export const logisticsReviewPublisherTemplate = (
  data: LogisticsReviewPublisherEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>订单物流信息待审核</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #ffc107; margin-bottom: 20px; text-align: center;">⏳ 待审核订单物流信息</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.publisherName}！接单者已提交订单和物流信息，请及时审核。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">委托信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">委托ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.taskId}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">平台分类：</span>
          <span style="color: #333; margin-left: 10px;">${data.platform} - ${data.category}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">数量：</span>
          <span style="color: #333; margin-left: 10px;">${data.quantity} 件</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">单价：</span>
          <span style="color: #333; margin-left: 10px;">$${data.unitPrice}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">总金额：</span>
          <span style="color: #333; margin-left: 10px;">$${data.totalAmount}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">提交时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.submittedAt}</span>
        </div>
      </div>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <h3 style="color: #333; margin-bottom: 15px; font-size: 16px;">提交的信息</h3>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">接单者：</span>
          <span style="color: #333; margin-left: 10px;">${data.accepterName} (${data.accepterEmail})</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">订单号：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">${data.orderNumber}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">物流单号：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace; background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">${data.trackingNumber}</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">物流截图：</span>
          <span style="color: #333; margin-left: 10px;">${data.logisticsScreenshots.length} 张截图已提交</span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">审核截止时间：</span>
          <span style="color: #dc3545; margin-left: 10px; font-weight: bold;">${data.reviewDeadline}</span>
        </div>
      </div>
      
      <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
          ⏰ <strong>重要提醒：</strong><br>
          • 请在24小时内完成审核，否则系统将自动通过<br>
          • 请仔细核对订单号和物流单号的真实性<br>
          • 如发现问题，请及时拒绝并说明原因<br>
          • 审核通过后，系统将自动跟踪物流状态
        </p>
      </div>
      
      <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #155724; margin: 0; font-size: 14px;">
          📋 <strong>审核要点：</strong><br>
          • 订单号格式是否正确<br>
          • 物流单号是否为有效快递单号<br>
          • 物流截图是否清晰完整<br>
          • 收货地址是否与委托要求一致<br>
          • 商品信息是否匹配
        </p>
      </div>
      
      <div style="text-align: center; margin: 20px 0;">
        <p style="color: #666; margin-bottom: 10px;">请登录系统进行审核操作</p>
        <a href="${process.env.NEXT_PUBLIC_APP_URL}/publisher/tasks" 
           style="display: inline-block; background: #007bff; color: white; text-decoration: none; padding: 12px 24px; border-radius: 5px; font-weight: bold;">
          立即审核
        </a>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
