import { useQuery } from '@tanstack/react-query';

import { TaskFilters } from '@/lib/types/task';

// 委托列表响应类型
interface TasksResponse {
  success: boolean;
  data: {
    tasks: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

// 获取委托列表
export function useTasks(
  filters: TaskFilters,
  page: number = 1,
  limit: number = 20,
) {
  return useQuery({
    queryKey: ['tasks', filters, page, limit],
    queryFn: async (): Promise<TasksResponse> => {
      // 构建查询参数
      const params = new URLSearchParams();

      if (filters.search) {
        params.append('search', filters.search);
      }

      if (filters.platform?.length) {
        params.append('platform', filters.platform.join(','));
      }

      if (filters.chargebackType?.length) {
        params.append('chargebackType', filters.chargebackType.join(','));
      }

      if (filters.paymentMethod?.length) {
        params.append('paymentMethod', filters.paymentMethod.join(','));
      }

      if (filters.status?.length) {
        params.append('status', filters.status.join(','));
      }

      if (filters.sortBy) {
        params.append('sortBy', filters.sortBy);
      }

      if (filters.sortOrder) {
        params.append('sortOrder', filters.sortOrder);
      }

      params.append('page', page.toString());
      params.append('limit', limit.toString());

      const response = await fetch(`/api/tasks?${params.toString()}`);

      if (!response.ok) {
        throw new Error('获取委托列表失败');
      }

      return response.json();
    },
    staleTime: 30 * 1000, // 30秒内数据保持新鲜
  });
}
