import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 升级请求验证Schema
const upgradeSchema = z.object({
  planId: z.string().min(1, '请选择要升级的套餐'),
  planName: z.string().min(1, '套餐名称不能为空'),
});

export async function POST(request: NextRequest) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    // 2. 验证请求数据
    const body = await request.json();
    const validation = upgradeSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: '数据验证失败',
          details: validation.error.issues.map(issue => issue.message),
        },
        { status: 400 },
      );
    }

    const { planId, planName } = validation.data;

    // 3. 验证套餐是否存在
    const plan = await prisma.membershipPlan.findFirst({
      where: {
        id: planId,
        isActive: true,
      },
    });

    if (!plan) {
      return NextResponse.json(
        { success: false, message: '选择的套餐不存在或已禁用' },
        { status: 400 },
      );
    }

    // 4. 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    // 5. 检查是否是当前套餐
    const currentPlanName =
      user.memberPlan === 'FREE'
        ? '免费版'
        : user.memberPlan === 'PRO'
          ? '专业版'
          : '商业版';

    if (plan.name === currentPlanName) {
      return NextResponse.json(
        { success: false, message: '您已经是此套餐用户' },
        { status: 400 },
      );
    }

    // 6. 防止降级操作
    if (plan.name === '免费版') {
      return NextResponse.json(
        { success: false, message: '不支持降级到免费版' },
        { status: 400 },
      );
    }

    // 7. 防止商业版降级到专业版
    if (user.memberPlan === 'BUSINESS' && plan.name === '专业版') {
      return NextResponse.json(
        { success: false, message: '商业版用户不支持降级到专业版' },
        { status: 400 },
      );
    }

    // 8. 返回支付信息，引导用户进行支付
    return NextResponse.json({
      success: true,
      message: `请完成支付以升级到${plan.name}`,
      data: {
        planId: plan.id,
        planName: plan.name,
        price: plan.price,
        action: 'upgrade',
        needPayment: true,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
