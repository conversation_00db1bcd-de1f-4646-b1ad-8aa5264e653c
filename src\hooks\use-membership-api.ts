import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  MembershipPlan,
  CreateMembershipPlanInput,
  UpdateMembershipPlanInput,
} from '@/types/membership';

// 获取所有会员套餐
export function useMembershipPlans() {
  return useQuery<MembershipPlan[]>({
    queryKey: ['membership-plans'],
    queryFn: async () => {
      const response = await fetch('/api/admin/membership-plans');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '获取会员套餐失败');
      }

      return result.data;
    },
  });
}

// 获取单个会员套餐
export function useMembershipPlan(id: string) {
  return useQuery<MembershipPlan>({
    queryKey: ['membership-plan', id],
    queryFn: async () => {
      const response = await fetch(`/api/admin/membership-plans/${id}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '获取会员套餐失败');
      }

      return result.data;
    },
    enabled: !!id,
  });
}

// 创建会员套餐
export function useCreateMembershipPlan() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateMembershipPlanInput) => {
      const response = await fetch('/api/admin/membership-plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '创建会员套餐失败');
      }

      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membership-plans'] });
      toast.success('会员套餐创建成功');
    },
    onError: (error: Error) => {
      toast.error(error.message || '创建会员套餐失败');
    },
  });
}

// 更新会员套餐
export function useUpdateMembershipPlan() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateMembershipPlanInput;
    }) => {
      const response = await fetch(`/api/admin/membership-plans/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '更新会员套餐失败');
      }

      return result.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['membership-plans'] });
      queryClient.invalidateQueries({ queryKey: ['membership-plan', id] });
      toast.success('会员套餐更新成功');
    },
    onError: (error: Error) => {
      toast.error(error.message || '更新会员套餐失败');
    },
  });
}

// 删除会员套餐
export function useDeleteMembershipPlan() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/admin/membership-plans/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '删除会员套餐失败');
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['membership-plans'] });
      toast.success('会员套餐删除成功');
    },
    onError: (error: Error) => {
      toast.error(error.message || '删除会员套餐失败');
    },
  });
}
