/**
 * Example Unit Test
 *
 * This is an example of how to write proper unit tests in this project.
 * Replace this with actual unit tests for your utility functions.
 */

describe('Example Unit Tests', () => {
  test('should demonstrate basic test structure', () => {
    // Arrange
    const input = 'hello world';
    const expected = 'HELLO WORLD';

    // Act
    const result = input.toUpperCase();

    // Assert
    expect(result).toBe(expected);
  });

  test('should demonstrate async test structure', async () => {
    // Arrange
    const promise = Promise.resolve('test value');

    // Act
    const result = await promise;

    // Assert
    expect(result).toBe('test value');
  });

  test('should demonstrate mock usage', () => {
    // Arrange
    const mockFn = jest.fn();
    mockFn.mockReturnValue('mocked value');

    // Act
    const result = mockFn();

    // Assert
    expect(mockFn).toHaveBeenCalled();
    expect(result).toBe('mocked value');
  });
});

describe('Math Operations', () => {
  test('should add two numbers correctly', () => {
    expect(2 + 2).toBe(4);
  });

  test('should multiply numbers correctly', () => {
    expect(3 * 4).toBe(12);
  });
});
