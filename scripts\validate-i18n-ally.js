#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 验证 i18n Ally 配置和翻译文件结构
 */
class I18nAllyValidator {
  constructor() {
    this.messagesDir = path.join(process.cwd(), 'messages');
    this.locales = ['zh', 'en'];
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 运行所有验证
   */
  async validate() {
    console.log('🔍 验证 i18n Ally 配置和翻译文件结构...\n');

    this.validateDirectoryStructure();
    this.validateConfigFiles();
    await this.validateTranslationFiles();
    this.validateVSCodeSettings();

    this.printResults();
    return this.errors.length === 0;
  }

  /**
   * 验证目录结构
   */
  validateDirectoryStructure() {
    console.log('📁 检查目录结构...');

    if (!fs.existsSync(this.messagesDir)) {
      this.errors.push('messages 目录不存在');
      return;
    }

    for (const locale of this.locales) {
      const localeDir = path.join(this.messagesDir, locale);
      if (!fs.existsSync(localeDir)) {
        this.errors.push(`语言目录 messages/${locale} 不存在`);
      }
    }

    console.log('✅ 目录结构检查完成\n');
  }

  /**
   * 验证配置文件
   */
  validateConfigFiles() {
    console.log('⚙️ 检查配置文件...');

    // 检查 i18n-ally.config.js
    const configFile = path.join(process.cwd(), 'i18n-ally.config.js');
    if (!fs.existsSync(configFile)) {
      this.warnings.push('i18n-ally.config.js 配置文件不存在');
    }

    // 检查 VSCode 设置
    const vscodeSettings = path.join(process.cwd(), '.vscode', 'settings.json');
    if (!fs.existsSync(vscodeSettings)) {
      this.warnings.push('.vscode/settings.json 文件不存在');
    }

    console.log('✅ 配置文件检查完成\n');
  }

  /**
   * 验证翻译文件
   */
  async validateTranslationFiles() {
    console.log('📝 检查翻译文件...');

    const zhFiles = this.getTranslationFiles('zh');
    const enFiles = this.getTranslationFiles('en');

    // 检查文件对称性
    const zhFileNames = zhFiles.map(f => path.basename(f));
    const enFileNames = enFiles.map(f => path.basename(f));

    const missingInEn = zhFileNames.filter(f => !enFileNames.includes(f));
    const missingInZh = enFileNames.filter(f => !zhFileNames.includes(f));

    if (missingInEn.length > 0) {
      this.errors.push(`英文翻译文件缺失: ${missingInEn.join(', ')}`);
    }

    if (missingInZh.length > 0) {
      this.errors.push(`中文翻译文件缺失: ${missingInZh.join(', ')}`);
    }

    // 验证 JSON 格式
    for (const file of [...zhFiles, ...enFiles]) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        JSON.parse(content);
      } catch (error) {
        this.errors.push(`JSON 格式错误 ${file}: ${error.message}`);
      }
    }

    // 验证键结构一致性
    await this.validateKeyStructure(zhFiles, enFiles);

    console.log('✅ 翻译文件检查完成\n');
  }

  /**
   * 验证键结构一致性
   */
  async validateKeyStructure(zhFiles, enFiles) {
    for (let i = 0; i < zhFiles.length; i++) {
      const zhFile = zhFiles[i];
      const enFile = enFiles.find(f => path.basename(f) === path.basename(zhFile));

      if (!enFile) continue;

      try {
        const zhContent = JSON.parse(fs.readFileSync(zhFile, 'utf8'));
        const enContent = JSON.parse(fs.readFileSync(enFile, 'utf8'));

        const zhKeys = this.getAllKeys(zhContent);
        const enKeys = this.getAllKeys(enContent);

        const missingInEn = zhKeys.filter(key => !enKeys.includes(key));
        const missingInZh = enKeys.filter(key => !zhKeys.includes(key));

        if (missingInEn.length > 0) {
          this.warnings.push(`${path.basename(zhFile)} - 英文缺失键: ${missingInEn.slice(0, 5).join(', ')}${missingInEn.length > 5 ? '...' : ''}`);
        }

        if (missingInZh.length > 0) {
          this.warnings.push(`${path.basename(zhFile)} - 中文缺失键: ${missingInZh.slice(0, 5).join(', ')}${missingInZh.length > 5 ? '...' : ''}`);
        }
      } catch (error) {
        this.errors.push(`键结构验证失败 ${path.basename(zhFile)}: ${error.message}`);
      }
    }
  }

  /**
   * 获取所有翻译键（扁平化）
   */
  getAllKeys(obj, prefix = '') {
    let keys = [];
    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      if (typeof value === 'object' && value !== null) {
        keys = keys.concat(this.getAllKeys(value, fullKey));
      } else {
        keys.push(fullKey);
      }
    }
    return keys;
  }

  /**
   * 获取指定语言的所有翻译文件
   */
  getTranslationFiles(locale) {
    const localeDir = path.join(this.messagesDir, locale);
    if (!fs.existsSync(localeDir)) return [];

    return fs.readdirSync(localeDir)
      .filter(file => file.endsWith('.json'))
      .map(file => path.join(localeDir, file));
  }

  /**
   * 验证 VSCode 设置
   */
  validateVSCodeSettings() {
    console.log('🔧 检查 VSCode 设置...');

    const settingsFile = path.join(process.cwd(), '.vscode', 'settings.json');
    if (!fs.existsSync(settingsFile)) {
      this.warnings.push('VSCode 设置文件不存在');
      return;
    }

    try {
      const settings = JSON.parse(fs.readFileSync(settingsFile, 'utf8'));
      
      if (!settings['i18n-ally.localesPaths']) {
        this.warnings.push('VSCode 设置中缺少 i18n-ally.localesPaths 配置');
      }

      if (!settings['i18n-ally.keystyle']) {
        this.warnings.push('VSCode 设置中缺少 i18n-ally.keystyle 配置');
      }

      if (!settings['i18n-ally.pathMatcher']) {
        this.warnings.push('VSCode 设置中缺少 i18n-ally.pathMatcher 配置');
      }

    } catch (error) {
      this.errors.push(`VSCode 设置文件格式错误: ${error.message}`);
    }

    console.log('✅ VSCode 设置检查完成\n');
  }

  /**
   * 打印验证结果
   */
  printResults() {
    console.log('📊 验证结果:');
    console.log('='.repeat(50));

    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('🎉 所有检查都通过了！i18n Ally 应该可以正常工作。');
    } else {
      if (this.errors.length > 0) {
        console.log('❌ 错误:');
        this.errors.forEach(error => console.log(`  - ${error}`));
        console.log('');
      }

      if (this.warnings.length > 0) {
        console.log('⚠️ 警告:');
        this.warnings.forEach(warning => console.log(`  - ${warning}`));
        console.log('');
      }
    }

    console.log(`总计: ${this.errors.length} 个错误, ${this.warnings.length} 个警告`);
  }
}

// 运行验证
if (require.main === module) {
  const validator = new I18nAllyValidator();
  validator.validate().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = I18nAllyValidator;
