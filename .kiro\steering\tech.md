# Technology Stack

## Core Framework

- **Next.js 14+** with App Router architecture
- **TypeScript 5.8+** with strict type checking enabled
- **React 18** with Server Components and Suspense
- **Node.js 18+** runtime requirement

## Frontend Technologies

- **Tailwind CSS** for styling with custom design system
- **shadcn/ui** component library built on Radix UI primitives
- **Framer Motion** for animations and transitions
- **React Three Fiber** for 3D graphics and interactive elements
- **TanStack Query** for server state management and caching
- **React Hook Form** with Zod validation for form handling
- **next-intl** for internationalization (English/Chinese)
- **next-themes** for dark/light mode support

## Backend & Database

- **Prisma ORM** with PostgreSQL database
- **NextAuth v5** for authentication with OAuth providers
- **bcryptjs** for password hashing
- **Resend** for transactional email delivery
- **Zod** for runtime schema validation

## Development Tools

- **ESLint** with comprehensive rules for code quality
- **Prettier** for code formatting
- **<PERSON>sky** + **lint-staged** for pre-commit hooks
- **Jest** + **Testing Library** for unit and integration testing
- **TypeScript** strict mode with path mapping (@/\* aliases)

## Common Commands

### Development

```bash
npm run dev              # Start development server (localhost:3000)
npm run build           # Production build
npm run start           # Start production server
```

### Code Quality

```bash
npm run lint            # Check linting issues
npm run lint:fix        # Auto-fix linting issues
npm run format          # Format code with Prettier
npm run fix             # Fix linting + formatting together
```

### Testing

```bash
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Generate coverage report
npm run test:unit       # Run unit tests only
npm run test:integration # Run integration tests only
```

### Database Operations

```bash
npx prisma studio       # Open database GUI
npx prisma generate     # Generate Prisma client
npx prisma db push      # Push schema changes to database
npm run db:seed         # Seed database with initial data
npm run db:reset        # Reset database and re-seed
```

### Production Deployment

```bash
npx prisma migrate deploy # Deploy database migrations
npm run build && npm start # Build and start production server
```

## Build System

- **Next.js** handles bundling, optimization, and deployment
- **PostCSS** for CSS processing with Tailwind
- **TypeScript** compilation integrated into Next.js build
- **Prisma** generates type-safe database client
- **ESM modules** used throughout the project
