import { TaskStatus } from '@prisma/client';
import { NextResponse } from 'next/server';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 强制动态渲染
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const userId = session.user.id;

    // 2. 获取用户基本信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        totalIncome: true,
        completedTasks: true,
        balance: true,
        frozenAmount: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 },
      );
    }

    // 3. 获取进行中委托数
    const activeTasksCount = await prisma.task.count({
      where: {
        accepterId: userId,
        status: {
          in: [
            TaskStatus.IN_PROGRESS,
            TaskStatus.PENDING_LOGISTICS,
            TaskStatus.PENDING_REVIEW,
            TaskStatus.PENDING_DELIVERY,
          ],
        },
      },
    });

    // 4. 获取过去30天的统计数据用于计算变化率
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [pastCompletedTasks, pastIncomeTransactions] = await Promise.all([
      // 过去30天完成的委托数
      prisma.task.count({
        where: {
          accepterId: userId,
          status: TaskStatus.COMPLETED,
          completedAt: {
            gte: thirtyDaysAgo,
          },
        },
      }),
      // 过去30天的收入交易
      prisma.walletTransaction.findMany({
        where: {
          userId,
          type: 'COMMISSION',
          status: 'COMPLETED',
          completedAt: {
            gte: thirtyDaysAgo,
          },
        },
        select: {
          amount: true,
        },
      }),
    ]);

    // 5. 计算变化率
    const pastIncome = pastIncomeTransactions.reduce(
      (sum, transaction) => sum + transaction.amount,
      0,
    );
    const previousIncome = user.totalIncome - pastIncome;
    const previousCompletedTasks = user.completedTasks - pastCompletedTasks;

    // 计算酬金变化率
    const earningsChange =
      previousIncome > 0
        ? (pastIncome / previousIncome) * 100
        : pastIncome > 0
          ? 100
          : 0;

    // 计算委托完成变化率
    const tasksChange =
      previousCompletedTasks > 0
        ? (pastCompletedTasks / previousCompletedTasks) * 100
        : pastCompletedTasks > 0
          ? 100
          : 0;

    // 6. 获取过去30天的活跃委托统计用于计算变化率
    const pastActiveTasksCount = await prisma.task.count({
      where: {
        accepterId: userId,
        status: {
          in: [
            TaskStatus.IN_PROGRESS,
            TaskStatus.PENDING_LOGISTICS,
            TaskStatus.PENDING_REVIEW,
            TaskStatus.PENDING_DELIVERY,
          ],
        },
        acceptedAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    const previousActiveTasksCount = activeTasksCount - pastActiveTasksCount;
    const activeTasksChange =
      previousActiveTasksCount > 0
        ? (pastActiveTasksCount / previousActiveTasksCount) * 100
        : pastActiveTasksCount > 0
          ? 100
          : 0;

    // 7. 返回统计数据
    return NextResponse.json({
      success: true,
      data: {
        totalEarnings: user.totalIncome,
        earningsChange: Math.round(earningsChange * 100) / 100,
        completedTasks: user.completedTasks,
        tasksChange: Math.round(tasksChange * 100) / 100,
        activeTasksCount,
        activeTasksChange: Math.round(activeTasksChange * 100) / 100,
        balance: user.balance,
        frozenAmount: user.frozenAmount,
      },
    });
  } catch (error) {
    console.error('获取仪表盘统计数据失败:', error);
    return NextResponse.json(
      { success: false, message: '获取统计数据失败' },
      { status: 500 },
    );
  }
}
