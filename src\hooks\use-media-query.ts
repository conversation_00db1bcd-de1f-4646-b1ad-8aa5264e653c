import { useEffect, useState } from 'react';

type Device = 'mobile' | 'tablet' | 'desktop' | null;

interface MediaQueryReturn {
  device: Device;
  width: number | undefined;
  height: number | undefined;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

export const useMediaQuery = (): MediaQueryReturn => {
  const [device, setDevice] = useState<Device>(null);
  const [dimensions, setDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    // 只在客户端运行，避免SSR问题
    if (typeof window === 'undefined') return;

    const checkDevice = () => {
      if (window.matchMedia('(max-width: 640px)').matches) {
        setDevice('mobile');
      } else if (
        window.matchMedia('(min-width: 641px) and (max-width: 1024px)').matches
      ) {
        setDevice('tablet');
      } else {
        setDevice('desktop');
      }

      // 同时设置dimensions
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    checkDevice();

    window.addEventListener('resize', checkDevice);

    return () => {
      window.removeEventListener('resize', checkDevice);
    };
  }, []);

  return {
    device,
    width: dimensions?.width,
    height: dimensions?.height,
    isMobile: device === 'mobile',
    isTablet: device === 'tablet',
    isDesktop: device === 'desktop',
  };
};
