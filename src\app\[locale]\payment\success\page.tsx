'use client';

import { CheckCircle, Loader2 } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState, Suspense } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@/i18n/navigation';

function PaymentSuccessContent() {
  const t = useTranslations('payment');
  const searchParams = useSearchParams();
  const [orderStatus, setOrderStatus] = useState<
    'loading' | 'success' | 'failed'
  >('loading');
  const orderId = searchParams.get('order_id');

  useEffect(() => {
    if (orderId) {
      // 模拟查询订单状态，实际项目中会调用API
      setTimeout(() => {
        setOrderStatus('success');
      }, 2000);
    } else {
      setOrderStatus('success'); // 如果没有订单ID，直接显示成功
    }
  }, [orderId]);

  if (orderStatus === 'loading') {
    return (
      <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4' />
          <p>{t('success.confirmingStatus')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <CheckCircle className='h-16 w-16 text-green-500 mx-auto mb-4' />
          <CardTitle className='text-2xl text-green-600'>
            {orderStatus === 'success' ? t('success.title') : t('success.confirming')}
          </CardTitle>
        </CardHeader>
        <CardContent className='text-center space-y-4'>
          <p className='text-muted-foreground'>
            {orderStatus === 'success'
              ? t('success.successMessage')
              : t('success.processingMessage')}
          </p>
          {orderId && (
            <div className='text-sm text-muted-foreground'>
              {t('success.orderId')}{orderId}
            </div>
          )}
          <div className='flex gap-4 justify-center'>
            <Button asChild>
              <Link href='/dashboard'>{t('success.backToHome')}</Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href='/orders'>{t('success.viewOrders')}</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function PaymentSuccessPage() {
  const t = useTranslations('payment');

  return (
    <Suspense
      fallback={
        <div className='container mx-auto p-6 flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4' />
            <p>{t('success.loading')}</p>
          </div>
        </div>
      }
    >
      <PaymentSuccessContent />
    </Suspense>
  );
}
