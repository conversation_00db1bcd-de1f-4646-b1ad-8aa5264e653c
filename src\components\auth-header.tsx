'use client';

import { ArrowLeft } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { LanguageSwitcher } from '@/components/language-switcher';
import { Button } from '@/components/ui/button';
import { Link } from '@/i18n/navigation';

interface AuthHeaderProps {
  className?: string;
}

export function AuthHeader({ className }: AuthHeaderProps) {
  const t = useTranslations('auth');

  return (
    <header className={`flex items-center justify-between p-4 md:p-6 ${className || ''}`}>
      {/* 返回首页 */}
      <Link href='/' className='flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors'>
        <ArrowLeft className='w-4 h-4' />
        <span className='text-sm font-medium'>{t('navigation.backToHome')}</span>
      </Link>

      {/* 语言切换 */}
      <LanguageSwitcher variant='dropdown' size='sm' />
    </header>
  );
}
