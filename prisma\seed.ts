import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('正在添加初始数据...');

  // 创建平台数据
  const platforms = await Promise.all([
    prisma.platform.upsert({
      where: { name: 'Amazon' },
      update: {},
      create: {
        name: 'Amazon',
        status: 'ACTIVE',
      },
    }),
    prisma.platform.upsert({
      where: { name: 'eBay' },
      update: {},
      create: {
        name: 'eBay',
        status: 'ACTIVE',
      },
    }),
    prisma.platform.upsert({
      where: { name: 'Shopify' },
      update: {},
      create: {
        name: 'Shopify',
        status: 'ACTIVE',
      },
    }),
  ]);

  console.log('已创建平台数据:', platforms.map(p => p.name).join(', '));

  // 创建分类数据
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { name: '电子产品' },
      update: {},
      create: {
        name: '电子产品',
        status: 'ACTIVE',
      },
    }),
    prisma.category.upsert({
      where: { name: '服装' },
      update: {},
      create: {
        name: '服装',
        status: 'ACTIVE',
      },
    }),
    prisma.category.upsert({
      where: { name: '家居用品' },
      update: {},
      create: {
        name: '家居用品',
        status: 'ACTIVE',
      },
    }),
  ]);

  console.log('已创建分类数据:', categories.map(c => c.name).join(', '));

  // 创建退款类型数据
  const chargebackTypes = await Promise.all([
    prisma.chargebackType.upsert({
      where: { name: '商品未收到' },
      update: {},
      create: {
        name: '商品未收到',
        rate: 0.1,
        status: 'ACTIVE',
      },
    }),
    prisma.chargebackType.upsert({
      where: { name: '商品与描述不符' },
      update: {},
      create: {
        name: '商品与描述不符',
        rate: 0.08,
        status: 'ACTIVE',
      },
    }),
    prisma.chargebackType.upsert({
      where: { name: '商品质量问题' },
      update: {},
      create: {
        name: '商品质量问题',
        rate: 0.12,
        status: 'ACTIVE',
      },
    }),
  ]);

  console.log(
    '已创建退款类型数据:',
    chargebackTypes.map(ct => ct.name).join(', ')
  );

  // 创建支付方式数据
  const paymentMethods = await Promise.all([
    prisma.paymentMethod.upsert({
      where: { name: '信用卡' },
      update: {},
      create: {
        name: '信用卡',
        rate: 0.03,
        status: 'ACTIVE',
      },
    }),
    prisma.paymentMethod.upsert({
      where: { name: 'PayPal' },
      update: {},
      create: {
        name: 'PayPal',
        rate: 0.035,
        status: 'ACTIVE',
      },
    }),
    prisma.paymentMethod.upsert({
      where: { name: '银行转账' },
      update: {},
      create: {
        name: '银行转账',
        rate: 0.01,
        status: 'ACTIVE',
      },
    }),
  ]);

  console.log(
    '已创建支付方式数据:',
    paymentMethods.map(pm => pm.name).join(', ')
  );

  // 创建系统费率数据
  await prisma.systemRate.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      noEvidenceExtraRate: 0.05,
      depositRatio: 0.1,
      bankWithdrawalRate: 0.02,
      erc20WithdrawalRate: 0.01,
      trc20WithdrawalRate: 0.005,
      minimumWithdrawalAmount: 50,
    },
  });

  console.log('已创建系统费率数据');

  // 创建会员计划数据
  const membershipPlans = await Promise.all([
    prisma.membershipPlan.upsert({
      where: { name: '免费版' },
      update: {},
      create: {
        name: '免费版',
        price: 0,
        period: '月',
        maxTasks: 5,
        taskTypes: ['基础委托'],
        platformRate: 0.15,
        whitelistSlots: 0,
        supportLevel: '基础支持',
        features: ['基础功能', '5个委托/月'],
      },
    }),
    prisma.membershipPlan.upsert({
      where: { name: '专业版' },
      update: {},
      create: {
        name: '专业版',
        price: 29.99,
        period: '月',
        maxTasks: 50,
        taskTypes: ['基础委托', '高级委托'],
        platformRate: 0.12,
        whitelistSlots: 5,
        supportLevel: '优先支持',
        features: ['所有功能', '50个委托/月', '优先支持', '白名单管理'],
      },
    }),
    prisma.membershipPlan.upsert({
      where: { name: '商业版' },
      update: {},
      create: {
        name: '商业版',
        price: 99.99,
        period: '月',
        maxTasks: null, // 无限制
        taskTypes: ['基础委托', '高级委托', '企业委托'],
        platformRate: 0.08,
        whitelistSlots: 20,
        supportLevel: '专属支持',
        features: [
          '所有功能',
          '无限委托',
          '专属支持',
          '高级白名单管理',
          '数据分析',
        ],
      },
    }),
  ]);

  console.log(
    '已创建会员计划数据:',
    membershipPlans.map(mp => mp.name).join(', ')
  );

  // 创建支付配置数据
  const paymentConfigs = await Promise.all([
    prisma.paymentConfig.upsert({
      where: { provider: 'nowpayments' },
      update: {},
      create: {
        provider: 'nowpayments',
        name: 'NOWPayments',
        isEnabled: true,
        settings: {
          apiKey: 'YOUR_API_KEY',
          environment: 'sandbox',
          supportedCurrencies: ['USD', 'EUR', 'BTC', 'ETH'],
        },
      },
    }),
    prisma.paymentConfig.upsert({
      where: { provider: 'yunpay' },
      update: {},
      create: {
        provider: 'yunpay',
        name: '云付支付',
        isEnabled: true,
        settings: {
          merchantId: 'YOUR_MERCHANT_ID',
          secretKey: 'YOUR_SECRET_KEY',
          environment: 'sandbox',
          supportedMethods: ['alipay', 'wechat', 'bank_card'],
        },
      },
    }),
  ]);

  console.log(
    '已创建支付配置数据:',
    paymentConfigs.map(pc => pc.name).join(', ')
  );

  console.log('\n✅ 所有初始数据已成功添加！');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async e => {
    console.error('添加初始数据时出错:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
