// 性能监控系统
'use client';

// 性能指标接口
export interface PerformanceMetrics {
  events: PerformanceEvent[];
  // Core Web Vitals
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte

  // 自定义指标
  domContentLoaded?: number;
  loadComplete?: number;
  firstPaint?: number;
  navigationStart?: number;

  // 资源加载
  resourceCount?: number;
  totalResourceSize?: number;

  // 内存使用
  usedJSHeapSize?: number;
  totalJSHeapSize?: number;
  jsHeapSizeLimit?: number;

  // 网络信息
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

// 性能事件接口
export interface PerformanceEvent {
  name: string;
  startTime: number;
  duration: number;
  metadata?: Record<string, any>;
}

// 性能监控配置
export interface PerformanceConfig {
  enableWebVitals?: boolean;
  enableResourceTiming?: boolean;
  enableUserTiming?: boolean;
  enableMemoryMonitoring?: boolean;
  enableNetworkMonitoring?: boolean;
  sampleRate?: number;
  reportInterval?: number;
  onMetric?: (metric: string, value: number, metadata?: any) => void;
  onReport?: (metrics: PerformanceMetrics) => void;
}

// 性能监控类
export class PerformanceMonitor {
  private config: PerformanceConfig;
  private metrics: PerformanceMetrics = {
    events: [],
  };
  private events: PerformanceEvent[] = [];
  private observers: PerformanceObserver[] = [];
  private reportTimer?: NodeJS.Timeout;

  constructor(config: PerformanceConfig = {}) {
    this.config = {
      enableWebVitals: true,
      enableResourceTiming: true,
      enableUserTiming: true,
      enableMemoryMonitoring: true,
      enableNetworkMonitoring: true,
      sampleRate: 1.0,
      reportInterval: 30000, // 30秒
      ...config,
    };

    this.init();
  }

  private init() {
    if (typeof window === 'undefined') return;

    // 采样率检查
    if (Math.random() > this.config.sampleRate!) return;

    // 初始化各种监控
    if (this.config.enableWebVitals) {
      this.initWebVitals();
    }

    if (this.config.enableResourceTiming) {
      this.initResourceTiming();
    }

    if (this.config.enableUserTiming) {
      this.initUserTiming();
    }

    if (this.config.enableMemoryMonitoring) {
      this.initMemoryMonitoring();
    }

    if (this.config.enableNetworkMonitoring) {
      this.initNetworkMonitoring();
    }

    // 设置定期报告
    if (this.config.reportInterval) {
      this.reportTimer = setInterval(() => {
        this.report();
      }, this.config.reportInterval);
    }

    // 页面卸载时报告
    window.addEventListener('beforeunload', () => {
      this.report();
    });
  }

  // 初始化 Web Vitals 监控
  private initWebVitals() {
    // FCP - First Contentful Paint
    this.observePerformanceEntry('paint', entries => {
      entries.forEach(entry => {
        if (entry.name === 'first-contentful-paint') {
          this.recordMetric('fcp', entry.startTime);
        } else if (entry.name === 'first-paint') {
          this.recordMetric('firstPaint', entry.startTime);
        }
      });
    });

    // LCP - Largest Contentful Paint
    this.observePerformanceEntry('largest-contentful-paint', entries => {
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        this.recordMetric('lcp', lastEntry.startTime);
      }
    });

    // FID - First Input Delay
    this.observePerformanceEntry('first-input', entries => {
      const firstInput = entries[0] as any;
      if (firstInput && firstInput.processingStart) {
        this.recordMetric(
          'fid',
          firstInput.processingStart - firstInput.startTime,
        );
      }
    });

    // CLS - Cumulative Layout Shift
    let clsValue = 0;
    this.observePerformanceEntry('layout-shift', entries => {
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          this.recordMetric('cls', clsValue);
        }
      });
    });

    // Navigation Timing
    this.observePerformanceEntry('navigation', entries => {
      const navigation = entries[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.recordMetric(
          'ttfb',
          navigation.responseStart - navigation.requestStart,
        );
        this.recordMetric(
          'domContentLoaded',
          navigation.domContentLoadedEventEnd - navigation.fetchStart,
        );
        this.recordMetric(
          'loadComplete',
          navigation.loadEventEnd - navigation.fetchStart,
        );
        this.recordMetric('navigationStart', navigation.fetchStart);
      }
    });
  }

  // 初始化资源时间监控
  private initResourceTiming() {
    this.observePerformanceEntry('resource', entries => {
      let totalSize = 0;
      let resourceCount = 0;

      entries.forEach((entry: any) => {
        resourceCount++;
        if (entry.transferSize) {
          totalSize += entry.transferSize;
        }

        // 记录慢资源
        if (entry.duration > 1000) {
          this.recordEvent('slow-resource', entry.startTime, entry.duration, {
            name: entry.name,
            type: entry.initiatorType,
            size: entry.transferSize,
          });
        }
      });

      this.recordMetric('resourceCount', resourceCount);
      this.recordMetric('totalResourceSize', totalSize);
    });
  }

  // 初始化用户时间监控
  private initUserTiming() {
    this.observePerformanceEntry('measure', entries => {
      entries.forEach(entry => {
        this.recordEvent(entry.name, entry.startTime, entry.duration);
      });
    });
  }

  // 初始化内存监控
  private initMemoryMonitoring() {
    const updateMemoryMetrics = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.recordMetric('usedJSHeapSize', memory.usedJSHeapSize);
        this.recordMetric('totalJSHeapSize', memory.totalJSHeapSize);
        this.recordMetric('jsHeapSizeLimit', memory.jsHeapSizeLimit);
      }
    };

    // 立即记录一次
    updateMemoryMetrics();

    // 定期更新
    setInterval(updateMemoryMetrics, 10000); // 10秒
  }

  // 初始化网络监控
  private initNetworkMonitoring() {
    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    if (connection) {
      const updateNetworkMetrics = () => {
        this.recordMetric('connectionType', connection.type);
        this.recordMetric('effectiveType', connection.effectiveType);
        this.recordMetric('downlink', connection.downlink);
        this.recordMetric('rtt', connection.rtt);
      };

      updateNetworkMetrics();
      connection.addEventListener('change', updateNetworkMetrics);
    }
  }

  // 观察性能条目
  private observePerformanceEntry(
    entryType: string,
    callback: (entries: PerformanceEntry[]) => void,
  ) {
    if (!('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver(list => {
        callback(list.getEntries());
      });

      observer.observe({ entryTypes: [entryType] });
      this.observers.push(observer);
    } catch (error) {
      console.warn(`Failed to observe ${entryType}:`, error);
    }
  }

  // 记录指标
  private recordMetric(name: string, value: any, metadata?: any) {
    (this.metrics as any)[name] = value;
    this.config.onMetric?.(name, value, metadata);
  }

  // 记录事件
  private recordEvent(
    name: string,
    startTime: number,
    duration: number,
    metadata?: any,
  ) {
    this.events.push({ name, startTime, duration, metadata });
  }

  // 手动记录性能标记
  public mark(name: string) {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  }

  // 手动记录性能测量
  public measure(name: string, startMark?: string, endMark?: string) {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
      } catch (error) {
        console.warn(`Failed to measure ${name}:`, error);
      }
    }
  }

  // 记录自定义指标
  public recordCustomMetric(name: string, value: number, metadata?: any) {
    this.recordMetric(name, value, metadata);
  }

  // 记录用户交互
  public recordInteraction(type: string, target?: string, duration?: number) {
    this.recordEvent(`interaction-${type}`, performance.now(), duration || 0, {
      target,
      type,
    });
  }

  // 记录错误
  public recordError(error: Error, context?: string) {
    this.recordEvent('error', performance.now(), 0, {
      message: error.message,
      stack: error.stack,
      context,
    });
  }

  // 获取当前指标
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // 获取事件列表
  public getEvents(): PerformanceEvent[] {
    return [...this.events];
  }

  // 生成性能报告
  public report() {
    const report = {
      ...this.metrics,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      events: this.events.slice(-50), // 只保留最近50个事件
    };

    this.config.onReport?.(report);

    // 清理旧事件
    if (this.events.length > 100) {
      this.events = this.events.slice(-50);
    }
  }

  // 清理资源
  public cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    if (this.reportTimer) {
      clearInterval(this.reportTimer);
    }
  }
}

// 全局性能监控实例
let globalMonitor: PerformanceMonitor | null = null;

// 初始化性能监控
export function initPerformanceMonitoring(config?: PerformanceConfig) {
  if (globalMonitor) {
    globalMonitor.cleanup();
  }

  globalMonitor = new PerformanceMonitor(config);
  return globalMonitor;
}

// 获取全局监控实例
export function getPerformanceMonitor(): PerformanceMonitor | null {
  return globalMonitor;
}

// 便捷函数
export function markPerformance(name: string) {
  globalMonitor?.mark(name);
}

export function measurePerformance(
  name: string,
  startMark?: string,
  endMark?: string,
) {
  globalMonitor?.measure(name, startMark, endMark);
}

export function recordCustomMetric(
  name: string,
  value: number,
  metadata?: any,
) {
  globalMonitor?.recordCustomMetric(name, value, metadata);
}

export function recordInteraction(
  type: string,
  target?: string,
  duration?: number,
) {
  globalMonitor?.recordInteraction(type, target, duration);
}

export function recordError(error: Error, context?: string) {
  globalMonitor?.recordError(error, context);
}

// 性能预算检查
export function checkPerformanceBudget(
  budgets: Record<string, number>,
): Record<string, boolean> {
  const metrics = globalMonitor?.getMetrics() || { events: [] };
  const results: Record<string, boolean> = {};

  Object.entries(budgets).forEach(([metric, budget]) => {
    const value = (metrics as any)[metric];
    results[metric] = typeof value === 'number' ? value <= budget : true;
  });

  return results;
}

export default PerformanceMonitor;
