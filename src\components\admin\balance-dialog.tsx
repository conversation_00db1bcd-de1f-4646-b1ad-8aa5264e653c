'use client';

import {
  CreditCard,
  Wallet,
  Save,
  AlertTriangle,
  Plus,
  Minus,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { User } from '@/types/user';

interface BalanceDialogProps {
  user: User;
  type: 'charge' | 'deduct';
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserUpdate?: (user: User) => void;
}

export function BalanceDialog({
  user,
  type,
  open,
  onOpenChange,
  onUserUpdate,
}: BalanceDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [amount, setAmount] = useState('');

  const isCharge = type === 'charge';
  const title = isCharge ? '账户充值' : '账户扣款';
  const icon = isCharge ? (
    <CreditCard className='h-5 w-5' />
  ) : (
    <Wallet className='h-5 w-5' />
  );
  const buttonText = isCharge ? '确认充值' : '确认扣款';
  const amountLabel = isCharge ? '充值金额' : '扣款金额';

  // 获取用户名首字母
  const getInitials = (nickname: string) => {
    return nickname.charAt(0).toUpperCase();
  };

  // 表单验证
  const validateForm = () => {
    if (!amount.trim()) {
      toast.error('请输入金额');
      return false;
    }
    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      toast.error('请输入有效的金额');
      return false;
    }
    if (amountValue > 999999) {
      toast.error('金额不能超过999,999');
      return false;
    }
    if (!isCharge && amountValue > user.balance) {
      toast.error('扣款金额不能超过当前余额');
      return false;
    }
    return true;
  };

  // 关闭对话框
  const handleClose = () => {
    onOpenChange(false);
    setAmount('');
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const amountValue = parseFloat(amount);

      // 调用真实的API更新余额
      const response = await fetch(`/api/admin/users/${user.id}/balance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: isCharge ? 'charge' : 'deduct',
          amount: amountValue,
          reason: `管理员${isCharge ? '充值' : '扣款'}操作`,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        toast.error(result.error || `${title}失败`);
        return;
      }

      // 调用更新回调，使用后端返回的真实用户数据
      onUserUpdate?.(result.user);

      toast.success(`${title}成功，金额：$${amountValue.toFixed(2)}`);
      handleClose();
    } catch (error) {
      console.error('操作失败:', error);
      toast.error('操作失败', {
        description: '请重试或联系客服',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='w-full max-w-md mx-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            {icon}
            {title}
          </DialogTitle>
          <DialogDescription>
            {isCharge ? '为用户账户充值' : '从用户账户扣款'}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 用户信息 */}
          <div className='flex items-center gap-3 p-3 bg-gray-50 rounded-lg'>
            <Avatar className='h-10 w-10'>
              <AvatarImage src={user.avatar} alt={user.nickname} />
              <AvatarFallback>{getInitials(user.nickname)}</AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <p className='font-medium'>{user.nickname}</p>
              <p className='text-sm text-gray-600'>{user.email}</p>
            </div>
          </div>

          {/* 当前余额 */}
          <div className='flex items-center justify-between p-3 bg-blue-50 rounded-lg'>
            <span className='text-sm font-medium'>当前余额</span>
            <span className='text-lg font-bold text-blue-600'>
              ${user.balance.toFixed(2)}
            </span>
          </div>

          {/* 金额输入 */}
          <div className='space-y-2'>
            <Label htmlFor='amount'>{amountLabel}</Label>
            <div className='relative'>
              <span className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500'>
                $
              </span>
              <Input
                id='amount'
                type='number'
                placeholder='0.00'
                value={amount}
                onChange={e => setAmount(e.target.value)}
                className='pl-8'
                min='0'
                step='0.01'
                disabled={isLoading}
              />
            </div>
            <p className='text-xs text-gray-500'>
              {isCharge
                ? '输入要充值的金额'
                : `最多可扣款 $${user.balance.toFixed(2)}`}
            </p>
          </div>

          {/* 预计余额 */}
          {amount && !isNaN(parseFloat(amount)) && (
            <div className='flex items-center justify-between p-3 bg-green-50 rounded-lg'>
              <span className='text-sm font-medium'>预计余额</span>
              <span className='text-lg font-bold text-green-600'>
                $
                {(
                  user.balance +
                  (isCharge ? 1 : -1) * parseFloat(amount)
                ).toFixed(2)}
              </span>
            </div>
          )}

          {/* 警告提示 */}
          {!isCharge && (
            <div className='flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg'>
              <AlertTriangle className='h-4 w-4 text-yellow-600 mt-0.5' />
              <div className='text-sm text-yellow-700'>
                <p className='font-medium'>扣款提醒</p>
                <p>此操作将从用户账户中扣除指定金额，请确认金额无误。</p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className='flex gap-2'>
          <Button variant='outline' onClick={handleClose} disabled={isLoading}>
            取消
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !amount.trim()}
            className={
              isCharge
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-red-600 hover:bg-red-700'
            }
          >
            {isLoading ? '处理中...' : buttonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
