import { UserRole } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { UpdateMembershipPlanSchema } from '@/types/membership';

// 获取单个会员套餐
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const plan = await prisma.membershipPlan.findUnique({
      where: { id },
    });

    if (!plan) {
      return NextResponse.json(
        { success: false, message: '会员套餐不存在' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: plan,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 更新会员套餐
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const body = await request.json();

    // 检查会员套餐是否存在
    const existingPlan = await prisma.membershipPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return NextResponse.json(
        { success: false, message: '会员套餐不存在' },
        { status: 404 },
      );
    }

    // 构建更新数据，只包含提供的字段
    const updateData: any = {};

    // 允许的字段列表
    const allowedFields = [
      'name',
      'price',
      'period',
      'maxTasks',
      'taskTypes',
      'platformRate',
      'whitelistSlots',
      'supportLevel',
      'features',
      'isActive',
    ];

    // 只包含提供的字段
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    // 如果没有提供任何可更新的字段
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, message: '没有提供可更新的字段' },
        { status: 400 },
      );
    }

    // 如果提供了名称且与现有名称不同，检查冲突
    if (updateData.name && updateData.name !== existingPlan.name) {
      const duplicatePlan = await prisma.membershipPlan.findFirst({
        where: {
          name: updateData.name,
          id: { not: id },
        },
      });

      if (duplicatePlan) {
        return NextResponse.json(
          { success: false, message: '套餐名称已存在' },
          { status: 400 },
        );
      }
    }

    // 更新会员套餐
    const updatedPlan = await prisma.membershipPlan.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      data: updatedPlan,
      message: '会员套餐更新成功',
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 部分更新会员套餐（如状态更改）
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;
    const body = await request.json();

    // 检查会员套餐是否存在
    const existingPlan = await prisma.membershipPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return NextResponse.json(
        { success: false, message: '会员套餐不存在' },
        { status: 404 },
      );
    }

    // 构建更新数据，只包含提供的字段
    const updateData: any = {};

    // 允许的字段列表
    const allowedFields = [
      'name',
      'price',
      'period',
      'maxTasks',
      'taskTypes',
      'platformRate',
      'whitelistSlots',
      'supportLevel',
      'features',
      'isActive',
    ];

    // 只包含提供的字段
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    // 如果提供了名称且与现有名称不同，检查冲突
    if (updateData.name && updateData.name !== existingPlan.name) {
      const duplicatePlan = await prisma.membershipPlan.findFirst({
        where: {
          name: updateData.name,
          id: { not: id },
        },
      });

      if (duplicatePlan) {
        return NextResponse.json(
          { success: false, message: '套餐名称已存在' },
          { status: 400 },
        );
      }
    }

    // 更新会员套餐
    const updatedPlan = await prisma.membershipPlan.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      data: updatedPlan,
      message: '会员套餐更新成功',
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// 删除会员套餐
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user || (session.user as any).role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const { id } = await params;

    // 检查会员套餐是否存在
    const existingPlan = await prisma.membershipPlan.findUnique({
      where: { id },
    });

    if (!existingPlan) {
      return NextResponse.json(
        { success: false, message: '会员套餐不存在' },
        { status: 404 },
      );
    }

    // 检查是否有用户正在使用此套餐
    const usersWithPlan = await prisma.user.findFirst({
      where: { memberPlan: existingPlan.name as any },
    });

    if (usersWithPlan) {
      return NextResponse.json(
        { success: false, message: '无法删除：仍有用户正在使用此套餐' },
        { status: 400 },
      );
    }

    // 删除会员套餐
    await prisma.membershipPlan.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: '会员套餐删除成功',
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
