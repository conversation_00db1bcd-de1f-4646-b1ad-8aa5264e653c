"use client";

import React from 'react';

/**
 * 跳转到主内容的无障碍链接组件
 * 提供键盘用户快速跳过导航直接到主内容的功能
 */
export const SkipToMainContent: React.FC = () => {
  const handleSkipToMain = (e: React.SyntheticEvent) => {
    e.preventDefault();
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLAnchorElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleSkipToMain(e);
    }
  };

  return (
    <a
      href="#main-content"
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50 focus:z-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      onClick={handleSkipToMain}
      onKeyDown={handleKeyDown}
      aria-label="跳转到主要内容"
    >
      跳转到主内容
    </a>
  );
};

export default SkipToMainContent;
