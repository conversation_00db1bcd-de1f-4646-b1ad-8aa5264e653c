export interface WithdrawalRejectedEmailData {
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  withdrawalMethod: string;
  rejectionReason: string;
  processedAt: string;
  transactionId?: string;
}

export const withdrawalRejectedTemplate = (
  data: WithdrawalRejectedEmailData
): string => `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>提现申请被拒绝通知</title>
</head>
<body>
  <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #333; margin: 0;">RefundGo</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
      <h2 style="color: #dc3545; margin-bottom: 20px; text-align: center;">❌ 提现申请被拒绝</h2>
      
      <p style="color: #666; margin-bottom: 20px; text-align: center;">
        您好，${data.userName}！很抱歉，您的提现申请未能通过审核。
      </p>
      
      <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">申请金额：</span>
          <span style="color: #dc3545; font-weight: bold; font-size: 18px; margin-left: 10px;">
            ${data.amount} ${data.currency}
          </span>
        </div>
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">提现方式：</span>
          <span style="color: #333; margin-left: 10px;">${data.withdrawalMethod}</span>
        </div>
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">处理时间：</span>
          <span style="color: #333; margin-left: 10px;">${data.processedAt}</span>
        </div>
        ${
          data.transactionId
            ? `
        <div style="margin-bottom: 15px;">
          <span style="color: #666; font-size: 14px;">申请ID：</span>
          <span style="color: #333; margin-left: 10px; font-family: monospace;">${data.transactionId}</span>
        </div>
        `
            : ''
        }
      </div>
      
      <div style="background: #f8d7da; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #dc3545;">
        <h3 style="color: #721c24; margin: 0 0 10px 0; font-size: 16px;">拒绝原因：</h3>
        <p style="color: #721c24; margin: 0; font-size: 14px; line-height: 1.5;">
          ${data.rejectionReason}
        </p>
      </div>
      
      <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #ffc107;">
        <p style="color: #856404; margin: 0; font-size: 14px;">
          💡 <strong>温馨提示：</strong><br>
          • 您的账户余额已恢复，可重新申请提现<br>
          • 请根据拒绝原因调整后重新提交申请<br>
          • 如有疑问，请联系客服获取帮助
        </p>
      </div>
      
      <div style="text-align: center; margin-top: 30px;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL || process.env.DOMAIN}/wallet" 
           style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
          重新申请提现
        </a>
      </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
      <p>此邮件由 RefundGo 系统自动发送，请勿回复。</p>
      <p>如需帮助，请联系客服：<EMAIL></p>
    </div>
  </div>
</body>
</html>
`;
