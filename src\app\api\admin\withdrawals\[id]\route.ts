import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

const updateWithdrawalSchema = z.object({
  status: z.enum(['APPROVED', 'REJECTED']),
  rejectionReason: z.string().optional(),
});

// PUT /api/admin/withdrawals/[id] - 更新提现申请状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const { status, rejectionReason } = updateWithdrawalSchema.parse(body);

    // 检查提现申请是否存在
    const existingRequest = await prisma.withdrawalRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            balance: true,
          },
        },
      },
    });

    if (!existingRequest) {
      return NextResponse.json(
        { success: false, message: '提现申请不存在' },
        { status: 404 },
      );
    }

    if (existingRequest.status !== 'PENDING') {
      return NextResponse.json(
        { success: false, message: '该提现申请已处理，无法再次操作' },
        { status: 400 },
      );
    }

    // 如果是拒绝操作，检查是否提供了拒绝原因
    if (status === 'REJECTED' && !rejectionReason) {
      return NextResponse.json(
        { success: false, message: '拒绝申请时必须提供拒绝原因' },
        { status: 400 },
      );
    }

    // 使用事务处理审核逻辑
    const result = await prisma.$transaction(async (tx: any) => {
      // 更新提现申请状态
      const updatedRequest = await tx.withdrawalRequest.update({
        where: { id },
        data: {
          status,
          rejectionReason: status === 'REJECTED' ? rejectionReason : null,
          reviewedBy: (session.user as any).id,
          reviewedAt: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              username: true,
            },
          },
          reviewer: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // 如果有关联的交易记录，更新其状态
      if (existingRequest.reference) {
        if (status === 'APPROVED') {
          // 审核通过：更新交易记录状态为完成
          await tx.walletTransaction.update({
            where: { id: existingRequest.reference },
            data: {
              status: 'COMPLETED',
              completedAt: new Date(),
            },
          });
        } else if (status === 'REJECTED') {
          // 审核拒绝：返还用户余额并更新交易记录状态为失败
          await tx.user.update({
            where: { id: existingRequest.userId },
            data: {
              balance: {
                increment: existingRequest.amount,
              },
            },
          });

          await tx.walletTransaction.update({
            where: { id: existingRequest.reference },
            data: {
              status: 'FAILED',
              description: `提现申请被拒绝，余额已返还 - ${existingRequest.rejectionReason || '未提供原因'}`,
            },
          });

          // 创建退款交易记录
          await tx.walletTransaction.create({
            data: {
              userId: existingRequest.userId,
              type: 'REFUND',
              amount: existingRequest.amount,
              status: 'COMPLETED',
              description: `提现申请拒绝退款 - ${existingRequest.rejectionReason || '未提供原因'}`,
              completedAt: new Date(),
            },
          });
        }
      }

      return updatedRequest;
    });

    // 如果审核通过，可以在这里添加实际的转账逻辑
    if (status === 'APPROVED') {
      // TODO: 实现实际的转账逻辑
      // 例如：调用支付接口、更新用户余额等
      console.log(`提现申请 ${id} 已审核通过，需要执行转账操作`);

      // 发送邮件通知
      try {
        const emailResponse = await fetch(
          `${process.env.DOMAIN}/api/send-email`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'withdrawal-approved',
              to: result.user.email,
              data: {
                userName:
                  result.user.name || result.user.username || '尊敬的用户',
                userEmail: result.user.email,
                amount: existingRequest.amount,
                fee: existingRequest.fee, // 添加手续费
                actualAmount: existingRequest.actualAmount, // 添加到账金额
                currency: 'USD',
                withdrawalMethod: getWithdrawMethodLabel(
                  existingRequest.withdrawMethod,
                ),
                processedAt: new Date().toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false,
                }),
                transactionId: existingRequest.reference || undefined,
              },
            }),
          },
        );

        if (!emailResponse.ok) {
          const emailError = await emailResponse.json();
          console.error('发送邮件失败:', emailError);
        } else {
          console.log('提现审核通过邮件已发送');
        }
      } catch (emailError) {
        console.error('发送邮件时出错:', emailError);
      }
    } else if (status === 'REJECTED') {
      // 发送邮件通知
      try {
        console.log('开始发送提现拒绝邮件:', {
          to: result.user.email,
          amount: existingRequest.amount,
          rejectionReason,
        });

        const emailResponse = await fetch(
          `${process.env.DOMAIN}/api/send-email`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'withdrawal-rejected',
              to: result.user.email,
              data: {
                userName:
                  result.user.name || result.user.username || '尊敬的用户',
                userEmail: result.user.email,
                amount: existingRequest.amount,
                currency: 'USD',
                withdrawalMethod: getWithdrawMethodLabel(
                  existingRequest.withdrawMethod,
                ),
                rejectionReason: rejectionReason || '未提供原因',
                processedAt: new Date().toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false,
                }),
                transactionId: existingRequest.reference || undefined,
              },
            }),
          },
        );

        const emailResult = await emailResponse.json();
        console.log('邮件发送响应:', emailResult);

        if (!emailResponse.ok) {
          console.error('发送拒绝邮件失败:', emailResult);
        } else {
          console.log('提现拒绝邮件已发送成功');
        }
      } catch (emailError) {
        console.error('发送拒绝邮件时出错:', emailError);
      }
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: status === 'APPROVED' ? '提现申请已审核通过' : '提现申请已拒绝',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, message: '请求参数错误', errors: error.errors },
        { status: 400 },
      );
    }

    console.error('更新提现申请状态失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}

// GET /api/admin/withdrawals/[id] - 获取单个提现申请详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const withdrawalRequest = await prisma.withdrawalRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            username: true,
          },
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!withdrawalRequest) {
      return NextResponse.json(
        { success: false, message: '提现申请不存在' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: withdrawalRequest,
    });
  } catch (error) {
    console.error('获取提现申请详情失败:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 },
    );
  }
}

// 获取提现方式标签的辅助函数
function getWithdrawMethodLabel(method: string): string {
  const methodLabels: { [key: string]: string } = {
    BANK_CARD: '美元账户',
    ALIPAY: 'Alipay',
    WECHAT: 'WeChat Pay',
    PAYPAL: 'PayPal',
    USDT_TRC20: 'USDT (TRC20)',
    USDT_ERC20: 'USDT (ERC20)',
    BTC: '比特币',
    ETH: '以太坊',
  };
  return methodLabels[method] || method;
}
