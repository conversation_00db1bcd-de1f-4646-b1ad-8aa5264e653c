'use client';

import { useTranslations } from 'next-intl';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { usePublishConfig } from '@/hooks/use-publish-config';
import { PublishTaskForm, FeeCalculation } from '@/lib/types/publish';

interface SummaryStepProps {
  formData: PublishTaskForm;
  feeCalculation: FeeCalculation;
}

export function SummaryStep({ formData, feeCalculation }: SummaryStepProps) {
  const t = useTranslations('Publish');
  const { chargebackTypes, paymentMethods, platforms, categories } =
    usePublishConfig();

  // 创建ID到名称的映射
  const chargebackTypeNames = Array.isArray(chargebackTypes)
    ? chargebackTypes.reduce(
        (acc, type) => {
          acc[type.id] =
            t(`platformSelection.chargebackTypeLabels.${type.name}`) ||
            type.name;
          return acc;
        },
        {} as Record<string, string>,
      )
    : {};

  const paymentMethodNames = Array.isArray(paymentMethods)
    ? paymentMethods.reduce(
        (acc, method) => {
          acc[method.id] =
            t(`platformSelection.paymentMethodLabels.${method.name}`) ||
            method.name;
          return acc;
        },
        {} as Record<string, string>,
      )
    : {};

  return (
    <div className='space-y-6'>
      {/* 委托信息总结 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('summary.title')}</CardTitle>
        </CardHeader>
        <CardContent className='space-y-6'>
          {/* 平台和分类 */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div>
              <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                {t('summary.targetPlatform')}
              </h4>
              <p className='text-sm'>
                {formData.platformName
                  ? t(
                      `platformSelection.platformLabels.${formData.platformName}`,
                    ) || formData.platformName
                  : '-'}
              </p>
            </div>
            <div>
              <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                {t('summary.productCategory')}
              </h4>
              <p className='text-sm'>
                {formData.categoryName
                  ? t(
                      `platformSelection.categoryLabels.${formData.categoryName}`,
                    ) || formData.categoryName
                  : '-'}
              </p>
            </div>
          </div>

          <Separator />

          {/* 拒付类型和支付方式 */}
          <div className='space-y-4'>
            <div>
              <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                {t('summary.chargebackTypes')}
              </h4>
              <div className='flex flex-wrap gap-2'>
                {formData.chargebackTypes.map(type => (
                  <Badge key={type} variant='secondary'>
                    {chargebackTypeNames[type] || type}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                {t('summary.paymentMethods')}
              </h4>
              <div className='flex flex-wrap gap-2'>
                {formData.paymentMethods.map(method => (
                  <Badge key={method} variant='secondary'>
                    {paymentMethodNames[method] || method}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          <Separator />

          {/* 商品信息 */}
          <div className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                  {t('summary.productUrl')}
                </h4>
                <p className='text-sm break-all'>
                  {formData.productUrl || '-'}
                </p>
              </div>
              <div>
                <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                  {t('summary.productUnitPrice')}
                </h4>
                <p className='text-sm font-medium'>
                  ${formData.unitPrice?.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>

            <div>
              <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                {t('summary.productDescriptionLabel')}
              </h4>
              <p className='text-sm'>{formData.productDescription || '-'}</p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                  {t('summary.quantityLabel')}
                </h4>
                <p className='text-sm'>{formData.quantity || 0}</p>
              </div>
              <div>
                <h4 className='text-sm font-medium text-muted-foreground mb-2'>
                  {t('summary.listingDuration')}
                </h4>
                <p className='text-sm'>
                  {formData.listingTime
                    ? `${formData.listingTime}${t('summary.hoursUnit')}（${Math.ceil(parseInt(formData.listingTime, 10) / 24)}${t('summary.daysUnit')}）`
                    : '-'}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* 收货地址 */}
          <div className='space-y-4'>
            <h4 className='text-sm font-medium text-muted-foreground mb-2'>
              {t('summary.recipientAddress')}
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <h5 className='text-xs font-medium text-muted-foreground mb-1'>
                  {t('summary.recipientNameLabel')}
                </h5>
                <p className='text-sm'>{formData.recipientName || '-'}</p>
              </div>
              <div>
                <h5 className='text-xs font-medium text-muted-foreground mb-1'>
                  {t('summary.contactPhone')}
                </h5>
                <p className='text-sm'>{formData.recipientPhone || '-'}</p>
              </div>
            </div>
            <div>
              <h5 className='text-xs font-medium text-muted-foreground mb-1'>
                {t('summary.detailedAddress')}
              </h5>
              <p className='text-sm'>{formData.shippingAddress || '-'}</p>
            </div>
          </div>

          <Separator />

          {/* 证据上传 */}
          <div>
            <h4 className='text-sm font-medium text-muted-foreground mb-2'>
              {t('summary.evidenceUploadMethod')}
            </h4>
            <p className='text-sm'>
              {formData.evidenceUploadType
                ? t(
                    `evidenceUpload.types.${formData.evidenceUploadType.toLowerCase()}.label`,
                  )
                : '-'}
            </p>
            {formData.evidenceFiles && formData.evidenceFiles.length > 0 && (
              <p className='text-xs text-muted-foreground mt-1'>
                {t('summary.filesUploadedPrefix')}{' '}
                {formData.evidenceFiles.length} {t('summary.filesUploaded')}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 费用计算 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('summary.feeCalculation')}</CardTitle>
          <p className='text-sm text-muted-foreground'>
            {t('summary.feeCalculationDescription')}
          </p>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-3'>
            <div className='flex justify-between items-center'>
              <span className='text-sm'>{t('summary.productTotalPrice')}</span>
              <span className='text-sm font-medium'>
                ${feeCalculation.productPrice.toFixed(2)}
              </span>
            </div>

            <div className='flex justify-between items-center'>
              <span className='text-sm'>
                {t('summary.paymentMethodRate')} (
                {feeCalculation.paymentMethodRate.toFixed(1)}%)：
              </span>
              <span className='text-sm font-medium'>
                ${feeCalculation.paymentMethodFee.toFixed(2)}
              </span>
            </div>

            <div className='flex justify-between items-center'>
              <span className='text-sm'>
                {t('summary.chargebackTypeFee')} (
                {feeCalculation.chargebackTypeRate.toFixed(1)}%)：
              </span>
              <span className='text-sm font-medium'>
                ${feeCalculation.chargebackTypeFee.toFixed(2)}
              </span>
            </div>

            {feeCalculation.evidenceRate > 0 && (
              <div className='flex justify-between items-center'>
                <span className='text-sm'>
                  {t('summary.evidenceFeeLabel')} (
                  {feeCalculation.evidenceRate.toFixed(1)}%)：
                </span>
                <span className='text-sm font-medium'>
                  ${feeCalculation.evidenceFee.toFixed(2)}
                </span>
              </div>
            )}

            <div className='flex justify-between items-center'>
              <span className='text-sm'>
                {t('summary.memberServiceFeeLabel')} (
                {feeCalculation.memberServiceRate.toFixed(1)}%)：
              </span>
              <span className='text-sm font-medium'>
                ${feeCalculation.memberServiceFee.toFixed(2)}
              </span>
            </div>

            <Separator />

            <Separator />

            <div className='flex justify-between items-center'>
              <span className='text-lg font-bold'>
                {t('summary.finalPayment')}
              </span>
              <span className='text-lg font-bold text-primary'>
                ${feeCalculation.finalTotal.toFixed(2)}
              </span>
            </div>
          </div>

          {/* 费用说明 */}
          <div className='mt-6 p-4 bg-muted/50 rounded-lg'>
            <h4 className='text-sm font-medium mb-2'>
              {t('summary.feeExplanationTitle')}
            </h4>
            <ul className='text-xs text-muted-foreground space-y-1'>
              <li>• {t('summary.feeExplanation.paymentMethodRate')}</li>
              <li>• {t('summary.feeExplanation.memberServiceRate')}</li>
              <li>• {t('summary.feeExplanation.evidenceRefund')}</li>
              <li>• {t('summary.feeExplanation.deductionNotice')}</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
