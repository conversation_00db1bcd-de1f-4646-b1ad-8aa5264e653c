import { UserRole } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';
import { systemRateSchema } from '@/types/rates';

// GET /api/admin/system-rates - 获取系统费率配置
export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    // 获取系统费率配置，如果不存在或缺少字段则创建/更新默认配置
    let systemRate = await prisma.systemRate.findFirst();

    if (!systemRate) {
      // 创建默认配置
      systemRate = await prisma.systemRate.create({
        data: {
          noEvidenceExtraRate: 5.0,
          depositRatio: 100.0,
          bankWithdrawalRate: 2.0,
          erc20WithdrawalRate: 1.0,
          trc20WithdrawalRate: 1.0,
          minimumWithdrawalAmount: 50.0,
        },
      });
    } else if (
      systemRate.bankWithdrawalRate === undefined ||
      systemRate.erc20WithdrawalRate === undefined ||
      systemRate.trc20WithdrawalRate === undefined ||
      systemRate.minimumWithdrawalAmount === undefined
    ) {
      // 更新现有记录，添加缺失的字段
      systemRate = await prisma.systemRate.update({
        where: { id: systemRate.id },
        data: {
          bankWithdrawalRate: systemRate.bankWithdrawalRate ?? 2.0,
          erc20WithdrawalRate: systemRate.erc20WithdrawalRate ?? 1.0,
          trc20WithdrawalRate: systemRate.trc20WithdrawalRate ?? 1.0,
          minimumWithdrawalAmount: systemRate.minimumWithdrawalAmount ?? 50.0,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: systemRate,
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// POST /api/admin/system-rates - 创建或更新系统费率配置
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validatedData = systemRateSchema.parse(body);

    // 检查是否已存在配置
    const existingRate = await prisma.systemRate.findFirst();

    let systemRate;

    if (existingRate) {
      // 更新现有配置
      systemRate = await prisma.systemRate.update({
        where: { id: existingRate.id },
        data: validatedData,
      });
    } else {
      // 创建新配置
      systemRate = await prisma.systemRate.create({
        data: validatedData,
      });
    }

    return NextResponse.json({
      success: true,
      data: systemRate,
      message: '系统费率配置保存成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}

// PUT /api/admin/system-rates - 更新系统费率配置
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      select: { role: true },
    });

    if (user?.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { success: false, message: '权限不足' },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validatedData = systemRateSchema.parse(body);

    // 获取现有配置
    const existingRate = await prisma.systemRate.findFirst();

    let systemRate;

    if (existingRate) {
      // 更新现有配置
      systemRate = await prisma.systemRate.update({
        where: { id: existingRate.id },
        data: validatedData,
      });
    } else {
      // 如果不存在则创建新配置
      systemRate = await prisma.systemRate.create({
        data: validatedData,
      });
    }

    return NextResponse.json({
      success: true,
      data: systemRate,
      message: '系统费率配置更新成功',
    });
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { success: false, message: '数据验证失败', errors: error },
        { status: 400 },
      );
    }

    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
