import { Task, EvidenceUploadType } from '@/lib/types/task';

export interface CommissionCalculation {
  chargebackTypeRate: number;
  paymentMethodRate: number;
  evidenceRate: number;
  totalRate: number;
  commission: number;
}

interface TaskForCommission {
  totalAmount?: number;
  unitPrice?: number;
  chargebackTypeIds?: string[];
  paymentMethodIds?: string[];
  evidenceUploadType?: string;
  evidenceStatus?: string | null | undefined;
}

// 计算委托酬金
export function calculateTaskCommission(
  task: TaskForCommission,
  chargebackTypes: Array<{ id: string; name: string; rate: number }> = [],
  paymentMethods: Array<{ id: string; name: string; rate: number }> = [],
  systemRate: { noEvidenceExtraRate: number } = { noEvidenceExtraRate: 2.0 },
): CommissionCalculation {
  const orderTotal = task.totalAmount || task.unitPrice || 0;

  const selectedChargebackTypes = (
    Array.isArray(chargebackTypes) ? chargebackTypes : []
  ).filter(
    type =>
      task.chargebackTypeIds?.includes(type.id) ||
      task.chargebackTypeIds?.includes(type.name),
  );
  const chargebackRates = selectedChargebackTypes.map(type => type.rate);
  const chargebackTypeRate =
    chargebackRates.length > 0 ? Math.min(...chargebackRates) : 0;

  const selectedPaymentMethods = (
    Array.isArray(paymentMethods) ? paymentMethods : []
  ).filter(
    method =>
      task.paymentMethodIds?.includes(method.id) ||
      task.paymentMethodIds?.includes(method.name),
  );
  const paymentRates = selectedPaymentMethods.map(method => method.rate);
  const paymentMethodRate =
    paymentRates.length > 0 ? Math.min(...paymentRates) : 0;

  // 证据费率计算
  let evidenceRate = 0;
  if (task.evidenceUploadType) {
    if (task.evidenceUploadType === EvidenceUploadType.NONE) {
      // 无证据类型
      evidenceRate = systemRate.noEvidenceExtraRate || 0;
    } else if (task.evidenceStatus === 'REVIEWED') {
      // 有证据且审核通过
      evidenceRate = 0;
    } else {
      // 有证据但未审核通过
      evidenceRate = systemRate.noEvidenceExtraRate || 0;
    }
  }

  const totalRate = chargebackTypeRate + paymentMethodRate + evidenceRate;
  const commission = (orderTotal * totalRate) / 100;

  return {
    chargebackTypeRate,
    paymentMethodRate,
    evidenceRate,
    totalRate,
    commission,
  };
}

export function getTaskCommission(
  task: TaskForCommission,
  chargebackTypes: Array<{ id: string; name: string; rate: number }> = [],
  paymentMethods: Array<{ id: string; name: string; rate: number }> = [],
  systemRate: { noEvidenceExtraRate: number } = { noEvidenceExtraRate: 2.0 },
): number {
  const result = calculateTaskCommission(
    task,
    chargebackTypes,
    paymentMethods,
    systemRate,
  );
  return result.commission;
}

// 计算基础酬金
export function getTaskBaseCommission(
  task: TaskForCommission,
  chargebackTypes: Array<{ id: string; name: string; rate: number }> = [],
  paymentMethods: Array<{ id: string; name: string; rate: number }> = [],
  systemRate: { noEvidenceExtraRate: number } = { noEvidenceExtraRate: 2.0 },
): number {
  const orderTotal = task.totalAmount || task.unitPrice || 0;

  const selectedChargebackTypes = (
    Array.isArray(chargebackTypes) ? chargebackTypes : []
  ).filter(
    type =>
      task.chargebackTypeIds?.includes(type.id) ||
      task.chargebackTypeIds?.includes(type.name),
  );
  const chargebackRates = selectedChargebackTypes.map(type => type.rate);
  const chargebackTypeRate =
    chargebackRates.length > 0 ? Math.min(...chargebackRates) : 0;

  const selectedPaymentMethods = (
    Array.isArray(paymentMethods) ? paymentMethods : []
  ).filter(
    method =>
      task.paymentMethodIds?.includes(method.id) ||
      task.paymentMethodIds?.includes(method.name),
  );
  const paymentRates = selectedPaymentMethods.map(method => method.rate);
  const paymentMethodRate =
    paymentRates.length > 0 ? Math.min(...paymentRates) : 0;

  // 基础酬金计算
  let evidenceRate = 0;
  if (task.evidenceUploadType === EvidenceUploadType.NONE) {
    // 无证据类型
    evidenceRate = systemRate.noEvidenceExtraRate || 0;
  }
  // 有证据委托不包含证据费

  const baseRate = chargebackTypeRate + paymentMethodRate + evidenceRate;
  const baseCommission = (orderTotal * baseRate) / 100;

  return baseCommission;
}

// 计算证据费用
export function getEvidenceFee(
  task: TaskForCommission,
  systemRate: { noEvidenceExtraRate: number } = { noEvidenceExtraRate: 2.0 },
): number {
  const orderTotal = task.totalAmount || task.unitPrice || 0;
  const evidenceRate = systemRate.noEvidenceExtraRate || 0;
  return (orderTotal * evidenceRate) / 100;
}
