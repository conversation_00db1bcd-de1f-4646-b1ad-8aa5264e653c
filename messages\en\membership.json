{"title": "Membership Plans", "breadcrumb": "Plan Selection", "description": "View and manage your membership plan, upgrade for more benefits", "navigation": {"title": "Membership Plans", "description": "View and manage your membership plan, upgrade for more benefits"}, "status": {"title": "Current Membership Status", "currentPlan": "Current Plan", "validUntil": "<PERSON>id <PERSON>", "permanent": "Permanent", "taskUsage": "Monthly Task Usage", "whitelistUsage": "Whitelist Shop Usage"}, "plans": {"free": "Free Plan", "pro": "Professional Plan", "business": "Business Plan", "current": "Current Plan", "unlimited": "Unlimited", "noData": "No Data", "noQuota": "No Quota"}, "features": {"taskLimit": "Task Publishing", "monthlyTasks": "per month", "times": "times", "platformRate": "Platform Rate", "whitelistSlots": "Whitelist Shop Slots", "none": "None", "slots": "slots", "customerSupport": "Customer Support", "basicSupport": "Basic Support", "prioritySupport": "Priority Support", "dedicatedSupport": "Dedicated Support"}, "actions": {"renew": "Renew Plan", "upgrade": "Upgrade to This Plan", "current": "Current Plan", "noDowngrade": "Downgrade Not Supported", "processing": "Processing..."}, "benefits": {"title": "Benefits Overview", "taskLimitTitle": "Task Publishing Limits", "taskLimitDesc": "Different plans have different monthly task publishing limits. Upgrade your plan when limits are exceeded.", "platformRateTitle": "Platform Rate", "platformRateDesc": "Higher membership levels enjoy lower platform rates, saving more costs.", "whitelistTitle": "Whitelist Shop Slots", "whitelistDesc": "Add specific shops to whitelist to prevent users from publishing related tasks for these shops.", "supportTitle": "Customer Support", "supportDesc": "Premium members enjoy priority customer support, business users have dedicated VIP support."}, "messages": {"loading": "Loading membership data...", "loadError": "Failed to load membership data, please refresh and try again", "noData": "Membership data does not exist", "currentPlanInfo": "Current Plan", "currentPlanDesc": "You are already a user of this plan", "paymentDialog": "Open payment dialog", "errorHandled": "Error handled in mutation", "paymentSuccess": "Refresh membership data after successful payment", "loadingMembership": "Loading membership information...", "membershipLoadError": "Failed to load membership information", "noMembershipData": "No membership data available", "upgradeSuccess": "Membership upgraded successfully!", "renewSuccess": "Membership renewed successfully!", "operationFailed": "Operation failed, please try again", "confirmUpgrade": "Are you sure you want to upgrade to {planName}?", "confirmRenew": "Are you sure you want to renew your current membership?"}, "payment": {"dialog": {"title": {"upgrade": "Upgrade Membership Plan", "renew": "Renew Membership Plan"}, "description": {"upgrade": "Select payment method to upgrade to {planName}", "renew": "Select payment method to renew {planName}"}, "planPrice": "Plan Price", "selectPaymentMethod": "Select Payment Method", "processingFee": "Processing Fee ({rate}%)", "total": "Total", "planCost": "Plan Cost", "cancel": "Cancel", "confirmPayment": "Confirm Payment", "creating": "Creating...", "selectMethodFirst": "Please select a payment method", "invalidPaymentMethod": "Invalid payment method"}, "result": {"title": "Payment Order Created", "description": {"upgrade": "Please complete payment to upgrade to {planName}", "renew": "Please complete payment to renew {planName}"}, "cryptoPaymentNote": "Please complete cryptocurrency payment in the newly opened page", "paymentPageNote": "Click the button below to go to the payment page", "goToPayment": "Go to Payment", "autoCheckingStatus": "System is automatically detecting payment status...", "paymentCompleted": "I have completed payment", "checkingPaymentStatus": "Checking payment status..."}, "status": {"orderNotFound": "Order number does not exist", "checkFailed": "Failed to check payment status", "upgradeSuccess": "Membership upgrade successful!", "renewSuccess": "Membership renewal successful!", "paymentExpired": "Payment has expired, please create a new order", "paymentFailed": "Payment failed, please try again", "paymentPending": "Payment not yet completed", "paymentPendingDescription": "Please ensure payment is completed, then click this button again after successful payment", "unknownError": "Unknown error", "redirectedToPayment": "Redirected to payment page, please complete payment", "createPaymentFailed": "Failed to create payment", "getPaymentMethodsFailed": "Failed to get payment methods"}}, "time": {"loading": "Loading..."}, "hooks": {"upgrade": {"error": "Failed to upgrade membership", "confirmed": "Upgrade information confirmed", "confirmedDesc": "Please select payment method to complete upgrade to {planName}", "success": "Upgrade successful", "successDesc": "Successfully upgraded to {planName}", "failed": "Upgrade failed"}, "purchase": {"error": "Failed to create payment order", "success": "Payment order created successfully", "successDesc": "Please complete payment to {action} {planName}", "upgrade": "upgrade to", "renew": "renew", "failed": "Failed to create payment order"}, "renew": {"error": "Renewal failed", "confirmed": "Renewal information confirmed", "confirmedDesc": "Please select payment method to complete renewal of {planName}", "success": "Renewal successful", "successDesc": "Membership plan renewed successfully", "failed": "Renewal failed"}}}