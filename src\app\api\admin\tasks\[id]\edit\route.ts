import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { requireAdmin } from '@/lib/admin-auth';
import prisma from '@/lib/db';

// 更新委托信息
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // 验证管理员权限
    try {
      await requireAdmin();
    } catch (error) {
      return NextResponse.json({ error: '需要管理员权限' }, { status: 401 });
    }

    const taskId = params.id;
    if (!taskId) {
      return NextResponse.json({ error: '委托ID不能为空' }, { status: 400 });
    }

    const body = await request.json();
    const {
      title,
      productDescription,
      quantity,
      unitPrice,
      status,
      evidenceStatus,
      evidenceRejectReason,
      recipientName,
      recipientPhone,
      shippingAddress,
    } = body;

    // 验证必要字段
    if (!title || !productDescription || !quantity || !unitPrice || !status) {
      return NextResponse.json({ error: '缺少必要字段' }, { status: 400 });
    }

    // 验证数值类型
    if (
      isNaN(quantity) ||
      isNaN(unitPrice) ||
      quantity <= 0 ||
      unitPrice <= 0
    ) {
      return NextResponse.json(
        { error: '数量和单价必须为正数' },
        { status: 400 },
      );
    }

    // 验证状态值
    const validStatuses = [
      'PENDING',
      'RECRUITING',
      'IN_PROGRESS',
      'COMPLETED',
      'REJECTED',
      'EXPIRED',
      'CANCELLED',
    ];
    if (!validStatuses.includes(status)) {
      return NextResponse.json({ error: '无效的委托状态' }, { status: 400 });
    }

    // 验证证据状态
    const validEvidenceStatuses = [
      'PENDING_SUBMISSION',
      'UNDER_REVIEW',
      'REVIEWED',
      'REJECTED',
      'NO_EVIDENCE',
    ];
    if (evidenceStatus && !validEvidenceStatuses.includes(evidenceStatus)) {
      return NextResponse.json({ error: '无效的证据状态' }, { status: 400 });
    }

    // 检查委托是否存在
    const existingTask = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!existingTask) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 计算总金额
    const totalAmount = quantity * unitPrice;

    // 更新委托
    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: {
        title,
        productDescription,
        quantity,
        unitPrice,
        status: status as TaskStatus,
        evidenceStatus: evidenceStatus || null,
        evidenceRejectReason: evidenceRejectReason || null,
        recipientName: recipientName || null,
        recipientPhone: recipientPhone || null,
        shippingAddress: shippingAddress || null,
        finalTotal: totalAmount,
        updatedAt: new Date(),
      },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        accepter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // 获取拒付类型和支付方式名称
    const [chargebackTypes, paymentMethods] = await Promise.all([
      prisma.chargebackType.findMany({
        where: {
          id: { in: updatedTask.chargebackTypeIds },
          status: 'ACTIVE',
        },
      }),
      prisma.paymentMethod.findMany({
        where: {
          id: { in: updatedTask.paymentMethodIds },
          status: 'ACTIVE',
        },
      }),
    ]);

    // 构造返回数据
    const taskData = {
      id: updatedTask.id,
      title: updatedTask.title,
      productDescription: updatedTask.productDescription,
      productUrl: updatedTask.productUrl,
      quantity: updatedTask.quantity,
      unitPrice: updatedTask.unitPrice,
      totalAmount: updatedTask.totalAmount,
      finalTotal: updatedTask.finalTotal,
      status: updatedTask.status,
      evidenceStatus: updatedTask.evidenceStatus,
      evidenceRejectReason: updatedTask.evidenceRejectReason,
      evidenceFiles: updatedTask.evidenceFiles,
      evidenceUploadType: updatedTask.evidenceUploadType,
      recipientName: updatedTask.recipientName,
      recipientPhone: updatedTask.recipientPhone,
      shippingAddress: updatedTask.shippingAddress,
      platform: updatedTask.platform?.name || '未知平台',
      category: updatedTask.category?.name || '未知分类',
      chargebackTypes: chargebackTypes.map(type => type.name),
      paymentMethods: paymentMethods.map(method => method.name),
      publisher: updatedTask.publisher,
      accepter: updatedTask.accepter,
      createdAt: updatedTask.createdAt,
      updatedAt: updatedTask.updatedAt,
      completedAt: updatedTask.completedAt,
      acceptedAt: updatedTask.acceptedAt,
      expiresAt: updatedTask.expiresAt,
      rejectedAt: updatedTask.rejectedAt,
      approvedAt: updatedTask.approvedAt,
      publishedAt: updatedTask.publishedAt,
      logisticsDeadline: updatedTask.logisticsDeadline,
      orderReviewDeadline: updatedTask.orderReviewDeadline,
      logisticsReviewDeadline: updatedTask.logisticsReviewDeadline,
      deliveryDeadline: updatedTask.deliveryDeadline,
      orderNumber: updatedTask.orderNumber,
      orderScreenshot: updatedTask.orderScreenshot,
      trackingNumber: updatedTask.trackingNumber,
      logisticsScreenshots: updatedTask.logisticsScreenshots,
      reviewedAt: updatedTask.reviewedAt,
      reviewRejectReason: updatedTask.reviewRejectReason,
    };

    return NextResponse.json({
      success: true,
      message: '委托更新成功',
      task: taskData,
    });
  } catch (error) {
    console.error('更新委托失败:', error);
    return NextResponse.json({ error: '内部服务器错误' }, { status: 500 });
  }
}
