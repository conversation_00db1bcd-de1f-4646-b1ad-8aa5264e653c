'use client';

import { AlertTriangle, RefreshCw, Shield, Home } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';

interface AdminErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * 管理员区域错误边界组件
 * 处理管理员页面中的错误
 */
export default function AdminError({ error, reset }: AdminErrorProps) {
  React.useEffect(() => {
    // 记录管理员区域错误
    console.error('Admin area error:', error);
  }, [error]);

  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center'>
        <div className='mb-6'>
          <div className='mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20'>
            <AlertTriangle className='h-8 w-8 text-red-600' />
          </div>
        </div>

        <h1 className='mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100'>
          管理面板错误
        </h1>

        <p className='mb-6 text-gray-600 dark:text-gray-400'>
          管理面板遇到了错误。这可能是权限问题或系统故障。请尝试重新加载或联系技术支持。
        </p>

        {/* 开发环境下显示错误详情 */}
        {process.env.NODE_ENV === 'development' && (
          <div className='mb-6 rounded-lg bg-red-50 p-4 text-left dark:bg-red-900/20'>
            <h3 className='mb-2 font-semibold text-red-800 dark:text-red-200'>
              管理员错误详情:
            </h3>
            <pre className='text-xs text-red-700 dark:text-red-300 overflow-auto'>
              {error.message}
            </pre>
            {error.digest && (
              <p className='mt-2 text-xs text-red-600 dark:text-red-400'>
                错误ID: {error.digest}
              </p>
            )}
          </div>
        )}

        <div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
          <Button
            onClick={reset}
            variant='default'
            className='flex items-center gap-2'
          >
            <RefreshCw className='h-4 w-4' />
            重试
          </Button>

          <Button
            onClick={() => (window.location.href = '/admin')}
            variant='outline'
            className='flex items-center gap-2'
          >
            <Shield className='h-4 w-4' />
            管理首页
          </Button>

          <Button
            onClick={() => (window.location.href = '/')}
            variant='ghost'
            className='flex items-center gap-2'
          >
            <Home className='h-4 w-4' />
            返回首页
          </Button>
        </div>

        {/* 管理员专用提示 */}
        <div className='mt-6 rounded-lg bg-amber-50 p-4 dark:bg-amber-900/20'>
          <p className='text-sm text-amber-800 dark:text-amber-200'>
            <Shield className='inline h-4 w-4 mr-1' />
            如果是权限相关错误，请确认您的管理员身份是否有效。
          </p>
        </div>
      </div>
    </div>
  );
}
