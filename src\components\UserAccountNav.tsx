'use client';
import { LayoutDashboard, LineChart, LogOutIcon, User } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { signOut } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';

interface UserAccountNavProps {
  session: {
    user?: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string | null;
    };
  } | null;
}

export default function UserAccountNav({ session }: UserAccountNavProps) {
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  // Always call the hook - React hooks must be called unconditionally
  const t = useTranslations('navigation');

  // 获取翻译文本的辅助函数
  const getText = (key: string, fallback: string) => {
    try {
      return t(key);
    } catch (error) {
      return fallback;
    }
  };
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 如果没有session或user，不渲染组件
  if (!session?.user) {
    return null;
  }

  // 此时我们确定session和user都存在
  const user = session.user;

  function toggleDropDownMenu(): void {
    setShowDropdown(!showDropdown);
  }

  function showDropDownMenu() {
    return (
      <div
        ref={dropdownRef}
        className='min-w-[180px] bg-white dark:bg-gray-800 shadow-xl border border-gray-200 dark:border-gray-700 absolute top-8 right-0 z-50 rounded-xl pt-2'
      >
        <div className='flex flex-col'>
          <div className='flex items-center border-b-[1px] border-gray-200 dark:border-gray-700 px-4 pb-2'>
            <div className='flex-1 min-w-0'>
              <p className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
                {user?.name}
              </p>
              <p className='text-xs text-ellipsis overflow-hidden text-gray-600 dark:text-gray-400'>
                {user?.email}
              </p>
              {user?.role === 'admin' ? (
                <p className='text-xs font-semibold rounded-md py-1 mt-1 text-green-700 dark:text-green-100 bg-green-500 bg-opacity-20 dark:bg-green-500 dark:bg-opacity-30 max-w-12 text-center'>
                  管理员
                </p>
              ) : null}
            </div>
          </div>

          <div className='cursor-pointer'>
            <div className='hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-4 py-2'>
              <Link href='/profile' className='flex items-center'>
                <User className='h-4 w-4 mr-2 text-gray-600 dark:text-gray-400' />
                <p className='text-sm text-gray-900 dark:text-gray-100'>
                  {getText('userAccountMenu.profile', '个人资料')}
                </p>
              </Link>
            </div>

            <div className='hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-4 py-2'>
              <Link href='/dashboard' className='flex items-center '>
                <LayoutDashboard className='h-4 w-4 mr-2 text-gray-600 dark:text-gray-400' />
                <p className='text-sm text-gray-900 dark:text-gray-100'>
                  {getText('userAccountMenu.dashboard', '仪表盘')}
                </p>
              </Link>
            </div>

            {user?.role === 'admin' ? (
              <div className='hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-4 py-2'>
                <div className='flex items-center'>
                  <LineChart className='h-4 w-4 mr-2 text-gray-600 dark:text-gray-400' />
                  <p className='text-sm text-gray-900 dark:text-gray-100'>
                    {getText('userAccountMenu.dataAnalysis', '数据分析')}
                  </p>
                </div>
              </div>
            ) : null}

            <div
              className='hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-4 py-2'
              onClick={() => signOut()}
            >
              <div className='flex items-center'>
                <LogOutIcon className='h-4 w-4 mr-2 text-gray-600 dark:text-gray-400' />
                <p className='text-sm text-gray-900 dark:text-gray-100'>
                  {getText('userAccountMenu.signOut', '退出登录')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='relative' onClick={toggleDropDownMenu}>
      <div className='w-7 h-7 cursor-pointer rounded-full bg-gradient-to-r from-pink-300 via-purple-300 to-blue-300 mr-2'></div>
      {/* <Image
                width={28}
                height={28}
                className='w-full rounded-full hover:opacity-85 cursor-pointer'
                src={user?.image || '/avatars/default.jpg'}
                alt='用户头像'
                referrerPolicy='no-referrer'
            /> */}
      {showDropdown && showDropDownMenu()}
    </div>
  );
}
