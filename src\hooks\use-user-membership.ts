import { useQuery } from '@tanstack/react-query';

import { MembershipPlan } from './use-membership-plans';

// 用户会员信息接口
interface UserMembership {
  id: string;
  memberPlan: string; // 会员套餐类型：FREE, PRO, BUSINESS
  memberPlanExpiry?: Date;
  membershipPlan?: MembershipPlan; // 完整的会员套餐信息
  tasksUsedThisMonth?: number; // 本月已使用委托数
  whitelistSlotsUsed?: number; // 已使用白名单数量
}

// 获取当前用户的会员信息
export function useUserMembership() {
  return useQuery<UserMembership>({
    queryKey: ['user-membership'],
    queryFn: async () => {
      const response = await fetch('/api/user/membership');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || '获取用户会员信息失败');
      }

      return result.data;
    },
  });
}
