import { CreditCard } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';

import styles from './loading.module.css';

/**
 * 支付页面加载组件
 * 在支付相关页面加载时显示
 */
export default function PaymentLoading() {
  const t = useTranslations('payment');

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900'>
      <div className='flex flex-col items-center space-y-6 p-8'>
        {/* 支付图标动画 */}
        <div className='relative'>
          <div className='h-16 w-16 animate-spin rounded-full border-4 border-gray-200 border-t-green-600'></div>
          <div className='absolute inset-0 flex items-center justify-center'>
            <CreditCard className='h-8 w-8 text-green-600' />
          </div>
        </div>

        {/* 加载文本 */}
        <div className='text-center'>
          <h2 className='text-xl font-semibold text-gray-900 dark:text-gray-100'>
            {t('loading.title')}
          </h2>
          <p className='text-sm text-gray-500 dark:text-gray-400 mt-2'>
            {t('loading.description')}
          </p>
        </div>

        {/* 安全提示 */}
        <div className='rounded-lg bg-green-50 p-4 dark:bg-green-900/20 max-w-md'>
          <div className='flex items-center space-x-2'>
            <div className='h-2 w-2 bg-green-500 rounded-full animate-pulse'></div>
            <p className='text-sm text-green-800 dark:text-green-200'>
              {t('loading.securityNotice')}
            </p>
          </div>
        </div>

        {/* 进度指示器 */}
        <div className='w-64 bg-gray-200 rounded-full h-2 dark:bg-gray-700'>
          <div
            className={`bg-green-600 h-2 rounded-full animate-pulse ${styles.progressBar}`}
          ></div>
        </div>

        {/* 支付步骤提示 */}
        <div className='flex space-x-4 text-xs text-gray-400'>
          <span className='flex items-center space-x-1'>
            <div className='h-2 w-2 bg-green-500 rounded-full'></div>
            <span>{t('loading.steps.verify')}</span>
          </span>
          <span className='flex items-center space-x-1'>
            <div className='h-2 w-2 bg-green-500 rounded-full animate-pulse'></div>
            <span>{t('loading.steps.process')}</span>
          </span>
          <span className='flex items-center space-x-1'>
            <div className='h-2 w-2 bg-gray-300 rounded-full'></div>
            <span>{t('loading.steps.complete')}</span>
          </span>
        </div>
      </div>
    </div>
  );
}
