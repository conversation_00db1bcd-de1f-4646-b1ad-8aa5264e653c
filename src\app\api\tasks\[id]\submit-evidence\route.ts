import { EvidenceStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';

// 验证参数
const paramsSchema = z.object({
  id: z.string(),
});

// 验证请求体
const requestBodySchema = z.object({
  evidenceFiles: z.array(z.string()).min(1, '请至少上传一个证据文件'),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 验证用户登录
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    // 验证参数
    const resolvedParams = await params;
    const { id } = paramsSchema.parse(resolvedParams);

    // 验证委托存在且属于当前用户
    const task = await prisma.task.findFirst({
      where: {
        id,
        publisherId: session.user.id,
      },
    });

    if (!task) {
      return NextResponse.json(
        { error: '委托不存在或无权限' },
        { status: 404 },
      );
    }

    // 验证证据上传类型：只有LATER类型的委托可以补充证据
    if (task.evidenceUploadType !== 'LATER') {
      return NextResponse.json(
        {
          error: '此委托不支持补充证据',
        },
        { status: 400 },
      );
    }

    // 验证证据状态：只有待上传状态才能提交证据
    const isPendingSubmission =
      !task.evidenceStatus ||
      task.evidenceStatus === EvidenceStatus.PENDING_SUBMISSION;
    if (!isPendingSubmission) {
      return NextResponse.json(
        {
          error: '证据已经提交，无法重复提交',
        },
        { status: 400 },
      );
    }

    // 验证委托状态：只有特定状态的委托可以提交证据
    const allowedStatuses = [
      'PENDING',
      'REJECTED',
      'RECRUITING',
      'IN_PROGRESS',
      'PENDING_LOGISTICS',
      'PENDING_REVIEW',
      'PENDING_DELIVERY',
    ];
    if (!allowedStatuses.includes(task.status)) {
      return NextResponse.json(
        {
          error: '当前委托状态不允许提交证据',
        },
        { status: 400 },
      );
    }

    // 验证请求体
    const body = await request.json();
    const { evidenceFiles } = requestBodySchema.parse(body);

    // 更新委托的证据文件和状态
    const updatedTask = await prisma.task.update({
      where: { id },
      data: {
        evidenceFiles,
        evidenceStatus: EvidenceStatus.UNDER_REVIEW, // 设置为审核中状态
      },
    });

    return NextResponse.json({
      success: true,
      message: '证据提交成功，请等待审核',
      data: {
        taskId: updatedTask.id,
        evidenceStatus: updatedTask.evidenceStatus,
        evidenceFiles: updatedTask.evidenceFiles,
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
