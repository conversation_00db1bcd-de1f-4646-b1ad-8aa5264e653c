import { TaskStatus } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/hooks/useEmailTranslation';
import { getAuthSession } from '@/lib/auth';
import prisma from '@/lib/db';
import { sendTaskAcceptedPublisherEmail } from '@/lib/email';

// 接受委托API
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // 1. 验证用户身份
    const session = await getAuthSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未授权访问，请先登录' },
        { status: 401 },
      );
    }

    const resolvedParams = await params;
    const { id: taskId } = resolvedParams;
    const userId = session.user.id;

    // 2. 查找委托并验证状态
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        publisher: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        platform: {
          select: {
            id: true,
            name: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json({ error: '委托不存在' }, { status: 404 });
    }

    // 3. 检查委托状态
    if (task.status !== TaskStatus.RECRUITING) {
      return NextResponse.json({ error: '委托当前不可接受' }, { status: 400 });
    }

    // 4. 检查是否为委托发布者
    if (task.publisherId === userId) {
      return NextResponse.json(
        { error: '不能接受自己发布的委托' },
        { status: 400 },
      );
    }

    // 5. 检查委托是否已经有接受者
    if (task.accepterId) {
      return NextResponse.json(
        { error: '委托已被其他用户接受' },
        { status: 400 },
      );
    }

    // 6. 检查委托是否过期
    if (task.expiresAt && new Date() > task.expiresAt) {
      return NextResponse.json({ error: '委托已过期' }, { status: 400 });
    }

    // 7. 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        balance: true,
        name: true,
        email: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    // 8. 获取系统费率配置来计算押金
    const systemRate = await prisma.systemRate.findFirst({
      select: {
        depositRatio: true,
      },
    });

    // 9. 计算所需押金
    const totalPrice = task.unitPrice * task.quantity;
    const depositRatio = systemRate?.depositRatio || 10.0; // 默认10%
    const requiredDeposit = totalPrice * (depositRatio / 100);

    // 10. 检查用户余额是否足够
    if (user.balance < requiredDeposit) {
      return NextResponse.json(
        {
          error: '余额不足，无法支付押金',
          details: {
            currentBalance: user.balance,
            requiredDeposit,
            shortfall: requiredDeposit - user.balance,
          },
        },
        { status: 400 },
      );
    }

    // 11. 使用事务处理接受委托的所有操作
    const result = await prisma.$transaction(async tx => {
      // 11.1 扣除用户押金
      await tx.user.update({
        where: { id: userId },
        data: {
          balance: {
            decrement: requiredDeposit,
          },
          frozenAmount: {
            increment: requiredDeposit,
          },
        },
      });

      // 11.2 创建钱包交易记录
      await tx.walletTransaction.create({
        data: {
          userId,
          type: 'TASK_FEE',
          amount: -requiredDeposit, // 负数表示支出
          status: 'COMPLETED',
          description: `接受委托押金 - ${taskId}`,
          reference: taskId,
          completedAt: new Date(),
        },
      });

      // 11.3 更新委托状态
      const updatedTask = await tx.task.update({
        where: { id: taskId },
        data: {
          status: TaskStatus.IN_PROGRESS,
          accepterId: userId,
          acceptedAt: new Date(),
        },
        include: {
          publisher: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          accepter: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          platform: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return {
        task: updatedTask,
        deposit: requiredDeposit,
        newBalance: user.balance - requiredDeposit,
      };
    });

    // 12. 发送邮件通知发布者
    try {
      const publisherEmail = result.task.publisher.email;
      if (publisherEmail) {
        // 获取用户的语言偏好
        const userLanguage = await getUserEmailLanguage(publisherEmail);

        await sendTaskAcceptedPublisherEmail(publisherEmail, {
          userName: result.task.publisher.name || '用户',
          userEmail: publisherEmail,
          taskId: result.task.id,
          taskTitle:
            result.task.title ||
            `${result.task.platform.name} - ${result.task.category.name}`,
          accepterName: result.task.accepter?.name || '用户',
          accepterEmail: result.task.accepter?.email || '',
          acceptedAt: new Date().toISOString(),
          taskReward: task.unitPrice * task.quantity,
          currency: 'USD',
          language: userLanguage,
        });
      }
    } catch (emailError) {
      console.error('发送委托接受邮件失败:', emailError);
      // 邮件发送失败不影响主流程
    }

    // 13. 返回成功响应
    return NextResponse.json({
      success: true,
      message: `委托接受成功！已扣除押金 $${requiredDeposit.toFixed(2)}`,
      data: {
        task: {
          id: result.task.id,
          status: result.task.status,
          acceptedAt: result.task.acceptedAt,
          platform: result.task.platform.name,
          category: result.task.category.name,
        },
        payment: {
          deposit: result.deposit,
          newBalance: result.newBalance,
          transactionId: `deposit-${taskId}`,
        },
      },
    });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
