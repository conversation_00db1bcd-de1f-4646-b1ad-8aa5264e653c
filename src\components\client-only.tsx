'use client';

import { useState, useEffect, ReactNode } from 'react';

interface ClientOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * 客户端专用渲染组件
 * 用于解决SSR/客户端渲染不一致问题
 *
 * @param children - 只在客户端渲染的内容
 * @param fallback - 服务端渲染时显示的内容（可选）
 */
export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // 服务端或客户端首次渲染时显示fallback
  if (!hasMounted) {
    return <>{fallback}</>;
  }

  // 客户端hydration完成后显示实际内容
  return <>{children}</>;
}
