{"types": {"DEPOSIT": "<PERSON><PERSON><PERSON><PERSON>", "WITHDRAW": "<PERSON><PERSON><PERSON>", "TASK_FEE": "Task Fee", "COMMISSION": "Commission", "MEMBERSHIP": "Membership Fee", "REFUND": "Refund", "DEPOSIT_FREEZE": "Deposit Freeze", "DEPOSIT_UNFREEZE": "Deposit Unfreeze", "PENALTY": "Penalty", "BONUS": "Bonus", "FEE": "Processing Fee", "TRANSFER_IN": "Transfer In", "TRANSFER_OUT": "Transfer Out"}, "status": {"PENDING": "Pending", "COMPLETED": "Completed", "FAILED": "Failed", "CANCELLED": "Cancelled"}, "descriptions": {"deposit": "Deposit to wallet", "withdraw": "Withdraw from wallet", "taskFee": "Task publishing fee", "commission": "Commission from completed task", "membershipFee": "Membership plan fee", "refund": "Refund to wallet", "depositFreeze": "Deposit freeze", "depositUnfreeze": "Deposit unfreeze", "penalty": "Penalty deduction", "bonus": "Bonus reward", "processingFee": "Processing fee", "transferIn": "Fund transfer in", "transferOut": "Fund transfer out"}, "patterns": {"deposit_with_method": "Deposit - {method}", "deposit_with_fee": "Deposit - {method} (Fee {fee})", "withdraw_with_method": "Withdrawal Application - {method}", "withdraw_with_fee": "Withdrawal Application - {method} (Fee {fee})", "task_fee_with_title": "Task Publishing Fee - {title}", "task_evidence_fee": "Task Evidence Fee (Frozen) - {title}", "evidence_refund": "Evidence Fee Refund for Task - {title}", "commission_from_task": "Commission from Completed Task - {title}", "membership_upgrade": "Membership Upgrade - {plan}", "membership_with_fee": "Membership {action} - {plan} (Including Fee {fee})", "deposit_freeze_task": "Task Deposit Freeze - {title}", "withdraw_refund": "Withdrawal Rejection Refund - {reason}", "evidence_fee_to_commission": "Task Completed, Evidence Fee Converted to Commission - {taskId}", "task_deposit_release": "Task Completed Deposit Release - {taskId}", "task_commission": "Task Completion Commission - {taskId}", "task_expired_refund": "Task Expired Refund (Publishing Fee) - {taskId}", "task_expired_deposit_deduction": "Task Expired Deposit Deduction - {taskId}", "task_expired_refund_simple": "Task Expired Refund - {taskId}", "refund_to_wallet": "Refund to Wallet - {reason}", "penalty_deduction": "Penalty Deduction - {reason}", "bonus_reward": "Bonus Reward - {reason}"}, "methods": {"Alipay": "Alipay", "WeChat": "WeChat Pay", "NOWPayments (Cryptocurrency)": "NOWPayments (Crypto)", "NOWPayments": "NOWPayments (Crypto)", "BANK_CARD": "Bank Card", "USDT": "USDT", "PayPal": "PayPal", "Stripe": "Stripe", "支付宝": "Alipay", "微信支付": "WeChat Pay", "美元账户": "USD Account", "银行卡": "Bank Card", "加密货币": "Cryptocurrency"}, "api": {"errors": {"unauthorized": "Unauthorized access, please login first", "serverError": "Internal server error", "transactionNotFound": "Transaction record not found", "invalidTransactionType": "Invalid transaction type", "insufficientBalance": "Insufficient balance"}, "success": {"transactionCreated": "Transaction record created successfully", "transactionUpdated": "Transaction record updated successfully", "transactionCompleted": "Transaction completed"}}}