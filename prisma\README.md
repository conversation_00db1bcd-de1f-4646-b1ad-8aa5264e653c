# Prisma 数据库迁移和种子数据

本项目使用 Prisma 作为数据库 ORM，已配置好迁移和种子数据功能。

## 迁移文件

已创建初始迁移文件：`migrations/20250712123242_init/migration.sql`

这个迁移文件包含了完整的数据库结构，包括：

- 用户管理系统
- 委托管理系统
- 支付系统
- 物流追踪系统
- 客服支持系统
- 白名单管理系统
- 钱包和提现系统

## 种子数据

`seed.ts` 文件包含了初始数据，包括：

- 平台数据 (Amazon, eBay, Shopify)
- 分类数据 (电子产品, 服装, 家居用品)
- 退款类型数据 (商品未收到, 商品与描述不符, 商品质量问题)
- 支付方式数据 (信用卡, PayPal, 银行转账)
- 系统费率配置
- 会员计划 (免费版, 专业版, 商业版)
- 支付配置 (NOWPayments, 云付支付)

## 可用的命令

### 基本迁移命令

```bash
# 生成新的迁移
npx prisma migrate dev --name your_migration_name

# 重置数据库并应用所有迁移
npx prisma migrate reset

# 部署迁移到生产环境
npx prisma migrate deploy

# 查看迁移状态
npx prisma migrate status
```

### 种子数据命令

```bash
# 运行种子脚本
npm run db:seed

# 重置数据库并运行种子脚本
npm run db:reset
```

### 其他有用的命令

```bash
# 生成 Prisma 客户端
npx prisma generate

# 启动 Prisma Studio (数据库可视化工具)
npx prisma studio

# 推送 schema 更改到数据库 (开发环境)
npx prisma db push
```

## 数据库设置

确保在 `.env` 文件中设置了正确的数据库连接字符串：

```env
DATABASE_URL="your_database_url"
DIRECT_URL="your_direct_url"  # 如果使用连接池
```

## 注意事项

1. 种子数据使用 `upsert` 操作，可以安全地多次运行
2. 迁移文件一旦应用到生产环境，请不要修改
3. 在生产环境中，使用 `prisma migrate deploy` 而不是 `prisma migrate dev`
4. 建议在部署前备份数据库
