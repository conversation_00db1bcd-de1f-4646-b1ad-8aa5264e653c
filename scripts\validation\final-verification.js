const fs = require('fs');

console.log('=== RefundGo 邮件国际化最终验证 ===\n');

// 检查所有重要文件
const files = [
  {
    path: 'src/lib/email-templates/withdrawal-approved-i18n.ts',
    name: '提现审核通过邮件模板',
    checks: ['language?:', 'zh:', 'en:', 'emailStyles'],
  },
  {
    path: 'src/lib/email-templates/withdrawal-rejected-i18n.ts',
    name: '提现审核拒绝邮件模板',
    checks: ['language?:', 'zh:', 'en:', 'emailStyles'],
  },
  {
    path: 'src/lib/email-templates/task-cancelled-publisher-i18n.ts',
    name: '委托取消邮件模板（发布者）',
    checks: ['language?:', 'zh:', 'en:', 'emailStyles'],
  },
  {
    path: 'src/lib/email-templates/task-cancelled-accepter-i18n.ts',
    name: '委托取消邮件模板（接单者）',
    checks: ['language?:', 'zh:', 'en:', 'emailStyles'],
  },
  {
    path: 'src/lib/email-templates/task-accepted-publisher-i18n.ts',
    name: '委托被接受邮件模板',
    checks: ['language?:', 'zh:', 'en:', 'emailStyles'],
  },
];

let allPassed = true;

console.log('📧 **邮件模板检查**：\n');

files.forEach(file => {
  console.log(`🔍 ${file.name}:`);

  try {
    if (fs.existsSync(file.path)) {
      const content = fs.readFileSync(file.path, 'utf8');

      file.checks.forEach(check => {
        if (content.includes(check)) {
          console.log(`   ✅ ${check}: 正常`);
        } else {
          console.log(`   ❌ ${check}: 缺失`);
          allPassed = false;
        }
      });

      console.log(`   📄 文件大小: ${Math.round(content.length / 1024)}KB`);
    } else {
      console.log(`   ❌ 文件不存在`);
      allPassed = false;
    }
  } catch (error) {
    console.log(`   ❌ 读取错误: ${error.message}`);
    allPassed = false;
  }

  console.log('');
});

// 检查email.ts文件
console.log('⚙️ **邮件发送函数检查**：\n');

try {
  const emailContent = fs.readFileSync('src/lib/email.ts', 'utf8');

  // 检查重复函数
  const functions = [
    'sendTaskCancelledPublisherEmail',
    'sendTaskCancelledAccepterEmail',
    'sendTaskAcceptedPublisherEmail',
    'sendWithdrawalApprovedEmail',
    'sendWithdrawalRejectedEmail',
  ];

  functions.forEach(func => {
    const matches = emailContent.match(
      new RegExp(`export async function ${func}`, 'g')
    );
    if (matches && matches.length === 1) {
      console.log(`✅ ${func}: 正常 (1个定义)`);
    } else if (matches && matches.length > 1) {
      console.log(`❌ ${func}: 重复定义 (${matches.length}个)`);
      allPassed = false;
    } else {
      console.log(`❌ ${func}: 未找到`);
      allPassed = false;
    }
  });

  // 检查国际化支持
  if (emailContent.includes("const language = data.language || 'zh';")) {
    console.log('✅ 语言检测逻辑: 正常');
  } else {
    console.log('❌ 语言检测逻辑: 缺失');
    allPassed = false;
  }
} catch (error) {
  console.log(`❌ email.ts 检查失败: ${error.message}`);
  allPassed = false;
}

// 检查index.ts导出
console.log('\n📦 **模板导出检查**：\n');

try {
  const indexContent = fs.readFileSync(
    'src/lib/email-templates/index.ts',
    'utf8'
  );

  const exports = [
    'withdrawalApprovedTemplateI18n',
    'withdrawalRejectedTemplateI18n',
    'taskCancelledPublisherTemplateI18n',
    'taskCancelledAccepterTemplateI18n',
    'taskAcceptedPublisherTemplateI18n',
  ];

  exports.forEach(exp => {
    if (indexContent.includes(exp)) {
      console.log(`✅ ${exp}: 已导出`);
    } else {
      console.log(`❌ ${exp}: 未导出`);
      allPassed = false;
    }
  });
} catch (error) {
  console.log(`❌ index.ts 检查失败: ${error.message}`);
  allPassed = false;
}

// 检查API路由
console.log('\n🌐 **API路由检查**：\n');

try {
  const routeContent = fs.readFileSync(
    'src/app/api/send-email/route.ts',
    'utf8'
  );

  const apiTypes = [
    'task-cancelled-publisher',
    'task-cancelled-accepter',
    'task-accepted-publisher',
  ];

  apiTypes.forEach(type => {
    if (routeContent.includes(`'${type}'`)) {
      console.log(`✅ ${type}: API支持`);
    } else {
      console.log(`❌ ${type}: API缺失`);
      allPassed = false;
    }
  });

  if (routeContent.includes('getUserEmailLanguage')) {
    console.log('✅ 用户语言检测: 已集成');
  } else {
    console.log('❌ 用户语言检测: 未集成');
    allPassed = false;
  }
} catch (error) {
  console.log(`❌ API路由检查失败: ${error.message}`);
  allPassed = false;
}

console.log('\n=== 最终结果 ===');

if (allPassed) {
  console.log('🎉 **所有检查通过！RefundGo邮件国际化系统已完全实现**');
  console.log('\n✨ **系统特性**：');
  console.log('   • 完整的中英文双语邮件模板');
  console.log('   • 智能的用户语言检测机制');
  console.log('   • 统一的邮件设计风格');
  console.log('   • 完整的API路由支持');
  console.log('   • 邮件主题和内容的国际化');
  console.log('   • 时间和金额的本地化格式');

  console.log('\n🌐 **支持的邮件类型**：');
  console.log('   • 注册验证码邮件');
  console.log('   • 任务完成通知邮件');
  console.log('   • 充值成功/失败邮件');
  console.log('   • 提现审核通过/拒绝邮件');
  console.log('   • 委托取消通知邮件');
  console.log('   • 委托被接受通知邮件');

  console.log('\n🚀 **使用方式**：');
  console.log('   • 系统自动根据用户注册语言发送邮件');
  console.log('   • API: POST /api/send-email');
  console.log('   • 支持中文(zh)和英文(en)');

  console.log('\n🎊 RefundGo邮件国际化系统已准备就绪！');
} else {
  console.log('⚠️ **部分检查未通过，需要进一步修复**');
  console.log('\n建议：');
  console.log('   • 检查重复的函数定义');
  console.log('   • 确认所有模板文件存在');
  console.log('   • 验证导出配置正确');
  console.log('   • 测试API路由集成');
}

console.log('\n=== 验证完成 ===');
