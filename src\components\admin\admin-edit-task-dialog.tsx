'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Edit, Save, X, MapPin, Phone, User } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

// 编辑收货信息表单Schema
const editShippingSchema = z.object({
  recipientName: z
    .string()
    .min(1, '收货人姓名不能为空')
    .max(50, '收货人姓名不能超过50个字符'),
  recipientPhone: z
    .string()
    .min(1, '收货人电话不能为空')
    .regex(/^[+]?[\d\s\-()]{6,20}$/, '请输入有效的电话号码'),
  shippingAddress: z
    .string()
    .min(1, '收货地址不能为空')
    .max(500, '收货地址不能超过500个字符'),
});

type EditShippingFormData = z.infer<typeof editShippingSchema>;

interface AdminEditTaskDialogProps {
  task: any;
  children: React.ReactNode;
  onTaskUpdated?: (updatedTask: any) => void;
}

export function AdminEditTaskDialog({
  task,
  children,
  onTaskUpdated,
}: AdminEditTaskDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<EditShippingFormData>({
    resolver: zodResolver(editShippingSchema),
    defaultValues: {
      recipientName: task.recipientName || '',
      recipientPhone: task.recipientPhone || '',
      shippingAddress: task.shippingAddress || '',
    },
  });

  const onSubmit = async (data: EditShippingFormData) => {
    try {
      setIsLoading(true);

      // 准备提交数据 - 包含API必要字段，但只更新收货信息
      const updateData = {
        // API必要字段 - 保持原值
        title: task.title,
        productDescription: task.productDescription,
        quantity: task.quantity,
        unitPrice: task.unitPrice,
        status: task.status,
        evidenceStatus: task.evidenceStatus,
        evidenceRejectReason: task.evidenceRejectReason,
        // 收货信息 - 更新的字段
        recipientName: data.recipientName,
        recipientPhone: data.recipientPhone,
        shippingAddress: data.shippingAddress,
      };

      // 调用API更新委托
      const response = await fetch(`/api/admin/tasks/${task.id}/edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '更新收货信息失败');
      }

      const result = await response.json();

      toast.success('收货信息更新成功');
      setIsOpen(false);

      // 通知父组件委托已更新
      if (onTaskUpdated) {
        onTaskUpdated(result.task);
      }
    } catch (error) {
      console.error('更新收货信息失败:', error);
      toast.error(error instanceof Error ? error.message : '更新收货信息失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      // 重置表单
      form.reset();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='max-w-lg'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Edit className='h-5 w-5' />
            编辑收货信息
          </DialogTitle>
          <DialogDescription>修改委托的收货人信息和收货地址</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            {/* 收货信息 */}
            <div className='space-y-4'>
              <FormField
                control={form.control}
                name='recipientName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <User className='h-4 w-4' />
                      收货人姓名
                    </FormLabel>
                    <FormControl>
                      <Input placeholder='请输入收货人姓名' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='recipientPhone'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <Phone className='h-4 w-4' />
                      收货人电话
                    </FormLabel>
                    <FormControl>
                      <Input placeholder='请输入收货人电话' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='shippingAddress'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='flex items-center gap-2'>
                      <MapPin className='h-4 w-4' />
                      收货地址
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='请输入详细的收货地址'
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
              >
                <X className='h-4 w-4 mr-2' />
                取消
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? (
                  <>
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className='h-4 w-4 mr-2' />
                    保存更改
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
