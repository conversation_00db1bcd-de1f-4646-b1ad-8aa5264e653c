"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { ArrowR<PERSON>, Play, Sparkles, TrendingUp, Shield, Users, Clock, CheckCircle } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useRouter } from "@/i18n/navigation";

export function HeroSection() {
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 1000], [0, 50]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0.3]);
  const t = useTranslations('homepage');
  const router = useRouter();
  const { data: session } = useSession();
  const [currentStat, setCurrentStat] = useState(0);

  // 动态统计数据轮播
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStat((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const scrollToBrandIntro = () => {
    const element = document.getElementById("brand-intro");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const stats = [
    { value: "20K+", label: t("brand.stats.users"), icon: Users, color: "text-blue-600" },
    { value: "98.5%", label: t("brand.stats.successRate"), icon: TrendingUp, color: "text-green-600" },
    { value: "$13M+", label: t("brand.stats.volume"), icon: Shield, color: "text-purple-600" },
    { value: "24/7", label: t("brand.stats.support"), icon: Clock, color: "text-orange-600" },
  ];

  return (
    <motion.section
      style={{ y, opacity }}
      className="relative min-h-screen flex items-center justify-center overflow-hidden pt-24"
    >
      {/* 现代化背景元素 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-hero-bg via-background to-hero-bg dark:from-hero-bg dark:via-background dark:to-hero-bg" />

        {/* 动态几何形状 */}
        <motion.div
          className="absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-gradient-primary-from/20 to-gradient-primary-to/20 dark:from-gradient-primary-from/10 dark:to-gradient-primary-to/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear",
          }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-gradient-primary-to/15 to-gradient-primary-from/15 dark:from-gradient-primary-to/8 dark:to-gradient-primary-from/8 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear",
          }}
        />

        {/* 网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,hsl(var(--muted-foreground))_1px,transparent_1px),linear-gradient(to_bottom,hsl(var(--muted-foreground))_1px,transparent_1px)] bg-[size:14px_24px] opacity-10 dark:opacity-5" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center z-10 relative">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="space-y-8 md:space-y-12"
        >
          {/* 顶部标签 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="flex justify-center"
          >
            <Badge className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to text-white px-6 py-2 text-sm font-medium">
              <Sparkles className="h-4 w-4 mr-2" />
              {t("hero.title1")}
            </Badge>
          </motion.div>

          {/* 主标题 */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight"
          >
            <span className="bg-gradient-to-r from-text-primary via-gradient-primary-from to-gradient-primary-to bg-clip-text text-transparent dark:from-foreground dark:via-gradient-primary-from dark:to-gradient-primary-to">
              {t("hero.title2")}
            </span>
          </motion.h1>

          {/* 副标题 */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="text-xl md:text-2xl text-text-secondary dark:text-text-secondary max-w-4xl mx-auto leading-relaxed"
          >
            {t("hero.subtitle")}
          </motion.p>

          {/* CTA按钮组 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-gradient-primary-from to-gradient-primary-to hover:from-gradient-primary-from/90 hover:to-gradient-primary-to/90 text-white shadow-xl hover:shadow-2xl transition-all duration-300 px-8 py-4 text-lg font-semibold group"
              onClick={() => router.push(session?.user ? "/publish" : "/sign-up")}
            >
              {t("hero.postTask")}
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-border dark:border-border hover:border-gradient-primary-from dark:hover:border-gradient-primary-from hover:text-gradient-primary-from dark:hover:text-gradient-primary-from px-8 py-4 text-lg font-semibold group transition-all duration-300"
              onClick={scrollToBrandIntro}
            >
              <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
              {t("hero.learnMore")}
            </Button>
          </motion.div>

          {/* 信任指标 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="bg-card/80 dark:bg-card/80 backdrop-blur-sm rounded-2xl p-6 border border-border/50 dark:border-border/50 shadow-lg hover:shadow-xl transition-all duration-300 group"
                whileHover={{ scale: 1.05, y: -5 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              >
                <div className="flex flex-col items-center space-y-3">
                  <div className={`p-3 rounded-full bg-gradient-to-r from-gradient-primary-from/10 to-gradient-primary-to/10 dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 group-hover:scale-110 transition-transform`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-text-primary dark:text-text-primary">
                    {stat.value}
                  </div>
                  <div className="text-sm text-text-secondary dark:text-text-secondary text-center leading-tight">
                    {stat.label}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* 社会证明 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 mt-12"
          >
            <div className="flex items-center space-x-2 text-text-secondary dark:text-text-secondary">
              <CheckCircle className="h-5 w-5 text-success" />
              <span className="text-sm font-medium">{t("hero.trustIndicators.secure")}</span>
            </div>
            <div className="flex items-center space-x-2 text-text-secondary dark:text-text-secondary">
              <CheckCircle className="h-5 w-5 text-success" />
              <span className="text-sm font-medium">{t("hero.trustIndicators.fast")}</span>
            </div>
            <div className="flex items-center space-x-2 text-text-secondary dark:text-text-secondary">
              <CheckCircle className="h-5 w-5 text-success" />
              <span className="text-sm font-medium">{t("hero.trustIndicators.professional")}</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
}
