'use client';

import { Eye, Calendar, Package, Copy, Truck } from 'lucide-react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { ReactNode } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ViewOrderDialogProps {
  children: ReactNode;
  taskId: string;
  orderNumber?: string;
  orderScreenshot?: string;
  trackingNumber?: string;
  logisticsScreenshots?: string[];
  orderReviewDeadline?: string;
  logisticsReviewDeadline?: string;
  taskStatus?: string;
}

export function ViewOrderDialog({
  children,
  taskId,
  orderNumber,
  orderScreenshot,
  trackingNumber,
  logisticsScreenshots = [],
  orderReviewDeadline,
  logisticsReviewDeadline,
  taskStatus,
}: ViewOrderDialogProps) {
  const t = useTranslations('my-accepted-tasks');

  const copyOrderNumber = () => {
    if (orderNumber) {
      navigator.clipboard.writeText(orderNumber);
      toast.success(t('messages.orderNumberCopied'));
    }
  };

  const copyTrackingNumber = () => {
    if (trackingNumber) {
      navigator.clipboard.writeText(trackingNumber);
      toast.success(t('viewOrder.trackingNumberCopied'));
    }
  };

  const openImage = (imageUrl: string) => {
    window.open(imageUrl, '_blank');
  };

  const getCountdownText = (deadline: string) => {
    const now = new Date();
    const target = new Date(deadline);
    const diffMs = target.getTime() - now.getTime();

    if (diffMs <= 0) {
      return { text: t('time.expired'), isExpired: true, isUrgent: false };
    }

    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);

    if (diffHours > 0) {
      const remainingMinutes = diffMinutes % 60;
      return {
        text: `${diffHours}${t('time.hours')}${remainingMinutes}${t('time.minutes')}`,
        isExpired: false,
        isUrgent: diffHours < 2,
      };
    } else {
      return {
        text: `${diffMinutes}${t('time.minutes')}`,
        isExpired: false,
        isUrgent: true,
      };
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='w-full max-w-md mx-4 sm:mx-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Eye className='h-5 w-5' />
            {t('viewOrder.title')}
          </DialogTitle>
          <DialogDescription>
            {t('viewOrder.taskId')}: {taskId}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 订单信息 */}
          <div className='space-y-2'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                <Package className='h-4 w-4' />
                <span>{t('viewOrder.orderNumber')}</span>
              </div>
              <Badge
                variant='secondary'
                className='bg-green-100 text-green-800 text-xs'
              >
                {t('viewOrder.submitted')}
              </Badge>
            </div>

            {orderNumber ? (
              <div className='flex items-center gap-2'>
                <div className='flex-1 p-2 bg-muted rounded text-sm font-mono'>
                  {orderNumber}
                </div>
                <Button size='sm' variant='outline' onClick={copyOrderNumber}>
                  <Copy className='h-4 w-4' />
                </Button>
              </div>
            ) : (
              <div className='text-sm text-muted-foreground'>
                {t('viewOrder.noOrderNumber')}
              </div>
            )}
          </div>

          {/* 订单截图 */}
          {orderScreenshot && (
            <div className='space-y-2'>
              <div className='text-sm text-muted-foreground'>
                {t('viewOrder.orderScreenshot')}
              </div>
              <div
                className='w-20 h-20 cursor-pointer hover:opacity-80 transition-opacity'
                onClick={() => openImage(orderScreenshot)}
              >
                <Image
                  src={orderScreenshot}
                  alt={t('viewOrder.orderScreenshot')}
                  width={80}
                  height={80}
                  className='w-full h-full rounded border object-cover'
                />
              </div>
            </div>
          )}

          {/* 物流信息 */}
          {trackingNumber && (
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                  <Truck className='h-4 w-4' />
                  <span>{t('viewOrder.trackingNumber')}</span>
                </div>
                {/* 根据委托状态显示不同的状态标识 */}
                {taskStatus === 'PENDING_DELIVERY' ? (
                  <Badge
                    variant='secondary'
                    className='bg-green-100 text-green-800 text-xs'
                  >
                    {t('viewOrder.approved')}
                  </Badge>
                ) : (
                  logisticsReviewDeadline && (
                    <div className='flex items-center gap-1'>
                      <Calendar className='h-3 w-3' />
                      {(() => {
                        const countdown = getCountdownText(
                          logisticsReviewDeadline,
                        );
                        return (
                          <Badge
                            variant={
                              countdown.isExpired
                                ? 'destructive'
                                : countdown.isUrgent
                                  ? 'secondary'
                                  : 'outline'
                            }
                            className={`text-xs ${countdown.isUrgent && !countdown.isExpired ? 'text-orange-600 bg-orange-50' : ''}`}
                          >
                            {countdown.text}
                          </Badge>
                        );
                      })()}
                    </div>
                  )
                )}
              </div>

              <div className='flex items-center gap-2'>
                <div className='flex-1 p-2 bg-muted rounded text-sm font-mono'>
                  {trackingNumber}
                </div>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={copyTrackingNumber}
                >
                  <Copy className='h-4 w-4' />
                </Button>
              </div>
            </div>
          )}

          {/* 物流截图 */}
          {logisticsScreenshots.length > 0 && (
            <div className='space-y-2'>
              <div className='text-sm text-muted-foreground'>
                {t('viewOrder.logisticsScreenshots', {
                  count: logisticsScreenshots.length,
                })}
              </div>
              <div className='flex flex-wrap gap-2'>
                {logisticsScreenshots.map((screenshot, index) => (
                  <div
                    key={index}
                    className='w-20 h-20 cursor-pointer hover:opacity-80 transition-opacity'
                    onClick={() => openImage(screenshot)}
                  >
                    <Image
                      src={screenshot}
                      alt={t('viewOrder.logisticsScreenshotAlt', {
                        index: index + 1,
                      })}
                      width={80}
                      height={80}
                      className='w-full h-full rounded border object-cover'
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 等待物流信息提示 */}
          {!trackingNumber && (
            <div className='p-3 bg-orange-50 border border-orange-200 rounded text-center'>
              <div className='text-sm text-orange-800'>
                {t('viewOrder.waitingForLogistics')}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
