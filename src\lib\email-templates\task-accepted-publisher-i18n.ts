import {
  emailStyles,
  formatEmailDateTime,
  formatEmailAmount,
} from '@/hooks/useEmailTranslation';

export interface TaskAcceptedPublisherEmailData {
  userName: string;
  userEmail: string;
  taskId: string;
  taskTitle: string;
  accepterName: string;
  accepterEmail: string;
  acceptedAt: string;
  taskReward: number;
  currency: string;
  language?: 'zh' | 'en';
}

export const taskAcceptedPublisherTemplateI18n = (
  data: TaskAcceptedPublisherEmailData
): string => {
  const language = data.language || 'zh';
  const langAttr = language === 'en' ? 'en' : 'zh-CN';
  const formattedDate = formatEmailDateTime(data.acceptedAt, language);
  const formattedReward = formatEmailAmount(
    data.taskReward,
    data.currency,
    language
  );

  const translations = {
    zh: {
      common: {
        brandName: 'RefundGo',
        greeting: '您好',
        regards: '此致敬礼',
        team: 'RefundGo 团队',
        footer: {
          copyright: '© 2024 RefundGo. 保留所有权利。',
          contact: '如有疑问，请联系我们的客服团队。',
          security: '为了您的账户安全，请勿向任何人透露您的账户信息。',
        },
        buttons: {
          viewTask: '查看委托详情',
          contactAccepter: '联系接单者',
          manageTasks: '管理我的委托',
        },
      },
      notifications: {
        taskAccepted: {
          title: '委托被接受通知',
          greeting: '您的委托已被接受！',
          description:
            '恭喜！有用户接受了您发布的委托任务，请及时跟进任务进度。',
          taskDetails: '委托详情',
          taskId: '委托编号：',
          taskTitle: '委托标题：',
          taskReward: '委托酬金：',
          acceptedAt: '接受时间：',
          accepterInfo: '接单者信息',
          accepterName: '接单者：',
          accepterEmail: '联系邮箱：',
          nextSteps: '接下来您需要：',
          stepsList: {
            review: '审核接单者提交的订单信息',
            communicate: '与接单者保持良好沟通',
            monitor: '跟踪任务执行进度',
          },
          tips: '温馨提示',
          tipsList: {
            timely: '请及时审核接单者提交的信息',
            communication: '保持与接单者的良好沟通',
            quality: '确保任务按要求完成',
          },
          congratulations: '恭喜您的委托找到了合适的执行者！',
        },
      },
    },
    en: {
      common: {
        brandName: 'RefundGo',
        greeting: 'Hello',
        regards: 'Best regards',
        team: 'RefundGo Team',
        footer: {
          copyright: '© 2024 RefundGo. All rights reserved.',
          contact:
            'If you have any questions, please contact our customer service team.',
          security:
            'For your account security, please do not share your account information with anyone.',
        },
        buttons: {
          viewTask: 'View Task Details',
          contactAccepter: 'Contact Accepter',
          manageTasks: 'Manage My Tasks',
        },
      },
      notifications: {
        taskAccepted: {
          title: 'Task Accepted Notification',
          greeting: 'Your task has been accepted!',
          description:
            'Congratulations! A user has accepted your published task. Please follow up on the task progress promptly.',
          taskDetails: 'Task Details',
          taskId: 'Task ID:',
          taskTitle: 'Task Title:',
          taskReward: 'Task Reward:',
          acceptedAt: 'Accepted At:',
          accepterInfo: 'Accepter Information',
          accepterName: 'Accepter:',
          accepterEmail: 'Contact Email:',
          nextSteps: 'What you need to do next:',
          stepsList: {
            review: 'Review the order information submitted by the accepter',
            communicate: 'Maintain good communication with the accepter',
            monitor: 'Track task execution progress',
          },
          tips: 'Important Notes',
          tipsList: {
            timely:
              'Please review the information submitted by the accepter promptly',
            communication: 'Maintain good communication with the accepter',
            quality: 'Ensure the task is completed according to requirements',
          },
          congratulations:
            'Congratulations on finding a suitable executor for your task!',
        },
      },
    },
  };

  const t = translations[language];

  return `
    <!DOCTYPE html>
    <html lang="${langAttr}">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${t.notifications.taskAccepted.title} - ${t.common.brandName}</title>
      <style>
        ${emailStyles}
        .success-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #10b981, #059669);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 24px;
        }
        .task-card {
          background: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .accepter-card {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 8px 0;
          border-bottom: 1px solid #e2e8f0;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .info-label {
          color: #64748b;
          font-weight: 500;
          min-width: 120px;
        }
        .info-value {
          color: #1e293b;
          font-weight: 600;
          flex: 1;
          text-align: right;
        }
        .reward-highlight {
          font-size: 20px;
          font-weight: bold;
          color: #10b981;
          text-align: center;
          margin: 16px 0;
        }
        .steps-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .steps-list li {
          padding: 8px 0;
          padding-left: 24px;
          position: relative;
          color: #1e40af;
        }
        .steps-list li:before {
          content: "→";
          position: absolute;
          left: 0;
          color: #3b82f6;
          font-weight: bold;
        }
        .tips-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
        }
        .tips-list li {
          padding: 6px 0;
          padding-left: 20px;
          position: relative;
          color: #10b981;
          font-size: 14px;
        }
        .tips-list li:before {
          content: "✓";
          position: absolute;
          left: 0;
          color: #10b981;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="email-header">
          <h1>${t.common.brandName}</h1>
        </div>

        <!-- Content -->
        <div class="email-content">
          <!-- Success Icon -->
          <div class="success-icon">
            <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>

          <!-- Greeting -->
          <h2 style="text-align: center; color: #1e293b; margin-bottom: 8px;">
            ${t.common.greeting}, ${data.userName}！
          </h2>
          
          <h3 style="text-align: center; color: #10b981; margin-bottom: 16px;">
            ${t.notifications.taskAccepted.greeting}
          </h3>

          <p style="text-align: center; color: #64748b; margin-bottom: 24px;">
            ${t.notifications.taskAccepted.description}
          </p>

          <!-- Task Details -->
          <div class="task-card">
            <h4 style="margin-top: 0; color: #16a34a;">
              ${t.notifications.taskAccepted.taskDetails}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskAccepted.taskId}</span>
              <span class="info-value" style="font-family: monospace;">${data.taskId}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskAccepted.taskTitle}</span>
              <span class="info-value">${data.taskTitle}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskAccepted.taskReward}</span>
              <span class="info-value" style="color: #10b981; font-weight: bold;">${formattedReward}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskAccepted.acceptedAt}</span>
              <span class="info-value">${formattedDate}</span>
            </div>
          </div>

          <!-- Accepter Information -->
          <div class="accepter-card">
            <h4 style="margin-top: 0; color: #1e293b;">
              👤 ${t.notifications.taskAccepted.accepterInfo}
            </h4>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskAccepted.accepterName}</span>
              <span class="info-value">${data.accepterName}</span>
            </div>
            
            <div class="info-row">
              <span class="info-label">${t.notifications.taskAccepted.accepterEmail}</span>
              <span class="info-value">${data.accepterEmail}</span>
            </div>
          </div>

          <!-- Next Steps -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              ${t.notifications.taskAccepted.nextSteps}
            </h4>
            <ul class="steps-list">
              <li>${t.notifications.taskAccepted.stepsList.review}</li>
              <li>${t.notifications.taskAccepted.stepsList.communicate}</li>
              <li>${t.notifications.taskAccepted.stepsList.monitor}</li>
            </ul>
          </div>

          <!-- Tips -->
          <div style="margin: 24px 0;">
            <h4 style="color: #1e293b; margin-bottom: 12px;">
              💡 ${t.notifications.taskAccepted.tips}
            </h4>
            <ul class="tips-list">
              <li>${t.notifications.taskAccepted.tipsList.timely}</li>
              <li>${t.notifications.taskAccepted.tipsList.communication}</li>
              <li>${t.notifications.taskAccepted.tipsList.quality}</li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="email-buttons">
            <a href="${process.env.DOMAIN}/my-published-tasks/${data.taskId}" class="email-button email-button-primary">
              ${t.common.buttons.viewTask}
            </a>
            <a href="${process.env.DOMAIN}/my-published-tasks" class="email-button email-button-secondary">
              ${t.common.buttons.manageTasks}
            </a>
          </div>

          <!-- Congratulations -->
          <p style="text-align: center; color: #64748b; margin-top: 32px; font-style: italic;">
            🎉 ${t.notifications.taskAccepted.congratulations}
          </p>
        </div>

        <!-- Footer -->
        <div class="email-footer">
          <p>${t.common.regards},<br>${t.common.team}</p>
          <div class="email-footer-links">
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.contact}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.security}
            </p>
            <p style="margin: 8px 0; color: #64748b; font-size: 12px;">
              ${t.common.footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `.trim();
};
