// Session Management System
import { redirect } from 'next/navigation';

import { auth } from '@/lib/auth';
import prisma from '@/lib/db';

// 会话配置
export const SESSION_CONFIG = {
  // 会话超时时间（毫秒）
  TIMEOUT_DURATION: 30 * 60 * 1000, // 30分钟
  // 警告时间（会话过期前多久警告用户）
  WARNING_DURATION: 5 * 60 * 1000, // 5分钟
  // 最大并发会话数
  MAX_CONCURRENT_SESSIONS: 3,
  // 会话刷新间隔
  REFRESH_INTERVAL: 5 * 60 * 1000, // 5分钟
} as const;

// 会话状态枚举
export enum SessionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED',
  WARNING = 'WARNING',
}

// 会话信息接口
export interface SessionInfo {
  id: string;
  userId: string;
  status: SessionStatus;
  lastActivity: Date;
  expiresAt: Date;
  userAgent?: string;
  ipAddress?: string;
  deviceInfo?: string;
}

// 检查会话是否有效
export async function validateSession(): Promise<boolean> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return false;
    }

    // 检查数据库中的会话状态
    const dbSession = await prisma.session.findFirst({
      where: {
        userId: session.user.id,
        expires: {
          gt: new Date(),
        },
      },
    });

    return !!dbSession;
  } catch {
    return false;
  }
}

// 更新会话活动时间
export async function updateSessionActivity(userId: string): Promise<void> {
  try {
    await prisma.session.updateMany({
      where: {
        userId,
        expires: {
          gt: new Date(),
        },
      },
      data: {
        expires: new Date(Date.now() + SESSION_CONFIG.TIMEOUT_DURATION),
      },
    });
  } catch (error) {
    console.error('Failed to update session activity:', error);
  }
}

// 检查会话超时
export async function checkSessionTimeout(): Promise<SessionStatus> {
  const session = await auth();
  if (!session?.user?.id) {
    return SessionStatus.EXPIRED;
  }

  const dbSession = await prisma.session.findFirst({
    where: {
      userId: session.user.id,
      expires: {
        gt: new Date(),
      },
    },
  });

  if (!dbSession) {
    return SessionStatus.EXPIRED;
  }

  const now = new Date();
  const timeUntilExpiry = dbSession.expires.getTime() - now.getTime();

  if (timeUntilExpiry <= 0) {
    return SessionStatus.EXPIRED;
  } else if (timeUntilExpiry <= SESSION_CONFIG.WARNING_DURATION) {
    return SessionStatus.WARNING;
  } else {
    return SessionStatus.ACTIVE;
  }
}

// 终止会话
export async function terminateSession(
  userId: string,
  sessionId?: string,
): Promise<void> {
  try {
    if (sessionId) {
      // 终止特定会话
      await prisma.session.delete({
        where: {
          id: sessionId,
          userId,
        },
      });
    } else {
      // 终止用户的所有会话
      await prisma.session.deleteMany({
        where: {
          userId,
        },
      });
    }
  } catch (error) {
    console.error('Failed to terminate session:', error);
  }
}

// 获取用户的所有活跃会话
export async function getUserActiveSessions(
  userId: string,
): Promise<SessionInfo[]> {
  try {
    const sessions = await prisma.session.findMany({
      where: {
        userId,
        expires: {
          gt: new Date(),
        },
      },
      orderBy: {
        expires: 'desc',
      },
    });

    return sessions.map(session => ({
      id: session.id,
      userId: session.userId,
      status: SessionStatus.ACTIVE,
      lastActivity: new Date(
        session.expires.getTime() - SESSION_CONFIG.TIMEOUT_DURATION,
      ),
      expiresAt: session.expires,
      userAgent: undefined, // 可以从请求头获取
      ipAddress: undefined, // 可以从请求获取
      deviceInfo: undefined, // 可以解析 user-agent 获取
    }));
  } catch {
    return [];
  }
}

// 检查并清理过期会话
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const result = await prisma.session.deleteMany({
      where: {
        expires: {
          lt: new Date(),
        },
      },
    });

    return result.count;
  } catch {
    return 0;
  }
}

// 限制并发会话数
export async function enforceConcurrentSessionLimit(
  userId: string,
): Promise<void> {
  try {
    const activeSessions = await getUserActiveSessions(userId);

    if (activeSessions.length > SESSION_CONFIG.MAX_CONCURRENT_SESSIONS) {
      // 删除最旧的会话
      const sessionsToDelete = activeSessions
        .sort((a, b) => a.lastActivity.getTime() - b.lastActivity.getTime())
        .slice(
          0,
          activeSessions.length - SESSION_CONFIG.MAX_CONCURRENT_SESSIONS,
        );

      for (const session of sessionsToDelete) {
        await terminateSession(userId, session.id);
      }
    }
  } catch (error) {
    console.error('Failed to enforce session limit:', error);
  }
}

// 会话安全检查
export async function performSecurityCheck(userId: string): Promise<boolean> {
  try {
    // 检查用户账户状态
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        // 可以添加更多安全相关字段，如 isBlocked, isSuspended 等
      },
    });

    if (!user) {
      return false;
    }

    // 检查邮箱验证状态
    if (!user.emailVerified) {
      // 可以根据业务需求决定是否允许未验证邮箱的用户继续使用
      console.warn(`User ${userId} has unverified email`);
    }

    // 可以添加更多安全检查，如：
    // - 检查是否有可疑活动
    // - 检查IP地址变化
    // - 检查设备指纹
    // - 检查地理位置变化

    return true;
  } catch {
    return false;
  }
}

// 自动登出处理
export async function handleAutoLogout(
  userId: string,
  reason: string = 'Session expired',
): Promise<void> {
  try {
    // 记录登出事件
    console.log(`Auto logout for user ${userId}: ${reason}`);

    // 终止所有会话
    await terminateSession(userId);

    // 可以发送通知给用户
    // await sendLogoutNotification(userId, reason);

    // 重定向到登录页面
    redirect('/sign-in');
  } catch (error) {
    console.error('Failed to handle auto logout:', error);
    redirect('/sign-in');
  }
}

// 会话刷新
export async function refreshSession(): Promise<boolean> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return false;
    }

    // 执行安全检查
    const isSecure = await performSecurityCheck(session.user.id);
    if (!isSecure) {
      await handleAutoLogout(session.user.id, 'Security check failed');
      return false;
    }

    // 更新会话活动时间
    await updateSessionActivity(session.user.id);

    return true;
  } catch {
    return false;
  }
}

// 会话监控中间件
export async function sessionMiddleware(): Promise<void> {
  const session = await auth();
  if (!session?.user?.id) {
    return;
  }

  const userId = session.user.id;

  // 检查会话状态
  const status = await checkSessionTimeout();

  switch (status) {
    case SessionStatus.EXPIRED:
      await handleAutoLogout(userId, 'Session expired');
      break;

    case SessionStatus.WARNING:
      // 可以在这里触发前端警告
      console.warn(`Session expiring soon for user ${userId}`);
      break;

    case SessionStatus.ACTIVE:
      // 更新会话活动时间
      await updateSessionActivity(userId);
      break;
  }

  // 执行定期安全检查
  const isSecure = await performSecurityCheck(userId);
  if (!isSecure) {
    await handleAutoLogout(userId, 'Security check failed');
  }

  // 强制执行并发会话限制
  await enforceConcurrentSessionLimit(userId);
}
