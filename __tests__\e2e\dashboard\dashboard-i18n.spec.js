// 简单的测试脚本来验证仪表盘国际化
const { test, expect } = require('@playwright/test');

test('Dashboard internationalization test', async ({ page }) => {
  // 测试中文版本
  console.log('Testing Chinese version...');
  await page.goto('http://localhost:3003/zh');

  // 检查页面是否加载
  await expect(page).toHaveTitle(/RefundGo/);

  // 检查中文内容
  await expect(page.locator('text=全球首个')).toBeVisible();

  console.log('Chinese version loaded successfully');

  // 测试英文版本
  console.log('Testing English version...');
  await page.goto('http://localhost:3003/en');

  // 检查页面是否加载
  await expect(page).toHaveTitle(/RefundGo/);

  // 检查英文内容
  await expect(page.locator('text=Global First')).toBeVisible();

  console.log('English version loaded successfully');

  console.log('All tests passed!');
});
