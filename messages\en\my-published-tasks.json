{"navigation": {"title": "My Published Tasks", "description": "Manage and track all tasks you have published", "breadcrumb": "Task Management"}, "tabs": {"all": "All", "available": "Recruiting", "inProgress": "In Progress", "completed": "Completed", "expired": "Expired", "cancelled": "Cancelled"}, "stats": {"total": "All Tasks", "available": "Recruiting", "inProgress": "In Progress", "completed": "Completed", "expired": "Expired", "cancelled": "Cancelled"}, "status": {"PENDING": "Pending Review", "REJECTED": "Review Failed", "RECRUITING": "Recruiting", "IN_PROGRESS": "In Progress", "PENDING_LOGISTICS": "Pending Logistics", "PENDING_REVIEW": "Pending Review", "PENDING_DELIVERY": "Pending Delivery", "COMPLETED": "Completed", "EXPIRED": "Expired", "CANCELLED": "Cancelled", "title": "Task Status"}, "actions": {"viewDetails": "View Details", "editTask": "Edit Task", "cancelTask": "Cancel Task", "extendDeadline": "Extend", "viewOrder": "View Order", "viewLogistics": "View Logistics", "confirmDelivery": "Confirm Delivery", "publishNew": "Publish New Task", "publishFirst": "Publish First Task", "edit": "Edit", "cancel": "Cancel Task", "cancelling": "Cancelling...", "submitting": "Submitting...", "delete": "Delete", "repost": "Repost", "submitEvidence": "Submit Evidence", "resubmitEvidence": "Resubmit Evidence", "reviewOrder": "Review Order"}, "card": {"platform": "Platform", "category": "Category", "reward": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deadline": "Deadline", "publishedAt": "Published At", "completedAt": "Completed At", "quantity": "Quantity", "unitPrice": "Unit Price", "totalPrice": "Product Total", "taskReward": "Task Reward", "evidenceStatus": "Evidence Status", "publisher": "Publisher", "accepter": "Accepter", "orderNumber": "Order Number", "trackingNumber": "Tracking Number", "applicants": "Applicants"}, "accepterStatus": {"pending": "Pending approval", "rejected": "Review failed", "expired": "Task expired", "cancelled": "Task cancelled", "completed": "Task completed", "recruiting": "Waiting for accepter", "accepter": "Accepter"}, "evidenceStatus": {"PENDING_SUBMISSION": "Pending Upload", "UNDER_REVIEW": "Under Review", "NO_EVIDENCE": "No Evidence", "REVIEWED": "Reviewed", "REJECTED": "Rejected"}, "loading": {"tasks": "Loading task data...", "cancelling": "Cancelling...", "processing": "Processing...", "submitting": "Submitting..."}, "countdown": {"reviewTimeLimit": "Review time limit: {time}", "waitingReview": "Waiting for review", "autoConfirmDelivery": "Auto confirm delivery", "autoConfirmIn": "Auto confirm in {time}", "waitingConfirmDelivery": "Waiting for delivery confirmation", "unknownStatus": "Unknown status", "days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "daysShort": "d", "hoursShort": "h", "minutesShort": "m", "noDeadline": "No deadline", "expired": "Expired", "timeout": "Timeout", "calculating": "Calculating...", "waitingLogistics": "Waiting for logistics", "waitingDelivery": "Waiting for delivery confirmation"}, "empty": {"title": "No Tasks", "all": "You haven't published any tasks yet", "filtered": "No tasks in current status", "description": "Publish your first task to get help", "publishButton": "Publish Task"}, "confirm": {"delete": {"cancel": "Cancel"}}, "dialog": {"taskDetail": {"title": "Task Details", "description": "View complete task information and requirements", "close": "Close"}, "submitEvidence": {"title": "Submit Evidence", "description": "Please upload evidence files related to the task", "uploadRequirements": "Upload Requirements:", "uploadRequirementsDesc": "Please upload clear evidence files, supports image and video formats, single file not exceeding 50MB", "evidenceFiles": "Evidence Files", "evidenceFilesRequired": "Evidence Files *", "uploadPlaceholder": "Click to upload or drag evidence files here", "uploadDescription": "Supports PNG, JPG, MP4, MOV formats, maximum 50MB per file", "submit": "Submit Evidence", "submitting": "Submitting...", "cancel": "Cancel", "validation": {"filesRequired": "Please upload at least one evidence file"}}, "resubmitEvidence": {"title": "Resubmit Evidence", "description": "Please re-upload evidence files", "rejectionReason": "Rejection Reason", "evidenceFiles": "Evidence Files", "uploadHint": "Support image and video formats, max 50MB per file", "submit": "Submit Evidence", "submitting": "Submitting...", "cancel": "Cancel", "validation": {"filesRequired": "Please upload at least one evidence file"}}, "cancelTask": {"title": "Confirm Cancel Task", "description": "Please read the following information carefully before confirming", "taskId": "Task ID", "currentStatus": "Current Status", "deductionAmount": "Deduction Amount", "publishTime": "Publish Time", "cancelNoticeTitle": "Cancellation Notice", "pendingNotice1": "The task is under review, cancellation will stop the review process", "pendingNotice2": "Deducted fees will be fully refunded to your account", "pendingNotice3": "Cancellation cannot be undone, please confirm carefully", "recruitingNotice1": "The task is recruiting, cancellation will remove it from the task hall", "recruitingNotice2": "Deducted fees will be fully refunded to your account", "recruitingNotice3": "If users have already accepted the task, you need to wait for completion first", "recruitingNotice4": "Cancellation cannot be undone, please confirm carefully", "confirm": "Confirm Cancel", "cancel": "Cancel"}, "extendDeadline": {"title": "Extend Deadline", "description": "Select new deadline", "newDeadline": "New Deadline", "confirm": "Confirm Extension", "cancel": "Cancel"}, "confirmDelivery": {"title": "Confirm Delivery", "description": "Confirm that you have received the goods and verified them?", "warning": "Important Notice", "warningText": "After confirming delivery, the deposit will be returned to the accepter and the commission will be paid to the accepter. This action cannot be undone.", "confirm": "Confirm Delivery", "cancel": "Cancel", "actions": {"confirm": "Confirm Delivery", "cancel": "Cancel", "processing": "Processing..."}}, "viewOrder": {"title": "Order Details", "description": "View submitted order number and related information", "orderNumber": "Order Number", "submittedAt": "Submitted At", "orderScreenshot": "Order Screenshot", "copyOrderNumber": "Copy Order Number", "viewScreenshot": "View Screenshot", "close": "Close"}}, "messages": {"loadFailed": "Failed to load task data, please refresh and try again", "cancelSuccess": "Task cancelled successfully", "cancelError": "Failed to cancel task, please try again", "extendSuccess": "Deadline extended successfully", "extendError": "Failed to extend deadline, please try again", "confirmDeliverySuccess": "Delivery confirmed successfully", "confirmDeliveryError": "Failed to confirm delivery, please try again", "submitEvidenceSuccess": "Evidence submitted successfully!", "submitEvidenceSuccessDesc": "Your evidence has been submitted, please wait for review", "submitEvidenceError": "Submission failed", "submitEvidenceErrorDesc": "Please try again or contact customer service", "resubmitEvidenceSuccess": "Evidence resubmitted successfully", "resubmitEvidenceSuccessDesc": "Your evidence has been resubmitted, please wait for review", "resubmitEvidenceError": "Evidence submission failed, please try again", "orderNumberCopied": "Order number copied to clipboard", "loading": "Loading...", "invalidDate": "Invalid date", "networkError": "Network error, please try again", "taskCancelled": "Task Cancelled", "taskCancelledDesc": "Your task has been successfully cancelled, fees will be refunded to your account", "taskCancelFailed": "Cancellation Failed", "taskCancelFailedDesc": "Failed to cancel task, please try again or contact customer service"}, "time": {"remaining": "Remaining", "expired": "Expired", "days": "days", "hours": "hours", "minutes": "minutes", "seconds": "seconds", "calculating": "Calculating...", "overtime": "Overtime", "recruitmentTime": "Recruitment Time", "recruitmentRemaining": "Recruitment Remaining", "logisticsSubmissionTime": "Logistics Submission Time", "logisticsSubmissionRemaining": "Logistics Submission Remaining", "logisticsSubmissionOvertime": "Logistics Submission Overtime", "loading": "Loading...", "notSet": "Not Set"}, "taskDetail": {"title": "Task Details", "submissionOvertime": "Accepter submission has timed out, you can contact customer service", "submissionDeadlinePrefix": "Accepter needs to submit order within", "submissionDeadlineSuffix": "", "recruiting": "Task is recruiting, waiting for accepter", "waitingLogistics": "Waiting for accepter to submit tracking number", "accepterInfo": "Accepter Information", "nickname": "Nickname", "email": "Email", "acceptedTime": "Accepted Time", "basicInfo": "Basic Information", "platform": "Platform", "category": "Category", "publishTime": "Publish Time", "productInfo": "Product Information", "unitPrice": "Unit Price", "quantity": "Quantity", "unit": "pcs", "totalPrice": "Total Price", "deposit": "Required <PERSON><PERSON><PERSON><PERSON>", "productUrl": "Product URL", "productDescription": "Product Description", "cartScreenshots": "<PERSON>t Screenshots", "chargebackInfo": "Chargeback Information", "chargebackTypes": "Chargeback Types", "paymentMethods": "Payment Methods", "shippingInfo": "Shipping Information", "recipientName": "Recipient Name", "recipientPhone": "Contact Phone", "shippingAddress": "Shipping Address", "evidenceStatus": "Evidence Status", "resubmitEvidence": "Resubmit Evidence", "rejectionReason": "Rejection Reason", "evidenceFiles": "Evidence Files", "orderLogisticsInfo": "Order & Logistics Information", "orderNumber": "Order Number", "trackingNumber": "Tracking Number", "orderScreenshot": "Order Screenshot", "logisticsScreenshots": "Logistics Screenshots"}, "evidenceViewer": {"imagePreview": "Image Preview", "videoPreview": "Video Preview", "fileDownload": "File Download", "evidenceFile": "Evidence File", "downloadFile": "Download File", "noEvidenceFiles": "No evidence files", "clickToView": "Click files to view enlarged", "totalFiles": "Total {{count}} files"}, "hooks": {"publishTask": {"success": {"description": "Task ID: {taskId}\nCost: ${cost}\nBalance: ${balance}", "viewTask": "View Task"}, "error": {"insufficientBalance": {"title": "Insufficient Balance", "action": "Top Up"}, "general": {"title": "Publish Failed"}}}, "submitEvidence": {"success": {"title": "Evidence submitted successfully!", "description": "Your evidence has been submitted, please wait for review"}, "error": {"title": "Submission failed", "description": "Please try again or contact customer service"}}, "resubmitEvidence": {"success": {"title": "Evidence resubmitted successfully!", "description": "Your evidence has been resubmitted, please wait for review"}}}}