import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// 放弃委托响应类型
interface AbandonTaskResponse {
  success: boolean;
  message: string;
  data: {
    task: {
      id: string;
      status: string;
      platform: string;
      category: string;
    };
    penalty: {
      amount: number;
      accepterRemainingBalance: number;
      publisherNewBalance: number;
    };
  };
}

export function useAbandonTask() {
  const queryClient = useQueryClient();

  return useMutation<AbandonTaskResponse, Error, string>({
    mutationFn: async (taskId: string) => {
      const response = await fetch(
        `/api/user/accepted-tasks/${taskId}/abandon`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '放弃委托失败');
      }

      return response.json();
    },
    onSuccess: data => {
      toast.success('委托已放弃', {
        description: data.message,
        duration: 5000,
      });

      // 刷新已接受委托列表
      queryClient.invalidateQueries({ queryKey: ['accepted-tasks'] });

      // 刷新委托大厅列表（委托会重新回到招募状态）
      queryClient.invalidateQueries({ queryKey: ['tasks'] });

      // 刷新用户余额等信息
      queryClient.invalidateQueries({ queryKey: ['user-profile'] });
    },
    onError: error => {
      toast.error('放弃委托失败', {
        description: error.message || '网络错误，请稍后重试',
        duration: 5000,
      });
    },
  });
}
